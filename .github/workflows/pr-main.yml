name: dryrun prod Upload DAGS to s3
on:
  pull_request:
    paths:
      - 'dags/**.py'
      - "dags/data/**.csv"
      - "dags/interdb_sql/**/*.sql"
      - "dags/crossdb_sql/**/*.sql"
      - "dags/pre_transform_sql/**/*.sql"
      - "dags/pre_transform_dailysync_sql/**/*.sql"
      - "dags/member_remove_dailysync_sql/**/*.sql"
      - "dags/member_remove_dailysync_seperatedservice_sql/**/*.sql"
      - "dags/validations/**.py"
      - "dags/migration_utils/**.py"
      - "dags/gwl_uat_crossdb_separatedservice_sql/**/*.sql"
      - "requirements.txt"
    types: [opened]
    branches:    
      - 'main'

jobs:
  upload:
    runs-on: ubuntu-22.04
    permissions:
      id-token: write # This is required for requesting the JWT
      contents: read # This is required for actions/checkout
    env:
      AWS_DEFAULT_REGION: ap-southeast-1

    steps:
    - uses: actions/checkout@v4

    - name: configure aws credentials
      uses: aws-actions/configure-aws-credentials@v3
      with:
        role-to-assume: arn:aws:iam::024848471695:role/github-cicd-gwl-role
        role-session-name: '${{ github.event.repository.name }}-${{github.run_number}}'
        aws-region: ap-southeast-1
    
    - name: Upload requirements.txt to S3
      run: aws s3 cp requirements.txt s3://kpc-gwl-airflow-prod/requirements.txt
    
    - name: dryrun Upload file to buckets
      run: aws s3 sync ./dags/ s3://kpc-gwl-airflow-prod/dags/ --exclude "*" --include "*.py" --include "*.sql" --include "*.csv" --dryrun
