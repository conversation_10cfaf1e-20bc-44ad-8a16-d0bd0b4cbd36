import json
import psycopg2
import pandas as pd
import numpy as np
from psycopg2.extras import execute_values


def insert_dataframe_into_table(
    df: pd.DataFrame,
    table_name: str, 
    conn: psycopg2.extensions.connection,
    chunk_size: int = 10000
):
    '''
    Function to insert DataFrame into PostgreSQL
    '''
    cur = conn.cursor()
    # Replace NaN and NaT to None value
    df = df.replace({np.nan: None, pd.NaT: None})
    # Convert DataFrame to a list of tuples
    values = df.values.tolist()
    # Create an SQL INSERT query
    columns = ', '.join(df.columns).lower()
    insert_query = f'INSERT INTO {table_name} ({columns}) VALUES %s'
    # Insert in batches
    for i in range(0, len(values), chunk_size):
        batch = values[i:i + chunk_size]
        execute_values(cur, insert_query, batch)
        conn.commit()
                
def get_tables_and_columns(
    conn: psycopg2.extensions.connection,
    schema: str
) -> pd.DataFrame:
    '''
    Function to retrieve all tables and their columns from a PostgreSQL database.
    '''
    # Query to get tables and columns
    query = '''
        select table_schema, table_name, column_name, data_type 
        from information_schema.columns 
        where table_schema = '%s'
        order by table_name, ordinal_position;
    ''' % (schema)
    # Use cursor to avoid SQLAlchemy warning
    cursor = conn.cursor()
    cursor.execute(query)
    results = cursor.fetchall()
    cursor.close()

    # Create DataFrame from cursor results
    df = pd.DataFrame(results, columns=['table_schema', 'table_name', 'column_name', 'data_type'])
    return df

def get_row_count(
    df: pd.DataFrame,
    table_schema: str,
    table_name: str,
    conn: psycopg2.extensions.connection,
    validation_conn: psycopg2.extensions.connection
) -> pd.DataFrame:
    '''
    Function to count rows
    '''
    validate_query = f'''
        select gwl_column_name
        from validation."ValidationBasicConditions_xc_logic"
        where gwl_table_schema = '{table_schema}' 
            and gwl_table_name = '{table_name}'
            and check_transformation = false
    '''
    # Use cursor to avoid SQLAlchemy warning
    cursor = validation_conn.cursor()
    cursor.execute(validate_query)
    validate_results = cursor.fetchall()
    cursor.close()
    validate_columns = [row[0] for row in validate_results]

    # Create a copy to avoid SettingWithCopyWarning
    df = df[(df['table_schema'] == table_schema) & (df['table_name'] == table_name)].copy()
    columns = df['column_name'].tolist()

    new_validate_columns = []
    for col in columns:
        if col in validate_columns:
            new_validate_columns.append(col) 

    if len(new_validate_columns) > 0:
        # Query to get tables and columns
        query = '''
            select 
                '%s' as table_schema, 
                '%s' as table_name, 
                count(1) as row_count
            from %s
        ''' % (
            table_schema, 
            table_name, 
            f'{table_schema}."{table_name}"'
        )
        # Use cursor to avoid SQLAlchemy warning
        cursor = conn.cursor()
        cursor.execute(query)
        row_count_results = cursor.fetchall()
        cursor.close()

        # Create DataFrame from cursor results
        row_count_df = pd.DataFrame(row_count_results, columns=['table_schema', 'table_name', 'row_count'])
        df = pd.merge(
            df, 
            row_count_df,
            on=['table_schema', 'table_name'],
            how='left'
        )
    else:
        df['row_count'] = None
    return df

def get_row_count_all_tables(
    df: pd.DataFrame,
    conn: psycopg2.extensions.connection,
    validation_conn: psycopg2.extensions.connection
) -> pd.DataFrame:
    '''
    Function to count rows of each tables
    '''
    print('---count rows---')
    row_count_df = pd.DataFrame()
    for index, row in df[['table_schema', 'table_name']].drop_duplicates().iterrows():
        table_schema = row['table_schema']
        table_name = row['table_name']
        print(f'table_schema: {table_schema}')
        print(f'table_name: {table_name}')
        row_count_df = pd.concat(
            [
                row_count_df, 
                get_row_count(
                    df,
                    table_schema, 
                    table_name, 
                    conn,
                    validation_conn
                )
            ], 
            ignore_index=True
        )
    return row_count_df

def get_null_count(
    df: pd.DataFrame,
    table_schema: str,
    table_name: str,
    conn: psycopg2.extensions.connection,
    validation_conn: psycopg2.extensions.connection
) -> pd.DataFrame:
    '''
    Function to count null for each column
    '''
    validate_query = f'''
        select gwl_column_name
        from validation."ValidationBasicConditions_xc_logic"
        where gwl_table_schema = '{table_schema}' 
            and gwl_table_name = '{table_name}'
            and check_transformation = false
    '''
    # Use cursor to avoid SQLAlchemy warning
    cursor = validation_conn.cursor()
    cursor.execute(validate_query)
    validate_results = cursor.fetchall()
    cursor.close()
    validate_columns = [row[0] for row in validate_results]

    # Create a copy to avoid SettingWithCopyWarning
    df = df[(df['table_schema'] == table_schema) & (df['table_name'] == table_name)].copy()
    columns = df['column_name'].tolist()

    new_validate_columns = []
    for col in columns:
        if col in validate_columns:
            new_validate_columns.append(col) 

    if len(new_validate_columns) > 0:
        query = f'''
            select {', '.join(f'sum(case when "{col}" is null then 1 else 0 end) as "{col}"' for col in new_validate_columns)}
            from {table_schema}."{table_name}"
        '''
        # Use cursor to avoid SQLAlchemy warning
        cursor = conn.cursor()
        cursor.execute(query)
        null_count_results = cursor.fetchall()
        cursor.close()

        # Create DataFrame from cursor results
        null_count_df = pd.DataFrame(null_count_results, columns=new_validate_columns)
        null_count_df = null_count_df.T.reset_index()
        null_count_df.columns = ['column_name', 'null_count']
        null_count_df['null_count'] = null_count_df['null_count'].fillna(0)
        df = pd.merge(
            df,
            null_count_df,
            on=['column_name'],
            how='left'
        )
    else:
        df['null_count'] = None
    return df

def get_null_count_all_tables(
    df: pd.DataFrame,
    conn: psycopg2.extensions.connection,
    validation_conn: psycopg2.extensions.connection
) -> pd.DataFrame:
    '''
    Function to count null of each tables
    '''
    print('---count null---')
    null_count_df = pd.DataFrame()
    for index, row in df[['table_schema', 'table_name']].drop_duplicates().iterrows():
        table_schema = row['table_schema']
        table_name = row['table_name']
        print(f'table_schema: {table_schema}')
        print(f'table_name: {table_name}')
        null_count_df = pd.concat(
            [
                null_count_df, 
                get_null_count(
                    df,
                    table_schema, 
                    table_name, 
                    conn,
                    validation_conn
                )
            ], 
            ignore_index=True
        )
    return null_count_df

def get_unique_count(
    df: pd.DataFrame,
    table_schema: str,
    table_name: str,
    conn: psycopg2.extensions.connection,
    validation_conn: psycopg2.extensions.connection
) -> pd.DataFrame:
    '''
    Function to count distinct for each column
    '''
    validate_query = f'''
        select gwl_column_name
        from validation."ValidationBasicConditions_xc_logic"
        where gwl_table_schema = '{table_schema}' 
            and gwl_table_name = '{table_name}'
            and check_unique_count = true
            and check_transformation = false
    '''
    # Use cursor to avoid SQLAlchemy warning
    cursor = validation_conn.cursor()
    cursor.execute(validate_query)
    validate_results = cursor.fetchall()
    cursor.close()
    validate_columns = [row[0] for row in validate_results]

    # Create a copy to avoid SettingWithCopyWarning
    df = df[(df['table_schema'] == table_schema) & (df['table_name'] == table_name)].copy()
    columns = df['column_name'].tolist()

    new_validate_columns = []
    for col in columns:
        if col in validate_columns:
            new_validate_columns.append(col) 

    if len(new_validate_columns) > 0:
        query = f'''
            select {', '.join(f'count(distinct "{col}"::text) as "{col}"' for col in new_validate_columns)}
            from {table_schema}."{table_name}"
        '''
        # Use cursor to avoid SQLAlchemy warning
        cursor = conn.cursor()
        cursor.execute(query)
        unique_count_results = cursor.fetchall()
        cursor.close()

        # Create DataFrame from cursor results
        unique_count_df = pd.DataFrame(unique_count_results, columns=new_validate_columns)
        unique_count_df = unique_count_df.T.reset_index()
        unique_count_df.columns = ['column_name', 'unique_count']
        unique_count_df['unique_count'] = unique_count_df['unique_count'].fillna(0)
        df = pd.merge(
            df,
            unique_count_df,
            on=['column_name'],
            how='left'
        )
    else:
        df['unique_count'] = None
    return df

def get_unique_count_all_tables(
    df: pd.DataFrame,
    conn: psycopg2.extensions.connection,
    validation_conn: psycopg2.extensions.connection
) -> pd.DataFrame:
    '''
    Function to count distinct of each tables
    '''
    print('---count unique---')
    unique_count_df = pd.DataFrame()
    for index, row in df[['table_schema', 'table_name']].drop_duplicates().iterrows():
        table_schema = row['table_schema']
        table_name = row['table_name']
        print(f'table_schema: {table_schema}')
        print(f'table_name: {table_name}')
        unique_count_df = pd.concat(
            [
                unique_count_df, 
                get_unique_count(
                    df,
                    table_schema, 
                    table_name, 
                    conn,
                    validation_conn
                )
            ], 
            ignore_index=True
        )
    return unique_count_df

def get_sum_value(
    df: pd.DataFrame,
    table_schema: str,
    table_name: str,
    conn: psycopg2.extensions.connection,
    validation_conn: psycopg2.extensions.connection
) -> pd.DataFrame:
    '''
    Function to sum value for each numeric column
    '''
    numeric_types = ['integer', 'bigint', 'smallint', 'decimal', 'numeric', 'real', 'double precision']
    validate_query = f'''
        select gwl_column_name
        from validation."ValidationBasicConditions_xc_logic"
        where gwl_table_schema = '{table_schema}' 
            and gwl_table_name = '{table_name}'
            and check_transformation = false
    '''
    # Use cursor to avoid SQLAlchemy warning
    cursor = validation_conn.cursor()
    cursor.execute(validate_query)
    validate_results = cursor.fetchall()
    cursor.close()
    validate_columns = [row[0] for row in validate_results]

    # Create a copy to avoid SettingWithCopyWarning
    df = df[(df['table_schema'] == table_schema) & (df['table_name'] == table_name)].copy()
    columns = df[df['data_type'].isin(numeric_types)]['column_name'].tolist()

    new_validate_columns = []
    for col in columns:
        if col in validate_columns:
            new_validate_columns.append(col) 

    if len(new_validate_columns) > 0:
        query = f'''
            select {', '.join(f'sum("{col}") as "{col}"' for col in new_validate_columns)}
            from {table_schema}."{table_name}"
        '''
        # Use cursor to avoid SQLAlchemy warning
        cursor = conn.cursor()
        cursor.execute(query)
        sum_results = cursor.fetchall()
        cursor.close()

        # Create DataFrame from cursor results
        sum_df = pd.DataFrame(sum_results, columns=new_validate_columns)
        sum_df = sum_df.T.reset_index()
        sum_df.columns = ['column_name', 'sum_value']
        df = pd.merge(
            df,
            sum_df,
            on=['column_name'],
            how='left'
        )
    else:
        df['sum_value'] = None
    return df

def get_sum_value_all_tables(
    df: pd.DataFrame,
    conn: psycopg2.extensions.connection,
    validation_conn: psycopg2.extensions.connection
) -> pd.DataFrame:
    '''
    Function to sum value of each tables
    '''
    print('---sum value---')
    sum_df = pd.DataFrame()
    for index, row in df[['table_schema', 'table_name']].drop_duplicates().iterrows():
        table_schema = row['table_schema']
        table_name = row['table_name']
        print(f'table_schema: {table_schema}')
        print(f'table_name: {table_name}')
        sum_df = pd.concat(
            [
                sum_df, 
                get_sum_value(
                    df,
                    table_schema, 
                    table_name, 
                    conn,
                    validation_conn
                )
            ], 
            ignore_index=True
        )
    return sum_df

def get_min_value(
    df: pd.DataFrame,
    table_schema: str,
    table_name: str,
    conn: psycopg2.extensions.connection,
    validation_conn: psycopg2.extensions.connection
) -> pd.DataFrame:
    '''
    Function to min value for each numeric column
    '''
    numeric_types = ['integer', 'bigint', 'smallint', 'decimal', 'numeric', 'real', 'double precision']
    validate_query = f'''
        select gwl_column_name
        from validation."ValidationBasicConditions_xc_logic"
        where gwl_table_schema = '{table_schema}' 
            and gwl_table_name = '{table_name}'
            and check_transformation = false
    '''
    # Use cursor to avoid SQLAlchemy warning
    cursor = validation_conn.cursor()
    cursor.execute(validate_query)
    validate_results = cursor.fetchall()
    cursor.close()
    validate_columns = [row[0] for row in validate_results]

    # Create a copy to avoid SettingWithCopyWarning
    df = df[(df['table_schema'] == table_schema) & (df['table_name'] == table_name)].copy()
    columns = df[df['data_type'].isin(numeric_types)]['column_name'].tolist()

    new_validate_columns = []
    for col in columns:
        if col in validate_columns:
            new_validate_columns.append(col) 

    if len(new_validate_columns) > 0:
        query = f'''
            select {', '.join(f'min("{col}") as "{col}"' for col in columns)}
            from {table_schema}."{table_name}"
        '''
        # Use cursor to avoid SQLAlchemy warning
        cursor = conn.cursor()
        cursor.execute(query)
        min_results = cursor.fetchall()
        cursor.close()

        # Create DataFrame from cursor results
        min_df = pd.DataFrame(min_results, columns=columns)
        min_df = min_df.T.reset_index()
        min_df.columns = ['column_name', 'min_value']
        df = pd.merge(
            df,
            min_df,
            on=['column_name'],
            how='left'
        )
    else:
        df['min_value'] = None
    return df

def get_min_value_all_tables(
    df: pd.DataFrame,
    conn: psycopg2.extensions.connection,
    validation_conn: psycopg2.extensions.connection
) -> pd.DataFrame:
    '''
    Function to min value of each tables
    '''
    print('---min value---')
    min_df = pd.DataFrame()
    for index, row in df[['table_schema', 'table_name']].drop_duplicates().iterrows():
        table_schema = row['table_schema']
        table_name = row['table_name']
        print(f'table_schema: {table_schema}')
        print(f'table_name: {table_name}')
        min_df = pd.concat(
            [
                min_df, 
                get_min_value(
                    df,
                    table_schema, 
                    table_name, 
                    conn,
                    validation_conn
                )
            ], 
            ignore_index=True
        )
    return min_df

def get_max_value(
    df: pd.DataFrame,
    table_schema: str,
    table_name: str,
    conn: psycopg2.extensions.connection,
    validation_conn: psycopg2.extensions.connection
) -> pd.DataFrame:
    '''
    Function to max value for each numeric column
    '''
    numeric_types = ['integer', 'bigint', 'smallint', 'decimal', 'numeric', 'real', 'double precision']
    validate_query = f'''
        select gwl_column_name
        from validation."ValidationBasicConditions_xc_logic"
        where gwl_table_schema = '{table_schema}' 
            and gwl_table_name = '{table_name}'
            and check_transformation = false
    '''
    # Use cursor to avoid SQLAlchemy warning
    cursor = validation_conn.cursor()
    cursor.execute(validate_query)
    validate_results = cursor.fetchall()
    cursor.close()
    validate_columns = [row[0] for row in validate_results]

    # Create a copy to avoid SettingWithCopyWarning
    df_filtered = df[(df['table_schema'] == table_schema) & (df['table_name'] == table_name)].copy()
    columns = df_filtered[df_filtered['data_type'].isin(numeric_types)]['column_name'].tolist()

    new_validate_columns = []
    for col in columns:
        if col in validate_columns:
            new_validate_columns.append(col)

    if len(new_validate_columns) > 0:
        query = f'''
            select {', '.join(f'max("{col}") as "{col}"' for col in columns)}
            from {table_schema}."{table_name}"
        '''
        # Use cursor to avoid SQLAlchemy warning
        cursor = conn.cursor()
        cursor.execute(query)
        max_results = cursor.fetchall()
        cursor.close()

        # Create DataFrame from cursor results
        max_df = pd.DataFrame(max_results, columns=columns)
        max_df = max_df.T.reset_index()
        max_df.columns = ['column_name', 'max_value']
        df_filtered = pd.merge(
            df_filtered,
            max_df,
            on=['column_name'],
            how='left'
        )
    else:
        df_filtered['max_value'] = None

    return df_filtered

def get_max_value_all_tables(
    df: pd.DataFrame,
    conn: psycopg2.extensions.connection,
    validation_conn: psycopg2.extensions.connection
) -> pd.DataFrame:
    '''
    Function to max value of each tables
    '''
    print('---max value---')
    max_df = pd.DataFrame()
    for index, row in df[['table_schema', 'table_name']].drop_duplicates().iterrows():
        table_schema = row['table_schema']
        table_name = row['table_name']
        print(f'table_schema: {table_schema}')
        print(f'table_name: {table_name}')
        max_df = pd.concat(
            [
                max_df, 
                get_max_value(
                    df,
                    table_schema, 
                    table_name, 
                    conn,
                    validation_conn
                )
            ], 
            ignore_index=True
        )
    return max_df

def build_validation_dataframe(
    temp_db_conn: psycopg2.extensions.connection,
    gwl_db_conn: psycopg2.extensions.connection,
    schema_list: list[str]
) -> pd.DataFrame:
    '''
    Function to build validation dataframe
    '''
    validation_df = pd.DataFrame()
    for schema in schema_list:
        temp_df = get_tables_and_columns(temp_db_conn, schema)
        gwl_df = get_tables_and_columns(gwl_db_conn, schema)
        # row count
        temp_df = get_row_count_all_tables(temp_df, temp_db_conn, gwl_db_conn)
        gwl_df = get_row_count_all_tables(gwl_df, gwl_db_conn, gwl_db_conn)
        # null count
        temp_df = get_null_count_all_tables(temp_df, temp_db_conn, gwl_db_conn)
        gwl_df = get_null_count_all_tables(gwl_df, gwl_db_conn, gwl_db_conn)
        # unique count
        temp_df = get_unique_count_all_tables(temp_df, temp_db_conn, gwl_db_conn)
        gwl_df = get_unique_count_all_tables(gwl_df, gwl_db_conn, gwl_db_conn)
        # sum numeric columns
        temp_df = get_sum_value_all_tables(temp_df, temp_db_conn, gwl_db_conn)
        gwl_df = get_sum_value_all_tables(gwl_df, gwl_db_conn, gwl_db_conn)
        # min numeric columns
        temp_df = get_min_value_all_tables(temp_df, temp_db_conn, gwl_db_conn)
        gwl_df = get_min_value_all_tables(gwl_df, gwl_db_conn, gwl_db_conn)
        # max numeric columns
        temp_df = get_max_value_all_tables(temp_df, temp_db_conn, gwl_db_conn)
        gwl_df = get_max_value_all_tables(gwl_df, gwl_db_conn, gwl_db_conn)
        
        merge_df = pd.merge(
            temp_df.add_prefix('temp_'), 
            gwl_df.add_prefix('gwl_'), 
            left_on=['temp_table_schema', 'temp_table_name', 'temp_column_name'],
            right_on=['gwl_table_schema', 'gwl_table_name', 'gwl_column_name'],
            how='outer'
        )
        validation_df = pd.concat([validation_df, merge_df], ignore_index=True) 
    return validation_df

def evaluate_condition(condition, row):
    try:        
        # Perform actual validation between source and target values
        if row['source'] is None:
            condition = condition.replace(f'source_values', 'None')
        else:
            condition = condition.replace(f'source_values', str(row['source']))
        if row['target'] is None:
            condition = condition.replace(f'target_values', 'None')
        else:
            condition = condition.replace(f'target_values', str(row['target']))
        return eval(condition)
    except Exception as e:
        print(f"Error evaluating condition: {condition}, Error: {e}")
        return False

def validate_data_with_conditions(row, conn):
    query = f'''
        select *
        from validation."ValidationTransformConditions"
        where gwl_table_schema = '{row['gwl_table_schema']}'
              and gwl_table_name = '{row['gwl_table_name']}'
              and gwl_column_name = '{row['gwl_column_name']}' 
    '''
    gwl_column_name = row['gwl_column_name']
    # Use cursor to avoid SQLAlchemy warning
    cursor = conn.cursor()
    cursor.execute(query)
    transformation_rules_results = cursor.fetchall()

    # Get column names from cursor description
    column_names = [desc[0] for desc in cursor.description] if cursor.description else []
    cursor.close()
    transformation_rules_df = pd.DataFrame(transformation_rules_results, columns=column_names)

    for rule in transformation_rules_df.to_dict(orient='records'):
        source_logic_sql = rule['source_logic']
        target_logic_sql = rule['target_logic']
        validation_criteria = rule['validation_criteria']
        join_key = rule['join_key']
        n_samples = rule['n_samples']
        if n_samples is None or n_samples.lower() == 'nan' or n_samples.lower() == 'null':
            n_samples = ''
        else:
            n_samples = f'limit {n_samples}'

        query = f'''
            select source.source_values as source, target.target_values as target
            from (
                {source_logic_sql}
                {n_samples}
            ) source
            left join (
                {target_logic_sql} 
            ) target
            on {join_key}
        '''
        print(query)
        # Use cursor to avoid SQLAlchemy warning
        cursor = conn.cursor()
        cursor.execute(query)
        query_results = cursor.fetchall()

        # Get column names from cursor description
        column_names = [desc[0] for desc in cursor.description] if cursor.description else []
        cursor.close()
        df = pd.DataFrame(query_results, columns=column_names)
        
        for row in df.to_dict(orient='records'):
            if evaluate_condition(validation_criteria, row):
                continue
            else:
                print('Validation result: FAILED')
                return False, f'Error while validating column: {gwl_column_name}'
        print('Validation result: SUCCESS')
    return True, None

def validate_sample_uniqueness(row, conn):
    
    n_samples = row['n_samples_uniqueness']
    if n_samples is None:
        n_samples = ''
    else:
        n_samples = f'limit {n_samples}'
    query = f'''
        select count(distinct "{row['gwl_column_name']}")
        from (
            select "{row['gwl_column_name']}"
            from "{row['gwl_table_schema']}"."{row['gwl_table_name']}"
            {n_samples}
        )
    '''
    cursor = conn.cursor()
    cursor.execute(query)
    count_value = cursor.fetchone()[0]
    cursor.close()
    
    if row['n_samples_uniqueness'] is None:
        if count_value == row['gwl_row_count']:
            return True, None
        else:
            return False, 'the column is not unique'
    else:
        if count_value == row['n_samples_uniqueness']:
            return True, None
        else:
            return False, 'the column is not unique'

def validate_all_condition(row, conn):
    if row['check_transformation'] is True:
        return validate_data_with_conditions(row, conn)
    else:
        # is table exist?
        if row['temp_column_name'] is None or row['gwl_column_name'] is None:
            return False, 'Error: Table or column is missing'
            
        # if check_data_type is True, checking that temp_data_type == gwl_data_type
        if row['check_data_type'] is True:
            if row['temp_data_type'] != row['gwl_data_type']:
                return False, 'Error: Data type mismatch'
                
        # if check_row_count is True, checking that temp_row_count == gwl_row_count
        if row['check_row_count'] is True:
            if row['temp_row_count'] != row['gwl_row_count']:
                return False, 'Error: Missing row data'
                
        # if check_null_count is True, checking that temp_null_count == gwl_null_count
        if row['check_null_count'] is True:
            if row['temp_null_count'] != row['gwl_null_count']:
                return False, 'Error: Mismatch in null count'
                
        # if check_unique_count is True, checking that temp_unique_count == gwl_unique_count
        if row['check_unique_count'] is True:
            if row['temp_unique_count'] != row['gwl_unique_count']:
                return False, 'Error: Mismatch in unique count'
                
        # if check_sum_value is True, checking that temp_sum_value == gwl_sum_value
        if row['check_sum_value'] is True:
            if row['temp_sum_value'] != row['gwl_sum_value']:
                return False, 'Error: Mismatch in the sum of values'
                
        # if check_min_value is True, checking that temp_min_value == gwl_min_value
        if row['check_min_value'] is True:
            if row['temp_min_value'] != row['gwl_min_value']:
                return False, 'Error: Mismatch in the min of values'
                
        # if check_max_value is True, checking that temp_max_value == gwl_max_value
        if row['check_max_value'] is True:
            if row['temp_max_value'] != row['gwl_max_value']:
                return False, 'Error: Mismatch in the max of values'
        
        # if check_samples_uniqueness is True, checking that column should be unique
        if row['check_samples_uniqueness'] is True:
            return validate_sample_uniqueness(row, conn)
    return True, None

def get_summary_results(
    df: pd.DataFrame
) -> pd.DataFrame:
    def filter_errors(columns, errors):
        """Filters out None values from error_message while keeping alignment with gwl_column_name."""
        return [(col, err) for col, err in zip(columns, errors) if err is not None]

    agg_df = df.groupby(['gwl_table_schema', 'gwl_table_name']).agg({
        'is_match': 'all',  # False if at least one False exists
        'gwl_column_name': lambda col: list(col),
        'error_message': lambda messages: list(messages)
    }).reset_index()
    # Concatenating gwl_column_name with error_column
    agg_df['error_message'] = agg_df.apply(
        lambda row: filter_errors(row['gwl_column_name'], row['error_message']),
        axis=1
    ).apply(json.dumps)

    agg_df.drop(columns=['gwl_column_name'], inplace=True)
    return agg_df