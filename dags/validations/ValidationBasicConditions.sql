-- engagement_service.MemberCoupon
INSERT INTO validation."ValidationBasicConditions" (gwl_table_schema,gwl_table_name,gwl_column_name,check_data_type,check_row_count,check_null_count,check_unique_count,check_sum_value,check_min_value,check_max_value,check_samples_uniqueness,n_samples_uniqueness,check_transformation,is_migrated) VALUES
	 ('engagement_service','MemberCoupon','id',false,false,false,false,false,false,false,false,NULL,true,true), 
	 ('engagement_service','MemberCoupon','memberId',false,false,false,false,false,false,false,false,NULL,true,true),
 	 ('engagement_service','MemberCoupon','entityType',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCoupon','entityId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCoupon','usedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCoupon','expiredAt',false,false,false,false,false,false,false,false,NULL,true,true),
 	 ('engagement_service','MemberCoupon','claimExpiredAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCoupon','couponRef',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCoupon','isUnlimited',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCoupon','isUsedForGuest',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCoupon','status',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCoupon','sourceType',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCoupon','sourceId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCoupon','remark',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCoupon','isActive',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCoupon','createdAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCoupon','updatedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCoupon','deletedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCoupon','updatedBy',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCoupon','createdBy',false,false,false,false,false,false,false,false,NULL,true,true);
	
-- engagement_service.MemberCouponActivity
INSERT INTO validation."ValidationBasicConditions" (gwl_table_schema,gwl_table_name,gwl_column_name,check_data_type,check_row_count,check_null_count,check_unique_count,check_sum_value,check_min_value,check_max_value,check_samples_uniqueness,n_samples_uniqueness,check_transformation,is_migrated) VALUES
	 ('engagement_service','MemberCouponActivity','id',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCouponActivity','memberId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCouponActivity','refId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCouponActivity','refType',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCouponActivity','remark',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCouponActivity','activity',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCouponActivity','couponCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCouponActivity','reason',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCouponActivity','location',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCouponActivity','createdBy',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCouponActivity','usedBy',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCouponActivity','createdAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberCouponActivity','deletedAt',false,false,false,false,false,false,false,false,NULL,true,true);
	
-- engagement_service.MemberPrivilege
INSERT INTO validation."ValidationBasicConditions" (gwl_table_schema,gwl_table_name,gwl_column_name,check_data_type,check_row_count,check_null_count,check_unique_count,check_sum_value,check_min_value,check_max_value,check_samples_uniqueness,n_samples_uniqueness,check_transformation,is_migrated) VALUES
	 ('engagement_service','MemberPrivilege','id',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberPrivilege','memberId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberPrivilege','privilegeId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberPrivilege','status',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberPrivilege','isUnlimited',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberPrivilege','issuerType',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberPrivilege','issuerCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberPrivilege','issuerIdentifier',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberPrivilege','issuer',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberPrivilege','grantedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberPrivilege','memberPrivilegeLogId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberPrivilege','createdAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberPrivilege','updatedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberPrivilege','createdBy',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('engagement_service','MemberPrivilege','updatedBy',false,false,false,false,false,false,false,false,NULL,true,true);

-- loyalty_service.Member
INSERT INTO validation."ValidationBasicConditions" (gwl_table_schema,gwl_table_name,gwl_column_name,check_data_type,check_row_count,check_null_count,check_unique_count,check_sum_value,check_min_value,check_max_value,check_samples_uniqueness,n_samples_uniqueness,check_transformation,is_migrated) VALUES
	 ('loyalty_service','Member','id',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','gwlNo',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','embossNo',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','email',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','emailVerifiedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','phone',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','phoneVerifiedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','registeredAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','deletedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','tierId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','minimumTierId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','tierStartedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','tierEndedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','accumulateSpending',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','lifeTimeSpending',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','createdAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','updatedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','isActive',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','reason',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','picRemark',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','referralCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','upgradeGroupCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','upgradeReasonCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','registrationChannelCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','registrationLocationCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','accumulateMaintainSpending',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','shoppingCardId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','onepassId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','phoneCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','isCoBrandNonMember',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','emailHash',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','phoneHash',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','minimumTierInvitedId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','updatedBy',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','Member','remark',false,false,false,false,false,false,false,false,NULL,true,true);
	
-- loyalty_service.SalesTransaction
INSERT INTO validation."ValidationBasicConditions" (gwl_table_schema,gwl_table_name,gwl_column_name,check_data_type,check_row_count,check_null_count,check_unique_count,check_sum_value,check_min_value,check_max_value,check_samples_uniqueness,n_samples_uniqueness,check_transformation,is_migrated) VALUES
	 ('loyalty_service','SalesTransaction','id',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','SalesTransaction','memberId',false,false,false,false,false,false,false,false,NULL,true,true),
 	 ('loyalty_service','SalesTransaction','completedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','SalesTransaction','externalId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','SalesTransaction','createdAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','SalesTransaction','updatedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','SalesTransaction','netTotalAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','SalesTransaction','totalAccumSpendableAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','SalesTransaction','branchCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','SalesTransaction','brandCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','SalesTransaction','partnerCode',false,false,false,false,false,false,false,false,NULL,true,true);
	
-- loyalty_service.MemberProfile
INSERT INTO validation."ValidationBasicConditions" (gwl_table_schema,gwl_table_name,gwl_column_name,check_data_type,check_row_count,check_null_count,check_unique_count,check_sum_value,check_min_value,check_max_value,check_samples_uniqueness,n_samples_uniqueness,check_transformation,is_migrated) VALUES
	 ('loyalty_service','MemberProfile','id',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','memberId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','firstName',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','firstNameTh',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','middleName',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','middleNameTh',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','lastName',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','lastNameTh',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','cid',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','passportNo',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','passportExpiryDate',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','dateOfBirth',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','gender',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','addressLine',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','subDistrict',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','district',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','province',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','city',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','postalCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','createdAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','updatedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','occupation',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','title',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','countryCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','nationalityCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','firstNameHash',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','firstNameThHash',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','middleNameHash',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','middleNameThHash',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','lastNameHash',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','lastNameThHash',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','cidHash',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','passportNoHash',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','dateOfBirthHash',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','genderHash',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','addressLineHash',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','createdBy',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberProfile','updatedBy',false,false,false,false,false,false,false,false,NULL,true,true);
	

-- loyalty_service.StaffProfile
INSERT INTO validation."ValidationBasicConditions" (gwl_table_schema,gwl_table_name,gwl_column_name,check_data_type,check_row_count,check_null_count,check_unique_count,check_sum_value,check_min_value,check_max_value,check_samples_uniqueness,n_samples_uniqueness,check_transformation,is_migrated) VALUES
	 ('loyalty_service','StaffProfile','id',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','StaffProfile','memberId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','StaffProfile','staffLevelCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','StaffProfile','companyCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','StaffProfile','staffNo',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','StaffProfile','createdAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','StaffProfile','updatedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','StaffProfile','staffPosition',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','StaffProfile','staffDivision',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','StaffProfile','staffJobLevel',false,false,false,false,false,false,false,false,NULL,true,true);
	 
-- loyalty_service.MemberCoBrandCard
INSERT INTO validation."ValidationBasicConditions" (gwl_table_schema,gwl_table_name,gwl_column_name,check_data_type,check_row_count,check_null_count,check_unique_count,check_sum_value,check_min_value,check_max_value,check_samples_uniqueness,n_samples_uniqueness,check_transformation,is_migrated) VALUES
	 ('loyalty_service','MemberCoBrandCard','id',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberCoBrandCard','memberId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberCoBrandCard','coBrandId',false,false,false,false,false,false,false,false,NULL,true,true),
 	 ('loyalty_service','MemberCoBrandCard','cardNo',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberCoBrandCard','memberCoBrandCardImportId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberCoBrandCard','status',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberCoBrandCard','createdAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberCoBrandCard','createdBy',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberCoBrandCard','updatedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberCoBrandCard','updatedBy',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberCoBrandCard','cardHolderName',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberCoBrandCard','cardHolderNameHash',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberCoBrandCard','remark',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberCoBrandCard','cardReason',false,false,false,false,false,false,false,false,NULL,true,true);

-- loyalty_service.MemberLegacyTierHistory
INSERT INTO validation."ValidationBasicConditions" (gwl_table_schema,gwl_table_name,gwl_column_name,check_data_type,check_row_count,check_null_count,check_unique_count,check_sum_value,check_min_value,check_max_value,check_samples_uniqueness,n_samples_uniqueness,check_transformation,is_migrated) VALUES
	 ('loyalty_service','MemberLegacyTierHistory','id',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberLegacyTierHistory','memberId',false,false,false,false,false,false,false,false,NULL,true,true),
 	 ('loyalty_service','MemberLegacyTierHistory','cardTypeCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberLegacyTierHistory','description',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberLegacyTierHistory','embossNo',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberLegacyTierHistory','tierStartedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberLegacyTierHistory','tierEndedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberLegacyTierHistory','cardStatus',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberLegacyTierHistory','cardReason',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberLegacyTierHistory','createdAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberLegacyTierHistory','updatedAt',false,false,false,false,false,false,false,false,NULL,true,true);
	
-- loyalty_service.MemberLegacyCoBrandHistory
INSERT INTO validation."ValidationBasicConditions" (gwl_table_schema,gwl_table_name,gwl_column_name,check_data_type,check_row_count,check_null_count,check_unique_count,check_sum_value,check_min_value,check_max_value,check_samples_uniqueness,n_samples_uniqueness,check_transformation,is_migrated) VALUES
	 ('loyalty_service','MemberLegacyCoBrandHistory','id',false,false,false,false,false,false,false,false,NULL,true,true),
 	 ('loyalty_service','MemberLegacyCoBrandHistory','memberId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberLegacyCoBrandHistory','cardTypeCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberLegacyCoBrandHistory','description',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberLegacyCoBrandHistory','embossNo',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberLegacyCoBrandHistory','startedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberLegacyCoBrandHistory','endedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberLegacyCoBrandHistory','cardStatus',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberLegacyCoBrandHistory','cardReason',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberLegacyCoBrandHistory','createdAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','MemberLegacyCoBrandHistory','updatedAt',false,false,false,false,false,false,false,false,NULL,true,true);

-- loyalty_service.RefundSalesTransaction
INSERT INTO validation."ValidationBasicConditions" (gwl_table_schema,gwl_table_name,gwl_column_name,check_data_type,check_row_count,check_null_count,check_unique_count,check_sum_value,check_min_value,check_max_value,check_samples_uniqueness,n_samples_uniqueness,check_transformation,is_migrated) VALUES
	 ('loyalty_service','RefundSalesTransaction','id',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','RefundSalesTransaction','memberId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','RefundSalesTransaction','type',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','RefundSalesTransaction','salesTransactionId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','RefundSalesTransaction','externalId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','RefundSalesTransaction','refundedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','RefundSalesTransaction','refundAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','RefundSalesTransaction','revokeAccumSpendableAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','RefundSalesTransaction','createdAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('loyalty_service','RefundSalesTransaction','updatedAt',false,false,false,false,false,false,false,false,NULL,true,true);
	
-- partner_service.SalesTransaction
INSERT INTO validation."ValidationBasicConditions" (gwl_table_schema,gwl_table_name,gwl_column_name,check_data_type,check_row_count,check_null_count,check_unique_count,check_sum_value,check_min_value,check_max_value,check_samples_uniqueness,n_samples_uniqueness,check_transformation,is_migrated) VALUES
	 ('partner_service','SalesTransaction','id',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','memberId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','gwlNo',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','externalId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','taxInvoice',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','partnerId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','brandId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','branchId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','netTotalAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','totalOriginalPrice',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','totalCaratEarnableAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','totalCashbackEarnableAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','totalAccumSpendableAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','importTaxAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','shippingAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','status',false,false,false,false,false,false,false,false,NULL,true,true),
 	 ('partner_service','SalesTransaction','settings',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','rawRequest',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','completedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','createdAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','updatedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','memberShoppingCardId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','refundStatus',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','detail',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','documentDate',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','status',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransaction','status',false,false,false,false,false,false,false,false,NULL,true,true);
	
-- partner_service.RefundSalesTransaction
INSERT INTO validation."ValidationBasicConditions" (gwl_table_schema,gwl_table_name,gwl_column_name,check_data_type,check_row_count,check_null_count,check_unique_count,check_sum_value,check_min_value,check_max_value,check_samples_uniqueness,n_samples_uniqueness,check_transformation,is_migrated) VALUES
	 ('partner_service','RefundSalesTransaction','id',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','RefundSalesTransaction','salesTransactionId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','RefundSalesTransaction','type',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','RefundSalesTransaction','taxInvoices',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','RefundSalesTransaction','externalId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','RefundSalesTransaction','reason',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','RefundSalesTransaction','caratRefundAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','RefundSalesTransaction','caratRevokeAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','RefundSalesTransaction','cashbackRefundAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','RefundSalesTransaction','cashbackRevokeAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','RefundSalesTransaction','chargeBackAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','RefundSalesTransaction','refundedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','RefundSalesTransaction','createdAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','RefundSalesTransaction','updatedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','RefundSalesTransaction','detail',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','RefundSalesTransaction','approvedAt',false,false,false,false,false,false,false,false,NULL,true,true);
	
-- partner_service.SalesTransactionItem
INSERT INTO validation."ValidationBasicConditions" (gwl_table_schema,gwl_table_name,gwl_column_name,check_data_type,check_row_count,check_null_count,check_unique_count,check_sum_value,check_min_value,check_max_value,check_samples_uniqueness,n_samples_uniqueness,check_transformation,is_migrated) VALUES
	 ('partner_service','SalesTransactionItem','id',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionItem','productId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionItem','quantity',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionItem','netAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionItem','caratEarnableAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionItem','normalPointEarned',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionItem','burnPaymentAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionItem','settings',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionItem','paymentDetail',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionItem','createdAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionItem','updatedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionItem','originalPrice',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionItem','salesTransactionId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionItem','cashbackEarnableAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionItem','tierExtraPointEarned',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionItem','sku',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionItem','couponExtraPointEarned',false,false,false,false,false,false,false,false,NULL,true,true);

-- partner_service.SalesTransactionBurnPayment
INSERT INTO validation."ValidationBasicConditions" (gwl_table_schema,gwl_table_name,gwl_column_name,check_data_type,check_row_count,check_null_count,check_unique_count,check_sum_value,check_min_value,check_max_value,check_samples_uniqueness,n_samples_uniqueness,check_transformation,is_migrated) VALUES
	 ('partner_service','SalesTransactionBurnPayment','id',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionBurnPayment','walletCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionBurnPayment','burnAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionBurnPayment','beforeAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionBurnPayment','afterAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionBurnPayment','paymentAmount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionBurnPayment','settings',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionBurnPayment','createdAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionBurnPayment','updatedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionBurnPayment','salesTransactionId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionBurnPayment','burnPurpose',false,false,false,false,false,false,false,false,NULL,true,true);

-- partner_service.SalesTransactionPayment
INSERT INTO validation."ValidationBasicConditions" (gwl_table_schema,gwl_table_name,gwl_column_name,check_data_type,check_row_count,check_null_count,check_unique_count,check_sum_value,check_min_value,check_max_value,check_samples_uniqueness,n_samples_uniqueness,check_transformation,is_migrated) VALUES
	 ('partner_service','SalesTransactionPayment','id',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionPayment','paymentMethodId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionPayment','amount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionPayment','settings',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionPayment','createdAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionPayment','updatedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionPayment','salesTransactionId',false,false,false,false,false,false,false,false,NULL,true,true);
	
-- partner_service.RefundSalesTransactionItem
INSERT INTO validation."ValidationBasicConditions" (gwl_table_schema,gwl_table_name,gwl_column_name,check_data_type,check_row_count,check_null_count,check_unique_count,check_sum_value,check_min_value,check_max_value,check_samples_uniqueness,n_samples_uniqueness,check_transformation,is_migrated) VALUES
	 ('partner_service','RefundSalesTransactionItem','id',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','RefundSalesTransactionItem','refundSalesTransactionId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','RefundSalesTransactionItem','salesTransactionItemId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','RefundSalesTransactionItem','quantity',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','RefundSalesTransactionItem','refundWallets',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','RefundSalesTransactionItem','revokeWallets',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','RefundSalesTransactionItem','createdAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','RefundSalesTransactionItem','updatedAt',false,false,false,false,false,false,false,false,NULL,true,true);

-- point_service.WalletAdjustmentTransaction
INSERT INTO validation."ValidationBasicConditions" (gwl_table_schema,gwl_table_name,gwl_column_name,check_data_type,check_row_count,check_null_count,check_unique_count,check_sum_value,check_min_value,check_max_value,check_samples_uniqueness,n_samples_uniqueness,check_transformation,is_migrated) VALUES
	 ('point_service','WalletAdjustmentTransaction','id',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletAdjustmentTransaction','memberId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletAdjustmentTransaction','walletCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletAdjustmentTransaction','reasonCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletAdjustmentTransaction','amount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletAdjustmentTransaction','type',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletAdjustmentTransaction','remark',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletAdjustmentTransaction','createdBy',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletAdjustmentTransaction','createdAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletAdjustmentTransaction','refNo',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletAdjustmentTransaction','expiredAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletAdjustmentTransaction','businessAreaBranchCode',false,false,false,false,false,false,false,false,NULL,true,true);

-- point_service.WalletBalance
INSERT INTO validation."ValidationBasicConditions" (gwl_table_schema,gwl_table_name,gwl_column_name,check_data_type,check_row_count,check_null_count,check_unique_count,check_sum_value,check_min_value,check_max_value,check_samples_uniqueness,n_samples_uniqueness,check_transformation,is_migrated) VALUES
	 ('point_service','WalletBalance','id',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletBalance','memberId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletBalance','walletCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletBalance','amount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletBalance','createdAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletBalance','updatedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletBalance','expiredAt',false,false,false,false,false,false,false,false,NULL,true,true);
	
-- point_service.WalletActivity
INSERT INTO validation."ValidationBasicConditions" (gwl_table_schema,gwl_table_name,gwl_column_name,check_data_type,check_row_count,check_null_count,check_unique_count,check_sum_value,check_min_value,check_max_value,check_samples_uniqueness,n_samples_uniqueness,check_transformation,is_migrated) VALUES
	 ('point_service','WalletActivity','id',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletActivity','memberId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletActivity','walletCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletActivity','type',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletActivity','refType',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletActivity','refId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletActivity','externalId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletActivity','amount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletActivity','partnerCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletActivity','brandCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletActivity','branchCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletActivity','detail',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletActivity','createdAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletActivity','updatedAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletActivity','documentDate',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletActivity','beforeBalance',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletActivity','afterBalance',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletActivity','remark',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletActivity','deletedAt',false,false,false,false,false,false,false,false,NULL,true,true);

-- point_service.WalletTransaction
INSERT INTO validation."ValidationBasicConditions" (gwl_table_schema,gwl_table_name,gwl_column_name,check_data_type,check_row_count,check_null_count,check_unique_count,check_sum_value,check_min_value,check_max_value,check_samples_uniqueness,n_samples_uniqueness,check_transformation,is_migrated) VALUES
	 ('point_service','WalletTransaction','id',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletTransaction','memberId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletTransaction','walletActivityId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletTransaction','balanceId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletTransaction','type',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletTransaction','walletCode',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletTransaction','amount',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletTransaction','expiredAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('point_service','WalletTransaction','createdAt',false,false,false,false,false,false,false,false,NULL,true,true);

-- partner_service.SalesTransactionWalletActivity
INSERT INTO validation."ValidationBasicConditions" (gwl_table_schema,gwl_table_name,gwl_column_name,check_data_type,check_row_count,check_null_count,check_unique_count,check_sum_value,check_min_value,check_max_value,check_samples_uniqueness,n_samples_uniqueness,check_transformation,is_migrated) VALUES
	 ('partner_service','SalesTransactionWalletActivity','id',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionWalletActivity','salesTransactionId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionWalletActivity','type',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionWalletActivity','activityId',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionWalletActivity','detail',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionWalletActivity','createdAt',false,false,false,false,false,false,false,false,NULL,true,true),
	 ('partner_service','SalesTransactionWalletActivity','updatedAt',false,false,false,false,false,false,false,false,NULL,true,true);