from datetime import datetime
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import Python<PERSON>perator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from migration_utils.pipeline_ops import (
    process_service_tables,
    process_pre_transform_tables,
    process_transformed_tables,
    truncate_tables
)
from migration_utils.ulid_utils import generate_ulid
from _table_sequence import ALL_MIGRATION_TABLES, NOT_IN_TEMP_TABLES, GEN_ULID_TABLES

def truncate_all_services():
    """Truncate staging tables for all services at once"""
    services = ["engagement_service", "partner_service", "loyalty_service", "point_service"]
    for service in services:
        service_tables = [t for t in ALL_MIGRATION_TABLES if t.startswith(f'{service}.')]
        # staging_tables = [f'''staging_{t.split('.')[-1].replace('"', '')}''' for t in service_tables]
        staging_tables = [f'''staging_{t}''' for t in service_tables]
        truncate_tables(', '.join(staging_tables))

def run_crossdb_for_service(service_name: str):
    """Run cross DB operations for a specific service"""
    migrated_tables = [table for table in ALL_MIGRATION_TABLES if table not in NOT_IN_TEMP_TABLES]
    process_service_tables(migrated_tables, service_name, "crossdb")

# def run_transform_load_for_service(service_name: str):
#     """Run transform and load for a specific service"""
#     process_service_tables(ALL_MIGRATION_TABLES, service_name, "transform")

def run_transform_load_for_service():
    """Run transform and load by order table"""
    process_transformed_tables(ALL_MIGRATION_TABLES)

def generate_ulid_for_service():
    for table_dict in GEN_ULID_TABLES:
        SOURCE_TABLE = table_dict['SOURCE_TABLE']
        TARGET_TABLE = table_dict['TARGET_TABLE']
        PK_NAME = table_dict['PK_NAME']
        generate_ulid(
            SOURCE_TABLE=SOURCE_TABLE,
            TARGET_TABLE=TARGET_TABLE,
            PK_NAME=PK_NAME
        )

with DAG(
    dag_id="migration_temp_gwl_chunk_rfct",
    start_date=datetime(2022, 6, 28),
    schedule_interval=None,
    catchup=False,
    tags=["crossdb_stg_gwl", "chunking", "rfct"],
) as dag:

    start_migration_task = EmptyOperator(task_id="start_migration")
    end_migration_task = EmptyOperator(task_id="end_migration")
    
    # Step 1: Truncate all services at once
    truncate_task = PythonOperator(
        task_id="1_truncate_all_services",
        python_callable=truncate_all_services,
    )

    # Step 2: Cross DB operations for each service in sequence
    service_names = ["engagement_service", "partner_service", "loyalty_service", "point_service"]
    crossdb_tasks = {}
    for idx, service in enumerate(service_names, 1):
        task = PythonOperator(
            task_id=f"2_{idx}_crossdb_{service}",
            python_callable=run_crossdb_for_service,
            op_args=[service],
        )
        crossdb_tasks[service] = task

    # Step 3: Pre-transform step (follows PRE_TRANSFORM_ORDER)
    pre_transform_task = PythonOperator(
        task_id="3_pre_transform_tables",
        python_callable=process_pre_transform_tables,
    )

    # Step 4: Gen ULID
    generate_ulid_task = PythonOperator(
        task_id="4_generate_ulid",
        python_callable=generate_ulid_for_service,
    )

    # Step 5: Transform and load for each service in sequence
    transform_task = PythonOperator(
        task_id=f"5_transform_load_tables",
        python_callable=run_transform_load_for_service
    )

    # Step 6: Trigger Validation DAG
    trigger_validation_dag_task = TriggerDagRunOperator(
        task_id='6_trigger_validation',
        trigger_dag_id='validation_temp_to_gwl',
        wait_for_completion=True,  # Wait for previous dag to complete before continuing
        poke_interval=60  # Check every 60 seconds
    )

    # Set dependencies
    start_migration_task >> truncate_task
    # Start with truncate
    truncate_task >> crossdb_tasks["engagement_service"]
    
    # # Chain crossdb tasks in sequence
    crossdb_tasks["engagement_service"] >> crossdb_tasks["partner_service"]
    crossdb_tasks["partner_service"] >> crossdb_tasks["loyalty_service"] 
    crossdb_tasks["loyalty_service"] >> crossdb_tasks["point_service"]
    
    # # Connect last crossdb to pre-transform
    crossdb_tasks["point_service"] >> pre_transform_task
    
    # # Connect pre-transform to transform task
    pre_transform_task >> generate_ulid_task
    generate_ulid_task >> transform_task

    # # Connect transform task to validation task
    transform_task >> trigger_validation_dag_task

    trigger_validation_dag_task >> end_migration_task
