import os
from airflow.models import Variable

BATCH_SIZE = int(Variable.get("loyalty_service.batch_size", default_var=None))
SALESTRANS_BATCH_SIZE = int(
    Variable.get("loyalty_service.salestrans.batch_size", default_var=None)
)


CLIENT_ID = Variable.get("client_id", default_var=None)
CLIENT_SECRET = Variable.get("client_secret", default_var=None)
ENCRYPT_KEY = Variable.get("SECRET_PII_ENCRYPTION", default_var=None)

# incremental migration: date setting
LS_INCREMENTAL_DATE = Variable.get(
    "loyalty_service.incremental_query_date", default_var=None
)
LS_FULLDUMP_DATE = Variable.get(
    "loyalty_service.full_dump_query_date", default_var=None
)

# cutoff date's `accumulateSpending`
CUTOFF_DATE_ACCUMULATE_SPENDING = Variable.get(
    "loyalty_service.cutoff_accumulate_spending", default_var=None
)

# validation
VALIDATION_CUTOFF_DATE = Variable.get(
    "loyalty_service.validation_cutoff_date", default_var=None
)
VALIDATION_SAMPLE_SIZE = int(
    Variable.get("loyalty_service.validation_sample", default_var=10000)
)


# database connection
NEWMEMBER_CONN_ID = "newmember_smc_db_connection_id"
TEMP_CONN_ID = "temp_db_connection_id"

# data's path
DAGS_DIR = os.path.dirname(os.path.abspath(__file__))
MAPPING_CSV_PATH = os.path.join(DAGS_DIR, "data", "gwl_mapping.csv")
SUBPROGRAM_PATH = os.path.join(DAGS_DIR, "data", "subprogram.csv")
EMAIL_MAPPING_PATH = os.path.join(DAGS_DIR, "data", "email_cleansing.csv")
TITTLE_MAPPING_PATH = os.path.join(DAGS_DIR, "data", "tittle_id.csv")
ISSUED_ADDR_PATH = os.path.join(DAGS_DIR, "data", "issued_address_mapping.csv")

# urls
HEADERS = {
    "partnerCode": "POS",
    "brandCode": "POS_BRAND",
    "branchCode": "POS_BRANCH",
}
BASE_URL = "https://pd6-open-api-gateway.gwl.kpc-dev.com"
ACCESS_TOKEN_URL = BASE_URL + "/api/v1/partners/access-token"
COUNTRY_CODE_URL = BASE_URL + "/api/v1/assets/countries"
ADDRESS_URL = BASE_URL + "/api/v1/assets/addresses"


RECORD_COUNTS_VALIDATION = "validate record counts"
SAMPLE_DATA_VALIDATION = "validate sample data"
AGGREGATE_DATA_VALIDATION = "validate aggregate data"
RECORD_COUNTS_VALIDATION_ERROR = "validate record counts error"
SAMPLE_DATA_VALIDATION_ERROR = "validate sample data error"
AGGREGATE_DATA_VALIDATION_ERROR = "validate aggregate data error"

# mail service
MAIL_SERVER = "smtp.gmail.com" # "***********"
MAIL_PORT = 465 # 25
MAIL_FROM = "<EMAIL>"
MAIL_PASSWORD = "idxpxgmmetyhqiaz"
MAIL_RECEIVER = [
    "<EMAIL>",
    "<EMAIL>", 
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
]

MAIL_MIGRATION_RESULT_SUBJECT = "[Data Migration][SMC2Temp] Migration result for daily sync {date}"
MAIL_MIGRATION_RESULT_TEXT = "Migration result for daily sync {date} which migrate data from SMC to Temp database."

MAIL_VALIDATION_RESULT_SUBJECT = "[Data Migration][SMC2Temp] Validation result for daily sync {date}"
MAIL_VALIDATION_RESULT_TEXT = "Validation result for daily sync {date} which migrate data from SMC to Temp database."
