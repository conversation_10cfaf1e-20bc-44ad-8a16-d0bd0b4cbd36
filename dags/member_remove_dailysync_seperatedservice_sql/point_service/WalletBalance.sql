WITH fethch_member_delete_list AS 

(SELECT 
    *
FROM dblink(
            'my_connection', 
            'SELECT   
                id,
                "gwlNo",     
                "createdAt",
                "updatedAt",
                "deletedAt"
                
            FROM loyalty_service."Member"'
            )  AS t1(
                    id TEXT,
                    "gwlNo" TEXT,	
                    "createdAt" TIMESTAMP(3),
                    "updatedAt" TIMESTAMP(3),
                    "deletedAt" TIMESTAMP(3)
                    )
WHERE (
        ((t1."createdAt" BETWEEN 'start_timestamps' AND 'end_timestamps') OR (t1."updatedAt" BETWEEN 'start_timestamps' AND 'end_timestamps'))
        AND t1."deletedAt" IS NOT NULL)
)
,members_blacklist AS (
    SELECT 
        *
    FROM dblink(
                'my_gwl_connection', 
                'SELECT   
                    "memberId"
                FROM public.members_blacklist'
                )  AS t1(
                    "memberId" TEXT
                )
)

,stg_m AS (
    SELECT 
        *
    FROM dblink(
                'my_gwl_connection', 
                'SELECT   
                    ulid_id,
                    id,     
                    "createdAt"
                FROM staging_loyalty_service.ulid_member'
                )  AS t1(
                    ulid_id TEXT,
                    id TEXT,	
                    "createdAt" TIMESTAMP(3)
                )
)
,member_delete_list AS
(
    SELECT 
        stg_m.ulid_id as member_uild,
        fmdl."gwlNo" "memberId",
        fmdl."createdAt",
        fmdl."updatedAt",
        fmdl."deletedAt"
    FROM fethch_member_delete_list AS fmdl
    LEFT JOIN stg_m as stg_m     ON fmdl.id = stg_m.id
    LEFT JOIN members_blacklist AS mbl ON fmdl.id = mbl."memberId"
    WHERE mbl."memberId" IS NULL
)
DELETE FROM public."WalletBalance"
WHERE "memberId" IN (SELECT member_uild FROM member_delete_list)




ON CONFLICT -- for prevent error
