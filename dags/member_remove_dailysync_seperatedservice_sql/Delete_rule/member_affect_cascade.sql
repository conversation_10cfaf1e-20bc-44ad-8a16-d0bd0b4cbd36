-- this script is for set delete rule to be cascade on foreign key
-- sql code to drop and recreate foreign key constraints in the database.


-- "memberId" in loyalty_service."Member" affect on:
    
    --Likely Tables with Foreign Keys to loyalty_service."Member".id:

    -- loyalty_service."Member".id affect on:
        -- loyalty_service tables:
    -- loyalty_service."RefundSalesTransaction"
    -- loyalty_service."MemberLegacyTierHistory"
    -- loyalty_service."SalesTransaction"
    -- loyalty_service."MemberLegacyCoBrandHistory"
    -- loyalty_service."MemberLog"
    -- loyalty_service."MemberSubSegment"
    -- loyalty_service."MemberTierHistory"
    -- loyalty_service."MemberCoBrandCard"
        -- loyalty_service."MemberCoBrandCardLog"
    -- loyalty_service."StaffProfile"
    -- loyalty_service."MemberProfile"

--loyalty_service."RefundSalesTransaction" affect on 1 table 2 foreign keys here:
ALTER TABLE public."RefundSalesTransaction" DROP CONSTRAINT "RefundSalesTransaction_memberId_fkey";
ALTER TABLE public."RefundSalesTransaction" ADD CONSTRAINT "RefundSalesTransaction_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES public."Member"(id) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE public."RefundSalesTransaction" DROP CONSTRAINT "RefundSalesTransaction_salesTransactionId_fkey";
ALTER TABLE public."RefundSalesTransaction" ADD CONSTRAINT "RefundSalesTransaction_salesTransactionId_fkey" FOREIGN KEY ("salesTransactionId") REFERENCES public."SalesTransaction"(id) ON DELETE CASCADE ON UPDATE CASCADE;
-- 2s
-- effect from sales transaction is below for more

--loyalty_service."MemberLegacyTierHistory" affect on 1 tables 1 foreign key here:
ALTER TABLE public."MemberLegacyTierHistory" DROP CONSTRAINT "MemberLegacyTierHistory_memberId_fkey";
ALTER TABLE public."MemberLegacyTierHistory" ADD CONSTRAINT "MemberLegacyTierHistory_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES public."Member"(id) ON DELETE CASCADE ON UPDATE CASCADE;
-- 4s

--loyalty_service."SalesTransaction" affect on 1 tables 1 foreign key here:
ALTER TABLE public."SalesTransaction" DROP CONSTRAINT "SalesTransaction_memberId_fkey";
ALTER TABLE public."SalesTransaction" ADD CONSTRAINT "SalesTransaction_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES public."Member"(id) ON DELETE CASCADE ON UPDATE CASCADE;
-- 15s

--loyalty_service."MemberLegacyCoBrandHistory" affect on 1 tables 1 foreign key here:
ALTER TABLE public."MemberLegacyCoBrandHistory" DROP CONSTRAINT "MemberLegacyCoBrandHistory_memberId_fkey";
ALTER TABLE public."MemberLegacyCoBrandHistory" ADD CONSTRAINT "MemberLegacyCoBrandHistory_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES public."Member"(id) ON DELETE CASCADE ON UPDATE CASCADE;

-- Execute time	1m 22s

--loyalty_service."MemberLog" affect on 1 tables 1 foreign key here:
ALTER TABLE public."MemberLog" DROP CONSTRAINT "MemberLog_memberId_fkey";
ALTER TABLE public."MemberLog" ADD CONSTRAINT "MemberLog_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES public."Member"(id) ON DELETE CASCADE ON UPDATE CASCADE;

-- 1s

--loyalty_service."MemberSubSegment" affect on 1 tables 1 foreign key here:
-- another foreign key is master table loyalty_service."SubSegment" do not change it!
ALTER TABLE public."MemberSubSegment" DROP CONSTRAINT "MemberSubSegment_memberId_fkey";
ALTER TABLE public."MemberSubSegment" ADD CONSTRAINT "MemberSubSegment_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES public."Member"(id) ON DELETE CASCADE ON UPDATE CASCADE;

-- 0.5s


-- loyalty_service."MemberTierHistory" affect on 1 tables 1 foreign key here:
ALTER TABLE public."MemberTierHistory" DROP CONSTRAINT "MemberTierHistory_memberId_fkey";
ALTER TABLE public."MemberTierHistory" ADD CONSTRAINT "MemberTierHistory_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES public."Member"(id) ON DELETE CASCADE ON UPDATE CASCADE;

-- ?? can not drop with sql code 2s


-- loyalty_service."MemberCoBrandCard" affect on 2 tables 
    -- 1 foreign key here:
ALTER TABLE public."MemberCoBrandCard" DROP CONSTRAINT "MemberCoBrandCard_memberId_fkey";
ALTER TABLE public."MemberCoBrandCard" ADD CONSTRAINT "MemberCoBrandCard_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES public."Member"(id) ON DELETE CASCADE ON UPDATE CASCADE;

-- -- ?? can not drop with sql code add new 2s

    -- 1 foreign key here:
    -- loyalty_service."MemberCoBrandCardLog"
ALTER TABLE public."MemberCoBrandCardLog" DROP CONSTRAINT "MemberCoBrandCardLog_memberCoBrandCardId_fkey";
ALTER TABLE public."MemberCoBrandCardLog" ADD CONSTRAINT "MemberCoBrandCardLog_memberCoBrandCardId_fkey" FOREIGN KEY ("memberCoBrandCardId") REFERENCES public."MemberCoBrandCard"(id) ON DELETE CASCADE ON UPDATE CASCADE;

-- loyalty_service."StaffProfile" affect on 1 tables 1 foreign key here:
ALTER TABLE public."StaffProfile" DROP CONSTRAINT "StaffProfile_memberId_fkey";
ALTER TABLE public."StaffProfile" ADD CONSTRAINT "StaffProfile_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES public."Member"(id) ON DELETE CASCADE ON UPDATE CASCADE;

-- 2s

-- loyalty_service."MemberProfile" affect on 1 tables 1 foreign key here:
ALTER TABLE public."MemberProfile" DROP CONSTRAINT "MemberProfile_memberId_fkey";
ALTER TABLE public."MemberProfile" ADD CONSTRAINT "MemberProfile_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES public."Member"(id) ON DELETE CASCADE ON UPDATE CASCADE;

-- 8s

-- =================================================================================

    -- engagement_service tables:
        -- engagement_service."CouponClaimTransaction"
    
    -- another on 1 tables 1 foreign key here:
    -- engagement_service."CouponClaimTransaction" affect on 1 tables 1 foreign key here:
ALTER TABLE public."CouponClaimTransaction" DROP CONSTRAINT "CouponClaimTransaction_memberCouponId_fkey";
ALTER TABLE public."CouponClaimTransaction" ADD CONSTRAINT "CouponClaimTransaction_memberCouponId_fkey" FOREIGN KEY ("memberCouponId") REFERENCES public."MemberCoupon"(id) ON DELETE CASCADE ON UPDATE CASCADE;

-- 0.6s


-- =================================================================================

-- "memberId" in loyalty_service."Member" affect on:
    --  Tables with Foreign Keys to point_service:

    -- point_service tables:
        -- point_service."WalletBalance"
            -- point_service."WalletTransaction"
        -- point_service."WalletActivity"
            -- point_service."WalletTransaction"
            -- point_service."VoidWalletActivity"


-- point_service."WalletBalance"
-- have to delete point_service."WalletBalance".id with sql file base on loyalty_service."Member".id = point_service."WalletBalance"."memberId"

    -- point_service."WalletTransaction"
ALTER TABLE public."WalletTransaction" DROP CONSTRAINT IF EXISTS "WalletTransaction_balanceId_fkey";
ALTER TABLE public."WalletTransaction" ADD CONSTRAINT "WalletTransaction_balanceId_fkey" FOREIGN KEY ("balanceId") REFERENCES public."WalletBalance"(id) ON DELETE CASCADE ON UPDATE CASCADE;-- 10s

-- point_service."WalletActivity"
-- have to delete point_service."WalletActivity".id with sql file base on loyalty_service."Member".id = point_service."WalletActivity"."memberId"
    -- point_service."WalletTransaction"
ALTER TABLE public."WalletTransaction" DROP CONSTRAINT "WalletTransaction_walletActivityId_fkey";
ALTER TABLE public."WalletTransaction" ADD CONSTRAINT "WalletTransaction_walletActivityId_fkey" FOREIGN KEY ("walletActivityId") REFERENCES public."WalletActivity"(id) ON DELETE CASCADE ON UPDATE CASCADE;
-- 40s

    -- point_service."VoidWalletActivity"
ALTER TABLE public."VoidWalletActivity" DROP CONSTRAINT "VoidWalletActivity_parentWalletActivityId_fkey";
ALTER TABLE public."VoidWalletActivity" ADD CONSTRAINT "VoidWalletActivity_parentWalletActivityId_fkey" FOREIGN KEY ("parentWalletActivityId") REFERENCES public."WalletActivity"(id) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE public."VoidWalletActivity" DROP CONSTRAINT "VoidWalletActivity_voidWalletActivityId_fkey";
ALTER TABLE public."VoidWalletActivity" ADD CONSTRAINT "VoidWalletActivity_voidWalletActivityId_fkey" FOREIGN KEY ("voidWalletActivityId") REFERENCES public."WalletActivity"(id) ON DELETE CASCADE ON UPDATE CASCADE;
-- 2s





-- =================================================================================

-- partner_service."SalesTransaction".id affect on:             -- loyalty_service."Member".id = partner_service."SalesTransaction"."memberId"
    -- partner_service."SalesTransactionItem":
    -- partner_service."SalesTransactionCoupon":
    -- partner_service."SalesTransactionPayment":
    -- partner_service."SalesTransactionWalletActivity":
    -- partner_service."SalesTransactionBurnPayment":
    -- partner_service."RefundSalesTransaction":
        -- partner_service."RefundSalesTransactionItem
        -- partner_service."RefundSalesTransactionWalletActivity"
        -- partner_service."SalesTransactionCouponAdjustment"


--partner_service."RefundSalesTransaction" affect on 3 tables foreign key here:
ALTER TABLE public."RefundSalesTransaction" DROP CONSTRAINT "RefundSalesTransaction_salesTransactionId_fkey";
ALTER TABLE public."RefundSalesTransaction" ADD CONSTRAINT "RefundSalesTransaction_salesTransactionId_fkey" FOREIGN KEY ("salesTransactionId") REFERENCES public."SalesTransaction"(id) ON DELETE CASCADE ON UPDATE CASCADE;

    -- partner_service."RefundSalesTransactionItem" 2 foreign keys
ALTER TABLE public."RefundSalesTransactionItem" DROP CONSTRAINT "RefundSalesTransactionItem_refundSalesTransactionId_fkey";
ALTER TABLE public."RefundSalesTransactionItem" ADD CONSTRAINT "RefundSalesTransactionItem_refundSalesTransactionId_fkey" FOREIGN KEY ("refundSalesTransactionId") REFERENCES public."RefundSalesTransaction"(id) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE public."RefundSalesTransactionItem" DROP CONSTRAINT "RefundSalesTransactionItem_salesTransactionItemId_fkey";
ALTER TABLE public."RefundSalesTransactionItem" ADD CONSTRAINT "RefundSalesTransactionItem_salesTransactionItemId_fkey" FOREIGN KEY ("salesTransactionItemId") REFERENCES public."SalesTransactionItem"(id) ON DELETE CASCADE ON UPDATE CASCADE;

    -- partner_service."RefundSalesTransactionWalletActivity" affect on 1 table foreign key here:
ALTER TABLE public."RefundSalesTransactionWalletActivity" DROP CONSTRAINT "RefundSalesTransactionWalletActivity_refundSalesTransactio_fkey";
ALTER TABLE public."RefundSalesTransactionWalletActivity" ADD CONSTRAINT "RefundSalesTransactionWalletActivity_refundSalesTransactio_fkey" FOREIGN KEY ("refundSalesTransactionId") REFERENCES public."RefundSalesTransaction"(id) ON DELETE CASCADE ON UPDATE CASCADE;



-- partner_service."SalesTransactionCouponUseLater" affect on 1 table foreign key here:
ALTER TABLE public."SalesTransactionCouponUseLater" DROP CONSTRAINT "SalesTransactionCouponUseLater_salesTransactionId_fkey";
ALTER TABLE public."SalesTransactionCouponUseLater" ADD CONSTRAINT "SalesTransactionCouponUseLater_salesTransactionId_fkey" FOREIGN KEY ("salesTransactionId") REFERENCES public."SalesTransaction"(id) ON DELETE CASCADE ON UPDATE CASCADE;


--partner_service."SalesTransactionItem" affect on 1 table foreign key here:
ALTER TABLE public."SalesTransactionItem" DROP CONSTRAINT "SalesTransactionItem_salesTransactionId_fkey";
ALTER TABLE public."SalesTransactionItem" ADD CONSTRAINT "SalesTransactionItem_salesTransactionId_fkey" FOREIGN KEY ("salesTransactionId") REFERENCES public."SalesTransaction"(id) ON DELETE CASCADE ON UPDATE CASCADE;

-- 1m 56s

-- partner_service."SalesTransactionCoupon" affect on 1 table foreign key here:
ALTER TABLE public."SalesTransactionCoupon" DROP CONSTRAINT "SalesTransactionCoupon_salesTransactionId_fkey";
ALTER TABLE public."SalesTransactionCoupon" ADD CONSTRAINT "SalesTransactionCoupon_salesTransactionId_fkey" FOREIGN KEY ("salesTransactionId") REFERENCES public."SalesTransaction"(id) ON DELETE CASCADE ON UPDATE CASCADE;


-- partner_service."SalesTransactionPayment" affect on 1 table foreign key here:
-- another foreign key is master table partner_service."paymentMethod" do not change it!
ALTER TABLE public."SalesTransactionPayment" DROP CONSTRAINT "SalesTransactionPayment_salesTransactionId_fkey";
ALTER TABLE public."SalesTransactionPayment" ADD CONSTRAINT "SalesTransactionPayment_salesTransactionId_fkey" FOREIGN KEY ("salesTransactionId") REFERENCES public."SalesTransaction"(id) ON DELETE CASCADE ON UPDATE CASCADE;
-- 56s

-- partner_service."SalesTransactionWalletActivity" affect on 1 table foreign key here:
ALTER TABLE public."SalesTransactionWalletActivity" DROP CONSTRAINT "SalesTransactionWalletActivity_salesTransactionId_fkey";
ALTER TABLE public."SalesTransactionWalletActivity" ADD CONSTRAINT "SalesTransactionWalletActivity_salesTransactionId_fkey" FOREIGN KEY ("salesTransactionId") REFERENCES public."SalesTransaction"(id) ON DELETE CASCADE ON UPDATE CASCADE;

-- 35s 

-- partner_service."SalesTransactionBurnPayment" affect on 1 table foreign key here:
ALTER TABLE public."SalesTransactionBurnPayment" DROP CONSTRAINT "SalesTransactionBurnPayment_salesTransactionId_fkey";
ALTER TABLE public."SalesTransactionBurnPayment" ADD CONSTRAINT "SalesTransactionBurnPayment_salesTransactionId_fkey" FOREIGN KEY ("salesTransactionId") REFERENCES public."SalesTransaction"(id) ON DELETE CASCADE ON UPDATE CASCADE;

-- 16s

-- =================================================================================

