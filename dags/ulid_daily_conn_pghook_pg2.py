from migration_utils.ulid_utils import generate_ulid_dailysync
from airflow.operators.python import PythonOperator
from airflow.operators.empty import EmptyOperator
from airflow import DAG
from datetime import datetime
import pytz


# SOURCE_TABLE = 'staging_loyalty_service."Member"'
# TARGET_TABLE = 'staging_loyalty_service."ulid_member"'
# PK_NAME = 'ulid_member_pkey'
# SOURCE_TABLE = 'staging_point_service."WalletActivity"'
# TARGET_TABLE = 'staging_point_service."ulid_WalletActivity"'
# PK_NAME = 'ulid_WalletActivity_pkey'
SOURCE_TABLE = 'staging_point_service."WalletBalanceTemp"'
TARGET_TABLE = 'staging_point_service."ulid_WalletBalance"'
PK_NAME = 'ulid_WalletBalance_pkey'
# SOURCE_TABLE = 'staging_point_service."WalletTransaction"'
# TARGET_TABLE = 'staging_point_service."ulid_WalletTransaction"'
# PK_NAME = 'ulid_WalletTransaction_pkey'
# SOURCE_TABLE = 'staging_point_service."WalletAdjustmentTransaction"'
# TARGET_TABLE = 'staging_point_service."ulid_WalletAdjustmentTransaction"'
# PK_NAME = 'ulid_WalletAdjustmentTransaction_pkey'
# SOURCE_TABLE = 'staging_engagement_service."MemberPrivilege"'
# TARGET_TABLE = 'staging_engagement_service."ulid_MemberPrivilege"'
# PK_NAME = 'ulid_MemberPrivilege_pkey'
# SOURCE_TABLE = 'staging_partner_service."SalesTransactionBurnPayment"'
# TARGET_TABLE = 'staging_partner_service."ulid_SalesTransactionBurnPayment"'
# PK_NAME = 'ulid_SalesTransactionBurnPayment_pkey'
# SOURCE_TABLE = 'public."RefundSalesTransactionItemId"'
# TARGET_TABLE = 'staging_partner_service."ulid_RefundSalesTransactionItem"'
# PK_NAME = 'ulid_RefundSalesTransactionItem_pkey'
# SOURCE_TABLE = 'staging_partner_service."SalesTransactionWalletActivityTemp"'
# TARGET_TABLE = 'staging_partner_service."ulid_SalesTransactionWalletActivity"'
# PK_NAME = 'ulid_SalesTransactionWalletActivity_pkey'
CHUNK_SIZE = 100000
LIMIT_CLAUSE = ''
PAGE_SIZE =10000  # Sub-batch size for execute_values

def generate_ulid_member_table(**kwargs):
    generate_ulid_dailysync(
        SOURCE_TABLE=SOURCE_TABLE,
        TARGET_TABLE=TARGET_TABLE,
        PK_NAME=PK_NAME,
        CHUNK_SIZE=CHUNK_SIZE,
        LIMIT_CLAUSE=LIMIT_CLAUSE,
        PAGE_SIZE=PAGE_SIZE,
        **kwargs
    )

with DAG(
    dag_id="generate_ulid_daily_member_table",
    start_date=None,
    schedule_interval=None,
    catchup=False,
    tags=["gen", "daily","ulid"],
    params= {
        'start_timestamps': datetime.now(pytz.timezone('Asia/Bangkok')).astimezone(pytz.utc),
        'end_timestamps': datetime.now(pytz.timezone('Asia/Bangkok')).astimezone(pytz.utc),
        # pendulum not available in mwaa
        # 'start_timestamps': pendulum.now('Asia/Bangkok').replace(hour=4, minute=0, second=0, microsecond=0).add(days=-1),
        # 'end_timestamps': pendulum.now('Asia/Bangkok').replace(hour=4, minute=0, second=0, microsecond=0),
    # Explanation: the end timestamp for the daily sync is set to 4 AM the next day, so that the sync
    # will process data up to the end of the previous day. This is to ensure that the incremental
    # sync picks up all the data from the previous day, in case the data was not fully processed
    # during the previous sync.
    }
) as dag:
    
    start_migration_task = EmptyOperator(task_id="start_migration")
    end_migration_task = EmptyOperator(task_id="end_migration")

    create_ulid_member_table = PythonOperator(
        task_id="create_ulid_member_table_in_stg",
        python_callable=generate_ulid_member_table,
    )

    start_migration_task>>create_ulid_member_table>>end_migration_task