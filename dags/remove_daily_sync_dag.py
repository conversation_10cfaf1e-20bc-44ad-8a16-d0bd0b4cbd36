from datetime import datetime, timedelta
import pytz
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from migration_utils.pipeline_ops import (
    process_remove_member_dailysync,
    
  
)
from migration_utils.ulid_utils import generate_ulid, generate_ulid_dailysync
from _table_sequence import ALL_MIGRATION_TABLES, NOT_IN_TEMP_TABLES, DELETE_MEMBER_TABLES,GEN_ULID_TABLES



def run_update_member_remove(**kwargs):
    """Run detect member remove and remove from related tables for a specific service"""
    process_remove_member_dailysync(DELETE_MEMBER_TABLES, **kwargs)




# Get the current time in Bangkok, then convert to UTC
now_bangkok = datetime.now(pytz.timezone('Asia/Bangkok'))
now_utc = now_bangkok.astimezone(pytz.utc)

# default_end_timestamp =  '2025-06-16 22:00:00+00:00'
# default_start_timestamp =  '2025-06-15 17:00:00+00:00'
# default_end_timestamp =  '2025-06-13 22:00:00+00:00'
# default_start_timestamp =  '2025-06-07 17:00:00+00:00'
default_end_timestamp =  '2025-06-25 22:00:00+00:00'
default_start_timestamp =  '2025-06-22 17:00:00+00:00'

with DAG(
    dag_id="remove_daily_sync_dag",
    schedule_interval=None, # # 0 22 * * *base on utc +0000 this is 5am bangkok time (utc+0700)
    start_date=datetime(2025, 6, 4, 23, 00),
    catchup=False,
    tags=["remove", "daily_sync"],
    params= {
        'start_timestamps': default_start_timestamp,
        'end_timestamps': default_end_timestamp,   
    }
) as dag:
    
    start_migration_task = EmptyOperator(task_id="start_migration")
    end_migration_task = EmptyOperator(task_id="end_migration")
    
    

    # Step 5: Transform and load for each service in sequence
    run_update_member_remove_task = PythonOperator(
        task_id=f"5_update_member_remove_related_tables",
        python_callable=run_update_member_remove
    )



    # Set dependencies
    start_migration_task >> run_update_member_remove_task
    
    run_update_member_remove_task >> end_migration_task

  

