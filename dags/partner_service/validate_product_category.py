from airflow.exceptions import AirflowException

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>gresHand<PERSON>
from common_helpers.logging import get_logger
from common_helpers.utils import insert_validation_logs
from constants import (
    RECORD_COUNTS_VALIDATION,
    RECORD_COUNTS_VALIDATION_ERROR,
    SAMPLE_DATA_VALIDATION,
    SAMPLE_DATA_VALIDATION_ERROR,
)

logger = get_logger()


class ProductCategoryValidation:
    def __init__(
        self,
        mssql_handler: MSSQLHandler,
        postgresql_handler: PostgresHandler,
    ):
        self.mssql_handler = mssql_handler
        self.postgresql_handler = postgresql_handler
        self.errors = []

    def get_incremental_query_condition(self) -> str:
        """
        Generates a query condition for incremental migration, with specific date supported.

        Args:
            None

        Returns:
            str: A query condition string.
        """
        return "SQL_CreateOn >= DATEADD(DAY, -1, CAST(CAST(GETDATE() AS DATE) AS DATETIME)) AND SQL_CreateOn < CAST(CAST(GETDATE() AS DATE) AS DATETIME)"

    def get_source_count_query_string(self) -> str:
        return """
            SELECT COUNT(*)
            FROM MAST_MC WHERE SQL_CreateOn < CAST('2025-06-20' AS DATETIME);
        """

    def get_source_select_query_string(self, is_full_dump: bool) -> str:
        return f"""
            SELECT
                MCCode AS code,
                MCName AS name
            FROM MAST_MC WHERE {"SQL_CreateOn < CAST('2025-06-20' AS DATETIME)" if is_full_dump else self.get_incremental_query_condition()}
        """

    def validate_record_counts(self):
        destination_count_query_string = """
            SELECT COUNT(*) FROM partner_service."ProductCategory";
        """

        source_count = self.mssql_handler.get_table_total_records(
            self.get_source_count_query_string(),
        )

        postgresql_connection = self.postgresql_handler.hook.get_conn()

        with postgresql_connection.cursor() as cursor:
            cursor.execute(destination_count_query_string)
            destination_count = cursor.fetchone()[0]

        if source_count != destination_count:
            logger.warning(
                f"count mismatch: source has {source_count} records, but destination has {destination_count} records"
            )
            self.errors.append(RECORD_COUNTS_VALIDATION_ERROR)
        else:
            logger.info(f"count validation passed: {source_count} records match")

    def validate_sample_data(self, is_full_dump: bool):
        source_sample_query_string = (
            f"""
                SELECT TOP 100000 * FROM ({self.get_source_select_query_string(is_full_dump)}) AS sample_data ORDER BY NEWID()
            """
            if is_full_dump
            else f"""
                SELECT * FROM ({self.get_source_select_query_string(is_full_dump)}) AS sample_data
            """
        )

        source_records = self.mssql_handler.extract_data(source_sample_query_string)

        if not source_records:
            logger.info("no source records found, skipping sample data validation")

            return

        source_codes = [record[0] for record in source_records]

        destination_sample_query_string = f"""
            SELECT
                "code",
                "name",
                "brandId",
                "createdBy"
            FROM partner_service."ProductCategory"
            WHERE "code" IN ({", ".join(["'" + str(code) + "'" for code in source_codes])})
        """

        postgresql_connection = self.postgresql_handler.hook.get_conn()

        with postgresql_connection.cursor() as cursor:
            cursor.execute(destination_sample_query_string)
            destination_records = cursor.fetchall()

        source_dict = {
            record[0]: {
                "code": record[0],
                "name": record[1],
            }
            for record in source_records
        }

        destination_dict = {
            record[0]: {
                "code": record[0],
                "name": record[1],
                "brandId": record[2],
                "createdBy": record[3],
            }
            for record in destination_records
        }

        source_code = set(source_dict.keys())
        destination_ids = set(destination_dict.keys())
        missing_ids = source_code - destination_ids
        extra_ids = destination_ids - source_code

        data_mismatches = []
        common_ids = source_code.intersection(destination_ids)

        for record_id in common_ids:
            source_data = source_dict[record_id]
            dest_data = destination_dict[record_id]

            mismatches = []

            if source_data["code"] != dest_data["code"]:
                mismatches.append(
                    f"code: source={source_data['code']}, dest={dest_data['code']}"
                )

            if source_data["name"] != dest_data["name"]:
                mismatches.append(
                    f"name: source={source_data['name']}, dest={dest_data['name']}"
                )

            if dest_data["brandId"] != "PARTNER_SERVICE_MIGRATION":
                mismatches.append(
                    f"brandId: source='PARTNER_SERVICE_MIGRATION', dest={dest_data['brandId']}"
                )

            if dest_data["createdBy"] != "PARTNER_SERVICE_MIGRATION":
                mismatches.append(
                    f"createdBy: source={'PARTNER_SERVICE_MIGRATION'}, dest={dest_data['createdBy']}"
                )

            if mismatches:
                data_mismatches.append((record_id, mismatches))

        if missing_ids:
            logger.warning(
                f"found {len(missing_ids)} records missing in destination table"
            )
            logger.warning(f"missing record IDs: {', '.join(sorted(missing_ids))}")
        else:
            logger.info("no missing records found in destination table")

        if extra_ids:
            logger.warning(f"found {len(extra_ids)} extra records in destination table")
            logger.warning(f"extra record IDs: {', '.join(sorted(extra_ids))}")
        else:
            logger.info("no extra records found in destination table")

        if data_mismatches:
            logger.warning(f"found {len(data_mismatches)} records with data mismatches")
            for record_id, mismatches in data_mismatches:
                logger.warning(f"data mismatches for record {record_id}:")
                for mismatch in mismatches:
                    logger.warning(f"  - {mismatch}")
        else:
            logger.info("no data mismatches found in matching records")

        if not missing_ids and not extra_ids and not data_mismatches:
            logger.info(f"sample data validation passed: all sample records match")
        else:
            self.errors.append(SAMPLE_DATA_VALIDATION_ERROR)

    def validate(
        self,
        temp_db_handler: PostgresHandler,
        service: str,
        is_full_dump: bool = True,
    ) -> None:
        validation_types = []

        if is_full_dump:
            logger.info("started record counts validation...")
            self.validate_record_counts()
            logger.info("finished record counts validation.")

            validation_types.append(
                {
                    "name": RECORD_COUNTS_VALIDATION,
                    "error": RECORD_COUNTS_VALIDATION_ERROR,
                }
            )

        logger.info("started sample data validation...")
        self.validate_sample_data(is_full_dump)
        logger.info("finished sample data validation.")

        validation_types.append(
            {
                "name": SAMPLE_DATA_VALIDATION,
                "error": SAMPLE_DATA_VALIDATION_ERROR,
            }
        )

        insert_validation_logs(
            errors=self.errors,
            db_handler=temp_db_handler,
            service=service,
            source_table="MAST_MC",
            destination_table="ProductCategory",
            validation_types=validation_types,
        )

        if len(self.errors):
            raise AirflowException("validation(s) failed.")
