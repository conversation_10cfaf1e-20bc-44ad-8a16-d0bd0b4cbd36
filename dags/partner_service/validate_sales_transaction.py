from airflow.exceptions import AirflowException

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.logging import get_logger
from common_helpers.utils import insert_validation_logs
from constants import (
    AGGREGATE_DATA_VALIDATION,
    AGGREGATE_DATA_VALIDATION_ERROR,
)


logger = get_logger()


class SalesTransactionValidation:
    def __init__(
        self,
        mssql_handler: MSSQLHandler,
        postgresql_handler: PostgresHandler,
    ):
        self.mssql_handler = mssql_handler
        self.postgresql_handler = postgresql_handler
        self.errors = []

    def validate_aggregate_data(self, is_full_dump: bool = True):
        """
        Validate aggregate data by grouping by sales_transaction_id and comparing total amounts.

        Args:
            is_full_dump (bool): Whether this is a full dump validation
        """
        mssql_connection = self.mssql_handler.hook.get_conn()

        try:
            if is_full_dump:
                source_aggregate_query = """
                    SELECT TOP 10000
                        id,
                        SUM(netTotalAmount) as total_net_total_amount,
                        SUM(totalOriginalPrice) as total_original_price,
                        SUM(totalDiscount) as total_discount,
                        SUM(totalEarnableAmount) as total_earnable_amount,
                        SUM(totalAccumSpendableAmount) as total_accum_spendable_amount,
                        SUM(totalPointEarned) as total_point_earned
                    FROM (
                        SELECT id, netTotalAmount, totalOriginalPrice, totalDiscount, totalEarnableAmount, totalAccumSpendableAmount, totalPointEarned
                        FROM temp_sales_transaction_with_lv_header_for_full_dump_migration
                        UNION ALL
                        SELECT id, netTotalAmount, totalOriginalPrice, totalDiscount, totalEarnableAmount, totalAccumSpendableAmount, totalPointEarned
                        FROM temp_sales_transaction_without_lv_header_for_full_dump_migration
                    ) combined_data
                    GROUP BY id
                    ORDER BY NEWID()
                """
            else:
                source_aggregate_query = """
                    SELECT
                        id,
                        SUM(netTotalAmount) as total_net_total_amount,
                        SUM(totalOriginalPrice) as total_original_price,
                        SUM(totalDiscount) as total_discount,
                        SUM(totalEarnableAmount) as total_earnable_amount,
                        SUM(totalAccumSpendableAmount) as total_accum_spendable_amount,
                        SUM(totalPointEarned) as total_point_earned
                    FROM (
                        SELECT id, netTotalAmount, totalOriginalPrice, totalDiscount, totalEarnableAmount, totalAccumSpendableAmount, totalPointEarned
                        FROM temp_sales_transaction_with_lv_header_for_incremental_migration
                        UNION ALL
                        SELECT id, netTotalAmount, totalOriginalPrice, totalDiscount, totalEarnableAmount, totalAccumSpendableAmount, totalPointEarned
                        FROM temp_sales_transaction_without_lv_header_for_incremental_migration
                    ) combined_data
                    GROUP BY id
                """
            with mssql_connection.cursor() as cursor:
                cursor.execute(source_aggregate_query)
                source_aggregates = cursor.fetchall()
        finally:
            if mssql_connection:
                mssql_connection.close()

        source_keys = [str(record[0]) for record in source_aggregates]

        postgresql_connection = self.postgresql_handler.hook.get_conn()

        try:
            keys_for_query = ",".join([f"'{key}'" for key in source_keys])

            if is_full_dump:
                destination_aggregate_query = f"""
                    SELECT 
                        "id",
                        SUM("netTotalAmount") as total_net_total_amount,
                        SUM("totalOriginalPrice") as total_original_price,
                        SUM("totalDiscount") as total_discount,
                        SUM("totalEarnableAmount") as total_earnable_amount,
                        SUM("totalAccumSpendableAmount") as total_accum_spendable_amount,
                        SUM("totalPointEarned") as total_point_earned
                    FROM partner_service."SalesTransaction"
                    WHERE "id" IN ({keys_for_query})
                    GROUP BY "id"
                """
            else:
                destination_aggregate_query = """
                    SELECT 
                        "id",
                        SUM("netTotalAmount") as total_net_total_amount,
                        SUM("totalOriginalPrice") as total_original_price,
                        SUM("totalDiscount") as total_discount,
                        SUM("totalEarnableAmount") as total_earnable_amount,
                        SUM("totalAccumSpendableAmount") as total_accum_spendable_amount,
                        SUM("totalPointEarned") as total_point_earned
                    FROM partner_service."SalesTransaction"
                    WHERE "updatedAt" >= CURRENT_DATE AT TIME ZONE '+07' + INTERVAL '3 hours'
                    AND "updatedAt" <= NOW() AT TIME ZONE '+07'
                    GROUP BY "id"
                """
            with postgresql_connection.cursor() as cursor:
                cursor.execute(destination_aggregate_query)
                destination_aggregates = cursor.fetchall()
        finally:
            if postgresql_connection:
                postgresql_connection.close()

        source_lookup = {}
        for record in source_aggregates:
            key = str(record[0])
            source_lookup[key] = {
                "total_net_total_amount": record[1],
                "total_original_price": record[2],
                "total_discount": record[3],
                "total_earnable_amount": record[4],
                "total_accum_spendable_amount": record[5],
                "total_point_earned": record[6],
            }

        destination_lookup = {}
        for record in destination_aggregates:
            key = str(record[0])
            destination_lookup[key] = {
                "total_net_total_amount": record[1],
                "total_original_price": record[2],
                "total_discount": record[3],
                "total_earnable_amount": record[4],
                "total_accum_spendable_amount": record[5],
                "total_point_earned": record[6],
            }

        source_keys = set(source_lookup.keys())
        destination_keys = set(destination_lookup.keys())
        missing_keys = source_keys - destination_keys
        extra_keys = destination_keys - source_keys

        if missing_keys:
            logger.warning(
                f"found {len(missing_keys)} sales_transaction_id missing in destination"
            )
            self.errors.append(AGGREGATE_DATA_VALIDATION_ERROR)
        else:
            logger.info("no missing sales_transaction_id in destination")

        if extra_keys:
            logger.warning(
                f"found {len(extra_keys)} extra sales_transaction_id in destination"
            )
            self.errors.append(AGGREGATE_DATA_VALIDATION_ERROR)
        else:
            logger.info("no extra sales_transaction_id in destination")

        common_keys = source_keys.intersection(destination_keys)
        aggregate_mismatches = []

        for key in common_keys:
            source_data = source_lookup[key]
            dest_data = destination_lookup[key]

            mismatches = []

            fields_to_compare = [
                "total_net_total_amount",
                "total_original_price", 
                "total_discount",
                "total_earnable_amount",
                "total_accum_spendable_amount",
                "total_point_earned"
            ]

            for field in fields_to_compare:
                if source_data[field] != dest_data[field]:
                    mismatches.append(
                        f"{field}: source={source_data[field]}, dest={dest_data[field]}"
                    )

            if mismatches:
                aggregate_mismatches.append((key, mismatches))

        if aggregate_mismatches:
            logger.warning(
                f"found {len(aggregate_mismatches)} sales_transaction_id with aggregate mismatches"
            )
            for key, mismatches in aggregate_mismatches:
                logger.warning(f"aggregate mismatches for {key}:")
                for mismatch in mismatches:
                    logger.warning(f"  - {mismatch}")
            self.errors.append(AGGREGATE_DATA_VALIDATION_ERROR)
        else:
            logger.info("no aggregate mismatches found")

        if not missing_keys and not extra_keys and not aggregate_mismatches:
            logger.info("aggregate validation passed: all aggregates match")

    def validate(
        self,
        temp_db_handler: PostgresHandler,
        service: str,
        is_full_dump: bool = True,
    ) -> None:
        """
        Run aggregate validation for SalesTransaction.

        Args:
            temp_db_handler (PostgresHandler): Temporary database handler for logging
            service (str): Service name for logging
            is_full_dump (bool): Whether this is a full dump validation
        """
        logger.info("started aggregate data validation...")
        self.validate_aggregate_data(is_full_dump)
        logger.info("finished aggregate data validation.")

        insert_validation_logs(
            errors=self.errors,
            db_handler=temp_db_handler,
            service=service,
            source_table="SMCSalesHeader",
            destination_table="SalesTransaction",
            validation_types=[
                {
                    "name": AGGREGATE_DATA_VALIDATION,
                    "error": AGGREGATE_DATA_VALIDATION_ERROR,
                },
            ],
        )

        if len(self.errors):
            raise AirflowException("validation(s) failed.")
