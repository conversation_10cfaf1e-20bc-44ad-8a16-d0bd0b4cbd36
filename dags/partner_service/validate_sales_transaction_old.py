from datetime import datetime
from dataclasses import dataclass
from typing import Dict, Any, List, Optional

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.logging import get_logger
from common_helpers.utils import insert_validation_logs

logger = get_logger()


@dataclass
class ValidationResult:
    """Data class สำหรับเก็บผลการ validation"""
    step_name: str
    source_value: float
    dest_value: float
    validation_result: str
    accuracy_percentage: float
    error_message: Optional[str] = None


class SalesTransactionValidation:
    def __init__(
        self,
        mssql_handler: MSSQLHandler,
        postgresql_handler: PostgresHandler,
    ) -> None:
        self.mssql_handler = mssql_handler
        self.postgresql_handler = postgresql_handler
        self.validation_results = []

        # Sample data validation query without temp table (Step 8)
        self.sample_validation_query = """
            SELECT TOP 10000
                ssh.key_search,
                TRIM(ssh.member_id) AS member_id,
                ssh.BranchNo,
                ssh.Site,
                ssh.DataDate,
                sst.Net as net_amount,
                sst.OriginalPrice as original_price,
                sst.Discount as discount,
                COALESCE(ssp.totalEarnableAmount, 0) as earnable_amount,
                COALESCE(ssp.totalAccumSpendableAmount, 0) as accum_spendable_amount,
                COALESCE(lvt.totalPointEarned, 0) as point_earned
            FROM Newmember.dbo.SMCSalesHeader ssh
            JOIN Newmember.dbo.df_member dm ON dm.member_id = ssh.member_id
            JOIN Newmember.dbo.SMCSalesTrans sst ON sst.key_search = ssh.key_search
            LEFT JOIN (
                SELECT 
                    ssp.key_search,
                    SUM(CASE WHEN ssp.MethodCode IN ('EP001', 'EP002', 'EP003', 'EP004', 'EP005', 'EP006', 'EP007', 'EP008', 'EP009', 'EP010') THEN ssp.Net ELSE 0 END) AS totalEarnableAmount,
                    SUM(CASE WHEN ssp.MethodCode IN ('AP001', 'KPC01', 'KPO02', 'CR001', 'PT001') THEN ssp.Net ELSE 0 END) AS totalAccumSpendableAmount
                FROM Newmember.dbo.SMCSalesPayment ssp
                WHERE ssp.DataDate < @validation_date
                GROUP BY ssp.key_search
            ) ssp ON ssp.key_search = sst.key_search
            LEFT JOIN (
                SELECT
                    lvh.KeySearch,
                    SUM(lvt.totalPointEarned) AS totalPointEarned
                FROM (
                    SELECT
                        lvt.LVHeaderKey AS LVHeaderKey,
                        SUM(CASE WHEN lvt.MovementCode = 'PTPOS' THEN lvt.Amount ELSE 0 END) AS totalPointEarned
                    FROM LoyaltyValue.dbo.LVTrans lvt
                    JOIN LoyaltyValue.dbo.LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
                    WHERE lvd.ValueCode IN ('AP001', 'EP001', 'EP002', 'EP003', 'EP004', 'EP005', 'EP006', 'EP007', 'EP008', 'EP009', 'EP010', 'KPC01', 'KPO02', 'CR001', 'PT001')
                    GROUP BY lvt.LVHeaderKey
                ) AS lvt 
                JOIN LoyaltyValue.dbo.LVHeader lvh ON lvh.LVHeaderKey = lvt.LVHeaderKey
                GROUP BY lvh.KeySearch
            ) lvt ON lvt.KeySearch = sst.key_search
            WHERE ssh.SaleStatus != 'R' 
              AND dm.del_flag = ''
              AND ssh.DataDate < @validation_date
            ORDER BY NEWID();
        """

    def validate_aggregations(self, validation_date: datetime) -> Dict[str, ValidationResult]:
        """ทำ aggregation validations (Steps 1-7)"""
        logger.info("Starting aggregation validations...")
        
        validation_steps = [
            'record_count',
            'net_total_amount',
            'original_price',
            'discount',
            'earnable_amount',
            'accum_spendable_amount',
            'point_earned'
        ]
        
        results = {}
        for step_name in validation_steps:
            try:
                logger.info(f"Running {step_name} validation...")
                result = self._run_aggregation_validation(validation_date, step_name)
                results[step_name] = result
                logger.info(f"{step_name} validation completed: {result.validation_result}")
            except Exception as e:
                logger.error(f"Error in {step_name} validation: {str(e)}")
                results[step_name] = ValidationResult(
                    step_name=step_name,
                    source_value=0,
                    dest_value=0,
                    validation_result='ERROR',
                    accuracy_percentage=0,
                    error_message=str(e)
                )
        
        return results

    def _run_aggregation_validation(self, validation_date: datetime, step_name: str) -> ValidationResult:
        """Run a single aggregation validation"""
        try:
            # Execute separate queries for source and destination
            connection = self.mssql_handler.hook.get_conn()
            try:
                with connection.cursor() as cursor:
                    # Get source data
                    source_query = self._get_source_query(step_name, validation_date)
                    cursor.execute(source_query)
                    source_result = cursor.fetchone()
                    source_value = source_result[0] if source_result else 0
            finally:
                connection.close()
            
            # Get destination data using PostgreSQL
            dest_query = self._get_destination_query(step_name)
            dest_connection = self.postgresql_handler.hook.get_conn()
            try:
                with dest_connection.cursor() as cursor:
                    cursor.execute(dest_query, [validation_date])
                    dest_result = cursor.fetchone()
                    dest_value = dest_result[0] if dest_result else 0
            finally:
                dest_connection.close()
            
            # Calculate validation result
            if step_name == 'record_count':
                validation_result = 'PASS' if source_value == dest_value else 'FAIL'
                accuracy_percentage = 100 if source_value == dest_value else (dest_value * 100.0 / source_value) if source_value > 0 else 0
            else:
                validation_result = 'PASS' if abs(source_value - dest_value) < 0.01 else 'FAIL'
                accuracy_percentage = 100 if source_value == dest_value else (dest_value * 100.0 / source_value) if source_value > 0 else 0
            
            return ValidationResult(
                step_name=step_name,
                source_value=source_value,
                dest_value=dest_value,
                validation_result=validation_result,
                accuracy_percentage=accuracy_percentage
            )
                
        except Exception as e:
            logger.error(f"Error running aggregation validation for {step_name}: {str(e)}")
            raise

    def _get_source_query(self, step_name: str, validation_date: datetime) -> str:
        """Get source query based on validation step"""
        # Format the validation date for SQL Server
        validation_date_str = validation_date.strftime('%Y-%m-%d')
        
        base_query = """
            SELECT {aggregation}
            FROM Newmember.dbo.SMCSalesHeader ssh
            JOIN Newmember.dbo.df_member dm ON dm.member_id = ssh.member_id
            JOIN Newmember.dbo.SMCSalesTrans sst ON sst.key_search = ssh.key_search
            {joins}
            WHERE ssh.SaleStatus != 'R' 
              AND dm.del_flag = ''
              AND ssh.DataDate < '{validation_date}'
        """
        
        if step_name == 'record_count':
            return base_query.format(aggregation="COUNT(*)", joins="", validation_date=validation_date_str)
        elif step_name == 'net_total_amount':
            return base_query.format(aggregation="SUM(sst.Net)", joins="", validation_date=validation_date_str)
        elif step_name == 'original_price':
            return base_query.format(aggregation="SUM(sst.OriginalPrice)", joins="", validation_date=validation_date_str)
        elif step_name == 'discount':
            return base_query.format(aggregation="SUM(sst.Discount)", joins="", validation_date=validation_date_str)
        elif step_name == 'earnable_amount':
            joins = """
                LEFT JOIN (
                    SELECT 
                        ssp.key_search,
                        SUM(CASE WHEN ssp.MethodCode IN ('EP001', 'EP002', 'EP003', 'EP004', 'EP005', 'EP006', 'EP007', 'EP008', 'EP009', 'EP010') THEN ssp.Net ELSE 0 END) AS totalEarnableAmount
                    FROM Newmember.dbo.SMCSalesPayment ssp
                    WHERE ssp.DataDate < '{validation_date}'
                    GROUP BY ssp.key_search
                ) ssp ON ssp.key_search = sst.key_search
            """
            return base_query.format(aggregation="SUM(COALESCE(ssp.totalEarnableAmount, 0))", joins=joins, validation_date=validation_date_str)
        elif step_name == 'accum_spendable_amount':
            joins = """
                LEFT JOIN (
                    SELECT 
                        ssp.key_search,
                        SUM(CASE WHEN ssp.MethodCode IN ('AP001', 'KPC01', 'KPO02', 'CR001', 'PT001') THEN ssp.Net ELSE 0 END) AS totalAccumSpendableAmount
                    FROM Newmember.dbo.SMCSalesPayment ssp
                    WHERE ssp.DataDate < '{validation_date}'
                    GROUP BY ssp.key_search
                ) ssp ON ssp.key_search = sst.key_search
            """
            return base_query.format(aggregation="SUM(COALESCE(ssp.totalAccumSpendableAmount, 0))", joins=joins, validation_date=validation_date_str)
        elif step_name == 'point_earned':
            joins = """
                LEFT JOIN (
                    SELECT
                        lvh.KeySearch,
                        SUM(lvt.totalPointEarned) AS totalPointEarned
                    FROM (
                        SELECT
                            lvt.LVHeaderKey AS LVHeaderKey,
                            SUM(CASE WHEN lvt.MovementCode = 'PTPOS' THEN lvt.Amount ELSE 0 END) AS totalPointEarned
                        FROM LoyaltyValue.dbo.LVTrans lvt
                        JOIN LoyaltyValue.dbo.LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
                        WHERE lvd.ValueCode IN ('AP001', 'EP001', 'EP002', 'EP003', 'EP004', 'EP005', 'EP006', 'EP007', 'EP008', 'EP009', 'EP010', 'KPC01', 'KPO02', 'CR001', 'PT001')
                        GROUP BY lvt.LVHeaderKey
                    ) AS lvt 
                    JOIN LoyaltyValue.dbo.LVHeader lvh ON lvh.LVHeaderKey = lvt.LVHeaderKey
                    GROUP BY lvh.KeySearch
                ) lvt ON lvt.KeySearch = sst.key_search
            """
            return base_query.format(aggregation="SUM(COALESCE(lvt.totalPointEarned, 0))", joins=joins, validation_date=validation_date_str)
        else:
            raise ValueError(f"Unknown step_name: {step_name}")

    def _get_destination_query(self, step_name: str) -> str:
        """Get destination query based on validation step"""
        field_mapping = {
            'record_count': 'COUNT(*)',
            'net_total_amount': 'SUM("netTotalAmount")',
            'original_price': 'SUM("totalOriginalPrice")',
            'discount': 'SUM("totalDiscount")',
            'earnable_amount': 'SUM("totalEarnableAmount")',
            'accum_spendable_amount': 'SUM("totalAccumSpendableAmount")',
            'point_earned': 'SUM("totalPointEarned")'
        }
        
        field = field_mapping.get(step_name)
        if not field:
            raise ValueError(f"Unknown step_name: {step_name}")
        
        return f"""
            SELECT {field}
            FROM "partner_service"."SalesTransaction"
            WHERE "createdAt" < %s
        """

    def validate_sample_data(self, validation_date: datetime, sample_size: int = 10000) -> ValidationResult:
        """ทำ sample data validation (Step 8)"""
        logger.info(f"Starting sample data validation with {sample_size} records...")
        
        try:
            # ดึงข้อมูล sample จาก source
            logger.info("Extracting sample data from source...")
            connection = self.mssql_handler.hook.get_conn()
            try:
                with connection.cursor() as cursor:
                    # Format the validation date for SQL Server
                    validation_date_str = validation_date.strftime('%Y-%m-%d')
                    sample_query = self.sample_validation_query.replace('@validation_date', f"'{validation_date_str}'")
                    cursor.execute(sample_query)
                    sample_data = cursor.fetchall()
            finally:
                connection.close()
            
            if not sample_data or len(sample_data) == 0:
                return ValidationResult(
                    step_name='sample_data',
                    source_value=0,
                    dest_value=0,
                    validation_result='ERROR',
                    accuracy_percentage=0,
                    error_message='No sample data found'
                )
            
            # ทำ validation แบบ manual
            logger.info("Running sample validation...")
            pass_count = 0
            total_count = 0
            
            for row in sample_data:
                key_search = row[0]
                member_id = row[1]
                net_amount = row[5] or 0
                original_price = row[6] or 0
                discount = row[7] or 0
                earnable_amount = row[8] or 0
                accum_spendable_amount = row[9] or 0
                point_earned = row[10] or 0
                
                # ดึงข้อมูลจาก destination
                dest_query = """
                    SELECT 
                        "memberId",
                        "netTotalAmount",
                        "totalOriginalPrice",
                        "totalDiscount",
                        "totalEarnableAmount",
                        "totalAccumSpendableAmount",
                        "totalPointEarned"
                    FROM "partner_service"."SalesTransaction"
                    WHERE "externalId" = %s
                """
                connection = self.postgresql_handler.hook.get_conn()
                try:
                    with connection.cursor() as cursor:
                        cursor.execute(dest_query, [key_search])
                        dest_result = cursor.fetchall()
                finally:
                    connection.close()
                
                if dest_result and len(dest_result) > 0:
                    dest_row = dest_result[0]
                    dest_member_id = dest_row[0]
                    dest_net_amount = dest_row[1] or 0
                    dest_original_price = dest_row[2] or 0
                    dest_discount = dest_row[3] or 0
                    dest_earnable_amount = dest_row[4] or 0
                    dest_accum_spendable_amount = dest_row[5] or 0
                    dest_point_earned = dest_row[6] or 0
                    
                    # ตรวจสอบความถูกต้อง
                    if (member_id == dest_member_id and
                        abs(net_amount - dest_net_amount) < 0.01 and
                        abs(original_price - dest_original_price) < 0.01 and
                        abs(discount - dest_discount) < 0.01 and
                        abs(earnable_amount - dest_earnable_amount) < 0.01 and
                        abs(accum_spendable_amount - dest_accum_spendable_amount) < 0.01 and
                        abs(point_earned - dest_point_earned) < 0.01):
                        pass_count += 1
                
                total_count += 1
            
            accuracy_percentage = (pass_count * 100.0 / total_count) if total_count > 0 else 0
            
            validation_result = ValidationResult(
                step_name='sample_data',
                source_value=total_count,
                dest_value=pass_count,
                validation_result='PASS' if accuracy_percentage >= 95 else 'FAIL',
                accuracy_percentage=accuracy_percentage
            )
            
            logger.info(f"Sample data validation completed: {validation_result.validation_result}")
            return validation_result
            
        except Exception as e:
            logger.error(f"Error in sample data validation: {str(e)}")
            return ValidationResult(
                step_name='sample_data',
                source_value=0,
                dest_value=0,
                validation_result='ERROR',
                accuracy_percentage=0,
                error_message=str(e)
            )

    def run_comprehensive_validation(self, validation_date: datetime, sample_size: int = 10000) -> Dict[str, Any]:
        """ทำ comprehensive validation ทั้งหมด"""
        logger.info("Starting comprehensive SalesTransaction validation...")
        
        # ทำ aggregation validations
        aggregation_results = self.validate_aggregations(validation_date)
        
        # ทำ sample validation
        sample_result = self.validate_sample_data(validation_date, sample_size)
        
        # สรุปผล
        overall_result = self._determine_overall_result(aggregation_results, sample_result)
        
        # สร้าง validation summary
        summary = {
            'validation_date': validation_date.isoformat(),
            'aggregation_validations': {
                step: {
                    'result': result.validation_result,
                    'source_value': result.source_value,
                    'dest_value': result.dest_value,
                    'accuracy_percentage': result.accuracy_percentage,
                    'error_message': result.error_message
                } for step, result in aggregation_results.items()
            },
            'sample_validation': {
                'result': sample_result.validation_result,
                'source_value': sample_result.source_value,
                'dest_value': sample_result.dest_value,
                'accuracy_percentage': sample_result.accuracy_percentage,
                'error_message': sample_result.error_message
            },
            'overall_result': overall_result
        }
        
        logger.info(f"Comprehensive validation completed. Overall result: {overall_result}")
        return summary

    def _determine_overall_result(self, aggregation_results: Dict[str, ValidationResult], sample_result: ValidationResult) -> str:
        """กำหนด overall result จากผลการ validation ทั้งหมด"""
        # ตรวจสอบ aggregation results
        aggregation_failures = sum(1 for result in aggregation_results.values() 
                                 if result.validation_result in ['FAIL', 'ERROR'])
        
        # ตรวจสอบ sample result
        sample_failure = sample_result.validation_result in ['FAIL', 'ERROR']
        
        # กำหนด overall result
        if aggregation_failures == 0 and not sample_failure:
            return 'PASS'
        elif aggregation_failures <= 2 and sample_result.accuracy_percentage >= 90:
            return 'PASS_WITH_WARNINGS'
        else:
            return 'FAIL'

    def validate(
        self,
        temp_db_handler: PostgresHandler,
        service: str,
        validation_date: Optional[datetime] = None,
        sample_size: int = 10000,
    ) -> None:
        """
        Main validation method that runs all validation checks in sequence.

        This method performs a comprehensive validation of the SalesTransaction migration
        with the following steps:

        Step 1: Record Counts Validation
        - Compares the total number of records between source and destination
        - Ensures no records are lost or duplicated during migration

        Step 2: Net Total Amount Validation
        - Compares the sum of netTotalAmount between source and destination
        - Ensures financial data integrity for net amounts

        Step 3: Total Original Price Validation
        - Compares the sum of totalOriginalPrice between source and destination
        - Ensures financial data integrity for original prices

        Step 4: Total Discount Validation
        - Compares the sum of totalDiscount between source and destination
        - Ensures financial data integrity for discounts

        Step 5: Total Earnable Amount Validation
        - Compares the sum of totalEarnableAmount between source and destination
        - Ensures financial data integrity for earnable amounts

        Step 6: Total Accum Spendable Amount Validation
        - Compares the sum of totalAccumSpendableAmount between source and destination
        - Ensures financial data integrity for accum spendable amounts

        Step 7: Total Point Earned Validation
        - Compares the sum of totalPointEarned between source and destination
        - Ensures point data integrity

        Step 8: Sample Data Validation
        - Randomly selects up to 10,000 records for detailed comparison
        - Checks each field for exact matches between source and destination
        - Identifies missing, extra, or mismatched records

        Args:
            temp_db_handler (PostgresHandler): Database handler for temporary operations.
            service (str): Service name for logging.
            validation_date (datetime): Date for validation cutoff.
            sample_size (int): Number of sample records to validate.

        Returns:
            None
        """
        if validation_date is None:
            validation_date = datetime.now()

        logger.info("=== Starting SalesTransaction Validation ===")
        logger.info(f"Validation date: {validation_date}")

        # Run comprehensive validation
        validation_summary = self.run_comprehensive_validation(validation_date, sample_size)
        
        # Log validation results
        logger.info("=== Validation Summary ===")
        logger.info(f"Overall Result: {validation_summary['overall_result']}")
        
        # Log aggregation results
        for step, result in validation_summary['aggregation_validations'].items():
            logger.info(f"{step}: {result['result']} ({result['accuracy_percentage']:.2f}%)")
        
        # Log sample validation
        sample = validation_summary['sample_validation']
        logger.info(f"Sample Validation: {sample['result']} ({sample['accuracy_percentage']:.2f}%)")

        # Insert validation logs
        errors = []
        if validation_summary['overall_result'] == 'FAIL':
            errors.append('VALIDATION_FAILED')
        elif validation_summary['overall_result'] == 'PASS_WITH_WARNINGS':
            errors.append('VALIDATION_WARNINGS')

        insert_validation_logs(
            errors=errors,
            db_handler=temp_db_handler,
            service=service,
            source_table="SMCSalesHeader",
            destination_table="SalesTransaction",
        )

        # Raise exception if validation failed
        if validation_summary['overall_result'] == 'FAIL':
            raise Exception("SalesTransaction validation failed")

        logger.info("=== SalesTransaction Validation Completed ===") 