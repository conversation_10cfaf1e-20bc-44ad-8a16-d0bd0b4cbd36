from airflow.exceptions import AirflowException

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.logging import get_logger
from common_helpers.utils import insert_validation_logs
from constants import (
    AGGREGATE_DATA_VALIDATION,
    AGGREGATE_DATA_VALIDATION_ERROR,
)


logger = get_logger()


class SalesTransactionPaymentValidation:
    def __init__(
        self,
        mssql_handler: MSSQLHandler,
        postgresql_handler: PostgresHandler,
    ):
        self.mssql_handler = mssql_handler
        self.postgresql_handler = postgresql_handler
        self.errors = []


    def validate_aggregate_data(self, is_full_dump: bool = True):
        """
        Validate aggregate data by grouping by sales_transaction_id and comparing total amounts.

        Args:
            is_full_dump (bool): Whether this is a full dump validation
        """
        mssql_connection = self.mssql_handler.hook.get_conn()

        try:
            if is_full_dump:
                source_aggregate_query = """
                    SELECT TOP 10000
                        sales_transaction_id,
                        SUM(amount) as total_amount
                    FROM temp_sales_transaction_payment_for_full_dump_migration
                    GROUP BY sales_transaction_id
                    ORDER BY NEWID()
                """
            else:
                source_aggregate_query = """
                    SELECT
                        sales_transaction_id,
                        SUM(amount) as total_amount
                    FROM temp_sales_transaction_payment_for_incremental_migration
                    GROUP BY sales_transaction_id
                """
            with mssql_connection.cursor() as cursor:
                cursor.execute(source_aggregate_query)
                source_aggregates = cursor.fetchall()
        finally:
            if mssql_connection:
                mssql_connection.close()

        source_keys = [record[0] for record in source_aggregates]

        postgresql_connection = self.postgresql_handler.hook.get_conn()

        try:
            keys_for_query = ",".join([f"'{key}'" for key in source_keys])

            if is_full_dump:
                destination_aggregate_query = f"""
                    SELECT 
                        "salesTransactionId",
                        SUM("amount") as total_amount
                    FROM partner_service."SalesTransactionPayment"
                    WHERE "salesTransactionId" IN ({keys_for_query})
                    GROUP BY "salesTransactionId"
                """
            else:
                destination_aggregate_query = """
                    SELECT 
                        "salesTransactionId",
                        SUM("amount") as total_amount
                    FROM partner_service."SalesTransactionPayment"
                    WHERE "updatedAt" >= CURRENT_DATE AT TIME ZONE '+07' + INTERVAL '3 hours'
                    AND "updatedAt" <= NOW() AT TIME ZONE '+07'
                    GROUP BY "salesTransactionId"
                """
            with postgresql_connection.cursor() as cursor:
                cursor.execute(destination_aggregate_query)
                destination_aggregates = cursor.fetchall()
        finally:
            if postgresql_connection:
                postgresql_connection.close()

        source_lookup = {}
        for record in source_aggregates:
            key = record[0]
            source_lookup[key] = {
                "total_amount": record[1],
            }

        destination_lookup = {}
        for record in destination_aggregates:
            key = record[0]
            destination_lookup[key] = {
                "total_amount": record[1],
            }

        source_keys = set(source_lookup.keys())
        destination_keys = set(destination_lookup.keys())
        missing_keys = source_keys - destination_keys
        extra_keys = destination_keys - source_keys

        if missing_keys:
            logger.warning(
                f"found {len(missing_keys)} sales_transaction_id missing in destination"
            )
            self.errors.append(AGGREGATE_DATA_VALIDATION_ERROR)
        else:
            logger.info("no missing sales_transaction_id in destination")

        if extra_keys:
            logger.warning(
                f"found {len(extra_keys)} extra sales_transaction_id in destination"
            )
            self.errors.append(AGGREGATE_DATA_VALIDATION_ERROR)
        else:
            logger.info("no extra sales_transaction_id in destination")

        common_keys = source_keys.intersection(destination_keys)
        aggregate_mismatches = []

        for key in common_keys:
            source_data = source_lookup[key]
            dest_data = destination_lookup[key]

            if source_data["total_amount"] != dest_data["total_amount"]:
                aggregate_mismatches.append(
                    f"total_amount mismatch for {key}: source={source_data['total_amount']}, dest={dest_data['total_amount']}"
                )

        if aggregate_mismatches:
            logger.warning(
                f"found {len(aggregate_mismatches)} sales_transaction_id with aggregate mismatches"
            )
            for mismatch in aggregate_mismatches:
                logger.warning(f"  - {mismatch}")
            self.errors.append(AGGREGATE_DATA_VALIDATION_ERROR)
        else:
            logger.info("no aggregate mismatches found")

        if not missing_keys and not extra_keys and not aggregate_mismatches:
            logger.info("aggregate validation passed: all aggregates match")

    def validate(
        self,
        temp_db_handler: PostgresHandler,
        service: str,
        is_full_dump: bool = True,
    ) -> None:
        """
        Run aggregate validation for SalesTransactionPayment.

        Args:
            temp_db_handler (PostgresHandler): Temporary database handler for logging
            service (str): Service name for logging
            is_full_dump (bool): Whether this is a full dump validation
        """
        logger.info("started aggregate data validation...")
        self.validate_aggregate_data(is_full_dump)
        logger.info("finished aggregate data validation.")

        insert_validation_logs(
            errors=self.errors,
            db_handler=temp_db_handler,
            service=service,
            source_table="SMCSalesPayment",
            destination_table="SalesTransactionPayment",
            validation_types=[
                {
                    "name": AGGREGATE_DATA_VALIDATION,
                    "error": AGGREGATE_DATA_VALIDATION_ERROR,
                },
            ],
        )

        if len(self.errors):
            raise AirflowException("validation(s) failed.")
