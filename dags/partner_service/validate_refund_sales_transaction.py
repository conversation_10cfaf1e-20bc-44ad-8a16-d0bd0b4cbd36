from airflow.exceptions import AirflowException

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.logging import get_logger
from common_helpers.utils import insert_validation_logs
from constants import (
    AGGREGATE_DATA_VALIDATION,
    AGGREGATE_DATA_VALIDATION_ERROR,
)


logger = get_logger()


class RefundSalesTransactionValidation:
    def __init__(
        self,
        mssql_handler: MSSQLHandler,
        postgresql_handler: PostgresHandler,
    ):
        self.mssql_handler = mssql_handler
        self.postgresql_handler = postgresql_handler
        self.errors = []

    def validate_aggregate_data(self, is_full_dump: bool = True):
        """
        Validate data by grouping by sales_transaction_id, type, and external_id to find missing or extra records.

        Args:
            is_full_dump (bool): Whether this is a full dump validation
        """
        mssql_connection = self.mssql_handler.hook.get_conn()

        try:
            if is_full_dump:
                source_aggregate_query = """
                    SELECT TOP 10000
                        sales_transaction_id,
                        type,
                        external_id
                    FROM temp_refund_sales_transaction_for_migration_full_dump
                    GROUP BY sales_transaction_id, type, external_id
                    ORDER BY NEWID()
                """
            else:
                source_aggregate_query = """
                    SELECT
                        sales_transaction_id,
                        type,
                        external_id
                    FROM temp_refund_sales_transaction_for_migration_incremental
                    GROUP BY sales_transaction_id, type, external_id
                """
            with mssql_connection.cursor() as cursor:
                cursor.execute(source_aggregate_query)
                source_aggregates = cursor.fetchall()
        finally:
            if mssql_connection:
                mssql_connection.close()

        source_keys = [
            (record[0], record[1], record[2]) for record in source_aggregates
        ]

        postgresql_connection = self.postgresql_handler.hook.get_conn()

        try:
            if is_full_dump:
                keys_condition = " OR ".join(
                    [
                        f"(\"salesTransactionId\" = '{key[0]}' AND \"type\" = '{key[1]}' AND \"externalId\" = '{key[2]}')"
                        for key in source_keys
                    ]
                )
                destination_aggregate_query = f"""
                    SELECT 
                        "salesTransactionId",
                        "type",
                        "externalId"
                    FROM partner_service."RefundSalesTransaction"
                    WHERE {keys_condition}
                    GROUP BY "salesTransactionId", "type", "externalId"
                """
            else:
                destination_aggregate_query = """
                    SELECT 
                        "salesTransactionId",
                        "type",
                        "externalId"
                    FROM partner_service."RefundSalesTransaction"
                    WHERE "updatedAt" >= CURRENT_DATE AT TIME ZONE '+07' + INTERVAL '3 hours'
                    AND "updatedAt" <= NOW() AT TIME ZONE '+07'
                    GROUP BY "salesTransactionId", "type", "externalId"
                """
            with postgresql_connection.cursor() as cursor:
                cursor.execute(destination_aggregate_query)
                destination_aggregates = cursor.fetchall()
        finally:
            if postgresql_connection:
                postgresql_connection.close()

        source_keys_set = set(source_keys)
        destination_keys_set = set(
            [(record[0], record[1], record[2]) for record in destination_aggregates]
        )

        missing_keys = source_keys_set - destination_keys_set
        extra_keys = destination_keys_set - source_keys_set

        if missing_keys:
            logger.warning(f"found {len(missing_keys)} records missing in destination")
            for key in list(missing_keys)[:10]:
                logger.warning(
                    f"  - Missing: salesTransactionId={key[0]}, type={key[1]}, externalId={key[2]}"
                )
            if len(missing_keys) > 10:
                logger.warning(
                    f"  - ... and {len(missing_keys) - 10} more missing records"
                )
            self.errors.append(AGGREGATE_DATA_VALIDATION_ERROR)
        else:
            logger.info("no missing records in destination")

        if extra_keys:
            logger.warning(f"found {len(extra_keys)} extra records in destination")
            for key in list(extra_keys)[:10]:
                logger.warning(
                    f"  - Extra: salesTransactionId={key[0]}, type={key[1]}, externalId={key[2]}"
                )
            if len(extra_keys) > 10:
                logger.warning(f"  - ... and {len(extra_keys) - 10} more extra records")
            self.errors.append(AGGREGATE_DATA_VALIDATION_ERROR)
        else:
            logger.info("no extra records in destination")

        if not missing_keys and not extra_keys:
            logger.info("validation passed: all records match")

    def validate(
        self,
        temp_db_handler: PostgresHandler,
        service: str,
        is_full_dump: bool = True,
    ) -> None:
        """
        Run validation for RefundSalesTransaction to find missing or extra records.

        Args:
            temp_db_handler (PostgresHandler): Temporary database handler for logging
            service (str): Service name for logging
            is_full_dump (bool): Whether this is a full dump validation
        """
        logger.info("started aggregate data validation...")
        self.validate_aggregate_data(is_full_dump)
        logger.info("finished aggregate data validation.")

        insert_validation_logs(
            errors=self.errors,
            db_handler=temp_db_handler,
            service=service,
            source_table="SMCSalesHeader",
            destination_table="RefundSalesTransaction",
            validation_types=[
                {
                    "name": AGGREGATE_DATA_VALIDATION,
                    "error": AGGREGATE_DATA_VALIDATION_ERROR,
                },
            ],
        )

        if len(self.errors):
            raise AirflowException("validation(s) failed.")
