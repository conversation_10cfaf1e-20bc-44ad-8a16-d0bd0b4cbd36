from airflow.exceptions import AirflowException

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresHand<PERSON>
from common_helpers.logging import get_logger
from common_helpers.smc_validation_helpers import get_wallet_code
from common_helpers.utils import insert_validation_logs
from constants import (
    RECORD_COUNTS_VALIDATION,
    RECORD_COUNTS_VALIDATION_ERROR,
    SAMPLE_DATA_VALIDATION,
    SAMPLE_DATA_VALIDATION_ERROR,
)


logger = get_logger()


class SalesTransactionBurnPaymentValidation:
    def __init__(
        self,
        mssql_handler: MSSQLHandler,
        postgresql_handler: PostgresHandler,
    ):
        self.mssql_handler = mssql_handler
        self.postgresql_handler = postgresql_handler
        self.errors = []

    def get_incremental_query_condition(self) -> str:
        """
        Generates a query condition for incremental migration, with specific date supported.

        Args:
            None

        Returns:
            str: A query condition string.
        """
        return "lvh.DocDate >= DATEADD(DAY, -1, CAST(CAST(GETDATE() AS DATE) AS DATETIME)) AND lvh.DocDate < CAST(CAST(GETDATE() AS DATE) AS DATETIME)"

    def get_source_count_query_string(self) -> str:
        return """
            SELECT
                COUNT(*)
            FROM LVHeader lvh
            JOIN LVTrans lvt ON lvt.LVHeaderKey = lvh.LVHeaderKey 
            JOIN LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
            WHERE
                lvt.MovementCode = 'USE'
                AND lvd.ValueCode IN (
                'AP001',
                'EP001',
                'EP002',
                'EP003',
                'EP004',
                'EP005',
                'EP006',
                'EP007',
                'EP008',
                'EP009',
                'EP010',
                'KPC01',
                'KPO02',
                'CR001',
                'PT001'
            ) AND lvh.DocDate < CAST('2025-06-20' AS DATETIME);
        """

    def get_source_select_query_string(self, is_full_dump: bool) -> str:
        return f"""
            SELECT
                CAST(lvh.LVHeaderKey AS VARCHAR(20)) + '_' + CAST(lvt.LVTransKey AS VARCHAR(8)) + '_' + CAST(lvd.LVMainKey AS VARCHAR(20)) AS id,
                lvd.ValueCode AS value_code,
                ABS(lvt.Amount) AS burn_amount,
                lvt.PreviousBalance AS before_amount,
                lvt.Amount + lvt.PreviousBalance AS after_amount,
                DATEADD (HOUR, -7, lvh.AddDT) AS created_at,
                lvh.LVHeaderKey AS sales_transaction_id
            FROM LVHeader lvh
            JOIN LVTrans lvt ON lvt.LVHeaderKey = lvh.LVHeaderKey 
            JOIN LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
            WHERE
                lvt.MovementCode = 'USE'
                AND lvd.ValueCode IN (
                'AP001',
                'EP001',
                'EP002',
                'EP003',
                'EP004',
                'EP005',
                'EP006',
                'EP007',
                'EP008',
                'EP009',
                'EP010',
                'KPC01',
                'KPO02',
                'CR001',
                'PT001'
            ) AND {"lvh.DocDate < CAST('2025-06-20' AS DATETIME)" if is_full_dump else self.get_incremental_query_condition()}
        """

    def transform_record(
        self,
        record: tuple,
    ) -> tuple:
        (
            id,
            value_code,
            burn_amount,
            before_amount,
            after_amount,
            created_at,
            sales_transaction_id,
        ) = record

        wallet_code = get_wallet_code(value_code)

        return (
            id,
            wallet_code,
            burn_amount,
            before_amount,
            after_amount,
            burn_amount,
            created_at,
            sales_transaction_id,
        )

    def validate_record_counts(self):
        destination_count_query_string = """
            SELECT COUNT(*) FROM partner_service."SalesTransactionBurnPayment";
        """

        source_count = self.mssql_handler.get_table_total_records(
            self.get_source_count_query_string(),
        )

        postgresql_connection = self.postgresql_handler.hook.get_conn()

        with postgresql_connection.cursor() as cursor:
            cursor.execute(destination_count_query_string)
            destination_count = cursor.fetchone()[0]

        if source_count != destination_count:
            logger.warning(
                f"count mismatch: source has {source_count} records, but destination has {destination_count} records"
            )
            self.errors.append(RECORD_COUNTS_VALIDATION_ERROR)
        else:
            logger.info(f"count validation passed: {source_count} records match")

    def validate_sample_data(self, is_full_dump: bool):
        source_sample_query_string = (
            f"""
                SELECT TOP 100000 * FROM ({self.get_source_select_query_string(is_full_dump)}) AS sample_data ORDER BY NEWID()
            """
            if is_full_dump
            else f"""
                SELECT * FROM ({self.get_source_select_query_string(is_full_dump)}) AS sample_data
            """
        )

        source_records = self.mssql_handler.extract_data(source_sample_query_string)

        if not source_records:
            logger.info("no source records found, skipping sample data validation")

            return

        transformed_source_records = [
            self.transform_record(record) for record in source_records
        ]

        source_record_ids = [record[0] for record in transformed_source_records]

        destination_sample_query_string = f"""
            SELECT
                "id",
                "walletCode",
                "burnAmount",
                "beforeAmount",
                "afterAmount",
                "paymentAmount",
                "createdAt",
                "salesTransactionId"
            FROM partner_service."SalesTransactionBurnPayment" WHERE id IN ({", ".join(["'" + str(code) + "'" for code in source_record_ids])})
        """

        postgresql_connection = self.postgresql_handler.hook.get_conn()

        with postgresql_connection.cursor() as cursor:
            cursor.execute(destination_sample_query_string)
            destination_records = cursor.fetchall()

        source_dict = {
            record[0]: {
                "walletCode": record[1],
                "burnAmount": record[2],
                "beforeAmount": record[3],
                "afterAmount": record[4],
                "paymentAmount": record[5],
                "createdAt": record[6],
                "salesTransactionId": record[7],
            }
            for record in transformed_source_records
        }

        destination_dict = {
            record[0]: {
                "walletCode": record[1],
                "burnAmount": record[2],
                "beforeAmount": record[3],
                "afterAmount": record[4],
                "paymentAmount": record[5],
                "createdAt": record[6],
                "salesTransactionId": record[7],
            }
            for record in destination_records
        }

        source_code = set(source_dict.keys())
        destination_ids = set(destination_dict.keys())
        missing_ids = source_code - destination_ids
        extra_ids = destination_ids - source_code

        data_mismatches = []
        common_ids = source_code.intersection(destination_ids)

        for record_id in common_ids:
            source_data = source_dict[record_id]
            dest_data = destination_dict[record_id]

            mismatches = []

            if source_data["walletCode"] != dest_data["walletCode"]:
                mismatches.append(
                    f"walletCode: source={source_data['walletCode']}, dest={dest_data['walletCode']}"
                )

            if source_data["burnAmount"] != dest_data["burnAmount"]:
                mismatches.append(
                    f"burnAmount: source={source_data['burnAmount']}, dest={dest_data['burnAmount']}"
                )

            if source_data["beforeAmount"] != dest_data["beforeAmount"]:
                mismatches.append(
                    f"beforeAmount: source={source_data['beforeAmount']}, dest={dest_data['beforeAmount']}"
                )

            if source_data["afterAmount"] != dest_data["afterAmount"]:
                mismatches.append(
                    f"afterAmount: source={source_data['afterAmount']}, dest={dest_data['afterAmount']}"
                )

            if source_data["paymentAmount"] != dest_data["paymentAmount"]:
                mismatches.append(
                    f"paymentAmount: source={source_data['paymentAmount']}, dest={dest_data['paymentAmount']}"
                )

            if source_data["createdAt"] != dest_data["createdAt"]:
                mismatches.append(
                    f"createdAt: source={source_data['createdAt']}, dest={dest_data['createdAt']}"
                )

            if source_data["salesTransactionId"] != dest_data["salesTransactionId"]:
                mismatches.append(
                    f"salesTransactionId: source={source_data['salesTransactionId']}, dest={dest_data['salesTransactionId']}"
                )

            if mismatches:
                data_mismatches.append((record_id, mismatches))

        if missing_ids:
            logger.warning(
                f"found {len(missing_ids)} records missing in destination table"
            )
            logger.warning(f"missing record IDs: {', '.join(sorted(missing_ids))}")
        else:
            logger.info("no missing records found in destination table")

        if extra_ids:
            logger.warning(f"found {len(extra_ids)} extra records in destination table")
            logger.warning(f"extra record IDs: {', '.join(sorted(extra_ids))}")
        else:
            logger.info("no extra records found in destination table")

        if data_mismatches:
            logger.warning(f"found {len(data_mismatches)} records with data mismatches")
            for record_id, mismatches in data_mismatches:
                logger.warning(f"data mismatches for record {record_id}:")
                for mismatch in mismatches:
                    logger.warning(f"  - {mismatch}")
        else:
            logger.info("no data mismatches found in matching records")

        if not missing_ids and not extra_ids and not data_mismatches:
            logger.info(f"sample data validation passed: all sample records match")
        else:
            self.errors.append(SAMPLE_DATA_VALIDATION_ERROR)

    def validate(
        self, temp_db_handler: PostgresHandler, service: str, is_full_dump: bool = True
    ) -> None:
        validation_types = []

        if is_full_dump:
            logger.info("started record counts validation...")
            self.validate_record_counts()
            logger.info("finished record counts validation.")

            validation_types.append(
                {
                    "name": RECORD_COUNTS_VALIDATION,
                    "error": RECORD_COUNTS_VALIDATION_ERROR,
                }
            )

        logger.info("started sample data validation...")
        self.validate_sample_data(is_full_dump)
        logger.info("finished sample data validation.")

        validation_types.append(
            {
                "name": SAMPLE_DATA_VALIDATION,
                "error": SAMPLE_DATA_VALIDATION_ERROR,
            }
        )

        insert_validation_logs(
            errors=self.errors,
            db_handler=temp_db_handler,
            service=service,
            source_table="LVHeader",
            destination_table="SalesTransactionBurnPayment",
            validation_types=validation_types,
        )

        if len(self.errors):
            raise AirflowException("validation(s) failed.")
