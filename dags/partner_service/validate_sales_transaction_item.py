from airflow.exceptions import AirflowException

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>gresHandler
from common_helpers.logging import get_logger
from common_helpers.utils import insert_validation_logs
from constants import (
    AGGREGATE_DATA_VALIDATION,
    RECORD_COUNTS_VALIDATION,
    AGGREGATE_DATA_VALIDATION_ERROR,
    RECORD_COUNTS_VALIDATION_ERROR,
)

logger = get_logger()


class SalesTransactionItemValidation:
    def __init__(
        self,
        mssql_handler: MSSQLHand<PERSON>,
        postgresql_handler: <PERSON>gresHand<PERSON>,
        incremental_query_date: str = None,
    ) -> None:
        self.mssql_handler = mssql_handler
        self.postgresql_handler = postgresql_handler
        self.incremental_query_date = incremental_query_date
        self.errors = []

        self.source_count = None

    def get_source_count(self) -> int:
        """
        Get count of records from source temp table.

        Returns:
            int: Count of records in source table.
        """
        if self.source_count is not None:
            return self.source_count

        mssql_connection = self.mssql_handler.hook.get_conn()
        try:
            source_count_query = "SELECT COUNT(*) FROM temp_sales_transaction_item_for_full_dump_migration;"

            with mssql_connection.cursor() as cursor:
                cursor.execute(source_count_query)
                self.source_count = cursor.fetchone()[0]

            logger.info(f"source count: {self.source_count}")

            return self.source_count
        finally:
            if mssql_connection:
                mssql_connection.close()

    def validate_record_counts(self):
        """
        Validate record counts between source temp table and destination table.
        """
        source_count = self.get_source_count()

        postgresql_connection = self.postgresql_handler.hook.get_conn()
        try:
            destination_count_query = (
                'SELECT COUNT(*) FROM partner_service."SalesTransactionItem";'
            )
            with postgresql_connection.cursor() as cursor:
                cursor.execute(destination_count_query)
                destination_count = cursor.fetchone()[0]
        finally:
            if postgresql_connection:
                postgresql_connection.close()

        if source_count != destination_count:
            logger.warning(
                f"count mismatch: source has {source_count} records, but destination has {destination_count} records"
            )
            self.errors.append(RECORD_COUNTS_VALIDATION_ERROR)
        else:
            logger.info(f"count validation passed: {source_count} records match")

    def validate_aggregate_data(self, is_full_dump: bool = True):
        """
        Validate aggregate data by grouping by sales_transaction_id and comparing sums.

        Args:
            is_full_dump (bool): Whether this is a full dump validation
        """
        mssql_connection = self.mssql_handler.hook.get_conn()

        try:
            if is_full_dump:
                source_aggregate_query = """
                    SELECT TOP 10000
                        sales_transaction_id,
                        SUM(net_amount) as total_net_amount,
                        SUM(original_price) as total_original_price,
                        SUM(normal_point_earned) as total_normal_point_earned
                    FROM temp_sales_transaction_item_for_full_dump_migration
                    GROUP BY sales_transaction_id
                    ORDER BY NEWID()
                """
            else:
                source_aggregate_query = """
                    SELECT
                        sales_transaction_id,
                        SUM(net_amount) as total_net_amount,
                        SUM(original_price) as total_original_price,
                        SUM(normal_point_earned) as total_normal_point_earned
                    FROM temp_sales_transaction_item_for_incremental_migration
                    GROUP BY sales_transaction_id
                """
            with mssql_connection.cursor() as cursor:
                cursor.execute(source_aggregate_query)
                source_aggregates = cursor.fetchall()
        finally:
            if mssql_connection:
                mssql_connection.close()

        source_keys = [record[0] for record in source_aggregates]

        postgresql_connection = self.postgresql_handler.hook.get_conn()

        try:
            keys_for_query = ",".join([f"'{key}'" for key in source_keys])

            if is_full_dump:
                destination_aggregate_query = f"""
                    SELECT 
                        "salesTransactionId",
                        SUM("netAmount") as total_net_amount,
                        SUM("originalPrice") as total_original_price,
                        SUM("normalPointEarned") as total_normal_point_earned
                    FROM partner_service."SalesTransactionItem"
                    WHERE "salesTransactionId" IN ({keys_for_query})
                    GROUP BY "salesTransactionId"
                """
            else:
                destination_aggregate_query = """
                    SELECT 
                        "salesTransactionId",
                        SUM("netAmount") as total_net_amount,
                        SUM("originalPrice") as total_original_price,
                        SUM("normalPointEarned") as total_normal_point_earned
                    FROM partner_service."SalesTransactionItem"
                    WHERE "updatedAt" >= CURRENT_DATE AT TIME ZONE '+07' + INTERVAL '3 hours'
                    AND "updatedAt" <= NOW() AT TIME ZONE '+07'
                    GROUP BY "salesTransactionId"
                """
            with postgresql_connection.cursor() as cursor:
                cursor.execute(destination_aggregate_query)
                destination_aggregates = cursor.fetchall()
        finally:
            if postgresql_connection:
                postgresql_connection.close()

        source_lookup = {}

        for record in source_aggregates:
            key = record[0]
            source_lookup[key] = {
                "total_net_amount": record[1],
                "total_original_price": record[2],
                "total_normal_point_earned": record[3],
            }

        destination_lookup = {}

        for record in destination_aggregates:
            key = record[0]
            destination_lookup[key] = {
                "total_net_amount": record[1],
                "total_original_price": record[2],
                "total_normal_point_earned": record[3],
            }

        source_keys = set(source_lookup.keys())
        destination_keys = set(destination_lookup.keys())
        missing_keys = source_keys - destination_keys
        extra_keys = destination_keys - source_keys

        if missing_keys:
            logger.warning(
                f"found {len(missing_keys)} sales_transaction_id missing in destination"
            )
            self.errors.append(AGGREGATE_DATA_VALIDATION_ERROR)
        else:
            logger.info("no missing sales_transaction_id in destination")

        if extra_keys:
            logger.warning(
                f"found {len(extra_keys)} extra sales_transaction_id in destination"
            )
            self.errors.append(AGGREGATE_DATA_VALIDATION_ERROR)
        else:
            logger.info("no extra sales_transaction_id in destination")

        common_keys = source_keys.intersection(destination_keys)
        aggregate_mismatches = []

        for key in common_keys:
            source_data = source_lookup[key]
            dest_data = destination_lookup[key]

            mismatches = []

            if source_data["total_net_amount"] != dest_data["total_net_amount"]:
                mismatches.append(
                    f"total_net_amount: source={source_data['total_net_amount']}, dest={dest_data['total_net_amount']}"
                )

            if source_data["total_original_price"] != dest_data["total_original_price"]:
                mismatches.append(
                    f"total_original_price: source={source_data['total_original_price']}, dest={dest_data['total_original_price']}"
                )

            if (
                source_data["total_normal_point_earned"]
                != dest_data["total_normal_point_earned"]
            ):
                mismatches.append(
                    f"total_normal_point_earned: source={source_data['total_normal_point_earned']}, dest={dest_data['total_normal_point_earned']}"
                )

            if mismatches:
                aggregate_mismatches.append((key, mismatches))

        if aggregate_mismatches:
            logger.warning(
                f"found {len(aggregate_mismatches)} sales_transaction_id with aggregate mismatches"
            )
            for key, mismatches in aggregate_mismatches:
                logger.warning(f"aggregate mismatches for {key}:")
                for mismatch in mismatches:
                    logger.warning(f"  - {mismatch}")
            self.errors.append(AGGREGATE_DATA_VALIDATION_ERROR)
        else:
            logger.info("no aggregate mismatches found")

        if not missing_keys and not extra_keys and not aggregate_mismatches:
            logger.info("aggregate validation passed: all aggregates match")

    def validate(
        self,
        temp_db_handler: PostgresHandler,
        service: str,
        is_full_dump: bool = True,
    ) -> None:
        """
        Run validations for SalesTransactionItem.
        For full dump: run both record counts and aggregate validations
        For incremental: run only aggregate validation

        Args:
            temp_db_handler (PostgresHandler): Temporary database handler for logging
            service (str): Service name for logging
            is_full_dump (bool): Whether this is a full dump validation
        """
        validation_types = []

        if is_full_dump:
            logger.info("started record counts validation...")
            self.validate_record_counts()
            logger.info("finished record counts validation.")

            validation_types.append(
                {
                    "name": RECORD_COUNTS_VALIDATION,
                    "error": RECORD_COUNTS_VALIDATION_ERROR,
                }
            )

        logger.info("started aggregate data validation...")
        self.validate_aggregate_data(is_full_dump)
        logger.info("finished aggregate data validation.")

        validation_types.append(
            {
                "name": AGGREGATE_DATA_VALIDATION,
                "error": AGGREGATE_DATA_VALIDATION_ERROR,
            }
        )

        insert_validation_logs(
            errors=self.errors,
            db_handler=temp_db_handler,
            service=service,
            source_table="SMCSalesTrans",
            destination_table="SalesTransactionItem",
            validation_types=validation_types,
        )

        if len(self.errors):
            raise AirflowException("validation(s) failed.")
