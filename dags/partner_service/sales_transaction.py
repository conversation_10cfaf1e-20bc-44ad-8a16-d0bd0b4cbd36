import math
import pytz
import threading
from concurrent.futures import as_completed, ThreadPoolExecutor
from datetime import datetime, timedelta

from psycopg2.extensions import connection as postgres_connection

from common_helpers.database_services import <PERSON>S<PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.logging import get_logger
from common_helpers.utils import (
    get_query_offsets,
    insert_migration_result,
)

logger = get_logger()


class SalesTransaction:
    def __init__(
        self,
        batch_size: int,
        executor_max_workers: int,
        mssql_handler: MSSQLHandler,
        postgresql_handler: PostgresHandler,
        incremental_query_date: str = None,
    ) -> None:
        self.batch_size = batch_size
        self.executor_max_workers = executor_max_workers
        self.incremental_query_date = incremental_query_date
        self.mssql_handler = mssql_handler
        self.postgresql_handler = postgresql_handler
        self.destination_insert_query = """
            INSERT INTO "partner_service"."SalesTransaction" (
                "id",
                "memberId",
                "gwlNo",
                "externalId",
                "partnerId",
                "brandId",
                "branchId",
                "netTotalAmount",
                "totalOriginalPrice",
                "totalDiscount",
                "totalEarnableAmount",
                "totalAccumSpendableAmount",
                "totalPointEarned",
                "shippingAmount",
                "status",
                "settings",
                "createdAt",
                "updatedAt",
                "completedAt",
                "documentDate",
                "memberShoppingCardId"
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, %s, %s, %s, NOW () AT TIME ZONE 'UTC', %s, %s, %s)
            ON CONFLICT ("id") DO NOTHING;
        """

    def get_count_query_string(
        self, is_full_dump: bool = True, with_lv_header: bool = True
    ) -> str:
        """
        Generates a query string for counting total records for both full dump and
        incremental migration.

        Args:
            is_full_dump (bool): Whether the migration is a full dump or incremental.
            with_lv_header (bool): Whether to include LV header data in the query.

        Returns:
            str: A query string.
        """
        if not with_lv_header:
            return (
                """
                    SELECT COUNT(*)
                    FROM temp_sales_transaction_without_lv_header_for_full_dump_migration;
                """
                if is_full_dump
                else """
                    SELECT COUNT(*)
                    FROM temp_sales_transaction_without_lv_header_for_incremental_migration;
                """
            )

        return (
            """
                SELECT COUNT(*)
                FROM temp_sales_transaction_with_lv_header_for_full_dump_migration;
            """
            if is_full_dump
            else """
                SELECT COUNT(*)
                FROM temp_sales_transaction_with_lv_header_for_incremental_migration;
            """
        )

    def get_select_query_string(
        self, is_full_dump: bool = True, with_lv_header: bool = True
    ) -> str:
        """
        Generates a query string for selecting records from source for both full dump and
        incremental migration.

        Args:
            is_full_dump (bool): Whether the migration is a full dump or incremental.
            with_lv_header (bool): Whether to include LV header data in the query.

        Returns:
            str: A query string.
        """
        if not with_lv_header:
            return (
                """
                    SELECT
                        id,
                        memberId,
                        gwlNo,
                        externalId,
                        partnerId,
                        brandId,
                        branchId,
                        netTotalAmount,
                        totalOriginalPrice,
                        totalDiscount,
                        totalEarnableAmount,
                        totalAccumSpendableAmount,
                        totalPointEarned,
                        status,
                        settings,
                        createdAt,
                        completedAt,
                        DataDate,
                        ShoppingCard
                    FROM temp_sales_transaction_without_lv_header_for_full_dump_migration
                    ORDER BY id
                    OFFSET
                        %s ROWS
                    FETCH NEXT
                        %s ROWS ONLY;
                """
                if is_full_dump
                else """
                    SELECT
                        id,
                        memberId,
                        gwlNo,
                        externalId,
                        partnerId,
                        brandId,
                        branchId,
                        netTotalAmount,
                        totalOriginalPrice,
                        totalDiscount,
                        totalEarnableAmount,
                        totalAccumSpendableAmount,
                        totalPointEarned,
                        status,
                        settings,
                        createdAt,
                        completedAt,
                        DataDate,
                        ShoppingCard
                    FROM temp_sales_transaction_without_lv_header_for_incremental_migration
                    ORDER BY id
                    OFFSET
                        %s ROWS
                    FETCH NEXT
                        %s ROWS ONLY;
                """
            )

        return (
            """
            SELECT
                id,
                memberId,
                gwlNo,
                externalId,
                partnerId,
                brandId,
                branchId,
                netTotalAmount,
                totalOriginalPrice,
                totalDiscount,
                totalEarnableAmount,
                totalAccumSpendableAmount,
                totalPointEarned,
                status,
                settings,
                createdAt,
                completedAt,
                DataDate,
                ShoppingCard
            FROM temp_sales_transaction_with_lv_header_for_full_dump_migration
            ORDER BY id
            OFFSET
                %s ROWS
            FETCH NEXT
                %s ROWS ONLY;
        """
            if is_full_dump
            else """
                SELECT
                    id,
                    memberId,
                    gwlNo,
                    externalId,
                    partnerId,
                    brandId,
                    branchId,
                    netTotalAmount,
                    totalOriginalPrice,
                    totalDiscount,
                    totalEarnableAmount,
                    totalAccumSpendableAmount,
                    totalPointEarned,
                    status,
                    settings,
                    createdAt,
                    completedAt,
                    DataDate,
                    ShoppingCard
                FROM temp_sales_transaction_with_lv_header_for_incremental_migration
                ORDER BY id
                OFFSET
                    %s ROWS
                FETCH NEXT
                    %s ROWS ONLY;
            """
        )

    def insert_batch_to_destination(
        self,
        connection: postgres_connection,
        batch: list[tuple],
    ) -> None:
        """
        Insert a batch to destination table.

        Args:
            connection (postgres_connection): A PostgreSQL connection.
            batch (list): A list of  records to insert to destination table.

        Returns:
            None
        """
        self.postgresql_handler.execute_with_rollback(
            connection, self.destination_insert_query, batch
        )

    def process_batch(
        self,
        connection: postgres_connection,
        batch: list[tuple],
        batch_no: int,
        total_batches: int,
        total_records: int,
        batch_tracker_table_name: str,
        is_full_dump: bool,
    ) -> None:
        """
        Transform queried result from source table and insert them to a new table.

        Args:
            connection (postgres_connection): A PostgreSQL connection.
            batch (list): A batch to process.
            batch_no (int): The current batch's number, used only for logging.
            total_batches (int): The total number of batches to process, used only for logging.
            total_records (int): The total number of records.
            batch_tracker_table_name (str): The name of the batch tracker table.
            is_full_dump (bool): Whether the migration is a full dump or incremental.

        Returns:
            None
        """
        logger.info(
            f"started transforming and inserting batch {batch_no}/{total_batches} (size {len(batch)})..."
        )
        self.insert_batch_to_destination(
            connection=connection,
            batch=batch,
        )
        logger.info(
            f"successfully transformed and inserted batch {batch_no}/{total_batches} (size {len(batch)})."
        )

        if is_full_dump:
            self.postgresql_handler.update_batch_tracker(
                connection=connection,
                service_name="partner_service",
                table_name=batch_tracker_table_name,
                total_records=total_records,
                batch_no=batch_no,
            )

    def migrate(
        self,
        count_query_string: str,
        select_query_string: str,
        is_full_dump: bool = True,
        with_lv_header: bool = True,
    ) -> None:
        """
        The main function for SalesTransaction migration flow.

        Args:
            count_query_string (str): The query to count the number of records to migrate.
            select_query_string (str): The query to select records to migrate.
            is_full_dump (bool): Whether the migration is a full dump or incremental.
            with_lv_header (bool): Whether to include LV header data in the migration.

        Returns:
            None
        """
        batch_tracker_table_name = (
            "SalesTransaction" if with_lv_header else "SalesTransactionWithoutLVHeader"
        )
        created_at = datetime.now()
        incremental_date = (
            None
            if is_full_dump
            else (
                self.incremental_query_date
                if self.incremental_query_date is not None
                else (datetime.now(pytz.timezone('Asia/Bangkok')).date() - timedelta(days=1)).strftime("%Y-%m-%d")
            )
        )

        mssql_connection = self.mssql_handler.hook.get_conn()
        postgresql_connection = self.postgresql_handler.hook.get_conn()

        tracker = (
            self.postgresql_handler.get_latest_batch_info(
                connection=postgresql_connection,
                service_name="partner_service",
                table_name=batch_tracker_table_name,
            )
            if is_full_dump
            else None
        )

        total_records = (
            self.mssql_handler.get_table_total_records(count_query_string)
            if tracker is None
            else tracker[0]
        )
        total_batches = math.ceil(total_records / self.batch_size)
        offsets = get_query_offsets(
            total_records=total_records,
            batch_size=self.batch_size,
            starting_offset=(
                0 if tracker is None else (tracker[1] - 1) * self.batch_size
            ),
        )
        completed_batches = tracker[2] if tracker is not None else []

        is_migration_succeeded = False

        try:
            futures = []

            batch_generator = self.mssql_handler.generate_batches(
                connection=mssql_connection,
                query_string=select_query_string,
                total_records=total_records,
                batch_size=self.batch_size,
                offsets=offsets,
                completed_batches=completed_batches,
            )

            with ThreadPoolExecutor(max_workers=self.executor_max_workers) as executor:
                semaphore = threading.Semaphore(self.executor_max_workers)

                while True:
                    semaphore.acquire()

                    try:
                        batch, batch_no = next(batch_generator)
                    except StopIteration:
                        break

                    future = executor.submit(
                        self.process_batch,
                        postgresql_connection,
                        batch,
                        batch_no,
                        total_batches,
                        total_records,
                        batch_tracker_table_name,
                        is_full_dump,
                    )
                    futures.append(future)
                    future.add_done_callback(lambda _: semaphore.release())

                for future in as_completed(futures):
                    future.result()

            logger.info(
                f"succesfully processed {total_records} records into SalesTransaction"
            )

            logger.info(f"started cleaning up batch tracker...")
            self.postgresql_handler.cleanup_batch_tracker(
                connection=postgresql_connection,
                service_name="partner_service",
                table_name=batch_tracker_table_name,
            )
            logger.info(f"finished cleaning up batch tracker.")

            is_migration_succeeded = True

        except Exception as error:
            logger.error(f"an error has occured: {error}")

            if postgresql_connection:
                logger.info("a postgresql connection is found, rolling back...")
                postgresql_connection.rollback()
                logger.info("successfully rolled back.")

            raise error
        finally:
            logger.info("started inserting migration result log...")
            if is_migration_succeeded:
                insert_migration_result(
                    postgresql_handler=self.postgresql_handler,
                    dag_name=f"partner_service_{'full_dump' if is_full_dump else 'incremental'}_migration_sales_transaction",
                    migration_type="FULL_DUMP" if is_full_dump else "INCREMENTAL",
                    source_table="SMCSalesHeader",
                    source_table_count=total_records,
                    destination_table=(
                        "SalesTransaction (with LVHeader)"
                        if with_lv_header
                        else "SalesTransaction (without LVHeader)"
                    ),
                    destination_table_count=total_records,
                    validation_type="COMPLETENESS",
                    validation_result=100,
                    created_at=created_at,
                    incremental_date=incremental_date,
                )
            else:
                tracker = self.postgresql_handler.get_latest_batch_info(
                    connection=postgresql_connection,
                    service_name="partner_service",
                    table_name=batch_tracker_table_name,
                )

                destination_table_count = (
                    0
                    if tracker is None
                    else (
                        ((tracker[1] - 1) * self.batch_size)
                        if (tracker[1] - 1) * self.batch_size <= total_records
                        else total_records
                    )
                )

                total_processed = (
                    0 if tracker is None else len(tracker[2]) * self.batch_size
                )

                if tracker is not None and total_batches in tracker[2]:
                    total_processed -= self.batch_size + total_records % self.batch_size

                insert_migration_result(
                    postgresql_handler=self.postgresql_handler,
                    dag_name=f"partner_service_{'full_dump' if is_full_dump else 'incremental'}_migration_sales_transaction",
                    migration_type="FULL_DUMP" if is_full_dump else "INCREMENTAL",
                    source_table="SMCSalesHeader",
                    source_table_count=total_records,
                    destination_table=(
                        "SalesTransaction (with LVHeader)"
                        if with_lv_header
                        else "SalesTransaction (without LVHeader)"
                    ),
                    destination_table_count=destination_table_count,
                    validation_type="COMPLETENESS",
                    validation_result=(
                        0 if tracker is None else total_processed / total_records * 100
                    ),
                    created_at=created_at,
                    incremental_date=incremental_date,
                )
            logger.info("finished inserting migration result log.")

            if mssql_connection:
                mssql_connection.close()
            if postgresql_connection:
                postgresql_connection.close()

    def migrate_full_dump(self) -> None:
        """
        The main function for SalesTransaction full dump migration flow.

        Args:
            None

        Returns:
            None
        """
        full_dump_count_query_string = self.get_count_query_string()
        full_dump_select_query_string = self.get_select_query_string()

        self.migrate(
            count_query_string=full_dump_count_query_string,
            select_query_string=full_dump_select_query_string,
        )

    def migrate_full_dump_without_lv_header(self) -> None:
        """
        The main function for SalesTransaction full dump migration flow without LV header.

        Args:
            None

        Returns:
            None
        """
        full_dump_count_query_string = self.get_count_query_string(with_lv_header=False)
        full_dump_select_query_string = self.get_select_query_string(
            with_lv_header=False
        )

        self.migrate(
            count_query_string=full_dump_count_query_string,
            select_query_string=full_dump_select_query_string,
            with_lv_header=False,
        )

    def migrate_incremental(self) -> None:
        """
        The main function for SalesTransaction incremental migration flow.

        Args:
            None

        Returns:
            None
        """
        incremental_count_query_string = self.get_count_query_string(is_full_dump=False)
        incremental_select_query_string = self.get_select_query_string(
            is_full_dump=False
        )

        self.migrate(
            count_query_string=incremental_count_query_string,
            select_query_string=incremental_select_query_string,
            is_full_dump=False,
        )

    def migrate_incremental_without_lv_header(self) -> None:
        """
        The main function for SalesTransaction incremental migration flow without LV header.

        Args:
            None

        Returns:
            None
        """
        incremental_count_query_string = self.get_count_query_string(
            is_full_dump=False, with_lv_header=False
        )
        incremental_select_query_string = self.get_select_query_string(
            is_full_dump=False, with_lv_header=False
        )

        self.migrate(
            count_query_string=incremental_count_query_string,
            select_query_string=incremental_select_query_string,
            is_full_dump=False,
            with_lv_header=False,
        )
