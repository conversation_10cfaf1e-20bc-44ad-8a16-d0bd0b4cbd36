import os
import pytz
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator

file_name = os.path.basename(__file__).split('.')[0]

# Get the current time in Bangkok, then convert to UTC
now_bangkok = datetime.now(pytz.timezone('Asia/Bangkok'))
now_utc = now_bangkok.astimezone(pytz.utc)
# Calculate start_timestamps and end_timestamps based on UTC midnight
default_end_timestamp = str(now_utc.replace(hour=22, minute=0, second=0, microsecond=0))
default_start_timestamp = str((now_utc.replace(hour=17, minute=0, second=0, microsecond=0)) - timedelta(days=1))

def show_sweep_timestamps(**kwargs):
    print("start_timestamps: ", kwargs['params']['start_timestamps'])
    print("end_timestamps: ", kwargs['params']['end_timestamps'])

with DAG(
    dag_id=file_name,
    schedule_interval="0 22 * * *", # # 0 22 * * *base on utc +0000 this is 5am bangkok time (utc+0700)
    start_date=datetime(2025, 6, 4, 23, 00),
    catchup=False,
    tags=['test', 'cron', 'utc'],
    params= {
        'start_timestamps': default_start_timestamp,
        'end_timestamps': default_end_timestamp,
    }
) as dag:

    start_validation_task = EmptyOperator(task_id="start_validation")

    show_sweep_timestamps_task = PythonOperator(
        task_id='show_sweep_timestamps',
        python_callable=show_sweep_timestamps,
        dag=dag
    )
    
    end_validation_task = EmptyOperator(task_id="end_validation")

    start_validation_task >> show_sweep_timestamps_task >> end_validation_task

