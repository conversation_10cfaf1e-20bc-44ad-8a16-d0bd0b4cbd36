from datetime import datetime, timezone

from airflow import DAG
from airflow.operators.python_operator import PythonOperator

from common_helpers.database_services import MSSQLHandler
from common_helpers.logging import get_logger

logger = get_logger()


def create_lv_birthday_snapshot(handler: MSSQLHandler) -> None:
    current_utc = datetime.now(timezone.utc)
    formatted_date = current_utc.strftime("%Y%m%d")

    query_string = f"""
        IF OBJECT_ID('snapshot_lv_birthday_{formatted_date}', 'U') IS NULL
        BEGIN
            SELECT *
            INTO snapshot_lv_birthday_{formatted_date}
            FROM LVBirthday;
        END
    """

    connection = handler.hook.get_conn()

    try:
        logger.info(f"started creating daily LVBirthday snapshot...")
        handler.execute_query_string(
            connection=connection,
            query_string=query_string,
        )
        logger.info(f"finished creating daily LVBirthday snapshot.")
    finally:
        connection.close()


def create_lv_data_snapshot(handler: MSSQLHandler) -> None:
    current_utc = datetime.now(timezone.utc)
    formatted_date = current_utc.strftime("%Y%m%d")

    query_string = f"""
        IF OBJECT_ID('snapshot_lv_data_{formatted_date}', 'U') IS NULL
        BEGIN
            SELECT *
            INTO snapshot_lv_data_{formatted_date}
            FROM LVData;
        END
    """

    connection = handler.hook.get_conn()

    try:
        logger.info(f"started creating daily LVData snapshot...")
        handler.execute_query_string(
            connection=connection,
            query_string=query_string,
        )
        logger.info(f"finished creating daily LVData snapshot.")
    finally:
        connection.close()


def create_df_member_snapshot(handler: MSSQLHandler) -> None:
    current_utc = datetime.now(timezone.utc)
    formatted_date = current_utc.strftime("%Y%m%d")

    query_string = f"""
        IF OBJECT_ID('Newmember.dbo.snapshot_df_member_{formatted_date}', 'U') IS NULL
        BEGIN
            SELECT *
            INTO Newmember.dbo.snapshot_df_member_{formatted_date}
            FROM Newmember.dbo.df_member;
        END
    """

    connection = handler.hook.get_conn()

    try:
        logger.info(f"started creating daily df_member snapshot...")
        handler.execute_query_string(
            connection=connection,
            query_string=query_string,
        )
        logger.info(f"finished creating daily df_member snapshot.")
    finally:
        connection.close()


with DAG(
    "create_daily_snapshots",
    default_args={
        "owner": "airflow",
    },
    description="Create snapshots of LVBirthday and LVData everyday at midnight UTC +7",
    schedule_interval="0 20 * * *",
    start_date=datetime(2025, 6, 22, 20, 0),
    catchup=False,
    tags=["chore"],
) as create_daily_snapshots_dag:
    loyalty_value_handler = MSSQLHandler(conn_id="loyalty_value_smc_db_connection_id")

    create_df_member_snapshot_task = PythonOperator(
        task_id="create_df_member_snapshot_task",
        python_callable=create_df_member_snapshot,
        op_args=[loyalty_value_handler],
    )
