INSERT INTO partner_service."SalesTransactionPayment" (
    id,
    "paymentMethodId",
    amount,
    settings,
    "createdAt",
    "updatedAt",
    "salesTransactionId"
)
WITH salestransactionpayment AS
(SELECT 
    stp.id
    -- ,stp."paymentMethodId"
    -- ,stp."newPaymentMethodId"
    ,pm.id AS "paymentMethodId" -- ULID for lookup at payment method table
    ,stp.amount
    ,stp.settings
    ,stp."createdAt"
    ,stp."updatedAt"
    ,stp."salesTransactionId"
FROM
    (select 
		*,
		case when TRIM("paymentMethodId") = '***' then '01'
			 when TRIM("paymentMethodId") = '%%%' then '02'
			 when TRIM("paymentMethodId") = 'SHOPEEPAY' then 'SPE'
			 when TRIM("paymentMethodId") = 'QRPAY' then 'QR'
			 when TRIM("paymentMethodId") = 'KCCOFF' then 'KCO'
			--  when "paymentMethodId" in ('161', '164', '165', '170', '171', '172', '174', '175', '176', '177', '702', 'CH32', 'KPC-009', 'KPC-016') then '01'
			 when TRIM("paymentMethodId") = 'CASH 57' then '57'
             --when "paymentMethodId" IN ('4', '301', 'TRUE') then '301'
            -- else "paymentMethodId"
			 else '301'
		end as "newPaymentMethodId"
	from staging_partner_service."SalesTransactionPayment"
	-- limit 1000
    ) as stp
LEFT JOIN partner_service."PaymentMethod" AS pm 
ON stp."newPaymentMethodId" = pm.code
INNER JOIN partner_service."SalesTransaction" st
on stp."salesTransactionId" = st.id
--limit 10
)

SELECT
    id,
    "paymentMethodId",
    amount,
    settings,
    "createdAt",
    "updatedAt",
    "salesTransactionId"
FROM salestransactionpayment

ON CONFLICT (id) DO UPDATE SET
    "paymentMethodId" = EXCLUDED."paymentMethodId",
    amount = EXCLUDED.amount,
    settings = EXCLUDED.settings,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "salesTransactionId" = EXCLUDED."salesTransactionId";