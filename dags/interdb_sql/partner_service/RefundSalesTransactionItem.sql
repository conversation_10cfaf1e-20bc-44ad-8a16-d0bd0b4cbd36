INSERT INTO partner_service."RefundSalesTransactionItem" (
	"id",
	"refundSalesTransactionId",
	"salesTransactionItemId",
	"quantity",
	"refundWallets",
	"revokeWallets",
	"createdAt",
	"updatedAt"
)
WITH refundtransactionitem AS
(SELECT
	ulid.ulid_id as id,
	rst.id as "refundSalesTransactionId",
	sti.id as "salesTransactionItemId",
	sti.quantity as "quantity",
	jsonb_build_array() as "refundWallets",
	jsonb_build_array() as "revokeWallets",
	sti."createdAt" as "createdAt",
	sti."updatedAt" as "updatedAt"
FROM staging_partner_service."RefundSalesTransaction" rst 
INNER JOIN staging_partner_service."SalesTransactionItem" sti
ON sti."salesTransactionId" = rst."salesTransactionId"
INNER JOIN partner_service."SalesTransaction" st
on rst."salesTransactionId" = st.id
LEFT JOIN public."RefundSalesTransactionItemId" temp
ON temp."refundSalesTransactionId" = rst.id
AND temp."salesTransactionItemId" = sti.id
LEFT JOIN staging_partner_service."ulid_RefundSalesTransactionItem" ulid
ON temp.id = ulid.id
)

SELECT
	"id",
	"refundSalesTransactionId",
	"salesTransactionItemId",
	"quantity",
	"refundWallets",
	"revokeWallets",
	"createdAt",
	"updatedAt"
FROM refundtransactionitem


ON CONFLICT ("refundSalesTransactionId", "salesTransactionItemId") DO UPDATE SET
    "quantity" = EXCLUDED."quantity",
    "refundWallets" = EXCLUDED."refundWallets",
    "revokeWallets" = EXCLUDED."revokeWallets",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";