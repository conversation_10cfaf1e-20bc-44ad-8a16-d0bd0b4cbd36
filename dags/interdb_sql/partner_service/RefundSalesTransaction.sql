INSERT into partner_service."RefundSalesTransaction" (
    id,
	"salesTransactionId",
	"type",
	"taxInvoices",
	"externalId",
	reason,
	"caratRefundAmount",
	"caratRevokeAmount",
	"cashbackRefundAmount",
	"cashbackRevokeAmount",
	"chargeBackAmount",
	"refundedAt",
	"createdAt",
	"updatedAt",
	detail,
	"approvedAt"
)
WITH refundtransaction AS
(SELECT 
    rst.id,
	rst."salesTransactionId",
	"type",
	"taxInvoices",
	"externalId",
	reason,
	"caratRefundAmount",
	"caratRevokeAmount",
	"cashbackRefundAmount",
	"cashbackRevokeAmount",
	"chargeBackAmount",
	"refundedAt",
	"createdAt",
	"updatedAt",
	detail,
	"approvedAt"
FROM staging_partner_service."RefundSalesTransaction" rst
INNER JOIN (
	select id
	from partner_service."SalesTransaction"
) st
on rst."salesTransactionId" = st.id
)

SELECT
	id,
	"salesTransactionId",
	"type",
	"taxInvoices",
	"externalId",
	reason,
	"caratRefundAmount",
	"caratRevokeAmount",
	"cashbackRefundAmount",
	"cashbackRevokeAmount",
	"chargeBackAmount",
	"refundedAt",
	"createdAt",
	"updatedAt",
	detail,
	"approvedAt"
FROM refundtransaction

ON CONFLICT (id) DO UPDATE SET
	"salesTransactionId" = EXCLUDED."salesTransactionId",
	"type" = EXCLUDED."type",
	"taxInvoices" = EXCLUDED."taxInvoices",
	"externalId" = EXCLUDED."externalId",
	reason = EXCLUDED.reason,
	"caratRefundAmount" = EXCLUDED."caratRefundAmount",
	"caratRevokeAmount" = EXCLUDED."caratRevokeAmount",
	"cashbackRefundAmount" = EXCLUDED."cashbackRefundAmount",
	"cashbackRevokeAmount" = EXCLUDED."cashbackRevokeAmount",
	"chargeBackAmount" = EXCLUDED."chargeBackAmount",
	"refundedAt" = EXCLUDED."refundedAt",
	"createdAt" = EXCLUDED."createdAt",
	"updatedAt" = EXCLUDED."updatedAt",
	detail = EXCLUDED.detail,
	"approvedAt" = EXCLUDED."approvedAt";