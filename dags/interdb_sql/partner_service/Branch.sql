INSERT into partner_service."Branch" 
SELECT * FROM staging_partner_service."Branch"
ON CONFLICT (id) DO UPDATE SET
    "partnerId" = EXCLUDED."partnerId",
    "brandId" = EXCLUDED."brandId",
    code = EXCLUDED.code,
    "name" = EXCLUDED."name",
    "branchType" = EXCLUDED."branchType",
    latitude = EXCLUDED.latitude,
    longitude = EXCLUDED.longitude,
    "location" = EXCLUDED."location",
    status = EXCLUDED.status,
    "createdAt" = EXCLUDED."createdAt",
    "createdBy" = EXCLUDED."createdBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "deletedAt" = EXCLUDED."deletedAt";