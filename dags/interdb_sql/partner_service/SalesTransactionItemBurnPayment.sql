INSERT into partner_service."SalesTransactionItemBurnPayment" 
SELECT * FROM staging_partner_service."SalesTransactionItemBurnPayment"
ON CONFLICT (id) DO UPDATE SET
    "itemId" = EXCLUDED."itemId",
    "burnAmount" = EXCLUDED."burnAmount",
    "paymentAmount" = EXCLUDED."paymentAmount",
    settings = EXCLUDED.settings,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "burnPaymentId" = EXCLUDED."burnPaymentId";




DROP TABLE partner_service."SalesTransactionItemBurnPayment" CASCADE;

CREATE TABLE partner_service."SalesTransactionItemBurnPayment" (
	id text NOT NULL,
	"itemId" text NOT NULL,
	"burnAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"paymentAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	settings jsonb NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"burnPaymentId" text NOT NULL,
	CONSTRAINT "SalesTransactionItemBurnPayment_pkey" PRIMARY KEY (id),
	CONSTRAINT "SalesTransactionItemBurnPayment_burnPaymentId_fkey" FOREIGN KEY ("burnPaymentId") REFERENCES partner_service."SalesTransactionBurnPayment"(id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT "SalesTransactionItemBurnPayment_itemId_fkey" FOREIGN KEY ("itemId") REFERENCES partner_service."SalesTransactionItem"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);
