INSERT into partner_service."Partner"
SELECT * FROM staging_partner_service."Partner"
ON CONFLICT (id) DO UPDATE SET
    code = EXCLUDED.code,
    "name" = EXCLUDED."name",
    "taxId" = EXCLUDED."taxId",
    "partnerType" = EXCLUDED."partnerType",
    "companyType" = EXCLUDED."companyType",
    "sapCode" = EXCLUDED."sapCode",
    "pointCost" = EXCLUDED."pointCost",
    categories = EXCLUDED.categories,
    address1 = EXCLUDED.address1,
    address2 = EXCLUDED.address2,
    attachments = EXCLUDED.attachments,
    contact = EXCLUDED.contact,
    status = EXCLUDED.status,
    "createdAt" = EXCLUDED."createdAt",
    "createdBy" = EXCLUDED."createdBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "type" = EXCLUDED."type";
