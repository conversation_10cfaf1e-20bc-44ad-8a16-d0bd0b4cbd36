INSERT into partner_service."PaymentMethod" 
SELECT * FROM staging_partner_service."PaymentMethod"
ON CONFLICT (id) DO UPDATE SET
    code = EXCLUDED.code,
    "name" = EXCLUDED."name",
    "type" = EXCLUDED."type",
    description = EXCLUDED.description,
    settings = EXCLUDED.settings,
    "createdBy" = EXCLUDED."createdBy",
    "createdAt" = EXCLUDED."createdAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt",
    "setting_properties_isEarnable" = EXCLUDED."setting_properties_isEarnable",
    "setting_properties_isAccumSpendable" = EXCLUDED."setting_properties_isAccumSpendable",
    "setting_effectiveAt" = EXCLUDED."setting_effectiveAt",
    "setting_createdAt" = EXCLUDED."setting_createdAt";