INSERT INTO partner_service."SalesTransaction" (
	id,
	"memberId",
	"gwlNo",
	"externalId",
	"taxInvoice",
	"partnerId",
	"brandId",
	"branchId",
	"netTotalAmount",
	"totalOriginalPrice",
	"totalCaratEarnableAmount",
	-- "totalCashbackEarnableAmount",
	"totalAccumSpendableAmount",
	-- "importTaxAmount",
	"shippingAmount",
	"status",
	settings,
	"rawRequest",
	"completedAt",
	"createdAt",
	"updatedAt",
	"memberShoppingCardId",
	"refundStatus",
	"detail",
	"documentDate"
)

WITH reactivate_list AS (
	select * FROM public.fulldump_smc_expired_list
	-- WHERE ("updatedAt" BETWEEN '2025-06-12 00:00:20.368' AND '2025-06-13 00:00:20.368') -- hard code for one time use migration later change to WHERE (rl."updatedAt" BETWEEN 'start_timestamps' AND 'end_timestamps')
)

,salestransaction AS
(SELECT 
	main.id,
	main."memberId",
	main."gwlNo",
	main."externalId",
	main."taxInvoice",
	p.id as "partnerId",
	b.id as "brandId",
	br.id as "branchId",
	main."netTotalAmount",
	main."totalOriginalPrice",
	main."totalEarnableAmount" as "totalCaratEarnableAmount", -- "totalCaratEarnableAmount", 
	--main."totalAccumSpendableAmount",
	CASE WHEN m.id IS NOT NULL THEN 0
		 ELSE main."totalAccumSpendableAmount"
	END AS "totalAccumSpendableAmount", -- 22,623,122
	main."shippingAmount",
	main."status",
	
	-- main.settings,
	(main."settings" || jsonb_build_object(
		'partner', jsonb_build_object('code', p.code, 'name', p.name->>'en'),
		'brand', jsonb_build_object('code', b.code, 'name', b.name->>'en'),
		'branch', jsonb_build_object('code', br.code, 'name', br.name->>'en')
	)) AS settings,
	
	main."rawRequest",
	main."completedAt",
	main."createdAt",
	main."updatedAt",
	main."memberShoppingCardId",
	main."refundStatus",
	main."detail",
	main."documentDate"
FROM (
	SELECT
		st.id,
		sm.id as "memberId",
		st."gwlNo",
		st."externalId",
		st."taxInvoice",
		CASE WHEN st."branchId" IS NULL OR st."branchId" = '' THEN 'KPC'
			 WHEN st."branchId" = '9999' AND st."brandId" = '58' THEN 'KPD'
			 WHEN st."branchId" = '9999' AND st."brandId" = '01' THEN 'KPD'
			 ELSE site."partner_code"
		END AS "partnerId",
		CASE WHEN st."branchId" IS NULL OR st."branchId" = '' THEN 'KPC_OFFLINE'
			 WHEN st."branchId" = '9999' AND st."brandId" = '58' THEN 'KPD_OFFLINE'
			 WHEN st."branchId" = '9999' AND st."brandId" = '01' THEN 'KPD_OFFLINE'
			 ELSE site."brand_code"
		END AS "brandId",
		CASE WHEN st."branchId" IS NULL OR st."branchId" = '' THEN 'KPC_RANGNAM'
			 WHEN st."branchId" = '9999' AND st."brandId" = '58' THEN 'KPD_DMK_AIRPORT'
			 WHEN st."branchId" = '9999' AND st."brandId" = '01' THEN 'KPD_SVB_AIRPORT'
			 ELSE site."gwl_branch_code"
		END AS "branchId",
		st."netTotalAmount",
		st."totalOriginalPrice",
		st."totalEarnableAmount",
		CASE WHEN st."memberId" = rl."memberId" AND (st."createdAt" < '2025-06-03 17:00:00 +0000') THEN 0.00
			ELSE st."totalAccumSpendableAmount"
			END AS "totalAccumSpendableAmount",--22,623,122
		--st."totalAccumSpendableAmount",
		
		st."shippingAmount",
		st.status,
		st.settings,
		st."rawRequest",
		st."createdAt" AS "completedAt",
		st."createdAt",
		st."updatedAt",
		st."memberShoppingCardId",
		CASE WHEN rsti."salesTransactionId" IS NOT NULL THEN 'FULL'
			 ELSE NULL
		END AS "refundStatus",
		st."detail",
		st."documentDate"
	FROM staging_partner_service."SalesTransaction" st
	LEFT JOIN public."RefundSalesTransactionId" rsti
	ON st.id = cast(rsti."salesTransactionId" as bigint)
	LEFT JOIN public.site_branch_mapping site
	ON st."branchId" = site.site_code
	INNER JOIN loyalty_service."Member" AS sm
    ON st."memberId" = sm."gwlNo"
	LEFT JOIN reactivate_list rl on st."memberId" = rl."memberId"
) main
LEFT JOIN partner_service."Brand" b on b.code = main."brandId"
LEFT JOIN partner_service."Branch" br on br.code = main."branchId"
LEFT JOIN partner_service."Partner" p on p.code = main."partnerId" --22,623,122
LEFT JOIN ( SELECT id FROM loyalty_service."Member" WHERE "isActive" = False ) m ON main."memberId" = m.id --22,623,122
)-- fix staging_loyalty_service."SalesTransaction" >> loyalty_service."SalesTransaction" that from testing script

SELECT
	id,
	"memberId",
	"gwlNo",
	"externalId",
	"taxInvoice",
	"partnerId",
	"brandId",
	"branchId",
	"netTotalAmount",
	"totalOriginalPrice",
	"totalCaratEarnableAmount",
	-- "totalCashbackEarnableAmount",
	"totalAccumSpendableAmount",
	-- "importTaxAmount",
	"shippingAmount",
	"status",
	settings,
	"rawRequest",
	"completedAt",
	"createdAt",
	"updatedAt",
	"memberShoppingCardId",
	"refundStatus",
	"detail",
	"documentDate"
FROM salestransaction

ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "gwlNo" = EXCLUDED."gwlNo",
    "externalId" = EXCLUDED."externalId",
    "taxInvoice" = EXCLUDED."taxInvoice",
    "partnerId" = EXCLUDED."partnerId",
    "brandId" = EXCLUDED."brandId",
    "branchId" = EXCLUDED."branchId",
    "netTotalAmount" = EXCLUDED."netTotalAmount",
    "totalOriginalPrice" = EXCLUDED."totalOriginalPrice",
	"totalCaratEarnableAmount" = EXCLUDED."totalCaratEarnableAmount",
    "totalAccumSpendableAmount" = EXCLUDED."totalAccumSpendableAmount",
    "shippingAmount" = EXCLUDED."shippingAmount",
    status = EXCLUDED.status,
    settings = EXCLUDED.settings,
    "rawRequest" = EXCLUDED."rawRequest",
    "completedAt" = EXCLUDED."completedAt",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "memberShoppingCardId" = EXCLUDED."memberShoppingCardId",
	"refundStatus" = EXCLUDED."refundStatus",
	"detail" = EXCLUDED."detail",
	"documentDate" = EXCLUDED."documentDate";
