INSERT into partner_service."ProductBrand" 
SELECT * FROM staging_partner_service."ProductBrand"
ON CONFLICT (id) DO UPDATE SET
    "brandId" = EXCLUDED."brandId",
    code = EXCLUDED.code,
    "name" = EXCLUDED."name",
    settings = EXCLUDED.settings,
    "createdBy" = EXCLUDED."createdBy",
    "createdAt" = EXCLUDED."createdAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt";