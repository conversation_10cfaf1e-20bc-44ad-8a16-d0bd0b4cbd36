INSERT into partner_service."Brand" 
SELECT * FROM staging_partner_service."Brand"
ON CONFLICT (id) DO UPDATE SET
    code = EXCLUDED.code,
    "name" = EXCLUDED."name",
    categories = EXCLUDED.categories,
    status = EXCLUDED.status,
    "createdAt" = EXCLUDED."createdAt",
    "createdBy" = EXCLUDED."createdBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    logo = EXCLUDED.logo,
    settings = EXCLUDED.settings,
    "deletedAt" = EXCLUDED."deletedAt",
    description = EXCLUDED.description;