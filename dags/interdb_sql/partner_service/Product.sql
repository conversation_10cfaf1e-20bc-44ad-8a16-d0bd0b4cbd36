INSERT into partner_service."Product" 
SELECT * FROM staging_partner_service."Product"
ON CONFLICT (id) DO UPDATE SET
    "brandId" = EXCLUDED."brandId",
    sku = EXCLUDED.sku,
    "name" = EXCLUDED."name",
    "categoryCode" = EXCLUDED."categoryCode",
    "brandCode" = EXCLUDED."brandCode",
    settings = EXCLUDED.settings,
    status = EXCLUDED.status,
    "createdBy" = EXCLUDED."createdBy",
    "createdAt" = EXCLUDED."createdAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt";