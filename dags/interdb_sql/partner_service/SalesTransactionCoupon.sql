INSERT into partner_service."SalesTransactionCoupon" 
SELECT * FROM staging_partner_service."SalesTransactionCoupon"
ON CONFLICT (id) DO UPDATE SET
    "salesTransactionId" = EXCLUDED."salesTransactionId",
    "couponSourceType" = EXCLUDED."couponSourceType",
    type = EXCLUDED.type,
    "subType" = EXCLUDED."subType",
    "memberCouponId" = EXCLUDED."memberCouponId",
    "promoCode" = EXCLUDED."promoCode",
    sapcode = EXCLUDED.sapcode,
    value = EXCLUDED.value,
    settings = EXCLUDED.settings,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";