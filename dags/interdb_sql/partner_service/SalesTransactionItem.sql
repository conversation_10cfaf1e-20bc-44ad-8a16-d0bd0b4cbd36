INSERT into partner_service."SalesTransactionItem" (
    "id",
    "productId",
    "quantity",
    "netAmount",
    "caratEarnableAmount",
    "normalPointEarned",
    "burnPaymentAmount",
    "settings",
    "paymentDetail",
    "createdAt",
    "updatedAt",
    "originalPrice",
    "salesTransactionId",
    "tierExtraPointEarned",
    "sku"
)

WITH product_max_date AS
(
SELECT DISTINCT ON (p."brandId", p.sku)
    p.id, -- This will be the 'id' of the chosen (latest) row for the group
    p."brandId",
    p.sku,
    p.name,
    p."categoryCode",
    p."brandCode",
    p.settings,
    p.status,
    p."createdBy",
    p."updatedBy",
    p."deletedAt",
    p."createdAt", -- This will be the 'createdAt' of the chosen (latest) row
    p."updatedAt"  -- This will be the 'updatedAt' of the chosen (latest) row
FROM
    partner_service."Product" AS p
--WHERE p.sku = '000000000001087875' AND p."brandId" ='01JNN94ACFV1ZZA8JBX2N7A0K2' -- Uncomment if you need specific filtering
ORDER BY
    p."brandId",
    p.sku,
    GREATEST(p."createdAt", p."updatedAt") DESC
)


,salestransactionitem AS
(
SELECT
    sti."id",
    p.id AS "productId",
    sti."quantity",
    sti."netAmount",
    sti."caratEarnableAmount",
    sti."normalPointEarned",
    sti."burnPaymentAmount",
    sti."settings",
    sti."paymentDetail",
    sti."createdAt",
    sti."updatedAt",
    sti."originalPrice",
    sti."salesTransactionId",
    sti."tierExtraPointEarned",
    sti."sku"
FROM staging_partner_service."SalesTransactionItem" sti
INNER JOIN partner_service."SalesTransaction" st on sti."salesTransactionId" = st.id
LEFT JOIN product_max_date AS p ON (st."brandId" = p."brandId" AND sti.sku = p.sku)
LEFT JOIN partner_service."Brand" AS b ON st."brandId" = b.id
)

SELECT
    "id",
    "productId",
    "quantity",
    "netAmount",
    "caratEarnableAmount",
    "normalPointEarned",
    "burnPaymentAmount",
    "settings",
    "paymentDetail",
    "createdAt",
    "updatedAt",
    "originalPrice",
    "salesTransactionId",
    "tierExtraPointEarned",
    "sku"
FROM salestransactionitem



ON CONFLICT (id) DO UPDATE SET
    "productId" = EXCLUDED."productId",
    quantity = EXCLUDED.quantity,
    "netAmount" = EXCLUDED."netAmount",
    "caratEarnableAmount" = EXCLUDED."caratEarnableAmount",
    "normalPointEarned" = EXCLUDED."normalPointEarned",
    "burnPaymentAmount" = EXCLUDED."burnPaymentAmount",
    settings = EXCLUDED.settings,
    "paymentDetail" = EXCLUDED."paymentDetail",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "originalPrice" = EXCLUDED."originalPrice",
    "salesTransactionId" = EXCLUDED."salesTransactionId",
    "tierExtraPointEarned" = EXCLUDED."tierExtraPointEarned",
    sku = EXCLUDED.sku;