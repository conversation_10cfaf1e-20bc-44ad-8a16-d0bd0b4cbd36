INSERT INTO partner_service."SalesTransactionWalletActivity" (
	id,
	"salesTransactionId",
	"type",
	"activityId",
	"detail",
	"createdAt",
	"updatedAt"
)
WITH salestransactionwalletactivity AS
(SELECT
	--stg_stwat.id as stg_stwat_id,
	ulid.ulid_id as id,
	stg_stwat."salesTransactionId"::bigint as "salesTransactionId",
	stg_stwat."type",
	--stg_stwat."activityId",
	wa_ulid.ulid_id  AS "activityId", 
	stg_stwat."detail",
	stg_stwat."createdAt",
	stg_stwat."updatedAt"
FROM staging_partner_service."SalesTransactionWalletActivityTemp" AS stg_stwat 
INNER JOIN partner_service."SalesTransaction" AS st ON stg_stwat."salesTransactionId" = st.id::text
LEFT JOIN staging_partner_service."ulid_SalesTransactionWalletActivity" ulid ON stg_stwat.id = ulid.id
LEFT JOIN staging_point_service."ulid_WalletActivity" wa_ulid ON stg_stwat."activityId" = wa_ulid.id
-- LIMIT 10
)

SELECT
	id,
	"salesTransactionId",
	"type",
	"activityId",
	"detail",
	"createdAt",
	"updatedAt"
FROM salestransactionwalletactivity

ON CONFLICT (id) DO UPDATE SET
    "salesTransactionId" = EXCLUDED."salesTransactionId",
    "type" = EXCLUDED."type",
	"activityId" = EXCLUDED."activityId",
    "detail" = EXCLUDED."detail",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";
-- airflow run 18-20 min
-- airflow run with added wa_ulid 26 min, update xxxx records INFO - Rows affected: 14304056