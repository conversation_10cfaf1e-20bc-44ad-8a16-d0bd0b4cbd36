INSERT into partner_service."CostCenter" 
SELECT * FROM staging_partner_service."CostCenter"
ON CONFLICT (id) DO UPDATE SET
    "partnerId" = EXCLUDED."partnerId",
    code = EXCLUDED.code,
    "name" = EXCLUDED."name",
    "businessAreaCode" = EXCLUDED."businessAreaCode",
    "businessAreaName" = EXCLUDED."businessAreaName",
    "createdBy" = EXCLUDED."createdBy",
    "createdAt" = EXCLUDED."createdAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt",
    "companyCode" = EXCLUDED."companyCode",
    "companyName" = EXCLUDED."companyName";