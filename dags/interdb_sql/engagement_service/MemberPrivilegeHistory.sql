INSERT into engagement_service."MemberPrivilegeHistory" 
SELECT * FROM staging_engagement_service."MemberPrivilegeHistory"
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "privilegeId" = EXCLUDED."privilegeId",
    "typeCode" = EXCLUDED."typeCode",
    "subTypeCode" = EXCLUDED."subTypeCode",
    "couponCode" = EXCLUDED."couponCode",
    activity = EXCLUDED.activity,
    reason = EXCLUDED.reason,
    "location" = EXCLUDED."location",
    "createdBy" = EXCLUDED."createdBy",
    "usedBy" = EXCLUDED."usedBy",
    "createdAt" = EXCLUDED."createdAt",
    "deletedAt" = EXCLUDED."deletedAt";