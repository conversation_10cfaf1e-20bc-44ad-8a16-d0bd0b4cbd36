INSERT into engagement_service."Reward" 
SELECT * FROM staging_engagement_service."Reward"
ON CONFLICT (id) DO UPDATE SET
    "runningId" = EXCLUDED."runningId",
    "nameEn" = EXCLUDED."nameEn",
    "nameTh" = EXCLUDED."nameTh",
    "nameCn" = EXCLUDED."nameCn",
    type = EXCLUDED.type,
    "rewardCategoryId" = EXCLUDED."rewardCategoryId",
    "visibilityType" = EXCLUDED."visibilityType",
    "displayType" = EXCLUDED."displayType",
    remark = EXCLUDED.remark,
    "couponId" = EXCLUDED."couponId",
    coupon = EXCLUDED.coupon,
    "walletSetting" = EXCLUDED."walletSetting",
    "criteriaType" = EXCLUDED."criteriaType",
    "redemptionCondition" = EXCLUDED."redemptionCondition",
    "effectiveDate" = EXCLUDED."effectiveDate",
    "expiredAt" = EXCLUDED."expiredAt",
    "eventType" = EXCLUDED."eventType",
    "eventCondition" = EXCLUDED."eventCondition",
    "eventCouponTrigger" = EXCLUDED."eventCouponTrigger",
    status = EXCLUDED.status,
    "isActive" = EXCLUDED."isActive",
    "typeCode" = EXCLUDED."typeCode",
    "subTypeCode" = EXCLUDED."subTypeCode",
    "value" = EXCLUDED."value",
    "quantity" = EXCLUDED."quantity",
    "quota" = EXCLUDED."quota",
    contractreference = EXCLUDED.contractreference,
    "cost" = EXCLUDED."cost",
    usagecondition = EXCLUDED.usagecondition,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt";