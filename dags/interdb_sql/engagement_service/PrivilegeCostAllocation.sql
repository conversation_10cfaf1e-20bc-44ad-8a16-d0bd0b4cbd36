INSERT into engagement_service."PrivilegeCostAllocation" 
SELECT * FROM staging_engagement_service."PrivilegeCostAllocation"
ON CONFLICT (id) DO UPDATE SET
    "privilegeId" = EXCLUDED."privilegeId",
    "entityId" = EXCLUDED."entityId",
    "entityType" = EXCLUDED."entityType",
    value = EXCLUDED.value,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt";