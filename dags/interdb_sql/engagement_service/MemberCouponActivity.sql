INSERT INTO "engagement_service"."MemberCouponActivity" (
	"id",
	"memberId",
	"refId",
	"refType",
	"remark",
	"activity",
	"couponCode",
	"reason",
	"location",
	"createdBy",
	"usedBy",
	"createdAt",
	"deletedAt"
	--,"channel"
)
WITH mem_coupon_activity AS
(SELECT
	ulid_memberprivilege.ulid_id as "id",
	sm.id AS "memberId",
	'01JPQ00MJ5AFR4P6H3H8HHK6Q4' AS "refId",
	'PRIVILEGE' AS "refType",
	'{"name": "_MIGRATION_BIRTHDAY"}'::jsonb AS "remark",
	'USED' AS "activity",
	'MIGRATE_PROMO_CODE' AS "couponCode",
	null AS "reason",
	null AS "location",
	jsonb_build_object(
                        'id', null, 
                        'name', 'SYSTEM', 
                        'email', null
                    ) AS "createdBy",
	null AS "usedBy",
	"grantedAt"::TIMESTAMPTZ AS "createdAt",
	null::TIMESTAMPTZ AS "deletedAt"
	--,"channel" 
FROM staging_engagement_service."MemberPrivilege" memberprivilege
INNER JOIN loyalty_service."Member" AS sm
ON memberprivilege."memberId" = sm."gwlNo"
LEFT JOIN staging_engagement_service."ulid_MemberPrivilege" AS ulid_memberprivilege 
ON memberprivilege.id = ulid_memberprivilege.id
where memberprivilege."createdAt" >= '2024-12-31T17:00:00.000Z'
)
SELECT
	"id",
	"memberId",
	"refId",
	"refType",
	"remark",
	"activity",
	"couponCode",
	"reason",
	"location",
	"createdBy",
	"usedBy",
	"createdAt",
	"deletedAt"
	--,"channel"
FROM mem_coupon_activity

ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "refId" = EXCLUDED."refId",
    "refType" = EXCLUDED."refType",
    "remark" = EXCLUDED."remark",
    "activity" = EXCLUDED."activity",
    "couponCode" = EXCLUDED."couponCode",
    "reason" = EXCLUDED."reason",
    "location" = EXCLUDED."location",
    "createdBy" = EXCLUDED."createdBy",
    "usedBy" = EXCLUDED."usedBy",
    "createdAt" = EXCLUDED."createdAt",
    "deletedAt" = EXCLUDED."deletedAt"
	--,channel = EXCLUDED.channel;
	;