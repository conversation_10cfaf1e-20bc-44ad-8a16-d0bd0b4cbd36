INSERT INTO "engagement_service"."MemberCoupon" (
	"id",
	"memberId",
	"entityType",
	"entityId",
	"status",
	"usedAt",
	"expiredAt",
	"couponRef",
	"createdAt",
	"updatedAt",
	"deletedAt",
	"isUnlimited",
	"isActive",
	"isUsedForGuest",
	"remark",
	"claimExpiredAt",
	"sourceId",
	"sourceType",
	"updatedBy",
	"createdBy"
)
WITH mem_coupon AS
(SELECT
	ulid_memberprivilege.ulid_id as "id",
	sm.id AS "memberId",
	'PRIVILEGE' as "entityType",
	'01JPQ00MJ5AFR4P6H3H8HHK6Q4' as "entityId",
	'USED' as "status",
	"grantedAt"::TIMESTAMPTZ as "usedAt",
	"grantedAt"::TIMESTAMPTZ as "expiredAt",
	'MIGRATE_PROMO_CODE' as "couponRef",
	"grantedAt"::TIMESTAMPTZ as "createdAt",
	"grantedAt"::TIMESTAMPTZ as "updatedAt",
	null::TIMESTAMPTZ as "deletedAt",
	false::bool as "isUnlimited",
	true::bool as "isActive",
	null::bool as "isUsedForGuest",
	'{"name": "_MIGRATION_BIRTHDAY", "endedAt": "2025-12-31T17:00:00.000Z", "couponId": "", "typeCode": "CASHBACK", "startedAt": "2024-12-31T17:00:00.000Z", "subTypeCode": "CASHBACK_BIRTHDAY_PERCENT", "couponCodeType": "FIXED"}'::jsonb as "remark",
	null::TIMESTAMPTZ as "claimExpiredAt",
	ulid_memberprivilege.ulid_id as "sourceId",
	'MEMBER_PRIVILEGE' as "sourceType",
	jsonb_build_object(
                        'id', null, 
                        'name', 'SYSTEM', 
                        'email', null
                    ) as "updatedBy",
	jsonb_build_object(
						'id', null, 
						'name', 'SYSTEM', 
						'email', null
					) as "createdBy"
FROM staging_engagement_service."MemberPrivilege" memberprivilege
INNER JOIN loyalty_service."Member" AS sm
ON memberprivilege."memberId" = sm."gwlNo"
LEFT JOIN staging_engagement_service."ulid_MemberPrivilege" AS ulid_memberprivilege 
ON memberprivilege.id = ulid_memberprivilege.id
WHERE memberprivilege."createdAt" >= '2024-12-31T17:00:00.000Z'
)

SELECT
	"id",
	"memberId",
	"entityType",
	"entityId",
	"status",
	"usedAt",
	"expiredAt",
	"couponRef",
	"createdAt",
	"updatedAt",
	"deletedAt",
	"isUnlimited",
	"isActive",
	"isUsedForGuest",
	"remark",
	"claimExpiredAt",
	"sourceId",
	"sourceType",
	"updatedBy",
	"createdBy"
FROM mem_coupon

ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "entityType" = EXCLUDED."entityType",
    "entityId" = EXCLUDED."entityId",
    "status" = EXCLUDED."status",
    "usedAt" = EXCLUDED."usedAt",
    "expiredAt" = EXCLUDED."expiredAt",
    "couponRef" = EXCLUDED."couponRef",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt",
    "isUnlimited" = EXCLUDED."isUnlimited",
    "isActive" = EXCLUDED."isActive",
    "isUsedForGuest" = EXCLUDED."isUsedForGuest",
    "remark" = EXCLUDED."remark",
    "claimExpiredAt" = EXCLUDED."claimExpiredAt",
    "sourceId" = EXCLUDED."sourceId",
    "sourceType" = EXCLUDED."sourceType",
	"updatedBy" = EXCLUDED."updatedBy",
	"createdBy" = EXCLUDED."createdBy";
