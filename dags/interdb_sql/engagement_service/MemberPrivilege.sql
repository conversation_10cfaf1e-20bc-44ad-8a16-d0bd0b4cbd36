INSERT INTO "engagement_service"."MemberPrivilege" (
	"id",
	"memberId",
	"privilegeId",
	"status",
	"isUnlimited",
	"grantedAt",
	"createdAt",
	"updatedAt",
    "issuer", 
    "issuerCode", 
    "issuerIdentifier", 
    "issuerType", 
    "memberPrivilegeLogId"
)
WITH ranked AS (
    SELECT *,
    	   ulid_memberprivilege.ulid_id AS ulid,
           ROW_NUMBER() OVER (PARTITION BY "memberId" ORDER BY memberprivilege."createdAt" DESC) AS rn
    FROM staging_engagement_service."MemberPrivilege" AS memberprivilege
    LEFT JOIN staging_engagement_service."ulid_MemberPrivilege" AS ulid_memberprivilege 
    ON memberprivilege.id = ulid_memberprivilege.id
    WHERE memberprivilege."createdAt" >= '2024-12-31T17:00:00.000Z'
)
,
post_ranked AS
(SELECT 
    ranked.ulid AS "id",
    sm.id AS "memberId",
    '01JPQ00MJ5AFR4P6H3H8HHK6Q4' as "privilegeId",
    "status",
    "isUnlimited"::bool,
    '2024-12-31T17:00:00.000Z'::TIMESTAMPTZ AS "grantedAt",
    '2024-12-31T17:00:00.000Z'::TIMESTAMPTZ AS "createdAt",
    '2024-12-31T17:00:00.000Z'::TIMESTAMPTZ AS "updatedAt",
    '{"id": "MIGRATE", "code": "MIGRATE", "name": "MIGRATE", "image": {}, "earnRate": 1, "minimumSpending": 0, "maintainSpending": 0}'::jsonb as "issuer", 
    'MIGRATE' as "issuerCode", 
    '0' as "issuerIdentifier", 
    'TIER' as "issuerType", 
    '' as "memberPrivilegeLogId"
FROM ranked
INNER JOIN loyalty_service."Member" AS sm
ON ranked."memberId" = sm."gwlNo"
WHERE rn = 1
)
SELECT 
    "id",
    "memberId",
    "privilegeId",
    "status",
    "isUnlimited",
    "grantedAt",
    "createdAt",
    "updatedAt",
    "issuer", 
    "issuerCode", 
    "issuerIdentifier", 
    "issuerType", 
    "memberPrivilegeLogId"
FROM post_ranked

ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "privilegeId" = EXCLUDED."privilegeId",
    status = EXCLUDED.status,
    "isUnlimited" = EXCLUDED."isUnlimited",
    "grantedAt" = EXCLUDED."grantedAt",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "issuer" = EXCLUDED."issuer",
    "issuerCode" = EXCLUDED."issuerCode",
    "issuerIdentifier" = EXCLUDED."issuerIdentifier",
    "issuerType" = EXCLUDED."issuerType",
    "memberPrivilegeLogId" = EXCLUDED."memberPrivilegeLogId";