INSERT into engagement_service."Member"
SELECT * FROM staging_engagement_service."Member"
ON CONFLICT (id) DO UPDATE SET
    "dateOfBirth" = EXCLUDED."dateOfBirth",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt",
    "firstName" = EXCLUDED."firstName",
    "firstNameTh" = EXCLUDED."firstNameTh",
    "lastName" = EXCLUDED."lastName",
    "lastNameTh" = EXCLUDED."lastNameTh",
    "middleName" = EXCLUDED."middleName",
    "middleNameTh" = EXCLUDED."middleNameTh",
    "titleId" = EXCLUDED."titleId",
    "titleName" = EXCLUDED."titleName",
    "gwlNo" = EXCLUDED."gwlNo";