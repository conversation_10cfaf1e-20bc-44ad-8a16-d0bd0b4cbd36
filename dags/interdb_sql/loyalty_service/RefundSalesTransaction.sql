INSERT into loyalty_service."RefundSalesTransaction" (
    id,
    "memberId",
    "type",
    "salesTransactionId",
    "externalId",
    "refundedAt",
    "refundAmount",
    "revokeAccumSpendableAmount",
    "createdAt",
    "updatedAt"
)

WITH refundtransaction AS 
(SELECT 
    rst.id,
	sm.id AS "memberId", -- made to ULID 
	rst."type",
    cast(rst."salesTransactionId" as text),
	rst."externalId",
    rst."refundedAt",
    st."netTotalAmount" as "refundAmount",
    st."totalAccumSpendableAmount" as "revokeAccumSpendableAmount",
    rst."createdAt",
    rst."updatedAt"
FROM staging_partner_service."RefundSalesTransaction" rst  
LEFT JOIN partner_service."SalesTransaction" st 
ON cast(rst."salesTransactionId" as bigint) = st.id
INNER JOIN loyalty_service."Member" AS sm
ON st."gwlNo" = sm."gwlNo"
WHERE st."memberId" IS NOT NULL 
--LIMIT 10000
)

SELECT
    id,
    "memberId",
    "type",
    "salesTransactionId",
    "externalId",
    "refundedAt",
    "refundAmount",
    "revokeAccumSpendableAmount",
    "createdAt",
    "updatedAt"
FROM refundtransaction

ON CONFLICT (id) DO UPDATE SET
	"memberId" = EXCLUDED."memberId",
    "type" = EXCLUDED."type",
	"salesTransactionId" = EXCLUDED."salesTransactionId",
	"externalId" = EXCLUDED."externalId",
	"refundedAt" = EXCLUDED."refundedAt",
	"refundAmount" = EXCLUDED."refundAmount",
	"revokeAccumSpendableAmount" = EXCLUDED."revokeAccumSpendableAmount",
	"createdAt" = EXCLUDED."createdAt",
	"updatedAt" = EXCLUDED."updatedAt";