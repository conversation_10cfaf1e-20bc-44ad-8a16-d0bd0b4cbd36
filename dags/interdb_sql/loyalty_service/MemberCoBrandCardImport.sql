INSERT into loyalty_service."MemberCoBrandCardImport" 
SELECT * FROM staging_loyalty_service."MemberCoBrandCardImport"
ON CONFLICT (id) DO UPDATE SET
    "coBrandId" = EXCLUDED."coBrandId",
    "file" = EXCLUDED."file",
    "status" = EXCLUDED."status",
    "error" = EXCLUDED."error",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";




-- INSERT into staging_loyalty_service."MemberCoBrandCardImport" 
-- SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."MemberCoBrandCardImport"') AS 
-- t1(id text,
--     "coBrandId" text,
--     "file" jsonb,
--     "status" text,
--     "error" text,
--     "createdAt" timestamp(3),
--     "updatedAt" timestamp(3))
-- ON CONFLICT (id) DO UPDATE SET
--     "coBrandId" = EXCLUDED."coBrandId",
--     "file" = EXCLUDED."file",
--     "status" = EXCLUDED."status",
--     "error" = EXCLUDED."error",
--     "createdAt" = EXCLUDED."createdAt",
--     "updatedAt" = EXCLUDED."updatedAt";
