INSERT into loyalty_service."Member" (
    "id",
    "gwlNo",
    "embossNo",
    "email",
    "emailVerifiedAt",
    "phone",
    "phoneVerifiedAt",
    "registeredAt",
    "deletedAt",
    "tierId",
    "minimumTierId",
    "tierStartedAt",
    "tierEndedAt",
    "accumulateSpending",
    "lifeTimeSpending",
    "createdAt",
    "updatedAt",
    "isActive",
    "reason",
    "picRemark",
    "referralCode",
    "upgradeGroupCode",
    "upgradeReasonCode",
    "registrationChannelCode",
    "registrationLocationCode",
    "accumulateMaintainSpending",
    "shoppingCardId",
    "onepassId",
    "phoneCode",
    "isCoBrandNonMember",
    "emailHash",
    "phoneHash",
    "minimumTierInvitedId",
    "updatedBy",
    "remark",
    "createdBy"
)

WITH postfilter_reason AS (
    SELECT
        "memberId"
        , CASE WHEN member_reason.reason = 'Suspended' THEN 'Suspended'
               WHEN member_reason.reason = 'MIGRATE_NO_LV_COBRAND_INACTIVE' THEN 'SCB Expired'
               WHEN member_reason.reason in ('MIGRATE_LV_INACTIVE_COBRAND_INACTIVE', 'MIGRATE_LV_INACTIVE_NO_COBRAND') THEN 'SMC Expired'
               ELSE NULL END AS reason
        , "createdAt"
        , "updatedAt"
    FROM public.member_reason
    WHERE (
        trim(UPPER(member_reason.reason)) NOT IN ('NOTMIGRATE_EXCLUDED_LIST','NOTMIGRATE_NO_LV_NO_COBRAND') 
        or (member_reason.reason is null)
    )
)

,zero_teir_id AS
(
SELECT
                    ulid_member.ulid_id,
                    member."gwlNo",
                    member."embossNo",
                    member.email,
                    member."emailVerifiedAt",
                    member.phone,
                    member."phoneVerifiedAt",
                    member."registeredAt",
                    member."deletedAt",
                    CASE WHEN
                            (CASE WHEN tiertransformed."isActive" = true THEN tiertransformed."isActive" ELSE false END) = false THEN 'NAVY' ELSE UPPER(tiertransformed."tierId")
                        END AS "tierId",
                    tiertransformed."minimumTierId",
                    
                    TIMESTAMP '2025-06-30 17:00:00' AS "tierStartedAt",

                    CASE WHEN TRIM(tiertransformed."tierId") IN ('Vega(GP)','Scarlet(GP-L4000)','Scarlet(GP-SVP10)','Crown(GP)') THEN TIMESTAMP '2026-12-31 16:59:59' 
                        ELSE TIMESTAMP '2027-12-31 16:59:59'
                        END AS "tierEndedAt", 
                        
                    
                    tiertransformed."accumulateSpending",
                    member."lifeTimeSpending",
                    member."createdAt",
                    -- member."updatedAt",
                    tiertransformed."updatedAt" as "updatedAt",
                    CASE WHEN tiertransformed."isActive" = true THEN tiertransformed."isActive" ELSE false END AS "isActive",
                    post_mr.reason,
                    member."picRemark",
                    member."referralCode",
                    member."upgradeGroupCode",
                    member."upgradeReasonCode",
                    member."registrationChannelCode",
                    member."registrationLocationCode",
                    member."accumulateMaintainSpending",
                    member."shoppingCardId",
                    member."onepassId",
                    member."phoneCode",
                    member.remark,
                    isconon_m."isCoBrandNonMember"::bool,
                    member."emailHash",
                    member."phoneHash",
                    tiertransformed."minimumTierInvitedId",
                    jsonb_build_object(
                        'id', null, 
                        'name', 'SYSTEM', 
                        'email', null
                    ) as "updatedBy",
                    jsonb_build_object(
                        'id', null, 
                        'name', 'SYSTEM', 
                        'email', null
                    ) as "createdBy"
                FROM staging_loyalty_service."Member" as member
                INNER JOIN postfilter_reason as post_mr ON member.id = post_mr."memberId"
                LEFT JOIN public.tiertransformed as tiertransformed ON member.id = tiertransformed."memberId"
                LEFT JOIN public.member_iscobrandnonmember as isconon_m ON member.id = isconon_m."memberId"
                LEFT JOIN staging_loyalty_service.ulid_member as ulid_member ON member.id = ulid_member.id
                --WHERE member."gwlNo" = '0000013'
                --WHERE member."gwlNo" = '0627828'
)

--select * from zero_teir_id
,pre_tier_id AS
(SELECT
            ulid_id AS id,
            "gwlNo",
            "embossNo",
            email,
            "emailVerifiedAt",
            phone,
            "phoneVerifiedAt",
            "registeredAt",
            "deletedAt",
            CASE
                WHEN trim(UPPER(zero_teir_id."tierId")) = 'VEGA(GP)' THEN 'VEGA'
                WHEN trim(UPPER(zero_teir_id."tierId")) = 'SCARLET(GP)' THEN 'SCARLET'
                WHEN trim(UPPER(zero_teir_id."tierId")) = 'SCARLET(GP-SVP10)' THEN 'SCARLET'
                WHEN trim(UPPER(zero_teir_id."tierId")) = 'SCARLET(GP-L4000)' THEN 'SCARLET'
                WHEN trim(UPPER(zero_teir_id."tierId")) = 'CROWN(GP)' THEN 'CROWN'
                WHEN trim(UPPER(zero_teir_id."tierId")) = 'NAVY' THEN 'NAVY'
                ELSE UPPER(zero_teir_id."tierId")
            END as "tierId", 
            CASE
                WHEN trim(UPPER("minimumTierId")) = 'VEGA(GP)' THEN 'VEGA'
                WHEN trim(UPPER("minimumTierId")) = 'SCARLET(GP)' THEN 'SCARLET'
                WHEN trim(UPPER("minimumTierId")) = 'CROWN(GP)' THEN 'CROWN'
                WHEN trim(UPPER("minimumTierId")) = 'NAVY' THEN 'NAVY'
                --ELSE trim(UPPER("minimumTierId"))
                ELSE COALESCE(trim(UPPER(zero_teir_id."minimumTierId")), 'NAVY')
                END AS "minimumTierId", -- this indicate for member that in Grace Period
            --UPPER("minimumTierId") as "xxxx",
            "tierStartedAt",
            "tierEndedAt",
            "accumulateSpending",
            "lifeTimeSpending",
            "createdAt",
            "updatedAt",
            "isActive",
            reason,
            "picRemark",
            "referralCode",
            "upgradeGroupCode",
            "upgradeReasonCode",
            "registrationChannelCode",
            "registrationLocationCode",
            "accumulateMaintainSpending",
            "shoppingCardId",
            "onepassId",
            "phoneCode",
            remark,
            "isCoBrandNonMember"::bool,
            "emailHash",
            "phoneHash",
            
            CASE
                WHEN trim(UPPER(zero_teir_id."minimumTierInvitedId")) = 'VEGA(GP)' THEN 'VEGA'
                WHEN trim(UPPER(zero_teir_id."minimumTierInvitedId")) = 'SCARLET(GP)' THEN 'SCARLET'
                WHEN trim(UPPER(zero_teir_id."minimumTierInvitedId")) = 'CROWN(GP)' THEN 'CROWN'
                --ELSE trim(UPPER(zero_teir_id."minimumTierInvitedId"))
                ELSE COALESCE(trim(UPPER(zero_teir_id."minimumTierInvitedId")), 'NAVY')
                END AS "minimumTierInvitedId",
            "updatedBy",
            "createdBy"
FROM zero_teir_id
)
            
            
,tier_id AS            
(SELECT 

    pre_tier_id."id",
    pre_tier_id."gwlNo",
    pre_tier_id."embossNo",
    pre_tier_id."email",
    pre_tier_id."emailVerifiedAt",
    pre_tier_id."phone",
    pre_tier_id."phoneVerifiedAt",
    pre_tier_id."registeredAt",
    pre_tier_id."deletedAt",
    tier.id as "tierId",

	-- CASE    WHEN TRIM(UPPER(pre_tier_id."minimumTierId")) = 'NAVY'    THEN '01JDH60A17HZSCM5EBF41DQ5N9'
	--         WHEN TRIM(UPPER(pre_tier_id."minimumTierId")) = 'SCARLET' THEN '01J7AR435H9RA6CKY8Y18SKYC1'
	--         WHEN TRIM(UPPER(pre_tier_id."minimumTierId")) = 'CROWN'   THEN '01J7AQT7546T9BF5X4HWAX205C'
	--         WHEN TRIM(UPPER(pre_tier_id."minimumTierId")) = 'VEGA'    THEN '01J7AQNC895Y186Q7XXEN2YGFH'
	--         WHEN TRIM(UPPER(pre_tier_id."minimumTierId")) = 'CRYSTAL' THEN '01JACNRT6EK46R4KFGWCAQWDAW'
	--         WHEN TRIM(UPPER(pre_tier_id."minimumTierId")) = 'VVIP'    THEN '01J7ASTER48X7XQG14SK3BEFAF'
    --         ELSE NULL
    -- END AS "minimumTierId",
    tier1.id as "minimumTierId",
    pre_tier_id."tierStartedAt",
    pre_tier_id."tierEndedAt",
    pre_tier_id."accumulateSpending",
    pre_tier_id."lifeTimeSpending",
    pre_tier_id."createdAt",
    pre_tier_id."updatedAt",
    pre_tier_id."isActive",
    pre_tier_id."reason",
    pre_tier_id."picRemark",
    pre_tier_id."referralCode",
    pre_tier_id."upgradeGroupCode",
    pre_tier_id."upgradeReasonCode",
    pre_tier_id."registrationChannelCode",
    pre_tier_id."registrationLocationCode",
    pre_tier_id."accumulateMaintainSpending",
    pre_tier_id."shoppingCardId",
    pre_tier_id."onepassId",
    pre_tier_id."phoneCode",
    pre_tier_id.remark,
    pre_tier_id."isCoBrandNonMember"::bool,
    pre_tier_id."emailHash",
    pre_tier_id."phoneHash",
    tier2.id AS "minimumTierInvitedId",
    pre_tier_id."updatedBy",
    pre_tier_id."createdBy"

FROM pre_tier_id
LEFT JOIN loyalty_service."Tier" as tier ON pre_tier_id."tierId" = tier.code
LEFT JOIN loyalty_service."Tier" as tier1 ON pre_tier_id."minimumTierId" = tier1.code
LEFT JOIN loyalty_service."Tier" as tier2 ON pre_tier_id."minimumTierId" = tier2.code
)

SELECT 
    "id",
    "gwlNo",
    "embossNo",
    "email",
    "emailVerifiedAt",
    "phone",
    "phoneVerifiedAt",
    "registeredAt",
    "deletedAt",
    "tierId",
    "minimumTierId",
    "tierStartedAt",
    "tierEndedAt",
    "accumulateSpending",
    "lifeTimeSpending",
    "createdAt",
    "updatedAt",
    "isActive",
    "reason",
    "picRemark",
    "referralCode",
    "upgradeGroupCode",
    "upgradeReasonCode",
    "registrationChannelCode",
    "registrationLocationCode",
    "accumulateMaintainSpending",
    "shoppingCardId",
    "onepassId",
    "phoneCode",
    "isCoBrandNonMember"::bool,
    "emailHash",
    "phoneHash",
    "minimumTierInvitedId",
    "updatedBy",
    "remark",
    "createdBy"
FROM tier_id

ON CONFLICT (id) DO UPDATE SET
"gwlNo" = EXCLUDED."gwlNo",
"embossNo" = EXCLUDED."embossNo",
email = EXCLUDED.email,
"emailVerifiedAt" = EXCLUDED."emailVerifiedAt",
phone = EXCLUDED.phone,
"phoneVerifiedAt" = EXCLUDED."phoneVerifiedAt",
"registeredAt" = EXCLUDED."registeredAt",
"deletedAt" = EXCLUDED."deletedAt",
"tierId" = EXCLUDED."tierId",
"minimumTierId" = EXCLUDED."minimumTierId",
"tierStartedAt" = EXCLUDED."tierStartedAt",
"tierEndedAt" = EXCLUDED."tierEndedAt",
"accumulateSpending" = EXCLUDED."accumulateSpending",
"lifeTimeSpending" = EXCLUDED."lifeTimeSpending",
"createdAt" = EXCLUDED."createdAt",
"updatedAt" = EXCLUDED."updatedAt",
"isActive" = EXCLUDED."isActive",
reason = EXCLUDED.reason,
"picRemark" = EXCLUDED."picRemark",
"referralCode" = EXCLUDED."referralCode",
"upgradeGroupCode" = EXCLUDED."upgradeGroupCode",
"upgradeReasonCode" = EXCLUDED."upgradeReasonCode",
"registrationChannelCode" = EXCLUDED."registrationChannelCode",
"registrationLocationCode" = EXCLUDED."registrationLocationCode",
"accumulateMaintainSpending" = EXCLUDED."accumulateMaintainSpending",
"shoppingCardId" = EXCLUDED."shoppingCardId",
"onepassId" = EXCLUDED."onepassId",
"phoneCode" = EXCLUDED."phoneCode",
"isCoBrandNonMember" = EXCLUDED."isCoBrandNonMember",
"emailHash" = EXCLUDED."emailHash",
"phoneHash" = EXCLUDED."phoneHash",
"minimumTierInvitedId" = EXCLUDED."minimumTierInvitedId",
"updatedBy" = EXCLUDED."updatedBy",
remark = EXCLUDED.remark,
"createdBy" = EXCLUDED."createdBy";
