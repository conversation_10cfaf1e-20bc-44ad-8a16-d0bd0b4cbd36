INSERT into loyalty_service."MemberSubSegment"
SELECT * FROM staging_loyalty_service."MemberSubSegment"
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "subSegmentCode" = EXCLUDED."subSegmentCode",
    "createdAt" = EXCLUDED."createdAt";

-- INSERT into staging_loyalty_service."MemberSubSegment"
-- SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."MemberSubSegment"')  AS 
-- t1(id text,
--     "memberId" text,
--     "subSegmentCode" text,
--     "createdAt" timestamp(3))
-- ON CONFLICT (id) DO UPDATE SET
--     "memberId" = EXCLUDED."memberId",
--     "subSegmentCode" = EXCLUDED."subSegmentCode",
--     "createdAt" = EXCLUDED."createdAt";

