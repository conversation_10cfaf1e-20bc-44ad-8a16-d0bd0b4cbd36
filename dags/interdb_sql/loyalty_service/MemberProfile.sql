SELECT pid, age(now(), query_start) AS duration, query
FROM pg_stat_activity
WHERE state = 'active'
ORDER BY duration DESC;

SELECT pg_terminate_backend(673);



SELECT column_name 
FROM information_schema.columns 
WHERE table_schema = 'loyalty_service' 
AND table_name = 'Member';


SELECT column_name 
FROM information_schema.columns 
WHERE table_schema = 'staging_loyalty_service' 
AND table_name = 'Member';





INSERT into loyalty_service."MemberProfile" (
    "id",
    "memberId",  -- ULID already
    "firstName",
    "firstNameTh",
    "middleName",
    "middleNameTh",
    "lastName",
    "lastNameTh",
    "cid",
    "passportNo",
    "passportExpiryDate",
    "dateOfBirth",
    "gender",
    "addressLine",
    "subDistrict",
    "district",
    "province",
    "city",
    "postalCode",
    "createdAt",
    "updatedAt",
    "occupation",
    "title",
    "countryCode",
    "nationalityCode",
    "firstNameHash",
    "firstNameThHash",
    "middleNameHash",
    "middleNameThHash",
    "lastNameHash",
    "lastNameThHash",
    "cidHash",
    "dateOfBirthHash",
    "genderHash",
    "passportNoHash",
    "addressLineHash",
    "updatedBy",
    "createdBy"
)
WITH member_profile AS
(SELECT 
        mp.id
        ,sm.id as "memberId" -- ULID already
        ,mp."firstName"
        ,mp."firstNameTh"
        ,mp."middleName"
        ,mp."middleNameTh"
        ,mp."lastName"
        ,mp."lastNameTh"
        ,mp.cid
        ,mp."passportNo"
        ,mp."passportExpiryDate"::TIMESTAMPTZ
        ,mp."dateOfBirth"
        ,mp."gender"
        ,mp."addressLine"
        ,mp."subDistrict"
        ,mp."district"
        ,mp."province"
        ,mp."city"
        ,mp."postalCode"
        ,mp."createdAt"::TIMESTAMPTZ
        ,mp."updatedAt"::TIMESTAMPTZ
        ,mp."occupation"
        ,mp."title"

        ,CASE 
                WHEN (mp."subDistrict" IS NOT NULL OR
                        mp.district IS NOT NULL OR
                        mp.province IS NOT NULL)
                THEN 'THA'
                ELSE NULL
            END AS "countryCode"

        ,mp."nationalityCode"
        ,mp."firstNameHash"
        ,mp."firstNameThHash"
        ,mp."middleNameHash"
        ,mp."middleNameThHash"
        ,mp."lastNameHash"
        ,mp."lastNameThHash"
        ,mp."cidHash"
        ,mp."dateOfBirthHash"
        ,mp."genderHash"
        ,mp."passportNoHash"
        ,mp."addressLineHash"
        ,jsonb_build_object(
                        'id', null, 
                        'name', 'SYSTEM', 
                        'email', null
                    ) as "updatedBy"
        ,jsonb_build_object(
                        'id', null, 
                        'name', 'SYSTEM', 
                        'email', null
                    ) as "createdBy"

-- FROM staging_loyalty_service."MemberProfile" AS mp
-- INNER JOIN loyalty_service."Member" AS member ON member.id = mp."memberId"
FROM loyalty_service."Member" AS sm
INNER JOIN staging_loyalty_service."MemberProfile" AS mp ON sm."gwlNo" = mp."memberId"
)

-- FROM staging_loyalty_service."MemberProfile" AS mp
-- RIGHT JOIN loyalty_service."Member" member ON mp."memberId" = member.id

SELECT
    "id",
    "memberId",  -- ULID already
    "firstName",
    "firstNameTh",
    "middleName",
    "middleNameTh",
    "lastName",
    "lastNameTh",
    "cid",
    "passportNo",
    "passportExpiryDate",
    "dateOfBirth",
    "gender",
    "addressLine",
    "subDistrict",
    "district",
    "province",
    "city",
    "postalCode",
    "createdAt",
    "updatedAt",
    "occupation",
    "title",
    "countryCode",
    "nationalityCode",
    "firstNameHash",
    "firstNameThHash",
    "middleNameHash",
    "middleNameThHash",
    "lastNameHash",
    "lastNameThHash",
    "cidHash",
    "dateOfBirthHash",
    "genderHash",
    "passportNoHash",
    "addressLineHash",
    "updatedBy",
    "createdBy"

FROM member_profile

ON CONFLICT ("memberId") DO UPDATE SET
    id = EXCLUDED.id,
    "firstName" = EXCLUDED."firstName",
    "firstNameTh" = EXCLUDED."firstNameTh",
    "middleName" = EXCLUDED."middleName",
    "middleNameTh" = EXCLUDED."middleNameTh",
    "lastName" = EXCLUDED."lastName",
    "lastNameTh" = EXCLUDED."lastNameTh",
    cid = EXCLUDED.cid,
    "passportNo" = EXCLUDED."passportNo",
    "passportExpiryDate" = EXCLUDED."passportExpiryDate",
    "dateOfBirth" = EXCLUDED."dateOfBirth",
    gender = EXCLUDED.gender,
    "addressLine" = EXCLUDED."addressLine",
    "subDistrict" = EXCLUDED."subDistrict",
    district = EXCLUDED.district,
    province = EXCLUDED.province,
    city = EXCLUDED.city,
    "postalCode" = EXCLUDED."postalCode",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    occupation = EXCLUDED.occupation,
    title = EXCLUDED.title,
    "countryCode" = EXCLUDED."countryCode",
    "nationalityCode" = EXCLUDED."nationalityCode",
    "firstNameHash" = EXCLUDED."firstNameHash",
    "firstNameThHash" = EXCLUDED."firstNameThHash",
    "middleNameHash" = EXCLUDED."middleNameHash",
    "middleNameThHash" = EXCLUDED."middleNameThHash",
    "lastNameHash" = EXCLUDED."lastNameHash",
    "lastNameThHash" = EXCLUDED."lastNameThHash",
    "cidHash" = EXCLUDED."cidHash",
    "dateOfBirthHash" = EXCLUDED."dateOfBirthHash",
    "genderHash" = EXCLUDED."genderHash",
    "passportNoHash" = EXCLUDED."passportNoHash",
    "addressLineHash" = EXCLUDED."addressLineHash",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdBy" = EXCLUDED."createdBy";
