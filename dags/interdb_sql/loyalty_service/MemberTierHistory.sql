INSERT into loyalty_service."MemberTierHistory" 
SELECT * FROM staging_loyalty_service."MemberTierHistory"
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "fromTierId" = EXCLUDED."fromTierId",
    "toTierId" = EXCLUDED."toTierId",
    "tierStartedAt" = EXCLUDED."tierStartedAt",
    "tierEndedAt" = EXCLUDED."tierEndedAt",
    "accumulateSpending" = EXCLUDED."accumulateSpending",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";


-- INSERT into staging_loyalty_service."MemberTierHistory" 
-- SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."MemberTierHistory"') AS 
-- t1(id text,
--     "memberId" text,
--     "fromTierId" text,
--     "toTierId" text,
--     "tierStartedAt" timestamp(3),
--     "tierEndedAt" timestamp(3),
--     "accumulateSpending" numeric(16, 2),
--     "createdAt" timestamp(3),
--     "updatedAt" timestamp(3))
-- ON CONFLICT (id) DO UPDATE SET
--     "memberId" = EXCLUDED."memberId",
--     "fromTierId" = EXCLUDED."fromTierId",
--     "toTierId" = EXCLUDED."toTierId",
--     "tierStartedAt" = EXCLUDED."tierStartedAt",
--     "tierEndedAt" = EXCLUDED."tierEndedAt",
--     "accumulateSpending" = EXCLUDED."accumulateSpending",
--     "createdAt" = EXCLUDED."createdAt",
--     "updatedAt" = EXCLUDED."updatedAt";
