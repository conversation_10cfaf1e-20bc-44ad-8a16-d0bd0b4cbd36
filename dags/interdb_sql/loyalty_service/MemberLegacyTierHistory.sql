INSERT into loyalty_service."MemberLegacyTierHistory" (
    "id",
    "memberId",
    "cardTypeCode",
    "description",
    "embossNo",
    "tierStartedAt",
    "tierEndedAt",
    "cardStatus",
    "cardReason",
    "createdAt",
    "updatedAt"
    -- source_runno in staging
    -- source_cardtypecode in staging
)
WITH memberlegacytierhistory AS
(
SELECT
    mlth."id",
    sm.id AS "memberId", -- made to ULID
    mlth."cardTypeCode",
    mlth."description",
    LEFT(mlth."embossNo", LENGTH(mlth."embossNo") - 8) ||'xxxx' || SUBSTRING(mlth."embossNo", LENGTH(mlth."embossNo") - 3) AS "embossNo",
    mlth."tierStartedAt",
    mlth."tierEndedAt",
    mlth."cardStatus",
    mlth."cardReason",
    mlth."createdAt",
    mlth."updatedAt"
FROM staging_loyalty_service."MemberLegacyTierHistory" AS mlth
INNER JOIN loyalty_service."Member" AS sm
ON mlth."memberId" = sm."gwlNo"
--LIMIT 1000
)
SELECT
    "id",
    "memberId",
    "cardTypeCode",
    "description",
    "embossNo",
    "tierStartedAt",
    "tierEndedAt",
    "cardStatus",
    "cardReason",
    "createdAt",
    "updatedAt"
FROM memberlegacytierhistory

ON CONFLICT ("id") DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "cardTypeCode" = EXCLUDED."cardTypeCode",
    description = EXCLUDED.description,
    "embossNo" = EXCLUDED."embossNo",
    "tierStartedAt" = EXCLUDED."tierStartedAt",
    "tierEndedAt" = EXCLUDED."tierEndedAt",
    "cardStatus" = EXCLUDED."cardStatus",
    "cardReason" = EXCLUDED."cardReason",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";
