INSERT into loyalty_service."StaffCompany" 
SELECT * FROM staging_loyalty_service."StaffCompany"
ON CONFLICT (code) DO UPDATE SET
    "name" = EXCLUDED."name",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";



-- INSERT into staging_loyalty_service."StaffCompany" 
-- SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."StaffCompany"')  AS 
-- t1(code text,
--     "name" text,
--     "createdAt" timestamp(3),
--     "updatedAt" timestamp(3))
-- ON CONFLICT (code) DO UPDATE SET
--     "name" = EXCLUDED."name",
--     "createdAt" = EXCLUDED."createdAt",
--     "updatedAt" = EXCLUDED."updatedAt";
