INSERT into loyalty_service."Bank" 
SELECT * FROM staging_loyalty_service."Bank"
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";



-- INSERT into staging_loyalty_service."Bank" 
-- SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."Bank"') AS 
-- t1(id text,
--     code text,
--     name text,
--     "createdAt" timestamp(3),
--     "updatedAt" timestamp(3))
-- ON CONFLICT (id) DO UPDATE SET
--     code = EXCLUDED.code,
--     name = EXCLUDED.name,
--     "createdAt" = EXCLUDED."createdAt",
--     "updatedAt" = EXCLUDED."updatedAt";

