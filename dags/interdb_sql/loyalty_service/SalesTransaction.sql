INSERT INTO loyalty_service."SalesTransaction" (
	"id",
	"memberId",
	"completedAt",
	"externalId",
	"createdAt",
	"updatedAt",
	"netTotalAmount",
	"totalAccumSpendableAmount",
	"branchCode",
	"brandCode",
	"partnerCode"
)
WITH reactivate_list AS (
	select * FROM public.fulldump_smc_expired_list
	-- WHERE ("updatedAt" BETWEEN '2025-06-12 00:00:20.368' AND '2025-06-13 00:00:20.368') -- hard code for one time use migration later change to WHERE (rl."updatedAt" BETWEEN 'start_timestamps' AND 'end_timestamps')
)

SELECT 
	main."id",
	main."memberId",
	main."completedAt",
	main."externalId",
	main."createdAt",
	main."updatedAt",
	main."netTotalAmount",
	CASE WHEN m.id IS NOT NULL THEN 0
		 ELSE main."totalAccumSpendableAmount"
	END AS "totalAccumSpendableAmount",
	main."branchCode",
	main."brandCode",
	main."partnerCode"
FROM 
(
	SELECT
		st."id",
		sm.id AS "memberId",
		st."createdAt" as "completedAt",
		st."externalId",
		st."createdAt",
		st."updatedAt",
		st."netTotalAmount",
		CASE WHEN st."memberId" = rl."memberId" AND (st."createdAt" < '2025-06-03 17:00:00 +0000') THEN 0.00
			ELSE st."totalAccumSpendableAmount"
			END AS "totalAccumSpendableAmount",
		CASE WHEN st."branchCode" IS NULL OR st."branchCode" = '' THEN 'KPC_RANGNAM'
			 WHEN st."branchCode" = '9999' AND st."brandCode" = '58' THEN 'KPD_DMK_AIRPORT'
			 WHEN st."branchCode" = '9999' AND st."brandCode" = '01' THEN 'KPD_SVB_AIRPORT'
			 ELSE site."gwl_branch_code"
		END AS "branchCode",
		CASE WHEN st."branchCode" IS NULL OR st."branchCode" = '' THEN 'KPC_OFFLINE'
			 WHEN st."branchCode" = '9999' AND st."brandCode" = '58' THEN 'KPD_OFFLINE'
			 WHEN st."branchCode" = '9999' AND st."brandCode" = '01' THEN 'KPD_OFFLINE'
			 ELSE site."brand_code"
		END AS "brandCode",
		CASE WHEN st."branchCode" IS NULL OR st."branchCode" = '' THEN 'KPC'
			 WHEN st."branchCode" = '9999' AND st."brandCode" = '58' THEN 'KPD'
			 WHEN st."branchCode" = '9999' AND st."brandCode" = '01' THEN 'KPD'
			 ELSE site."partner_code"
		END AS "partnerCode"
	FROM staging_loyalty_service."SalesTransaction" st
	LEFT JOIN public.site_branch_mapping site 	ON st."branchCode" = site.site_code 
	INNER JOIN loyalty_service."Member" AS sm 	ON st."memberId"  = sm."gwlNo"  
	LEFT JOIN reactivate_list rl on st."memberId" = rl."memberId"
) main
LEFT JOIN ( SELECT id FROM loyalty_service."Member" WHERE "isActive" = False ) m
ON main."memberId" = m.id
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "completedAt" = EXCLUDED."completedAt",
    "externalId" = EXCLUDED."externalId",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "netTotalAmount" = EXCLUDED."netTotalAmount",
    "totalAccumSpendableAmount" = EXCLUDED."totalAccumSpendableAmount",
    "branchCode" = EXCLUDED."branchCode",
    "brandCode" = EXCLUDED."brandCode",
    "partnerCode" = EXCLUDED."partnerCode";