INSERT INTO loyalty_service."MemberCoBrandCard" ( 
    "id",
    "memberId",
    "coBrandId",
    "cardNo",
    "memberCoBrandCardImportId",
    "status",
    "createdAt",
    "createdBy",
    "updatedAt",
    "updatedBy",
    "cardHolderName",
    "cardHolderNameHash",
    "remark",
    "cardReason"
)
WITH post_card_hash AS
(
SELECT
	pre_card_hash.id
    ,pre_card_hash."memberId"  -- made to ULID
    ,pre_card_hash."coBrandId"
    -- ,pre_card_hash."cardNo"
    ,case when length(pre_card_hash."cardNo") = 16 
		 then concat(substring(pre_card_hash."cardNo",1,6), 'xxxxxx', substring(pre_card_hash."cardNo",13,4))
		 else pre_card_hash."cardNo"
		 end as "cardNo"
    ,pre_card_hash."memberCoBrandCardImportId"
    ,pre_card_hash."status"
    ,pre_card_hash."createdAt"
    ,pre_card_hash."createdBy"
    ,pre_card_hash."updatedAt"
    ,pre_card_hash."updatedBy"
    
    ,pgp_sym_encrypt(
		concat(
			pgp_sym_decrypt(
				mp."firstName"::bytea, 
				'<encrypted_key>'
			),
			' ',
			pgp_sym_decrypt(
				mp."lastName"::bytea, 
				'<encrypted_key>'
			)
		) 
	, '<encrypted_key>') as "cardHolderName"
	
	,encode(
	digest(
		UPPER(
			concat(
				pgp_sym_decrypt(
					mp."firstName"::bytea, 
					'<encrypted_key>'
				),
				' ',
				pgp_sym_decrypt(
					mp."lastName"::bytea, 
					'<encrypted_key>'
				)
			)
		)
	, 'sha256')
	, 'hex') as "cardHolderNameHash"
    
    
    ,pre_card_hash."remark"
    ,pre_card_hash."cardReason"
FROM
(
SELECT
    mcbc.id
    ,sm.id AS "memberId"  -- made to ULID
    ,cb.id AS "coBrandId"
    ,mcbc."cardNo"
    ,mcbc."memberCoBrandCardImportId"
    ,mcbc."status"
    ,mcbc."createdAt"
    ,mcbc."createdBy"
    ,mcbc."updatedAt"
    ,mcbc."updatedBy"
    ,mcbc."cardHolderName"
	,mcbc."cardHolderNameHash"
    ,mcbc."remark"
    ,mcbc."cardReason"
FROM staging_loyalty_service."MemberCoBrandCard" AS mcbc

INNER JOIN loyalty_service."Member" AS sm ON mcbc."memberId" = sm."gwlNo"
INNER JOIN loyalty_service."CoBrand" AS cb ON substring(mcbc."cardNo",1,6) = cb."cardBin"
-- INNER JOIN loyalty_service."CoBrand" AS cb ON substring(mcbc."cardNo",1,6) = cb."cardBin" --  commented out this line prepared for changeing from using 8 digits for cardBin, to 6 later.
--LIMIT 10
) AS pre_card_hash 
Left JOIN loyalty_service."MemberProfile" AS mp ON pre_card_hash."memberId" = mp."memberId"
)
SELECT
    "id",
    "memberId",
    "coBrandId",
    "cardNo",
    "memberCoBrandCardImportId",
    "status",
    "createdAt",
    "createdBy",
    "updatedAt",
    "updatedBy",
    "cardHolderName",
    "cardHolderNameHash",
    "remark",
    "cardReason"
FROM post_card_hash

ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "coBrandId" = EXCLUDED."coBrandId",
    "cardNo" = EXCLUDED."cardNo",
    "memberCoBrandCardImportId" = EXCLUDED."memberCoBrandCardImportId",
    "status" = EXCLUDED."status",
    "createdAt" = EXCLUDED."createdAt",
    "createdBy" = EXCLUDED."createdBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "cardHolderName" = EXCLUDED."cardHolderName",
    "cardHolderNameHash" = EXCLUDED."cardHolderNameHash",
    "remark" = EXCLUDED."remark",
    "cardReason" = EXCLUDED."cardReason";