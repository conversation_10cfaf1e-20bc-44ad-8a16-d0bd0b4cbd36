INSERT into loyalty_service."CoBrandCardLog" 
SELECT * FROM staging_loyalty_service."CoBrandCardLog"
ON CONFLICT (id) DO UPDATE SET
    "memberCoBrandCardId" = EXCLUDED."memberCoBrandCardId",
    type = EXCLUDED.type,
    description = EXCLUDED.description,
    "oldData" = EXCLUDED."oldData",
    "newData" = EXCLUDED."newData",
    "createdBy" = EXCLUDED."createdBy",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";




-- INSERT into staging_loyalty_service."CoBrandCardLog" 
-- SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."CoBrandCardLog"')  AS 
-- t1(id text,
--     "memberCoBrandCardId" text,
--     type text,
--     description text[],
--     "oldData" jsonb,
--     "newData" jsonb,
--     "createdBy" jsonb,
--     "createdAt" timestamp(3),
--     "updatedAt" timestamp(3))
-- ON CONFLICT (id) DO UPDATE SET
--     "memberCoBrandCardId" = EXCLUDED."memberCoBrandCardId",
--     type = EXCLUDED.type,
--     description = EXCLUDED.description,
--     "oldData" = EXCLUDED."oldData",
--     "newData" = EXCLUDED."newData",
--     "createdBy" = EXCLUDED."createdBy",
--     "createdAt" = EXCLUDED."createdAt",
--     "updatedAt" = EXCLUDED."updatedAt";