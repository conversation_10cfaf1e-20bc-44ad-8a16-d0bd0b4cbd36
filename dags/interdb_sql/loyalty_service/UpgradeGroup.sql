INSERT into loyalty_service."UpgradeGroup" 
SELECT * FROM staging_loyalty_service."UpgradeGroup"
ON CONFLICT (code) DO UPDATE SET
    "name" = EXCLUDED."name",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";



-- INSERT into staging_loyalty_service."UpgradeGroup" 
-- SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."UpgradeGroup"')  AS 
-- t1(code text,
--     "name" text,
--     "createdAt" timestamp(3),
--     "updatedAt" timestamp(3))
-- ON CONFLICT (code) DO UPDATE SET
--     "name" = EXCLUDED."name",
--     "createdAt" = EXCLUDED."createdAt",
--     "updatedAt" = EXCLUDED."updatedAt";
