INSERT into loyalty_service."MemberLegacyCoBrandHistory" (
    "id",
    "memberId",
    "cardTypeCode",
    "description",
    "embossNo",
    "startedAt",
    "endedAt",
    "cardStatus",
    "cardReason",
    "createdAt",
    "updatedAt"
)
WITH memberlegacycobrandhistory AS
(SELECT
    mlcbh."id",
    sm.id AS "memberId", -- made to ULID 
    mlcbh."cardTypeCode",
    mlcbh."description",
    LEFT(mlcbh."embossNo", LENGTH(mlcbh."embossNo") - 8) || 'xxxx' || SUBSTRING(mlcbh."embossNo", LENGTH(mlcbh."embossNo") - 3) AS "embossNo",
    mlcbh."startedAt" as "startedAt",
    mlcbh."endedAt" as "endedAt",
    mlcbh."cardStatus",
    mlcbh."cardReason",
    mlcbh."createdAt",
    mlcbh."updatedAt"
FROM staging_loyalty_service."MemberLegacyCoBrandHistory" AS mlcbh
INNER JOIN loyalty_service."Member" AS sm
ON mlcbh."memberId" = sm."gwlNo"
--LIMIT 500
)
SELECT 
    "id",
    "memberId",
    "cardTypeCode",
    "description",
    "embossNo",
    "startedAt",
    "endedAt",
    "cardStatus",
    "cardReason",
    "createdAt",
    "updatedAt"
FROM memberlegacycobrandhistory


ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "cardTypeCode" = EXCLUDED."cardTypeCode",
    "description" = EXCLUDED."description", 
    "embossNo" = EXCLUDED."embossNo",
    "startedAt" = EXCLUDED."startedAt",
    "endedAt" = EXCLUDED."endedAt",
    "cardStatus" = EXCLUDED."cardStatus",
    "cardReason" = EXCLUDED."cardReason",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";
