INSERT into loyalty_service."TierUpdateRequest" 
SELECT * FROM staging_loyalty_service."TierUpdateRequest" 
ON CONFLICT (id) DO UPDATE SET
    "updatedBy" = EXCLUDED."updatedBy",
    "oldData" = EXCLUDED."oldData",
    "newData" = EXCLUDED."newData",
    "effectiveDate" = EXCLUDED."effectiveDate",
    "tierId" = EXCLUDED."tierId",
    detail = EXCLUDED.detail,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "privileges" = EXCLUDED."privileges";




-- INSERT into staging_loyalty_service."TierUpdateRequest" 
-- SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."TierUpdateRequest"')  AS 
-- t1(id text,
--     "updatedBy" jsonb,
--     "oldData" jsonb,
--     "newData" jsonb,
--     "effectiveDate" timestamp(3),
--     "tierId" text,
--     detail _text,
--     "createdAt" timestamp(3),
--     "updatedAt" timestamp(3),
--     "privileges" _text)
-- ON CONFLICT (id) DO UPDATE SET
--     "updatedBy" = EXCLUDED."updatedBy",
--     "oldData" = EXCLUDED."oldData",
--     "newData" = EXCLUDED."newData",
--     "effectiveDate" = EXCLUDED."effectiveDate",
--     "tierId" = EXCLUDED."tierId",
--     detail = EXCLUDED.detail,
--     "createdAt" = EXCLUDED."createdAt",
--     "updatedAt" = EXCLUDED."updatedAt",
--     "privileges" = EXCLUDED."privileges";

