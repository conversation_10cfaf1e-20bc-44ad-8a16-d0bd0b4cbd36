INSERT into loyalty_service."PrivilegeTier" 
SELECT * FROM staging_loyalty_service."PrivilegeTier" ON CONFLICT (id) DO UPDATE SET
    "tierId" = EXCLUDED."tierId",
    "privilegeId" = EXCLUDED."privilegeId",
    "createdAt" = EXCLUDED."createdAt";


-- INSERT into staging_loyalty_service."PrivilegeTier" 
-- SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."PrivilegeTier"')  AS 
-- t1(id text,
--     "tierId" text,
--     "privilegeId" text,
--     "createdAt" timestamp(3))
-- ON CONFLICT (id) DO UPDATE SET
--     "tierId" = EXCLUDED."tierId",
--     "privilegeId" = EXCLUDED."privilegeId",
--     "createdAt" = EXCLUDED."createdAt";
