INSERT into loyalty_service."MemberLog"
SELECT * FROM staging_loyalty_service."MemberLog"
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "type" = EXCLUDED."type",
    "name" = EXCLUDED."name",
    description = EXCLUDED.description,
    "oldData" = EXCLUDED."oldData",
    "newData" = EXCLUDED."newData",
    "createdBy" = EXCLUDED."createdBy",
    "createdAt" = EXCLUDED."createdAt";


-- INSERT into staging_loyalty_service."MemberLog"
-- SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."MemberLog"')  AS 
-- t1(id text,
--     "memberId" text,
--     "type" text,
--     "name" text,
--     description _text,
--     "oldData" jsonb,
--     "newData" jsonb,
--     "createdBy" jsonb,
--     "createdAt" timestamp(3))
-- ON CONFLICT (id) DO UPDATE SET
--     "memberId" = EXCLUDED."memberId",
--     "type" = EXCLUDED."type",
--     "name" = EXCLUDED."name",
--     description = EXCLUDED.description,
--     "oldData" = EXCLUDED."oldData",
--     "newData" = EXCLUDED."newData",
--     "createdBy" = EXCLUDED."createdBy",
--     "createdAt" = EXCLUDED."createdAt";