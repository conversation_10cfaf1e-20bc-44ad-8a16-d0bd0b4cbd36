INSERT into loyalty_service."RegisterLocation" 
SELECT * FROM staging_loyalty_service."RegisterLocation"
ON CONFLICT (code) DO UPDATE SET
    "name" = EXCLUDED."name",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";



-- INSERT into staging_loyalty_service."RegisterLocation" 
-- SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."RegisterLocation"')  AS 
-- t1(code text,
--     "name" text,
--     "createdAt" timestamp(3),
--     "updatedAt" timestamp(3))
-- ON CONFLICT (code) DO UPDATE SET
--     "name" = EXCLUDED."name",
--     "createdAt" = EXCLUDED."createdAt",
--     "updatedAt" = EXCLUDED."updatedAt";
