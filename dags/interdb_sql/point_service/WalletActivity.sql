INSERT into point_service."WalletActivity" (
    id,
    "memberId",
    "walletCode",
    "type",
    "refType",
    "refId",
    "externalId",
    amount,
    "partnerCode",
    "brandCode",
    "branchCode",
    detail,
    "createdAt",
    "updatedAt",
    "documentDate",
    "remark"
)
WITH walletactivity AS
(SELECT
    main.id,
    main."memberId",
    main."walletCode",
    main."type",
    main."refType",
    main."refId",
    main."externalId",
    main.amount,
    main."partnerCode",
    main."brandCode",
    main."branchCode",
    main.detail,
    main."createdAt",
    main."updatedAt",
    main."documentDate",
    main."remark"
FROM (
    SELECT
        ulid.ulid_id as id,
        sm.id as "memberId",
        wa."walletCode",
        wa."type",
        wa."refType",
        wa."refId",
        wa."externalId",
        
        CASE WHEN wa."walletCode" = 'CARAT_WALLET' then wa.amount*4
            ELSE wa.amount
        END AS amount,
        
        CASE WHEN wa."branchCode" IS NULL OR wa."branchCode" = '' THEN 'KPC'
			 WHEN wa."branchCode" = '9999' AND wa."brandCode" = '58' THEN 'KPD'
			 WHEN wa."branchCode" = '9999' AND wa."brandCode" = '01' THEN 'KPD'
			 ELSE site."partner_code"
        END AS "partnerCode",
        CASE WHEN wa."branchCode" IS NULL OR wa."branchCode" = '' THEN 'KPC_OFFLINE'
			 WHEN wa."branchCode" = '9999' AND wa."brandCode" = '58' THEN 'KPD_OFFLINE'
			 WHEN wa."branchCode" = '9999' AND wa."brandCode" = '01' THEN 'KPD_OFFLINE'
			 ELSE site."brand_code"
		END AS "brandCode",
        CASE WHEN wa."branchCode" IS NULL OR wa."branchCode" = '' THEN 'KPC_RANGNAM'
             WHEN wa."branchCode" = '9999' AND wa."brandCode" = '58' THEN 'KPD_DMK_AIRPORT'
             WHEN wa."branchCode" = '9999' AND wa."brandCode" = '01' THEN 'KPD_SVB_AIRPORT'
             ELSE site."gwl_branch_code"
        END AS "branchCode",
        detail,
        wa."createdAt",
        wa."updatedAt",
        wa."documentDate",
        wa."remark"
    FROM staging_point_service."WalletActivity" wa
    LEFT JOIN public.site_branch_mapping site
    ON wa."branchCode" = site.site_code
    LEFT JOIN staging_point_service."ulid_WalletActivity" ulid
    ON wa.id = ulid.id
    INNER JOIN loyalty_service."Member" AS sm
    ON wa."memberId" = sm."gwlNo"
) main
)
SELECT
    id,
    "memberId",
    "walletCode",
    "type",
    "refType",
    "refId",
    "externalId",
    amount,
    "partnerCode",
    "brandCode",
    "branchCode",
    detail,
    "createdAt",
    "updatedAt",
    "documentDate",
    "remark"
FROM walletactivity

ON CONFLICT (id) DO UPDATE SET
"memberId" = EXCLUDED."memberId",
"walletCode" = EXCLUDED."walletCode",
"type" = EXCLUDED."type",
"refType" = EXCLUDED."refType",
"refId" = EXCLUDED."refId",
"externalId" = EXCLUDED."externalId",
amount = EXCLUDED.amount,
"partnerCode" = EXCLUDED."partnerCode",
"brandCode" = EXCLUDED."brandCode",
"branchCode" = EXCLUDED."branchCode",
detail = EXCLUDED.detail,
"createdAt" = EXCLUDED."createdAt",
"updatedAt" = EXCLUDED."updatedAt",
"documentDate" = EXCLUDED."documentDate",
"remark" = EXCLUDED."remark";