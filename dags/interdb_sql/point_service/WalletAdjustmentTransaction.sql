INSERT into point_service."WalletAdjustmentTransaction" (
    "id",
    "memberId",
    "walletCode",
    "reasonCode",
    "amount",
    "type",
    "remark",
    "createdBy",
    "createdAt"
)
WITH walletadjustmenttransaction AS
(SELECT
    ulid.ulid_id as id,
    sm.id as "memberId",
    wat."walletCode",
    wat."reasonCode",
    wat."amount",
    wat."type",
    wat."remark",
    wat."createdBy",
    wat."createdAt"
FROM staging_point_service."WalletAdjustmentTransaction" wat
LEFT JOIN staging_point_service."ulid_WalletAdjustmentTransaction" ulid
ON wat.id = ulid.id
INNER JOIN loyalty_service."Member" AS sm
ON wat."memberId" = sm."gwlNo"
)

SELECT
    id,
    "memberId",
    "walletCode",
    "reasonCode",
    "amount",
    "type",
    "remark",
    "createdBy",
    "createdAt"
FROM walletadjustmenttransaction

ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "walletCode" = EXCLUDED."walletCode",
    "reasonCode" = EXCLUDED."reasonCode",
    amount = EXCLUDED.amount,
    "type" = EXCLUDED."type",
    remark = EXCLUDED.remark,
    "createdBy" = EXCLUDED."createdBy",
    "createdAt" = EXCLUDED."createdAt",
    "refNo" = EXCLUDED."refNo",
    "expiredAt" = EXCLUDED."expiredAt",
    "businessAreaBranchCode" = EXCLUDED."businessAreaBranchCode";
