INSERT INTO point_service."WalletTransaction" (
    "id",
    "memberId",
    "walletActivityId",
    "balanceId",
    "type",
    "walletCode",
    "amount",
    "expiredAt",
    "createdAt"
)
WITH combined_wallets AS (
  SELECT * FROM staging_point_service."WalletBalance"
  WHERE "walletCode" = 'CASH_WALLET'
    AND "expiredAt" >= DATE '2025-07-01'
    AND "expiredAt" < DATE '2099-01-01'
    AND "amount" <> 0

  UNION ALL

  SELECT * FROM staging_point_service."PreWalletBalanceTemp"
  WHERE "walletCode" = 'CASH_WALLET'
    AND "expiredAt" >= DATE '2025-07-01'
    AND "expiredAt" < DATE '2099-01-01'
    AND "amount" <> 0
),

ranked_wallets AS (
  SELECT
    wb.*,
    ROW_NUMBER() OVER (
      PARTITION BY wb."memberId"
      ORDER BY wb."expiredAt" DESC
    ) AS rn
  FROM combined_wallets wb
),

replace_exp_date_group AS (
  SELECT 
    id,
    "memberId",
    "walletCode",
    amount,
    "expiredAt"
    -- You can re-enable this if you want to use it:
    -- , DATE '2099-12-30' - (rn - 1) * INTERVAL '1 day' AS "new_expiredAt"
  FROM ranked_wallets
),

ranked_wallet_trans AS (
  SELECT
    wt.*,
    ROW_NUMBER() OVER (
      PARTITION BY wt."memberId"
      ORDER BY wt."expiredAt" DESC
    ) AS rn
  FROM staging_point_service."WalletTransaction" wt
  WHERE wt."walletCode" = 'CASH_WALLET'
    AND wt."expiredAt" >= DATE '2025-07-01'
    AND wt."expiredAt" < DATE '2099-01-01'
    AND wt."amount" <> 0
),

wt_replace_exp_group AS (
  SELECT 
    rwt.id,
    rwt."memberId",
    rwt."balanceId",
    wb.id AS real_wb_ulid,
    wb."expiredAt" AS "wb_expiredAt"
  FROM ranked_wallet_trans rwt
  INNER JOIN replace_exp_date_group redg 
      ON rwt."balanceId" = redg.id
  LEFT JOIN staging_point_service."ulid_WalletBalance" ulid_wb 
      ON rwt."balanceId" = ulid_wb.id
  LEFT JOIN point_service."WalletBalance" wb 
      ON ulid_wb.ulid_id = wb.id
)

-- Final SELECT for use or preview
--SELECT * FROM wt_replace_exp_group





,base_wallet_txn AS (
    SELECT
        wt.id,
        wt."memberId",
        wt."walletActivityId",
        wt."balanceId",
        wt."walletCode",
        wt."amount",
        wt."expiredAt",
        wt."type",
        wt."createdAt"
    FROM staging_point_service."WalletTransaction" wt
    --where "memberId" = '2000683'
),

wallet_txn_with_amount AS (
    SELECT
        *,
        CASE 
            WHEN "walletCode" = 'CARAT_WALLET' THEN COALESCE(amount, 0) * 4
            ELSE amount
        END AS adjusted_amount
    FROM base_wallet_txn
),

wallet_txn_with_expiry AS (
    SELECT
        *,
        CASE
            WHEN "walletCode" = 'CARAT_WALLET' AND ("expiredAt" >= '2099-01-01' AND "expiredAt" < '2100-01-01') THEN 
                TO_TIMESTAMP('2007-01-01 16:59:59.999', 'YYYY-MM-DD HH24:MI:SS.MS')
            WHEN "walletCode" = 'CARAT_WALLET' AND "expiredAt" > now() THEN 
                TO_TIMESTAMP(EXTRACT(YEAR FROM "expiredAt") || '-12-31 16:59:59.999', 'YYYY-MM-DD HH24:MI:SS.MS')
            ELSE "expiredAt"
        END AS normalized_expiredAt
    FROM wallet_txn_with_amount
)

,wallettransaction AS (
SELECT
    ulid.ulid_id AS id,
    sm.id AS "memberId",
    wa_ulid.ulid_id AS "walletActivityId",
    wb_ulid.ulid_id AS "balanceId",
    --wt."balanceId",
    wt."type",
    wt."walletCode",
    wt.adjusted_amount AS amount,
    --wt.normalized_expiredAt AS "expiredAt",
    case when wt."walletCode" = 'CASH_WALLET' and wt."balanceId" = wtreg.real_wb_ulid then wtreg."wb_expiredAt"
    else wt.normalized_expiredAt 
    end as "expiredAt",
    --wtreg."wb_expiredAt" as replace_exp_date,
    wt."createdAt"
FROM wallet_txn_with_expiry wt
LEFT JOIN staging_point_service."ulid_WalletTransaction" ulid 
    ON wt.id = ulid.id
INNER JOIN loyalty_service."Member" sm 
    ON wt."memberId" = sm."gwlNo"
LEFT JOIN staging_point_service."ulid_WalletActivity" wa_ulid 
    ON wt."walletActivityId" = wa_ulid.id
INNER JOIN point_service."WalletActivity" wa 
    ON wa_ulid.ulid_id = wa.id
LEFT JOIN staging_point_service."ulid_WalletBalance" wb_ulid 
    ON wt."balanceId" = wb_ulid.id
LEFT JOIN wt_replace_exp_group wtreg 
    ON wt."balanceId" = wtreg.real_wb_ulid
INNER JOIN point_service."WalletBalance" wb 
    ON wb_ulid.ulid_id = wb.id 
)

SELECT
    "id",
    "memberId",
    "walletActivityId",
    "balanceId",
    "type",
    "walletCode",
    "amount",
    "expiredAt",
    "createdAt"
FROM wallettransaction


ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "walletActivityId" = EXCLUDED."walletActivityId",
    "balanceId" = EXCLUDED."balanceId",
    "type" = EXCLUDED."type",
    "walletCode" = EXCLUDED."walletCode",
    amount = EXCLUDED.amount,
    "expiredAt" = EXCLUDED."expiredAt",
    "createdAt" = EXCLUDED."createdAt";
