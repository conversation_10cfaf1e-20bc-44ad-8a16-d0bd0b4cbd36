INSERT INTO point_service."WalletBalance" (
	"id",
    "memberId",
    "walletCode",
    "amount",
    "createdAt",
    "updatedAt",
    "expiredAt"
)
WITH walletbalance AS
(SELECT 
    id,
    "memberId",
    "walletCode",
    coalesce("amount", 0.00) as "amount",
    "createdAt",
    "updatedAt",
    "expiredAt"
FROM (
    SELECT 
        ulid.ulid_id as id,
        sm.id as "memberId",
        wb."walletCode",
        agg.total_amount as "amount",
        wb."createdAt",
        wb."updatedAt",
        agg."expiredAt",
        ROW_NUMBER() OVER (PARTITION BY wb."memberId", wb."walletCode", agg."expiredAt" ORDER BY wb."createdAt", wb.id) AS rn
    FROM staging_point_service."WalletBalanceTemp" AS wb
    LEFT JOIN (
        SELECT 
            "memberId",
            "walletCode",
            "expiredAt",
            SUM(amount) AS total_amount
        FROM staging_point_service."WalletBalanceTemp"
        GROUP BY "memberId", "walletCode", "expiredAt"
    ) agg 
    ON wb."memberId" = agg."memberId"
    AND wb."walletCode" = agg."walletCode"
    AND coalesce(wb."expiredAt", TIMESTAMP '2099-01-01 00:00:00') = coalesce(agg."expiredAt", TIMESTAMP '2099-01-01 00:00:00')
    LEFT JOIN staging_point_service."ulid_WalletBalance" ulid
    ON wb.id = ulid.id
    INNER JOIN loyalty_service."Member" AS sm
    ON wb."memberId" = sm."gwlNo"
) ranked
WHERE rn = 1
)

SELECT
    id,
    "memberId",
    "walletCode",
    "amount",
    "createdAt",
    "updatedAt",
    "expiredAt"
FROM walletbalance

ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "walletCode" = EXCLUDED."walletCode",
    amount = EXCLUDED.amount,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "expiredAt" = EXCLUDED."expiredAt";
    -- airflow around 6-7 mins
--     Updated Rows	5667664
--      Execute time	5m 56s