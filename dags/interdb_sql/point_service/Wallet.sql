INSERT into point_service."Wallet" 
SELECT * FROM staging_point_service."Wallet"
ON CONFLICT (id,code) DO UPDATE SET
    "runningId" = EXCLUDED."runningId",
    "name" = EXCLUDED."name",
    "walletTypeCode" = EXCLUDED."walletTypeCode",
    status = EXCLUDED.status,
    description = EXCLUDED.description,
    currency = EXCLUDED.currency,
    image = EXCLUDED.image,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy";