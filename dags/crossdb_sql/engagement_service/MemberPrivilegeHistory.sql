INSERT into staging_engagement_service."MemberPrivilegeHistory" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."MemberPrivilegeHistory"')  AS 
t1(id text,
    "memberId" text,
    "privilegeId" text,
    "typeCode" text,
    "subTypeCode" text,
    "couponCode" text,
    activity text,
    reason text,
    "location" text,
    "createdBy" text,
    "usedBy" text,
    "createdAt" timestamp(3) without time zone,
    "deletedAt" timestamp(3) without time zone)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "privilegeId" = EXCLUDED."privilegeId",
    "typeCode" = EXCLUDED."typeCode",
    "subTypeCode" = EXCLUDED."subTypeCode",
    "couponCode" = EXCLUDED."couponCode",
    activity = EXCLUDED.activity,
    reason = EXCLUDED.reason,
    "location" = EXCLUDED."location",
    "createdBy" = EXCLUDED."createdBy",
    "usedBy" = EXCLUDED."usedBy",
    "createdAt" = EXCLUDED."createdAt",
    "deletedAt" = EXCLUDED."deletedAt";
