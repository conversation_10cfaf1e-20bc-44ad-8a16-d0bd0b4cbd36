INSERT into staging_engagement_service."PrivilegeCostAllocation" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."PrivilegeCostAllocation"')  AS 
t1(id text,
    "privilegeId" text,
    "entityId" text,
    "entityType" text,
    value int4,
    "createdAt" timestamp(3) without time zone,
    "updatedAt" timestamp(3) without time zone,
    "deletedAt" timestamp(3) without time zone)
ON CONFLICT (id) DO UPDATE SET
    "privilegeId" = EXCLUDED."privilegeId",
    "entityId" = EXCLUDED."entityId",
    "entityType" = EXCLUDED."entityType",
    value = EXCLUDED.value,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt";
