INSERT into staging_engagement_service."MemberCouponActivity" (
    id, 
    "memberId", 
    "refId", 
    "refType", 
    "remark", 
    "activity", 
    "couponCode", 
    "reason", 
    "location", 
    "createdBy", 
    "usedBy", 
    "createdAt", 
    "deletedAt" 
)
SELECT * FROM dblink(
    'my_connection', 
    'SELECT 
        id, 
        "memberId", 
        "refId", 
        "refType", 
        "remark", 
        "activity", 
        "couponCode", 
        "reason", 
        "location", 
        "createdBy", 
        "usedBy", 
        "createdAt", 
        "deletedAt"  
    FROM engagement_service."MemberCouponActivity"'
)  AS t1 (
    id text,
    "memberId" text,
    "refId" text,
    "refType" text,
    remark jsonb,
    activity text,
    "couponCode" text,
    reason text,
    "location" text,
    "createdBy" text,
    "usedBy" text,
    "createdAt" timestamp(3),
    "deletedAt" timestamp(3)
)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "refId" = EXCLUDED."refId",
    "refType" = EXCLUDED."refType",
    remark = EXCLUDED.remark,
    activity = EXCLUDED.activity,
    "couponCode" = EXCLUDED."couponCode",
    reason = EXCLUDED.reason,
    "location" = EXCLUDED."location",
    "createdBy" = EXCLUDED."createdBy",
    "usedBy" = EXCLUDED."usedBy",
    "createdAt" = EXCLUDED."createdAt",
    "deletedAt" = EXCLUDED."deletedAt";
