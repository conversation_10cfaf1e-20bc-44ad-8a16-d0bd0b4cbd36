INSERT into staging_engagement_service."RewardCostCenter" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."RewardCostCenter"')  AS 
t1(id text,
    "rewardId" text,
    "costCenterId" text,
    "subsidizePercent" numeric(16, 2))
ON CONFLICT (id) DO UPDATE SET
    "rewardId" = EXCLUDED."rewardId",
    "costCenterId" = EXCLUDED."costCenterId",
    "subsidizePercent" = EXCLUDED."subsidizePercent";
