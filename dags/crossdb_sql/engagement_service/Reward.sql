INSERT into staging_engagement_service."Reward" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."Reward"')  AS 
t1(id text,
    "runningId" int4,
    "nameEn" text,
    "nameTh" text,
    "nameCn" text,
    type text,
    "rewardCategoryId" text,
    "visibilityType" text,
    "displayType" text,
    remark text,
    "couponId" text,
    coupon jsonb,
    "walletSetting" jsonb,
    "criteriaType" text,
    "redemptionCondition" jsonb,
    "effectiveDate" timestamp(3) without time zone,
    "expiredAt" timestamp(3) without time zone,
    "eventType" text,
    "eventCondition" jsonb,
    "eventCouponTrigger" text,
    status text,
    "isActive" bool,
    "typeCode" text,
    "subTypeCode" text,
    "value" numeric(16, 2),
    "quantity" int4,
    "quota" int4,
    contractreference text,
    "cost" numeric(16, 2),
    usagecondition json,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "deletedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "runningId" = EXCLUDED."runningId",
    "nameEn" = EXCLUDED."nameEn",
    "nameTh" = EXCLUDED."nameTh",
    "nameCn" = EXCLUDED."nameCn",
    type = EXCLUDED.type,
    "rewardCategoryId" = EXCLUDED."rewardCategoryId",
    "visibilityType" = EXCLUDED."visibilityType",
    "displayType" = EXCLUDED."displayType",
    remark = EXCLUDED.remark,
    "couponId" = EXCLUDED."couponId",
    coupon = EXCLUDED.coupon,
    "walletSetting" = EXCLUDED."walletSetting",
    "criteriaType" = EXCLUDED."criteriaType",
    "redemptionCondition" = EXCLUDED."redemptionCondition",
    "effectiveDate" = EXCLUDED."effectiveDate",
    "expiredAt" = EXCLUDED."expiredAt",
    "eventType" = EXCLUDED."eventType",
    "eventCondition" = EXCLUDED."eventCondition",
    "eventCouponTrigger" = EXCLUDED."eventCouponTrigger",
    status = EXCLUDED.status,
    "isActive" = EXCLUDED."isActive",
    "typeCode" = EXCLUDED."typeCode",
    "subTypeCode" = EXCLUDED."subTypeCode",
    "value" = EXCLUDED."value",
    "quantity" = EXCLUDED."quantity",
    "quota" = EXCLUDED."quota",
    contractreference = EXCLUDED.contractreference,
    "cost" = EXCLUDED."cost",
    usagecondition = EXCLUDED.usagecondition,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt";
