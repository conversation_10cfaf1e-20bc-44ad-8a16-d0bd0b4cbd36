INSERT into staging_engagement_service."MemberPrivilege" (
    id, 
    "memberId", 
    "privilegeId", 
    "isUnlimited", 
    "grantedAt", 
    "createdAt", 
    "updatedAt", 
    status,
    "expiredAt"
)
SELECT * FROM dblink(
    'my_connection', 
    'SELECT 
        id, 
        "memberId", 
        "privilegeId", 
        "isUnlimited", 
        "grantedAt", 
        "createdAt", 
        "updatedAt", 
        status,
        "expiredAt"
    FROM engagement_service."MemberPrivilege"'
)  AS t1 (
    id text,
    "memberId" text,
    "privilegeId" text,
    "isUnlimited" bool,
    "grantedAt" timestamp(3),
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    status text,
    "expiredAt" timestamp(3)
)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "privilegeId" = EXCLUDED."privilegeId",
    "isUnlimited" = EXCLUDED."isUnlimited",
    "grantedAt" = EXCLUDED."grantedAt",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    status = EXCLUDED.status,
    "expiredAt" = EXCLUDED."expiredAt";
