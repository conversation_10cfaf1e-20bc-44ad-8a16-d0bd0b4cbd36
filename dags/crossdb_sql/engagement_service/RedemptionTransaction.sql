INSERT into staging_engagement_service."RedemptionTransaction" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."RedemptionTransaction"')  AS 
t1(id text,
    "memberId" text,
    "rewardId" text,
    status text,
    "createdAt" timestamp(3) without time zone,
    "updatedAt" timestamp(3) without time zone)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "rewardId" = EXCLUDED."rewardId",
    status = EXCLUDED.status,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";
