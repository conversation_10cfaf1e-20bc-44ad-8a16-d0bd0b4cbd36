INSERT into staging_engagement_service."RewardCostAllocation" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."RewardCostAllocation"')  AS 
t1(id text,
    "rewardId" text,
    "entityId" text,
    "entityType" text,
    value int4,
    "subsidizePercent" numeric(16, 2))
ON CONFLICT (id) DO UPDATE SET
    "rewardId" = EXCLUDED."rewardId",
    "entityId" = EXCLUDED."entityId",
    "entityType" = EXCLUDED."entityType",
    value = EXCLUDED.value,
    "subsidizePercent" = EXCLUDED."subsidizePercent";

