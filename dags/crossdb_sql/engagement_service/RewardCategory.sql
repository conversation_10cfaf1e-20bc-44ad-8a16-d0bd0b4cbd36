INSERT into staging_engagement_service."RewardCategory" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."RewardCategory"')  AS 
t1(id text,
    "name" text,
    "isActive" bool,
    "createdAt" timestamp(3) without time zone,
    "updatedAt" timestamp(3) without time zone,
    "deletedAt" timestamp(3) without time zone)
ON CONFLICT (id) DO UPDATE SET
    "name" = EXCLUDED."name",
    "isActive" = EXCLUDED."isActive",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt";
