INSERT into staging_engagement_service."MemberCoupon" (
    id, 
    "memberId", 
    "entityType", 
    "entityId", 
    status, 
    "usedAt", 
    "expiredAt", 
    "couponRef", 
    "createdAt", 
    "updatedAt", 
    "deletedAt", 
    "isUnlimited", 
    "isUsedForGuest",
    "isActive", 
    "remark" 
)
SELECT * FROM dblink(
    'my_connection', 
    'SELECT 
        id, 
        "memberId", 
        "entityType", 
        "entityId", 
        status, 
        "usedAt", 
        "expiredAt", 
        "couponRef", 
        "createdAt", 
        "updatedAt", 
        "deletedAt", 
        "isUnlimited", 
        "isUsedForGuest",
        "isActive", 
        "remark" 
    FROM engagement_service."MemberCoupon"'
)  AS 
t1(id text,
    "memberId" text,
    "entityType" text,
    "entityId" text,
    status text,
    "usedAt" timestamp(3),
    "expiredAt" timestamp(3),
    "couponRef" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "deletedAt" timestamp(3),
    "isUnlimited" boolean,
    "isUsedForGuest" boolean,
    "isActive" boolean,
    remark jsonb)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "entityType" = EXCLUDED."entityType",
    "entityId" = EXCLUDED."entityId",
    status = EXCLUDED.status,
    "usedAt" = EXCLUDED."usedAt",
    "expiredAt" = EXCLUDED."expiredAt",
    "couponRef" = EXCLUDED."couponRef",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt",
    "isUnlimited" = EXCLUDED."isUnlimited",
    "isUsedForGuest" = EXCLUDED."isUsedForGuest",
    "isActive" = EXCLUDED."isActive",
    remark = EXCLUDED.remark;