INSERT into staging_engagement_service."Member"
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."Member"')  AS 
t1(id text,
    "dateOfBirth" timestamp(3) without time zone,
    "createdAt" timestamp(3) without time zone,
    "updatedAt" timestamp(3) without time zone,
    "deletedAt" timestamp(3) without time zone,
    "firstName" text,
    "firstNameTh" text,
    "lastName" text,
    "lastNameTh" text,
    "middleName" text,
    "middleNameTh" text,
    "titleId" int4,
    "titleName" jsonb,
    "gwlNo" text)
ON CONFLICT (id) DO UPDATE SET
    "dateOfBirth" = EXCLUDED."dateOfBirth",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt",
    "firstName" = EXCLUDED."firstName",
    "firstNameTh" = EXCLUDED."firstNameTh",
    "lastName" = EXCLUDED."lastName",
    "lastNameTh" = EXCLUDED."lastNameTh",
    "middleName" = EXCLUDED."middleName",
    "middleNameTh" = EXCLUDED."middleNameTh",
    "titleId" = EXCLUDED."titleId",
    "titleName" = EXCLUDED."titleName",
    "gwlNo" = EXCLUDED."gwlNo"

