INSERT into staging_point_service."WalletAdjustmentTransaction" (
    id,
    "memberId",
    "walletCode",
    "reasonCode",
    amount,
    "type",
    remark,
    "createdBy",
    "createdAt"
)
SELECT * FROM dblink(
    'my_connection', 
    'SELECT
        id,
        "memberId",
        "walletCode",
        "reasonCode",
        amount,
        "type",
        remark,
        "createdBy",
        "createdAt"
    FROM point_service."WalletAdjustmentTransaction"'
) AS t1 (
    id text,
    "memberId" text,
    "walletCode" text,
    "reasonCode" text,
    amount numeric(16, 2),
    "type" text,
    remark text,
    "createdBy" jsonb,
    "createdAt" timestamp(3)
)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "walletCode" = EXCLUDED."walletCode",
    "reasonCode" = EXCLUDED."reasonCode",
    amount = EXCLUDED.amount,
    "type" = EXCLUDED."type",
    remark = EXCLUDED.remark,
    "createdBy" = EXCLUDED."createdBy",
    "createdAt" = EXCLUDED."createdAt";
