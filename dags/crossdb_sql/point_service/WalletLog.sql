INSERT into staging_point_service."WalletLog" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM point_service."WalletLog"')  AS 
t1(id text,
    "oldData" jsonb,
    "newData" jsonb,
    detail _text,
    status text,
    "effectiveDate" timestamp(3),
    "updatedBy" jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "walletCode" text)
ON CONFLICT (id) DO UPDATE SET
    "oldData" = EXCLUDED."oldData",
    "newData" = EXCLUDED."newData",
    detail = EXCLUDED.detail,
    status = EXCLUDED.status,
    "effectiveDate" = EXCLUDED."effectiveDate",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "walletCode" = EXCLUDED."walletCode";
