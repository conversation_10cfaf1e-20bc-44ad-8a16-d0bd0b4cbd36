INSERT into staging_point_service."WalletTransaction" (
    id,
    "memberId",
    "walletActivityId",
    "balanceId",
    "type",
    "walletCode",
    amount,
    "expiredAt",
    "createdAt"
)
SELECT * FROM dblink(
    'my_connection', 
    'SELECT
        id,
        "memberId",
        "walletActivityId",
        "balanceId",
        "type",
        "walletCode",
        amount,
        "expiredAt",
        "createdAt"
    FROM point_service."WalletTransaction"'
)  AS t1 (
    id text,
    "memberId" text,
    "walletActivityId" text,
    "balanceId" text,
    "type" text,
    "walletCode" text,
    amount numeric(16, 2),
    "expiredAt" timestamp(3),
    "createdAt" timestamp(3)
)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "walletActivityId" = EXCLUDED."walletActivityId",
    "balanceId" = EXCLUDED."balanceId",
    "type" = EXCLUDED."type",
    "walletCode" = EXCLUDED."walletCode",
    amount = EXCLUDED.amount,
    "expiredAt" = EXCLUDED."expiredAt",
    "createdAt" = EXCLUDED."createdAt";
