-- SELECT dblink_connect('my_connection', 'host=kpc-gwl-postgres-nonprod.cz8mo8a2qmp8.ap-southeast-1.rds.amazonaws.com dbname=migration_temp_db_from_smc user=migration-user password=migration-user');

INSERT into staging_point_service."PointSetting" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM point_service."PointSetting"')  AS 
t1(id text,
    "nameEn" text,
    "nameTh" text,
    "unitEn" text,
    "unitTh" text,
    earn jsonb,
    burn jsonb,
    "expiryPeriodYear" int4,
    "cost" numeric(16, 2),
    image jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "updatedBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "nameEn" = EXCLUDED."nameEn",
    "nameTh" = EXCLUDED."nameTh",
    "unitEn" = EXCLUDED."unitEn",
    "unitTh" = EXCLUDED."unitTh",
    earn = EXCLUDED.earn,
    burn = EXCLUDED.burn,
    "expiryPeriodYear" = EXCLUDED."expiryPeriodYear",
    "cost" = EXCLUDED."cost",
    image = EXCLUDED.image,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy";

