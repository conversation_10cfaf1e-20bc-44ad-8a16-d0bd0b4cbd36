INSERT INTO staging_point_service."WalletBalance" (
    id,
    "memberId",
    "walletCode",
    amount,
    "createdAt",
    "updatedAt",
    "expiredAt"
)
SELECT * FROM dblink(
    'my_connection', 
    'SELECT
        id,
        "memberId",
        "walletCode",
        amount,
        "createdAt",
        "updatedAt",
        "expiredAt"
    FROM point_service."WalletBalance"'
) AS t1(
    id text,
    "memberId" text,
    "walletCode" text,
    amount numeric(16, 2),
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "expiredAt" timestamp(3)
)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "walletCode" = EXCLUDED."walletCode",
    amount = EXCLUDED.amount,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "expiredAt" = EXCLUDED."expiredAt";
