INSERT into staging_point_service."WalletActivity" (
    id,
    "memberId",
    "walletCode",
    "type",
    "refType",
    "refId",
    "externalId",
    amount,
    "partnerCode",
    "brandCode",
    "branchCode",
    remark,
    detail,
    "createdAt",
    "updatedAt",
    "documentDate"
)
SELECT * FROM dblink(
    'my_connection', 
    'SELECT 
        id,
        "memberId",
        "walletCode",
        "type",
        "refType",
        "refId",
        "externalId",
        amount,
        "partnerCode",
        "brandCode",
        "branchCode",
        remark,
        detail,
        "createdAt",
        "updatedAt",
        "documentDate"
    FROM point_service."WalletActivity"'
) AS t1 (
    id text,
    "memberId" text,
    "walletCode" text,
    "type" text,
    "refType" text,
    "refId" text,
    "externalId" text,
    amount numeric(16, 2),
    "partnerCode" text,
    "brandCode" text,
    "branchCode" text,
    remark text,
    detail jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "documentDate" timestamp(3)
)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "walletCode" = EXCLUDED."walletCode",
    "type" = EXCLUDED."type",
    "refType" = EXCLUDED."refType",
    "refId" = EXCLUDED."refId",
    "externalId" = EXCLUDED."externalId",
    amount = EXCLUDED.amount,
    "partnerCode" = EXCLUDED."partnerCode",
    "brandCode" = EXCLUDED."brandCode",
    "branchCode" = EXCLUDED."branchCode",
    remark = EXCLUDED.remark,
    detail = EXCLUDED.detail,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "documentDate" = EXCLUDED."documentDate";
