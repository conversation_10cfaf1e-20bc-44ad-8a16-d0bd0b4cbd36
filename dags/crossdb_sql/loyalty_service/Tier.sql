INSERT into staging_loyalty_service."Tier" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."Tier"')  AS 
t1(id text,
    code text,
    "name" text,
    "earnRate" numeric(16, 2),
    "isActive" bool,
    "minimumSpending" numeric(16, 2),
    "maintainSpending" numeric(16, 2),
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    description text,
    image jsonb,
    "type" text)
ON CONFLICT (id) DO UPDATE SET
    code = EXCLUDED.code,
    "name" = EXCLUDED."name",
    "earnRate" = EXCLUDED."earnRate",
    "isActive" = EXCLUDED."isActive",
    "minimumSpending" = EXCLUDED."minimumSpending",
    "maintainSpending" = EXCLUDED."maintainSpending",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    description = EXCLUDED.description,
    image = EXCLUDED.image,
    "type" = EXCLUDED."type";

