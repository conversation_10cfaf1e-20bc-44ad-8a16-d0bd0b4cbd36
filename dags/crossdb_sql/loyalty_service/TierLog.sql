INSERT into staging_loyalty_service."TierLog" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."TierLog"')  AS 
t1(id text,
    "updatedBy" jsonb,
    "oldData" jsonb,
    "newData" jsonb,
    "tierId" text,
    detail _text,
    "effectiveDate" timestamp(3),
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "updatedBy" = EXCLUDED."updatedBy",
    "oldData" = EXCLUDED."oldData",
    "newData" = EXCLUDED."newData",
    "tierId" = EXCLUDED."tierId",
    detail = EXCLUDED.detail,
    "effectiveDate" = EXCLUDED."effectiveDate",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

