------ TO PROTECT CODE ERROR ------
INSERT INTO staging_loyalty_service."MemberCoBrandCard"
SELECT * FROM loyalty_service."MemberCoBrandCard"
LIMIT 0
ON CONFLICT (id) DO NOTHING;


-- INSERT into staging_loyalty_service."MemberCoBrandCard" (
--     id,
--     "memberId",
--     "coBrandId",
--     "cardNo",
--     "memberCoBrandCardImportId",
--     "status",
--     "createdAt",
--     "createdBy",
--     "updatedAt",
--     "updatedBy",
--     "cardHolderName",
--     "cardHolderNameHash",
--     remark,
--     "cardReason"
-- )
-- SELECT 
--     *
-- FROM dblink(
--     'my_connection', 
--     'SELECT
--         id,
--         "memberId",
--         "coBrandId",
--         "cardNo",
--         "memberCoBrandCardImportId",
--         "status",
--         "createdAt",
--         "createdBy",
--         "updatedAt",
--         "updatedBy",
--         "cardHolderName",
--         "cardHolderNameHash",
--         remark,
--         "cardReason"
--     FROM loyalty_service."MemberCoBrandCard"'
-- )  AS t1(
--     id text,
--     "memberId" text,
--     "coBrandId" text,
--     "cardNo" text,
--     "memberCoBrandCardImportId" text,
--     "status" text,
--     "createdAt" timestamp(3),
--     "createdBy" jsonb,
--     "updatedAt" timestamp(3),
--     "updatedBy" jsonb,
--     "cardHolderName" text,
-- 	"cardHolderNameHash" text,
-- 	remark text,
-- 	"cardReason" text
-- )
-- ON CONFLICT (id) DO UPDATE SET
--     "memberId" = EXCLUDED."memberId",
--     "coBrandId" = EXCLUDED."coBrandId",
--     "cardNo" = EXCLUDED."cardNo",
--     "memberCoBrandCardImportId" = EXCLUDED."memberCoBrandCardImportId",
--     "status" = EXCLUDED."status",
--     "createdAt" = EXCLUDED."createdAt",
--     "createdBy" = EXCLUDED."createdBy",
--     "updatedAt" = EXCLUDED."updatedAt",
--     "updatedBy" = EXCLUDED."updatedBy",
--     "cardHolderName" = EXCLUDED."cardHolderName",
--     "cardHolderNameHash" = EXCLUDED."cardHolderNameHash",
--     "remark" = EXCLUDED."remark",
--     "cardReason" = EXCLUDED."cardReason";
