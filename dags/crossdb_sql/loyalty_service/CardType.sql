INSERT into staging_loyalty_service."CardType" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."CardType"') AS 
t1(code text,
    name text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- DROP TABLE staging_loyalty_service."CardType" CASCADE;

-- CREATE TABLE staging_loyalty_service."CardType" (
-- 	code text NOT NULL,
-- 	"name" text NOT NULL,
-- 	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
-- 	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
-- 	CONSTRAINT "CardType_pkey" PRIMARY KEY (code)
-- );