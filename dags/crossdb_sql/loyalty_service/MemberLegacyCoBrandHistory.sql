INSERT into staging_loyalty_service."MemberLegacyCoBrandHistory" (
    id,
    "memberId",
    "cardTypeCode",
    "description",
    "embossNo",
    "startedAt",
    "endedAt",
    "cardStatus",
    "cardReason",
    "createdAt",
    "updatedAt",
    source_cardtypecode,
    source_runno
)
SELECT * FROM dblink(
    'my_connection', 
    'SELECT
        id,
        "memberId",
        "cardTypeCode",
        "description",
        "embossNo",
        "startedAt",
        "endedAt",
        "cardStatus",
        "cardReason",
        "createdAt",
        "updatedAt",
        source_cardtypecode,
        source_runno
    FROM loyalty_service."MemberLegacyCobrandHistory"'
)  AS t1(
    id text,
    "memberId" text,
    "cardTypeCode" text,
    "description" text,
    "embossNo" text,
    "startedAt" timestamp(3),
    "endedAt" timestamp(3),
    "cardStatus" text,
    "cardReason" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    source_cardtypecode text,
	source_runno integer
)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "cardTypeCode" = EXCLUDED."cardTypeCode",
    "description" = EXCLUDED."description", 
    "embossNo" = EXCLUDED."embossNo",
    "startedAt" = EXCLUDED."startedAt",
    "endedAt" = EXCLUDED."endedAt",
    "cardStatus" = EXCLUDED."cardStatus",
    "cardReason" = EXCLUDED."cardReason",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "source_cardtypecode" = EXCLUDED."source_cardtypecode",
    "source_runno" = EXCLUDED."source_runno";
