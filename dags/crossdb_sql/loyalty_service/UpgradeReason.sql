-- SELECT dblink_connect('my_connection', 'host=kpc-gwl-postgres-nonprod.cz8mo8a2qmp8.ap-southeast-1.rds.amazonaws.com dbname=migration_temp_db_from_smc user=migration-user password=migration-user');

INSERT into staging_loyalty_service."UpgradeReason" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."UpgradeReason"')  AS 
t1(code text,
    "name" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (code) DO UPDATE SET
    "name" = EXCLUDED."name",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";
