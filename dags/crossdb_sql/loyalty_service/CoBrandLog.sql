INSERT into staging_loyalty_service."CoBrandLog" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."CoBrandLog"') AS 
t1(id text,
    "oldData" jsonb,
    "newData" jsonb,
    "coBrandId" text,
    "detail" text[],
    "effectiveDate" timestamp(3),
    "status" text,
    "createdAt" timestamp(3),
    "createdBy" jsonb,
    "updatedAt" timestamp(3),
    "updatedBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "oldData" = EXCLUDED."oldData",
    "newData" = EXCLUDED."newData",
    "coBrandId" = EXCLUDED."coBrandId",
    "detail" = EXCLUDED."detail",
    "effectiveDate" = EXCLUDED."effectiveDate",
    "status" = EXCLUDED."status",
    "createdAt" = EXCLUDED."createdAt",
    "createdBy" = EXCLUDED."createdBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy";

