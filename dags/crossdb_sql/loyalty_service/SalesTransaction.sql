INSERT into staging_loyalty_service."SalesTransaction" (
    id,
    "memberId",
    "completedAt",
    "externalId",
    "createdAt",
    "updatedAt",
    "netTotalAmount",
    "totalAccumSpendableAmount",
    "totalDiscount",
    "totalEarnableAmount",
    "branchCode",
    "brandCode",
    "partnerCode"
)
SELECT * FROM dblink(
    'my_connection', 
    'SELECT
        id,
        "memberId",
        "completedAt",
        "externalId",
        "createdAt",
        "updatedAt",
        "netTotalAmount",
        "totalAccumSpendableAmount",
        "totalDiscount",
        "totalEarnableAmount",
        "branchCode",
        "brandCode",
        "partnerCode"
    FROM loyalty_service."SalesTransaction"'
)  AS t1(
    id text,
    "memberId" text,
    "completedAt" timestamp(3),
    "externalId" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "netTotalAmount" numeric(16, 2),
    "totalAccumSpendableAmount" numeric(16, 2),
    "totalDiscount" numeric(16, 2),
    "totalEarnableAmount" numeric(16, 2),
    "branchCode" text,
    "brandCode" text,
    "partnerCode" text
)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "completedAt" = EXCLUDED."completedAt",
    "externalId" = EXCLUDED."externalId",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "netTotalAmount" = EXCLUDED."netTotalAmount",
    "totalAccumSpendableAmount" = EXCLUDED."totalAccumSpendableAmount",
    "totalDiscount" = EXCLUDED."totalDiscount",
    "totalEarnableAmount" = EXCLUDED."totalEarnableAmount",
    "branchCode" = EXCLUDED."branchCode",
    "brandCode" = EXCLUDED."brandCode",
    "partnerCode" = EXCLUDED."partnerCode";
