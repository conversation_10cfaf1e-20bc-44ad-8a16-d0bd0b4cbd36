INSERT into staging_loyalty_service."CoBrand" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."CoBrand"')  AS 
t1(id text,
    code text,
    name text,
    "bankCode" text,
    "cardTypeCode" text,
    "cardBin" text,
    "cardImage" jsonb,
    "minimumTierCode" text,
    "status" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "updatedBy" jsonb,
    detail jsonb)
ON CONFLICT (id) DO UPDATE SET
    code = EXCLUDED.code,
    name = EXCLUDED.name,
    "bankCode" = EXCLUDED."bankCode",
    "cardTypeCode" = EXCLUDED."cardTypeCode",
    "cardBin" = EXCLUDED."cardBin",
    "cardImage" = EXCLUDED."cardImage",
    "minimumTierCode" = EXCLUDED."minimumTierCode",
    "status" = EXCLUDED."status",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    detail = EXCLUDED.detail;