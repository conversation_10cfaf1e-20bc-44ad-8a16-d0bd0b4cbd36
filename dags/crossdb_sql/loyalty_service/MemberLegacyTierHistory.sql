INSERT into staging_loyalty_service."MemberLegacyTierHistory" (
    id,
    "memberId",
    "cardTypeCode",
    description,
    "embossNo",
    "tierStartedAt",
    "tierEndedAt",
    "cardStatus",
    "cardReason",
    "createdAt",
    "updatedAt",
    source_runno,
    source_cardtypecode
)
SELECT * FROM dblink(
    'my_connection', 
    'SELECT
        id,
        "memberId",
        "cardTypeCode",
        description,
        "embossNo",
        "tierStartedAt",
        "tierEndedAt",
        "cardStatus",
        "cardReason",
        "createdAt",
        "updatedAt",
        source_runno,
        source_cardtypecode
    FROM loyalty_service."MemberLegacyTierHistory"'
)  AS t1 (
    id text,
    "memberId" text,
    "cardTypeCode" text,
    description text,
    "embossNo" text,
    "tierStartedAt" timestamp(3),
    "tierEndedAt" timestamp(3),
    "cardStatus" text,
    "cardReason" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    source_runno integer,
	source_cardtypecode text
)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "cardTypeCode" = EXCLUDED."cardTypeCode",
    description = EXCLUDED.description,
    "embossNo" = EXCLUDED."embossNo",
    "tierStartedAt" = EXCLUDED."tierStartedAt",
    "tierEndedAt" = EXCLUDED."tierEndedAt",
    "cardStatus" = EXCLUDED."cardStatus",
    "cardReason" = EXCLUDED."cardReason",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "source_runno" = EXCLUDED."source_runno",
    "source_cardtypecode" = EXCLUDED."source_cardtypecode";
