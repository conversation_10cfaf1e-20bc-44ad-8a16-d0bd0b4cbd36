INSERT into staging_partner_service."SalesTransactionCoupon" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."SalesTransactionCoupon"')  AS 
t1(id text,
    "salesTransactionId" bigint,
    "couponSourceType" text,
    type text,
    "subType" text,
    "memberCouponId" text,
    "promoCode" text,
    sapcode text,
    value numeric(16, 2),
    settings jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "salesTransactionId" = EXCLUDED."salesTransactionId",
    "couponSourceType" = EXCLUDED."couponSourceType",
    type = EXCLUDED.type,
    "subType" = EXCLUDED."subType",
    "memberCouponId" = EXCLUDED."memberCouponId",
    "promoCode" = EXCLUDED."promoCode",
    sapcode = EXCLUDED.sapcode,
    value = EXCLUDED.value,
    settings = EXCLUDED.settings,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

