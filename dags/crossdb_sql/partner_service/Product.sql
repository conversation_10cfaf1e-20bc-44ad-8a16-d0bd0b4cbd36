INSERT into staging_partner_service."Product" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."Product"')  AS 
t1(id text,
    "brandId" text,
    sku text,
    "name" text,
    "categoryCode" text,
    "brandCode" text,
    settings jsonb,
    status text,
    "createdBy" text,
    "createdAt" timestamp(3),
    "updatedBy" text,
    "updatedAt" timestamp(3),
    "deletedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "brandId" = EXCLUDED."brandId",
    sku = EXCLUDED.sku,
    "name" = EXCLUDED."name",
    "categoryCode" = EXCLUDED."categoryCode",
    "brandCode" = EXCLUDED."brandCode",
    settings = EXCLUDED.settings,
    status = EXCLUDED.status,
    "createdBy" = EXCLUDED."createdBy",
    "createdAt" = EXCLUDED."createdAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt";
