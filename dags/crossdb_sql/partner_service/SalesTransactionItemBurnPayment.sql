INSERT into staging_partner_service."SalesTransactionItemBurnPayment" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."SalesTransactionItemBurnPayment"')  AS 
t1(id text,
    "itemId" text,
    "burnAmount" numeric(16, 2),
    "paymentAmount" numeric(16, 2),
    settings jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "burnPaymentId" text)
ON CONFLICT (id) DO UPDATE SET
    "itemId" = EXCLUDED."itemId",
    "burnAmount" = EXCLUDED."burnAmount",
    "paymentAmount" = EXCLUDED."paymentAmount",
    settings = EXCLUDED.settings,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "burnPaymentId" = EXCLUDED."burnPaymentId";

