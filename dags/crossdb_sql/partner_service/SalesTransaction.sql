INSERT into staging_partner_service."SalesTransaction" (
    id,
    "memberId",
    "gwlNo",
    "externalId",
    "taxInvoice",
    "partnerId",
    "brandId",
    "branchId",
    "netTotalAmount",
    "totalOriginalPrice",
    "totalDiscount",
    "totalEarnableAmount",
    "totalAccumSpendableAmount",
    "totalPointEarned",
    "shippingAmount",
    status,
    settings,
    "rawRequest",
    "completedAt",
    "createdAt",
    "updatedAt",
    detail,
    "memberShoppingCardId",
    "documentDate"
)
SELECT * FROM dblink(
    'my_connection', 
    'SELECT
        cast(id as bigint) as id,
        "memberId",
        "gwlNo",
        "externalId",
        "taxInvoice",
        "partnerId",
        "brandId",
        "branchId",
        "netTotalAmount",
        "totalOriginalPrice",
        "totalDiscount",
        "totalEarnableAmount",
        "totalAccumSpendableAmount",
        "totalPointEarned",
        "shippingAmount",
        status,
        settings,
        "rawRequest",
        "completedAt",
        "createdAt",
        "updatedAt",
        detail,
        "memberShoppingCardId",
        "documentDate"
    FROM partner_service."SalesTransaction"'
)  AS t1 (
    id bigint,
	"memberId" text,
	"gwlNo" text,
	"externalId" text,
	"taxInvoice" text,
	"partnerId" text,
	"brandId" text,
	"branchId" text,
	"netTotalAmount" numeric(16, 2),
	"totalOriginalPrice" numeric(16, 2),
	"totalDiscount" numeric(16, 2),
	"totalEarnableAmount" numeric(16, 2),
	"totalAccumSpendableAmount" numeric(16, 2),
	"totalPointEarned" numeric(16, 2),
	"shippingAmount" numeric(16, 2),
	status text,
	settings jsonb,
	"rawRequest" jsonb,
	"completedAt" timestamp(3),
	"createdAt" timestamp(3),
	"updatedAt" timestamp(3),
	detail jsonb,
	"memberShoppingCardId" text,
	"documentDate" timestamp(3)
)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "gwlNo" = EXCLUDED."gwlNo",
    "externalId" = EXCLUDED."externalId",
    "taxInvoice" = EXCLUDED."taxInvoice",
    "partnerId" = EXCLUDED."partnerId",
    "brandId" = EXCLUDED."brandId",
    "branchId" = EXCLUDED."branchId",
    "netTotalAmount" = EXCLUDED."netTotalAmount",
    "totalOriginalPrice" = EXCLUDED."totalOriginalPrice",
    "totalDiscount" = EXCLUDED."totalDiscount",
    "totalEarnableAmount" = EXCLUDED."totalEarnableAmount",
    "totalAccumSpendableAmount" = EXCLUDED."totalAccumSpendableAmount",
    "totalPointEarned" = EXCLUDED."totalPointEarned",
    "shippingAmount" = EXCLUDED."shippingAmount",
    status = EXCLUDED.status,
    settings = EXCLUDED.settings,
    "rawRequest" = EXCLUDED."rawRequest",
    "completedAt" = EXCLUDED."completedAt",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    detail = EXCLUDED.detail,
    "memberShoppingCardId" = EXCLUDED."memberShoppingCardId",
    "documentDate" = EXCLUDED."documentDate";
