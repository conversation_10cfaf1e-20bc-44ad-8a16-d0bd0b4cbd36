INSERT into staging_partner_service."Import" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."Import"') AS 
t1(id text,
    "module" text,
    file jsonb,
    status text,
    errorlog jsonb,
    metadata jsonb,
    "createdBy" text,
    "createdAt" timestamp(3),
    "updatedBy" text,
    "updatedAt" timestamp(3),
    "deletedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "module" = EXCLUDED."module",
    file = EXCLUDED.file,
    status = EXCLUDED.status,
    errorlog = EXCLUDED.errorlog,
    metadata = EXCLUDED.metadata,
    "createdBy" = EXCLUDED."createdBy",
    "createdAt" = EXCLUDED."createdAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt";

