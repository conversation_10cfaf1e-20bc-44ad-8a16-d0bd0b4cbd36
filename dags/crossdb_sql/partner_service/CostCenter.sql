INSERT into staging_partner_service."CostCenter" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."CostCenter"')  AS 
t1(id text,
    "partnerId" text,
    code text,
    "name" text,
    "businessAreaCode" text,
    "businessAreaName" text,
    "createdBy" text,
    "createdAt" timestamp(3),
    "updatedBy" text,
    "updatedAt" timestamp(3),
    "deletedAt" timestamp(3),
    "companyCode" text,
    "companyName" text)
ON CONFLICT (id) DO UPDATE SET
    "partnerId" = EXCLUDED."partnerId",
    code = EXCLUDED.code,
    "name" = EXCLUDED."name",
    "businessAreaCode" = EXCLUDED."businessAreaCode",
    "businessAreaName" = EXCLUDED."businessAreaName",
    "createdBy" = EXCLUDED."createdBy",
    "createdAt" = EXCLUDED."createdAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt",
    "companyCode" = EXCLUDED."companyCode",
    "companyName" = EXCLUDED."companyName";

