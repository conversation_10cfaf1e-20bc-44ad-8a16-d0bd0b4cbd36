INSERT into staging_partner_service."SalesTransactionItem" (
    id,
    "productId",
    quantity,
    "netAmount",
    "caratEarnableAmount",
    "normalPointEarned",
    "burnPaymentAmount",
    settings,
    "paymentDetail",
    "createdAt",
    "updatedAt",
    "originalPrice",
    "salesTransactionId",
    discount,
    "tierExtraPointEarned",
    sku
)
SELECT * FROM dblink(
    'my_connection', 
    'SELECT
        id,
        "productId",
        quantity,
        "netAmount",
        "caratEarnableAmount",
        "normalPointEarned",
        "burnPaymentAmount",
        settings,
        "paymentDetail",
        "createdAt",
        "updatedAt",
        "originalPrice",
        "salesTransactionId", 
        discount,
        "tierExtraPointEarned",
        sku
    FROM partner_service."SalesTransactionItem"'
)  AS t1 (
    id text,
    "productId" text,
    quantity int4,
    "netAmount" numeric(16, 2),
    "caratEarnableAmount" numeric(16, 2),
    "normalPointEarned" numeric(16, 2),
    "burnPaymentAmount" numeric(16, 2),
    settings jsonb,
    "paymentDetail" jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "originalPrice" numeric(16, 2),
    "salesTransactionId" bigint,
    discount numeric(16, 2),
    "tierExtraPointEarned" numeric(16, 2),
    sku text
)
ON CONFLICT (id) DO UPDATE SET
    "productId" = EXCLUDED."productId",
    quantity = EXCLUDED.quantity,
    "netAmount" = EXCLUDED."netAmount",
    "caratEarnableAmount" = EXCLUDED."caratEarnableAmount",
    "normalPointEarned" = EXCLUDED."normalPointEarned",
    "burnPaymentAmount" = EXCLUDED."burnPaymentAmount",
    settings = EXCLUDED.settings,
    "paymentDetail" = EXCLUDED."paymentDetail",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "originalPrice" = EXCLUDED."originalPrice",
    "salesTransactionId" = EXCLUDED."salesTransactionId",
    discount = EXCLUDED.discount,
    "tierExtraPointEarned" = EXCLUDED."tierExtraPointEarned",
    sku = EXCLUDED.sku;
