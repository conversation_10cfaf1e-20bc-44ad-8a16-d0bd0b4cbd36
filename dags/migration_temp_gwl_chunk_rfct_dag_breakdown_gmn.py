from datetime import datetime
from functools import partial
from airflow import DAG
import os
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from migration_utils.pipeline_ops import (
    process_service_tables,
    process_pre_transform_tables,
    process_transformed_tables,
    
)
from migration_utils.ulid_utils import generate_ulid
from _table_sequence import GEN_ULID_TABLES

REPAIR_MIGRATION_TABLES = [
    'engagement_service."MemberCoupon"'
    ,'engagement_service."MemberCouponActivity"'
                        #     ,'engagement_service."MemberCouponActivity"'
                        #     'loyalty_service."Member"'
                        #     ,'engagement_service."MemberPrivilege"'
                        #     ,'engagement_service."MemberCoupon"'
                        #     ,'engagement_service."MemberCouponActivity"'
                        #     ,'loyalty_service."SalesTransaction"'       #big table
                        #     ,'loyalty_service."MemberProfile"'          #big table
                        #     ,'loyalty_service."StaffProfile"'
                        #     ,'loyalty_service."MemberCoBrandCard"'
                        #     ,'loyalty_service."MemberLegacyTierHistory"'
                        #     ,'loyalty_service."MemberLegacyCoBrandHistory"'
                        #     ,'partner_service."SalesTransaction"'                 #big table
                        #     ,'point_service."WalletAdjustmentTransaction"'
                        #     ,'point_service."WalletBalance"'

                        #     # #### Set 3 ####
                        #     ,'partner_service."RefundSalesTransaction"'
                        #     ,'partner_service."SalesTransactionItem"'              #big table
                        #     ,'partner_service."SalesTransactionBurnPayment"'
                        #     ,'partner_service."SalesTransactionPayment"'          #big table
                        #     ,'point_service."WalletActivity"'           #big table
                        #     ,'loyalty_service."RefundSalesTransaction"'

                        #     # #### Set 4 ####
                        #     ,'partner_service."RefundSalesTransactionItem"'
                        #     ,'partner_service."SalesTransactionWalletActivity"'
                        #     ,'point_service."WalletTransaction"'        #big table bigest?    
]

def generate_ulid_for_service():
    for table_dict in GEN_ULID_TABLES:
        SOURCE_TABLE = table_dict['SOURCE_TABLE']
        TARGET_TABLE = table_dict['TARGET_TABLE']
        PK_NAME = table_dict['PK_NAME']
        generate_ulid(
            SOURCE_TABLE=SOURCE_TABLE,
            TARGET_TABLE=TARGET_TABLE,
            PK_NAME=PK_NAME
        )

# Modified function to process a single table
def run_transform_load_for_single_table(table_name):
    """Run transform and load for a single table."""
    process_transformed_tables([table_name])

file_name = os.path.basename(__file__).split('.')[0]

with DAG(
    dag_id=file_name,
    start_date=datetime(2022, 6, 28),
    schedule_interval=None,
    catchup=False,
    tags=["crossdb_stg_gwl", "repair", "rfct"],
) as dag:

    start_migration_task = EmptyOperator(task_id="start_migration")
    end_migration_task = EmptyOperator(task_id="end_migration")
    
    
    
    # Step 3: Pre-transform step (follows PRE_TRANSFORM_ORDER)
    pre_transform_task = PythonOperator(
        task_id="3_pre_transform_tables",
        python_callable=process_pre_transform_tables,
    )


    # Step 4: Gen ULID
    generate_ulid_task = PythonOperator(
        task_id="4_generate_ulid",
        python_callable=generate_ulid_for_service,
    )
    
    # Define the initial dependencies
    start_migration_task >> pre_transform_task >> generate_ulid_task

    # Initialize the "previous" task for sequential chaining after generate_ulid_task
    # This ensures it's defined before the loop's first dependency
    previous_task_in_sequence = generate_ulid_task

    # Step 5: Transform and load for each service in sequence
    # Create individual tasks for each table and chain them sequentially
    for i, table_name in enumerate(REPAIR_MIGRATION_TABLES):
        # Sanitize table_name for use in task_id (replace special characters)
        sanitized_table_name = table_name.replace('.', '_').replace('"', '')
        transform_table_task_id = f"5_transform_load_{sanitized_table_name}_{i+1}"
        
        transform_table_task = PythonOperator(
            task_id=transform_table_task_id,
            python_callable=partial(run_transform_load_for_single_table, table_name),
        )
        
        # Set the dependency: current table transform task depends on the previous task
        previous_task_in_sequence >> transform_table_task
        
        # Update the 'previous_task_in_sequence' to the current task for the next iteration
        previous_task_in_sequence = transform_table_task
    
    # Ensure the last transform task (which is now `previous_task_in_sequence` after the loop)
    # connects to the end_migration_task
    previous_task_in_sequence >> end_migration_task