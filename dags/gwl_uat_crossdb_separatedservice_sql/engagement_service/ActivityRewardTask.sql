-- ActivityRewardTask
INSERT into public."ActivityRewardTask" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."ActivityRewardTask"') AS 
t1(id text,
    "rewardId" text,
    status text,
    remark jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "createdBy" jsonb,
    "updatedBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "rewardId" = EXCLUDED."rewardId",
    status = EXCLUDED.status,
    remark = EXCLUDED.remark,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "createdBy" = EXCLUDED."createdBy",
    "updatedBy" = EXCLUDED."updatedBy";