-- Reward
INSERT into public."Reward" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."Reward"') AS 
t1(id text,
    "runningId" int4,
    "name" text,
    "type" text,
    "rewardCategoryId" text,
    "visibilityType" text,
    "displayType" text,
    remark text,
    "couponId" text,
    coupon jsonb,
    "walletSetting" jsonb,
    "criteriaType" text,
    "redemptionCondition" jsonb,
    "effectiveDate" timestamp(3),
    "expiredAt" timestamp(3),
    "eventType" text,
    "eventCondition" jsonb,
    "eventCouponTrigger" text,
    "redemptionCost" int4,
    status text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "deletedAt" timestamp(3),
    "updatedBy" jsonb,
    "createdBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "runningId" = EXCLUDED."runningId",
    "name" = EXCLUDED."name",
    "type" = EXCLUDED."type",
    "rewardCategoryId" = EXCLUDED."rewardCategoryId",
    "visibilityType" = EXCLUDED."visibilityType",
    "displayType" = EXCLUDED."displayType",
    remark = EXCLUDED.remark,
    "couponId" = EXCLUDED."couponId",
    coupon = EXCLUDED.coupon,
    "walletSetting" = EXCLUDED."walletSetting",
    "criteriaType" = EXCLUDED."criteriaType",
    "redemptionCondition" = EXCLUDED."redemptionCondition",
    "effectiveDate" = EXCLUDED."effectiveDate",
    "expiredAt" = EXCLUDED."expiredAt",
    "eventType" = EXCLUDED."eventType",
    "eventCondition" = EXCLUDED."eventCondition",
    "eventCouponTrigger" = EXCLUDED."eventCouponTrigger",
    "redemptionCost" = EXCLUDED."redemptionCost",
    status = EXCLUDED.status,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdBy" = EXCLUDED."createdBy";