-- RewardEarnTransaction
INSERT into public."RewardEarnTransaction" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."RewardEarnTransaction"') AS 
t1(id text,
    "entityType" text,
    "entityId" text,
    "walletActivityId" text,
    "walletType" text,
    "walletCode" text,
    amount numeric(16,2),
    remark jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "updatedBy" jsonb,
    "createdBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "entityType" = EXCLUDED."entityType",
    "entityId" = EXCLUDED."entityId",
    "walletActivityId" = EXCLUDED."walletActivityId",
    "walletType" = EXCLUDED."walletType",
    "walletCode" = EXCLUDED."walletCode",
    amount = EXCLUDED.amount,
    remark = EXCLUDED.remark,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdBy" = EXCLUDED."createdBy";