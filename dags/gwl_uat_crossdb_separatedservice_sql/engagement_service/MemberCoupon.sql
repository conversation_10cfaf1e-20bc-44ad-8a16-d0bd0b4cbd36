-- MemberCoupon
INSERT into public."MemberCoupon" (
    "id"
    ,"memberId"
    ,"entityType"
    ,"entityId"
    ,"status"
    ,"usedAt"
    ,"expiredAt"
    ,"couponRef"
    ,"createdAt"
    ,"updatedAt"
    ,"deletedAt"
    ,"isUnlimited"
    ,"isUsedForGuest"
    ,"isActive"
    ,"remark"
    ,"claimExpiredAt"
    ,"sourceId"
    ,"sourceType"
    ,"createdBy"
    ,"updatedBy"
)
SELECT
    "id"
    ,"memberId"
    ,"entityType"
    ,"entityId"
    ,"status"
    ,"usedAt"
    ,"expiredAt"
    ,"couponRef"
    ,"createdAt"
    ,"updatedAt"
    ,"deletedAt"
    ,"isUnlimited"
    ,"isUsedForGuest"
    ,"isActive"
    ,"remark"
    ,"claimExpiredAt"
    ,"sourceId"
    ,"sourceType"
    ,"createdBy" -- inserted jsonb from  0.prod gwl 
    ,"updatedBy" -- inserted jsonb from  0.prod gwl 
    
FROM dblink('my_connection', 'SELECT * FROM engagement_service."MemberCoupon"') AS 
t1(id text,
    "memberId" text,
    "entityType" text,
    "entityId" text,
    "usedAt" timestamp(3),
    "expiredAt" timestamp(3),
    "claimExpiredAt" timestamp(3),
    "couponRef" text,
    "isUnlimited" bool,
    "isUsedForGuest" bool,
    status text,
    "sourceType" text,
    "sourceId" text,
    remark jsonb,
    "isActive" bool,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "deletedAt" timestamp(3),
    "updatedBy" jsonb,
    "createdBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "entityType" = EXCLUDED."entityType",
    "entityId" = EXCLUDED."entityId",
    "usedAt" = EXCLUDED."usedAt",
    "expiredAt" = EXCLUDED."expiredAt",
    "claimExpiredAt" = EXCLUDED."claimExpiredAt",
    "couponRef" = EXCLUDED."couponRef",
    "isUnlimited" = EXCLUDED."isUnlimited",
    "isUsedForGuest" = EXCLUDED."isUsedForGuest",
    status = EXCLUDED.status,
    "sourceType" = EXCLUDED."sourceType",
    "sourceId" = EXCLUDED."sourceId",
    remark = EXCLUDED.remark,
    "isActive" = EXCLUDED."isActive",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdBy" = EXCLUDED."createdBy";

-- fixed prod sep to ba same shcema as prod gwl and uat