-- MemberPrivilegeLog
INSERT INTO public."MemberPrivilegeLog"
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."MemberPrivilegeLog"') AS 
t1(id text,
    "memberId" text,
    status text,
    remark jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "updatedBy" jsonb,
    "createdBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    status = EXCLUDED.status,
    remark = EXCLUDED.remark,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdBy" = EXCLUDED."createdBy";