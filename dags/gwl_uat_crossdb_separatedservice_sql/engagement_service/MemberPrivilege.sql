-- MemberPrivilege
INSERT into public."MemberPrivilege" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."MemberPrivilege"') AS 
t1(id text,
    "memberId" text,
    "privilegeId" text,
    status text,
    "isUnlimited" bool,
    "issuerType" text,
    "issuerCode" text,
    "issuerIdentifier" text,
    issuer jsonb,
    "grantedAt" timestamp(3),
    "memberPrivilegeLogId" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "updatedBy" jsonb,
    "createdBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "privilegeId" = EXCLUDED."privilegeId",
    status = EXCLUDED.status,
    "isUnlimited" = EXCLUDED."isUnlimited",
    "issuerType" = EXCLUDED."issuerType",
    "issuerCode" = EXCLUDED."issuerCode",
    "issuerIdentifier" = EXCLUDED."issuerIdentifier",
    issuer = EXCLUDED.issuer,
    "grantedAt" = EXCLUDED."grantedAt",
    "memberPrivilegeLogId" = EXCLUDED."memberPrivilegeLogId",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdBy" = EXCLUDED."createdBy";