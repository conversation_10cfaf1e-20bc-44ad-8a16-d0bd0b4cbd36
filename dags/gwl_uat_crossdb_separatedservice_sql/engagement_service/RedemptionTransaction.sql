-- RedemptionTransaction
INSERT into public."RedemptionTransaction" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."RedemptionTransaction"') AS 
t1(id text,
    "memberId" text,
    "rewardId" text,
    "redemptionPoint" int4,
    "refId" text,
    "refType" text,
    "orderNo" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "memberCouponId" text,
    "updatedBy" jsonb,
    "createdBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "rewardId" = EXCLUDED."rewardId",
    "redemptionPoint" = EXCLUDED."redemptionPoint",
    "refId" = EXCLUDED."refId",
    "refType" = EXCLUDED."refType",
    "orderNo" = EXCLUDED."orderNo",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "memberCouponId" = EXCLUDED."memberCouponId",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdBy" = EXCLUDED."createdBy";