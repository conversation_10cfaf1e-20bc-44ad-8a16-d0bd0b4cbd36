-- RewardContent
INSERT into public."RewardContent" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."RewardContent"') AS 
t1(id text,
    "rewardId" text,
    "nameEn" text,
    "nameTh" text,
    "nameCn" text,
    "brandCode" text,
    "brandNameEn" text,
    "brandNameTh" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "createdBy" jsonb,
    "updatedBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "rewardId" = EXCLUDED."rewardId",
    "nameEn" = EXCLUDED."nameEn",
    "nameTh" = EXCLUDED."nameTh",
    "nameCn" = EXCLUDED."nameCn",
    "brandCode" = EXCLUDED."brandCode",
    "brandNameEn" = EXCLUDED."brandNameEn",
    "brandNameTh" = EXCLUDED."brandNameTh",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "createdBy" = EXCLUDED."createdBy",
    "updatedBy" = EXCLUDED."updatedBy";