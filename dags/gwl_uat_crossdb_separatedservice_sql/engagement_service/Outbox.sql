-- Outbox
INSERT INTO public."Outbox"
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."Outbox"') AS 
t1(id text,
    "aggregateId" text,
    "aggregateType" text,
    topic text,
    payload text,
    status text,
    "retryCount" int4,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "processedAt" timestamp(3),
    "errorLog" text,
    "updatedBy" jsonb,
    "createdBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "aggregateId" = EXCLUDED."aggregateId",
    "aggregateType" = EXCLUDED."aggregateType",
    topic = EXCLUDED.topic,
    payload = EXCLUDED.payload,
    status = EXCLUDED.status,
    "retryCount" = EXCLUDED."retryCount",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "processedAt" = EXCLUDED."processedAt",
    "errorLog" = EXCLUDED."errorLog",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdBy" = EXCLUDED."createdBy";