-- Privilege
INSERT INTO public."Privilege"
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."Privilege"') AS 
t1(id text,
    "runningId" int4,
    "name" text,
    "type" text,
    "displayType" text,
    remark text,
    "couponId" text,
    coupon jsonb,
    status text,
    "grantCondition" jsonb,
    "effectiveDate" timestamp(3),
    "walletSetting" jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "deletedAt" timestamp(3),
    "updatedBy" jsonb,
    "createdBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "runningId" = EXCLUDED."runningId",
    "name" = EXCLUDED."name",
    "type" = EXCLUDED."type",
    "displayType" = EXCLUDED."displayType",
    remark = EXCLUDED.remark,
    "couponId" = EXCLUDED."couponId",
    coupon = EXCLUDED.coupon,
    status = EXCLUDED.status,
    "grantCondition" = EXCLUDED."grantCondition",
    "effectiveDate" = EXCLUDED."effectiveDate",
    "walletSetting" = EXCLUDED."walletSetting",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdBy" = EXCLUDED."createdBy";