-- from DDL of gwl
-- DROP SCHEMA engagement_service;

CREATE SCHEMA engagement_service AUTHORIZATION migration_gwl_db_target;
-- engagement_service."MemberPrivilegeLog" definition

-- Drop table

-- DROP TABLE engagement_service."MemberPrivilegeLog";

CREATE TABLE engagement_service."MemberPrivilegeLog" (
	id text NOT NULL,
	"memberId" text NOT NULL,
	status text NULL,
	remark jsonb NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedBy" jsonb NULL,
	"createdBy" jsonb NULL,
	CONSTRAINT "MemberPrivilegeLog_pkey" PRIMARY KEY (id)
);


-- engagement_service."Outbox" definition

-- Drop table

-- DROP TABLE engagement_service."Outbox";

CREATE TABLE engagement_service."Outbox" (
	id text NOT NULL,
	"aggregateId" text NULL,
	"aggregateType" text NULL,
	topic text NOT NULL,
	payload text NOT NULL,
	status text NOT NULL,
	"retryCount" int4 NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"processedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NULL,
	"errorLog" text NULL,
	"updatedBy" jsonb NULL,
	"createdBy" jsonb NULL,
	CONSTRAINT "Outbox_pkey" PRIMARY KEY (id)
);


-- engagement_service."Privilege" definition

-- Drop table

-- DROP TABLE engagement_service."Privilege";

CREATE TABLE engagement_service."Privilege" (
	id text NOT NULL,
	"runningId" int4 NOT NULL,
	"name" text NOT NULL,
	"type" text NOT NULL,
	"displayType" text NOT NULL,
	remark text NULL,
	"couponId" text NULL,
	coupon jsonb NULL,
	status text NOT NULL,
	"grantCondition" jsonb NOT NULL,
	"effectiveDate" timestamp(3) NULL,
	"walletSetting" jsonb NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"deletedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NULL,
	"updatedBy" jsonb NULL,
	"createdBy" jsonb NULL,
	CONSTRAINT "Privilege_pkey" PRIMARY KEY (id)
);


-- engagement_service."RewardCategory" definition

-- Drop table

-- DROP TABLE engagement_service."RewardCategory";

CREATE TABLE engagement_service."RewardCategory" (
	id text NOT NULL,
	"name" text NOT NULL,
	"isActive" bool DEFAULT false NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"deletedAt" timestamp(3) NULL,
	"updatedBy" jsonb NULL,
	"createdBy" jsonb NULL,
	CONSTRAINT "RewardCategory_pkey" PRIMARY KEY (id)
);


-- engagement_service."Reward" definition

-- Drop table

-- DROP TABLE engagement_service."Reward";

CREATE TABLE engagement_service."Reward" (
	id text NOT NULL,
	"runningId" int4 NOT NULL,
	"name" text NOT NULL,
	"type" text NOT NULL,
	"rewardCategoryId" text NOT NULL,
	"visibilityType" text NOT NULL,
	"displayType" text NULL,
	remark text NULL,
	"couponId" text NULL,
	coupon jsonb NULL,
	"walletSetting" jsonb NULL,
	"criteriaType" text NULL,
	"redemptionCondition" jsonb NOT NULL,
	"effectiveDate" timestamp(3) NULL,
	"expiredAt" timestamp(3) NULL,
	"eventType" text NULL,
	"eventCondition" jsonb NULL,
	"eventCouponTrigger" text NULL,
	"redemptionCost" int4 NULL,
	status text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"deletedAt" timestamp(3) NULL,
	"updatedBy" jsonb NULL,
	"createdBy" jsonb NULL,
	CONSTRAINT "Reward_pkey1" PRIMARY KEY (id),
	CONSTRAINT "Reward_rewardCategoryId_fkey" FOREIGN KEY ("rewardCategoryId") REFERENCES engagement_service."RewardCategory"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- engagement_service."RewardContent" definition

-- Drop table

-- DROP TABLE engagement_service."RewardContent";

CREATE TABLE engagement_service."RewardContent" (
	id text NOT NULL,
	"rewardId" text NOT NULL,
	"nameEn" text NOT NULL,
	"nameTh" text NOT NULL,
	"nameCn" text NULL,
	"brandCode" text NULL,
	"brandNameEn" text NULL,
	"brandNameTh" text NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"createdBy" jsonb NULL,
	"updatedBy" jsonb NULL,
	CONSTRAINT "RewardContent_pkey" PRIMARY KEY (id),
	CONSTRAINT "RewardContent_Reward_fkey" FOREIGN KEY ("rewardId") REFERENCES engagement_service."Reward"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- engagement_service."RewardCriteriaValue" definition

-- Drop table

-- DROP TABLE engagement_service."RewardCriteriaValue";

CREATE TABLE engagement_service."RewardCriteriaValue" (
	id text NOT NULL,
	"rewardId" text NOT NULL,
	value text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"deletedAt" timestamp(3) NULL,
	"updatedBy" jsonb NULL,
	"createdBy" jsonb NULL,
	CONSTRAINT "RewardCriteriaValue_pkey" PRIMARY KEY (id),
	CONSTRAINT "RewardCriteriaValue_rewardId_fkey" FOREIGN KEY ("rewardId") REFERENCES engagement_service."Reward"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- engagement_service."RewardEarnTransaction" definition

-- Drop table

-- DROP TABLE engagement_service."RewardEarnTransaction";

CREATE TABLE engagement_service."RewardEarnTransaction" (
	id text NOT NULL,
	"entityType" text NULL,
	"entityId" text NOT NULL,
	"walletActivityId" text NOT NULL,
	"walletType" text NOT NULL,
	"walletCode" text NOT NULL,
	amount numeric(16, 2) NOT NULL,
	remark jsonb NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedBy" jsonb NULL,
	"createdBy" jsonb NULL,
	CONSTRAINT rewardearntransaction_pkey PRIMARY KEY (id),
	CONSTRAINT "RewardEarnTransaction_Reward_fkey" FOREIGN KEY ("entityId") REFERENCES engagement_service."Reward"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- engagement_service."ActivityRewardTask" definition

-- Drop table

-- DROP TABLE engagement_service."ActivityRewardTask";

CREATE TABLE engagement_service."ActivityRewardTask" (
	id text NOT NULL,
	"rewardId" text NOT NULL,
	status text NOT NULL,
	remark jsonb NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"createdBy" jsonb NULL,
	"updatedBy" jsonb NULL,
	CONSTRAINT "ActivityRewardTask_pkey" PRIMARY KEY (id),
	CONSTRAINT "ActivityRewardTask_rewardId_fkey" FOREIGN KEY ("rewardId") REFERENCES engagement_service."Reward"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- engagement_service."MemberActivityRewardCriteria" definition

-- Drop table

-- DROP TABLE engagement_service."MemberActivityRewardCriteria";

CREATE TABLE engagement_service."MemberActivityRewardCriteria" (
	id text NOT NULL,
	"memberId" text NOT NULL,
	"criteriaId" text NOT NULL,
	status text NOT NULL,
	remark jsonb NULL,
	"activityRewardTaskId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"createdBy" jsonb NULL,
	"updatedBy" jsonb NULL,
	CONSTRAINT "MemberActivityRewardCriteria_pkey" PRIMARY KEY (id),
	CONSTRAINT "MemberActivityRewardCriteria_activityRewardTaskId_fkey" FOREIGN KEY ("activityRewardTaskId") REFERENCES engagement_service."ActivityRewardTask"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- engagement_service."RedemptionTransaction" definition

-- Drop table

-- DROP TABLE engagement_service."RedemptionTransaction";

CREATE TABLE engagement_service."RedemptionTransaction" (
	id text NOT NULL,
	"memberId" text NOT NULL,
	"rewardId" text NOT NULL,
	"redemptionPoint" int4 NOT NULL,
	"refId" text NULL,
	"refType" text NULL,
	"orderNo" text NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"memberCouponId" text NULL,
	"updatedBy" jsonb NULL,
	"createdBy" jsonb NULL,
	CONSTRAINT redemptiontransaction_pkey PRIMARY KEY (id),
	CONSTRAINT "RedemptionTransaction_rewardId_fkey" FOREIGN KEY ("rewardId") REFERENCES engagement_service."Reward"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- engagement_service."CouponClaimTransaction" definition

-- Drop table

-- DROP TABLE engagement_service."CouponClaimTransaction";

CREATE TABLE engagement_service."CouponClaimTransaction" (
	id text NOT NULL,
	"memberCouponId" text NOT NULL,
	status text NOT NULL,
	"docNo" text NULL,
	remark text NULL,
	"claimCouponRef" text NOT NULL,
	"cancelCouponRef" text NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"deletedAt" timestamp(3) NULL,
	"canceledAt" timestamp(3) NULL,
	"createdBy" jsonb NULL,
	"updatedBy" jsonb NULL,
	CONSTRAINT "CouponClaimTransaction_pkey" PRIMARY KEY (id)
);


-- engagement_service."MemberCoupon" definition

-- Drop table

-- DROP TABLE engagement_service."MemberCoupon";

CREATE TABLE engagement_service."MemberCoupon" (
	id text NOT NULL,
	"memberId" text NOT NULL,
	"entityType" text NULL,
	"entityId" text NOT NULL,
	"usedAt" timestamp(3) NULL,
	"expiredAt" timestamp(3) NOT NULL,
	"claimExpiredAt" timestamp(3) NULL,
	"couponRef" text NULL,
	"isUnlimited" bool NOT NULL,
	"isUsedForGuest" bool NULL,
	status text NOT NULL,
	"sourceType" text NOT NULL,
	"sourceId" text NOT NULL,
	remark jsonb NULL,
	"isActive" bool DEFAULT false NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"deletedAt" timestamp(3) NULL,
	"updatedBy" jsonb NULL,
	"createdBy" jsonb NULL,
	CONSTRAINT "MemberCoupon_pkey" PRIMARY KEY (id)
);
CREATE INDEX "MemberCoupon_entityType_entityId_idx" ON engagement_service."MemberCoupon" USING btree ("entityType", "entityId");
CREATE INDEX "MemberCoupon_memberId_idx" ON engagement_service."MemberCoupon" USING btree ("memberId");


-- engagement_service."MemberCouponActivity" definition

-- Drop table

-- DROP TABLE engagement_service."MemberCouponActivity";

CREATE TABLE engagement_service."MemberCouponActivity" (
	id text NOT NULL,
	"memberId" text NOT NULL,
	"refId" text DEFAULT ''::text NOT NULL,
	"refType" text DEFAULT ''::text NOT NULL,
	remark jsonb DEFAULT '{"name": "", "type": "", "subType": ""}'::jsonb NOT NULL,
	activity text NOT NULL,
	"couponCode" text NULL,
	reason text NULL,
	"location" text NULL,
	"createdBy" text NULL,
	"usedBy" text NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"deletedAt" timestamp(3) NULL,
	CONSTRAINT "MemberCouponActivity_pkey" PRIMARY KEY (id)
);
CREATE INDEX "MemberCouponActivity_memberId_idx" ON engagement_service."MemberCouponActivity" USING btree ("memberId");


-- engagement_service."MemberPrivilege" definition

-- Drop table

-- DROP TABLE engagement_service."MemberPrivilege";

CREATE TABLE engagement_service."MemberPrivilege" (
	id text NOT NULL,
	"memberId" text NOT NULL,
	"privilegeId" text NOT NULL,
	status text NULL,
	"isUnlimited" bool NOT NULL,
	"issuerType" text NULL,
	"issuerCode" text NULL,
	"issuerIdentifier" text NULL,
	issuer jsonb NULL,
	"grantedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"memberPrivilegeLogId" text NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedBy" jsonb NULL,
	"createdBy" jsonb NULL,
	CONSTRAINT "MemberPrivilege_pkey" PRIMARY KEY (id)
);
CREATE INDEX "MemberPrivilege_memberId_idx" ON engagement_service."MemberPrivilege" USING btree ("memberId");


-- engagement_service."CouponClaimTransaction" foreign keys

ALTER TABLE engagement_service."CouponClaimTransaction" ADD CONSTRAINT "CouponClaimTransaction_memberCouponId_fkey" FOREIGN KEY ("memberCouponId") REFERENCES engagement_service."MemberCoupon"(id) ON DELETE RESTRICT ON UPDATE CASCADE;


-- engagement_service."MemberCoupon" foreign keys

ALTER TABLE engagement_service."MemberCoupon" ADD CONSTRAINT "MemberCoupon_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES loyalty_service."Member"(id) ON DELETE RESTRICT ON UPDATE CASCADE;


-- engagement_service."MemberCouponActivity" foreign keys

ALTER TABLE engagement_service."MemberCouponActivity" ADD CONSTRAINT "MemberCouponActivity_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES loyalty_service."Member"(id) ON DELETE RESTRICT ON UPDATE CASCADE;


-- engagement_service."MemberPrivilege" foreign keys

ALTER TABLE engagement_service."MemberPrivilege" ADD CONSTRAINT "MemberPrivilege_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES loyalty_service."Member"(id) ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE engagement_service."MemberPrivilege" ADD CONSTRAINT "MemberPrivilege_privilegeId_fkey" FOREIGN KEY ("privilegeId") REFERENCES engagement_service."Privilege"(id) ON DELETE RESTRICT ON UPDATE CASCADE;











-- MemberPrivilegeLog
INSERT INTO engagement_service."MemberPrivilegeLog"
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."MemberPrivilegeLog"') AS 
t1(id text,
    "memberId" text,
    status text,
    remark jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "updatedBy" jsonb,
    "createdBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    status = EXCLUDED.status,
    remark = EXCLUDED.remark,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdBy" = EXCLUDED."createdBy";

-- Outbox
INSERT INTO engagement_service."Outbox"
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."Outbox"') AS 
t1(id text,
    "aggregateId" text,
    "aggregateType" text,
    topic text,
    payload text,
    status text,
    "retryCount" int4,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "processedAt" timestamp(3),
    "errorLog" text,
    "updatedBy" jsonb,
    "createdBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "aggregateId" = EXCLUDED."aggregateId",
    "aggregateType" = EXCLUDED."aggregateType",
    topic = EXCLUDED.topic,
    payload = EXCLUDED.payload,
    status = EXCLUDED.status,
    "retryCount" = EXCLUDED."retryCount",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "processedAt" = EXCLUDED."processedAt",
    "errorLog" = EXCLUDED."errorLog",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdBy" = EXCLUDED."createdBy";

-- Privilege
INSERT INTO engagement_service."Privilege"
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."Privilege"') AS 
t1(id text,
    "runningId" int4,
    "name" text,
    "type" text,
    "displayType" text,
    remark text,
    "couponId" text,
    coupon jsonb,
    status text,
    "grantCondition" jsonb,
    "effectiveDate" timestamp(3),
    "walletSetting" jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "deletedAt" timestamp(3),
    "updatedBy" jsonb,
    "createdBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "runningId" = EXCLUDED."runningId",
    "name" = EXCLUDED."name",
    "type" = EXCLUDED."type",
    "displayType" = EXCLUDED."displayType",
    remark = EXCLUDED.remark,
    "couponId" = EXCLUDED."couponId",
    coupon = EXCLUDED.coupon,
    status = EXCLUDED.status,
    "grantCondition" = EXCLUDED."grantCondition",
    "effectiveDate" = EXCLUDED."effectiveDate",
    "walletSetting" = EXCLUDED."walletSetting",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdBy" = EXCLUDED."createdBy";



-- RewardCategory
INSERT into engagement_service."RewardCategory" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."RewardCategory"') AS 
t1(id text,
    "name" text,
    "isActive" bool,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "deletedAt" timestamp(3),
    "updatedBy" jsonb,
    "createdBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "name" = EXCLUDED."name",
    "isActive" = EXCLUDED."isActive",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdBy" = EXCLUDED."createdBy";

-- Reward
INSERT into engagement_service."Reward" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."Reward"') AS 
t1(id text,
    "runningId" int4,
    "name" text,
    "type" text,
    "rewardCategoryId" text,
    "visibilityType" text,
    "displayType" text,
    remark text,
    "couponId" text,
    coupon jsonb,
    "walletSetting" jsonb,
    "criteriaType" text,
    "redemptionCondition" jsonb,
    "effectiveDate" timestamp(3),
    "expiredAt" timestamp(3),
    "eventType" text,
    "eventCondition" jsonb,
    "eventCouponTrigger" text,
    "redemptionCost" int4,
    status text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "deletedAt" timestamp(3),
    "updatedBy" jsonb,
    "createdBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "runningId" = EXCLUDED."runningId",
    "name" = EXCLUDED."name",
    "type" = EXCLUDED."type",
    "rewardCategoryId" = EXCLUDED."rewardCategoryId",
    "visibilityType" = EXCLUDED."visibilityType",
    "displayType" = EXCLUDED."displayType",
    remark = EXCLUDED.remark,
    "couponId" = EXCLUDED."couponId",
    coupon = EXCLUDED.coupon,
    "walletSetting" = EXCLUDED."walletSetting",
    "criteriaType" = EXCLUDED."criteriaType",
    "redemptionCondition" = EXCLUDED."redemptionCondition",
    "effectiveDate" = EXCLUDED."effectiveDate",
    "expiredAt" = EXCLUDED."expiredAt",
    "eventType" = EXCLUDED."eventType",
    "eventCondition" = EXCLUDED."eventCondition",
    "eventCouponTrigger" = EXCLUDED."eventCouponTrigger",
    "redemptionCost" = EXCLUDED."redemptionCost",
    status = EXCLUDED.status,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdBy" = EXCLUDED."createdBy";

-- RewardContent
INSERT into engagement_service."RewardContent" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."RewardContent"') AS 
t1(id text,
    "rewardId" text,
    "nameEn" text,
    "nameTh" text,
    "nameCn" text,
    "brandCode" text,
    "brandNameEn" text,
    "brandNameTh" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "createdBy" jsonb,
    "updatedBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "rewardId" = EXCLUDED."rewardId",
    "nameEn" = EXCLUDED."nameEn",
    "nameTh" = EXCLUDED."nameTh",
    "nameCn" = EXCLUDED."nameCn",
    "brandCode" = EXCLUDED."brandCode",
    "brandNameEn" = EXCLUDED."brandNameEn",
    "brandNameTh" = EXCLUDED."brandNameTh",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "createdBy" = EXCLUDED."createdBy",
    "updatedBy" = EXCLUDED."updatedBy";

-- RewardCriteriaValue
INSERT into engagement_service."RewardCriteriaValue" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."RewardCriteriaValue"') AS 
t1(id text,
    "rewardId" text,
    value text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "deletedAt" timestamp(3),
    "updatedBy" jsonb,
    "createdBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "rewardId" = EXCLUDED."rewardId",
    value = EXCLUDED.value,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdBy" = EXCLUDED."createdBy";

-- RewardEarnTransaction
INSERT into engagement_service."RewardEarnTransaction" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."RewardEarnTransaction"') AS 
t1(id text,
    "entityType" text,
    "entityId" text,
    "walletActivityId" text,
    "walletType" text,
    "walletCode" text,
    amount numeric(16,2),
    remark jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "updatedBy" jsonb,
    "createdBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "entityType" = EXCLUDED."entityType",
    "entityId" = EXCLUDED."entityId",
    "walletActivityId" = EXCLUDED."walletActivityId",
    "walletType" = EXCLUDED."walletType",
    "walletCode" = EXCLUDED."walletCode",
    amount = EXCLUDED.amount,
    remark = EXCLUDED.remark,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdBy" = EXCLUDED."createdBy";

-- ActivityRewardTask
INSERT into engagement_service."ActivityRewardTask" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."ActivityRewardTask"') AS 
t1(id text,
    "rewardId" text,
    status text,
    remark jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "createdBy" jsonb,
    "updatedBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "rewardId" = EXCLUDED."rewardId",
    status = EXCLUDED.status,
    remark = EXCLUDED.remark,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "createdBy" = EXCLUDED."createdBy",
    "updatedBy" = EXCLUDED."updatedBy";

-- MemberActivityRewardCriteria
INSERT into engagement_service."MemberActivityRewardCriteria" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."MemberActivityRewardCriteria"') AS 
t1(id text,
    "memberId" text,
    "criteriaId" text,
    status text,
    remark jsonb,
    "activityRewardTaskId" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "createdBy" jsonb,
    "updatedBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "criteriaId" = EXCLUDED."criteriaId",
    status = EXCLUDED.status,
    remark = EXCLUDED.remark,
    "activityRewardTaskId" = EXCLUDED."activityRewardTaskId",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "createdBy" = EXCLUDED."createdBy",
    "updatedBy" = EXCLUDED."updatedBy";

-- RedemptionTransaction
INSERT into engagement_service."RedemptionTransaction" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."RedemptionTransaction"') AS 
t1(id text,
    "memberId" text,
    "rewardId" text,
    "redemptionPoint" int4,
    "refId" text,
    "refType" text,
    "orderNo" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "memberCouponId" text,
    "updatedBy" jsonb,
    "createdBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "rewardId" = EXCLUDED."rewardId",
    "redemptionPoint" = EXCLUDED."redemptionPoint",
    "refId" = EXCLUDED."refId",
    "refType" = EXCLUDED."refType",
    "orderNo" = EXCLUDED."orderNo",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "memberCouponId" = EXCLUDED."memberCouponId",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdBy" = EXCLUDED."createdBy";

-- CouponClaimTransaction
INSERT into engagement_service."CouponClaimTransaction" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."CouponClaimTransaction"') AS 
t1(id text,
    "memberCouponId" text,
    status text,
    "docNo" text,
    remark text,
    "claimCouponRef" text,
    "cancelCouponRef" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "deletedAt" timestamp(3),
    "canceledAt" timestamp(3),
    "createdBy" jsonb,
    "updatedBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "memberCouponId" = EXCLUDED."memberCouponId",
    status = EXCLUDED.status,
    "docNo" = EXCLUDED."docNo",
    remark = EXCLUDED.remark,
    "claimCouponRef" = EXCLUDED."claimCouponRef",
    "cancelCouponRef" = EXCLUDED."cancelCouponRef",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt",
    "canceledAt" = EXCLUDED."canceledAt",
    "createdBy" = EXCLUDED."createdBy",
    "updatedBy" = EXCLUDED."updatedBy";

-- MemberCoupon
INSERT into engagement_service."MemberCoupon" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."MemberCoupon"') AS 
t1(id text,
    "memberId" text,
    "entityType" text,
    "entityId" text,
    "usedAt" timestamp(3),
    "expiredAt" timestamp(3),
    "claimExpiredAt" timestamp(3),
    "couponRef" text,
    "isUnlimited" bool,
    "isUsedForGuest" bool,
    status text,
    "sourceType" text,
    "sourceId" text,
    remark jsonb,
    "isActive" bool,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "deletedAt" timestamp(3),
    "updatedBy" jsonb,
    "createdBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "entityType" = EXCLUDED."entityType",
    "entityId" = EXCLUDED."entityId",
    "usedAt" = EXCLUDED."usedAt",
    "expiredAt" = EXCLUDED."expiredAt",
    "claimExpiredAt" = EXCLUDED."claimExpiredAt",
    "couponRef" = EXCLUDED."couponRef",
    "isUnlimited" = EXCLUDED."isUnlimited",
    "isUsedForGuest" = EXCLUDED."isUsedForGuest",
    status = EXCLUDED.status,
    "sourceType" = EXCLUDED."sourceType",
    "sourceId" = EXCLUDED."sourceId",
    remark = EXCLUDED.remark,
    "isActive" = EXCLUDED."isActive",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdBy" = EXCLUDED."createdBy";

-- MemberCouponActivity
INSERT into engagement_service."MemberCouponActivity" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."MemberCouponActivity"') AS 
t1(id text,
    "memberId" text,
    "refId" text,
    "refType" text,
    remark jsonb,
    activity text,
    "couponCode" text,
    reason text,
    "location" text,
    "createdBy" text,
    "usedBy" text,
    "createdAt" timestamp(3),
    "deletedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "refId" = EXCLUDED."refId",
    "refType" = EXCLUDED."refType",
    remark = EXCLUDED.remark,
    activity = EXCLUDED.activity,
    "couponCode" = EXCLUDED."couponCode",
    reason = EXCLUDED.reason,
    "location" = EXCLUDED."location",
    "createdBy" = EXCLUDED."createdBy",
    "usedBy" = EXCLUDED."usedBy",
    "createdAt" = EXCLUDED."createdAt",
    "deletedAt" = EXCLUDED."deletedAt";

-- MemberPrivilege
INSERT into engagement_service."MemberPrivilege" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."MemberPrivilege"') AS 
t1(id text,
    "memberId" text,
    "privilegeId" text,
    status text,
    "isUnlimited" bool,
    "issuerType" text,
    "issuerCode" text,
    "issuerIdentifier" text,
    issuer jsonb,
    "grantedAt" timestamp(3),
    "memberPrivilegeLogId" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "updatedBy" jsonb,
    "createdBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "privilegeId" = EXCLUDED."privilegeId",
    status = EXCLUDED.status,
    "isUnlimited" = EXCLUDED."isUnlimited",
    "issuerType" = EXCLUDED."issuerType",
    "issuerCode" = EXCLUDED."issuerCode",
    "issuerIdentifier" = EXCLUDED."issuerIdentifier",
    issuer = EXCLUDED.issuer,
    "grantedAt" = EXCLUDED."grantedAt",
    "memberPrivilegeLogId" = EXCLUDED."memberPrivilegeLogId",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdBy" = EXCLUDED."createdBy";