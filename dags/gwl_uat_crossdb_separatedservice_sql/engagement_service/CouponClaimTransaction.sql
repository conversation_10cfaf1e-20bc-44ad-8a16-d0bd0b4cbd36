-- CouponClaimTransaction
INSERT into public."CouponClaimTransaction" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."CouponClaimTransaction"') AS 
t1(id text,
    "memberCouponId" text,
    status text,
    "docNo" text,
    remark text,
    "claimCouponRef" text,
    "cancelCouponRef" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "deletedAt" timestamp(3),
    "canceledAt" timestamp(3),
    "createdBy" jsonb,
    "updatedBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "memberCouponId" = EXCLUDED."memberCouponId",
    status = EXCLUDED.status,
    "docNo" = EXCLUDED."docNo",
    remark = EXCLUDED.remark,
    "claimCouponRef" = EXCLUDED."claimCouponRef",
    "cancelCouponRef" = EXCLUDED."cancelCouponRef",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt",
    "canceledAt" = EXCLUDED."canceledAt",
    "createdBy" = EXCLUDED."createdBy",
    "updatedBy" = EXCLUDED."updatedBy";