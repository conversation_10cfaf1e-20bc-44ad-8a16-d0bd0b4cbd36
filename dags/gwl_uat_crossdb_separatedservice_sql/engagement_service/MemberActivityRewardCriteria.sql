-- MemberActivityRewardCriteria
INSERT into public."MemberActivityRewardCriteria" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."MemberActivityRewardCriteria"') AS 
t1(id text,
    "memberId" text,
    "criteriaId" text,
    status text,
    remark jsonb,
    "activityRewardTaskId" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "createdBy" jsonb,
    "updatedBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "criteriaId" = EXCLUDED."criteriaId",
    status = EXCLUDED.status,
    remark = EXCLUDED.remark,
    "activityRewardTaskId" = EXCLUDED."activityRewardTaskId",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "createdBy" = EXCLUDED."createdBy",
    "updatedBy" = EXCLUDED."updatedBy";