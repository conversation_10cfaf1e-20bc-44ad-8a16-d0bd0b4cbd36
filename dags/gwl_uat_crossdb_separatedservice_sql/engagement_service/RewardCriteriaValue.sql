-- RewardCriteriaValue
INSERT into public."RewardCriteriaValue" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."RewardCriteriaValue"') AS 
t1(id text,
    "rewardId" text,
    value text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "deletedAt" timestamp(3),
    "updatedBy" jsonb,
    "createdBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "rewardId" = EXCLUDED."rewardId",
    value = EXCLUDED.value,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdBy" = EXCLUDED."createdBy";