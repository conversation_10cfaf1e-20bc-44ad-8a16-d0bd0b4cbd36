-- RewardCategory
INSERT into public."RewardCategory" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM engagement_service."RewardCategory"') AS 
t1(id text,
    "name" text,
    "isActive" bool,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "deletedAt" timestamp(3),
    "updatedBy" jsonb,
    "createdBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "name" = EXCLUDED."name",
    "isActive" = EXCLUDED."isActive",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdBy" = EXCLUDED."createdBy";