INSERT into public."RefundSalesTransaction" (
    id, 
    "salesTransactionId", 
    "type", 
    "taxInvoices", 
    "externalId", 
    "reason", 
    "caratRefundAmount", 
    "caratRevokeAmount", 
    "cashbackRefundAmount", 
    "cashbackRevokeAmount", 
    "chargeBackAmount",
    "refundedAt", 
    "createdAt", 
    "updatedAt",
    detail,
    "approvedAt",
    "createdBy",
    "updatedBy"
)
SELECT
   t1.id, 
   t1."salesTransactionId", 
   t1."type", 
   t1."taxInvoices", 
   t1."externalId", 
   t1."reason", 
   t1."caratRefundAmount", 
   t1."caratRevokeAmount", 
   t1."cashbackRefundAmount", 
   t1."cashbackRevokeAmount", 
   t1."chargeBackAmount",
   t1."refundedAt", 
   t1."createdAt", 
   t1."updatedAt",
   t1.detail,
   t1."approvedAt",
    jsonb_build_object(
        'id', NULL,
        'name', 'SYSTEM',
        'email', NULL
    ) AS "createdBy"
    ,jsonb_build_object(
        'id', NULL,
            'name', 'SYSTEM',
            'email', NULL
    ) AS "updatedBy"
FROM dblink(
    'my_connection', 
    'SELECT 
        *
     FROM partner_service."RefundSalesTransaction"'
) AS t1 (
    id text,
    "salesTransactionId" bigint,
    "type" text,
    "taxInvoices" text,
    "externalId" text,
    reason text,
    "caratRefundAmount" numeric(16, 2),
    "caratRevokeAmount" numeric(16, 2),
    "cashbackRefundAmount" numeric(16, 2),
    "cashbackRevokeAmount" numeric(16, 2),
    "chargeBackAmount" numeric(16, 2),
    "refundedAt" timestamp(3),
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    detail jsonb,
    "approvedAt" timestamp(3)
)
INNER JOIN public."SalesTransaction" st ON st.id = t1."salesTransactionId"

ON CONFLICT (id) DO UPDATE SET
    "salesTransactionId" = EXCLUDED."salesTransactionId",
    "type" = EXCLUDED."type",
    "taxInvoices" = EXCLUDED."taxInvoices",
    "externalId" = EXCLUDED."externalId",
    reason = EXCLUDED.reason,
    "caratRefundAmount" = EXCLUDED."caratRefundAmount",
    "caratRevokeAmount" = EXCLUDED."caratRevokeAmount",
    "cashbackRefundAmount" = EXCLUDED."cashbackRefundAmount",
    "cashbackRevokeAmount" = EXCLUDED."cashbackRevokeAmount",
    "chargeBackAmount" = EXCLUDED."chargeBackAmount",
    "refundedAt" = EXCLUDED."refundedAt",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    detail = EXCLUDED.detail,
    "approvedAt" = EXCLUDED."approvedAt",
    "createdBy" = EXCLUDED."createdBy",
    "updatedBy" = EXCLUDED."updatedBy";