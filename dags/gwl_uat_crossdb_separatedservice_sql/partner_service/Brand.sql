INSERT into public."Brand" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."Brand"')  AS 
t1(id text,
    code text,
    "name" jsonb,
    categories text,
    status text,
    "createdAt" timestamp(3),
    "createdBy" jsonb,
    "updatedAt" timestamp(3),
    "updatedBy" jsonb,
    logo jsonb,
    settings jsonb,
    "deletedAt" timestamp(3),
    description text)
ON CONFLICT (id) DO UPDATE SET
    code = EXCLUDED.code,
    "name" = EXCLUDED."name",
    categories = EXCLUDED.categories,
    status = EXCLUDED.status,
    "createdAt" = EXCLUDED."createdAt",
    "createdBy" = EXCLUDED."createdBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    logo = EXCLUDED.logo,
    settings = EXCLUDED.settings,
    "deletedAt" = EXCLUDED."deletedAt",
    description = EXCLUDED.description;

