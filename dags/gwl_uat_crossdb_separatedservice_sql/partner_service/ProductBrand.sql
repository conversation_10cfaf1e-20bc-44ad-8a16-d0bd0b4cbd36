INSERT into public."ProductBrand" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."ProductBrand"')  AS 
t1(id text,
    "brandId" text,
    code text,
    "name" text,
    settings jsonb,
    "createdBy" text,
    "createdAt" timestamp(3),
    "updatedBy" text,
    "updatedAt" timestamp(3),
    "deletedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "brandId" = EXCLUDED."brandId",
    code = EXCLUDED.code,
    "name" = EXCLUDED."name",
    settings = EXCLUDED.settings,
    "createdBy" = EXCLUDED."createdBy",
    "createdAt" = EXCLUDED."createdAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt";
