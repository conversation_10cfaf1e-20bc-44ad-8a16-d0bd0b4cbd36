INSERT into public."SalesTransactionItemCoupon" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."SalesTransactionItemCoupon"')  AS 
t1(id text,
    "itemId" text,
    amount numeric(16, 2),
    detail jsonb,
    "salesTransactionCouponId" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "itemId" = EXCLUDED."itemId",
    amount = EXCLUDED.amount,
    detail = EXCLUDED.detail,
    "salesTransactionCouponId" = EXCLUDED."salesTransactionCouponId",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

