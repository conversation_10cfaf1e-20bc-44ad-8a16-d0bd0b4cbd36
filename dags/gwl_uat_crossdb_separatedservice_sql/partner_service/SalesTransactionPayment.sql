INSERT into public."SalesTransactionPayment" (
    "id",    
    "paymentMethodId",
    "amount",
    "settings",
    "createdAt",
    "updatedAt",
    "salesTransactionId"
)
SELECT
    t1."id",    
    t1."paymentMethodId",
    t1."amount",
    t1."settings",
    t1."createdAt",
    t1."updatedAt",
    t1."salesTransactionId"
FROM dblink(
    'my_connection', 
    'SELECT
        *
    FROM partner_service."SalesTransactionPayment"'
)  AS t1 (
    id text,
    "paymentMethodId" text,
    amount numeric(16, 2),
    settings jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "salesTransactionId" bigint
)
INNER JOIN public."SalesTransaction" st ON st.id = t1."salesTransactionId"

ON CONFLICT (id) DO UPDATE SET
    "paymentMethodId" = EXCLUDED."paymentMethodId",
    amount = EXCLUDED.amount,
    settings = EXCLUDED.settings,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "salesTransactionId" = EXCLUDED."salesTransactionId";
