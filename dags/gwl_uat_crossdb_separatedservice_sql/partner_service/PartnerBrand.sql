INSERT into public."PartnerBrand" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."PartnerBrand"')  AS 
t1(id text,
    "partnerId" text,
    "brandId" text,
    "createdBy" jsonb,
    "updatedBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "partnerId" = EXCLUDED."partnerId",
    "brandId" = EXCLUDED."brandId",
    "createdBy" = EXCLUDED."createdBy",
    "updatedBy" = EXCLUDED."updatedBy";

