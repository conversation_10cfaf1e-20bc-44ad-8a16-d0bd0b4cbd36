-- from DDL of gwl
-- database name : partner_service
-- insert correct seqencne ...


-- DROP DOMAIN partner_service."lo";

CREATE DOMAIN partner_service."lo" AS oid;
-- DROP SEQUENCE partner_service.salestransaction_id_seq;

CREATE SEQUENCE partner_service.salestransaction_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 20871458
	CACHE 1
	NO CYCLE;-- partner_service."Brand" definition

-- Drop table

-- DROP TABLE partner_service."Brand";

CREATE TABLE partner_service."Brand" (
	id text NOT NULL,
	code text NOT NULL,
	"name" jsonb NOT NULL,
	categories text NULL,
	status text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"createdBy" jsonb NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"updatedBy" jsonb NOT NULL,
	logo jsonb NULL,
	settings jsonb NULL,
	"deletedAt" timestamp(3) NULL,
	description text NULL,
	CONSTRAINT "Brand_pkey" PRIMARY KEY (id)
);
CREATE UNIQUE INDEX "Brand_code_key" ON partner_service."Brand" USING btree (code);


-- partner_service."ImportHistory" definition

-- Drop table

-- DROP TABLE partner_service."ImportHistory";

CREATE TABLE partner_service."ImportHistory" (
	id text NOT NULL,
	"module" text NOT NULL,
	file jsonb NOT NULL,
	status text NOT NULL,
	errorlog jsonb NULL,
	metadata jsonb NOT NULL,
	"createdBy" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedBy" jsonb NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"deletedAt" timestamp(3) NULL,
	CONSTRAINT import_pkey PRIMARY KEY (id)
);


-- partner_service."Outbox" definition

-- Drop table

-- DROP TABLE partner_service."Outbox";

CREATE TABLE partner_service."Outbox" (
	id text NOT NULL,
	"aggregateId" text NULL,
	"aggregateType" text NULL,
	topic text NOT NULL,
	payload text NOT NULL,
	status text NOT NULL,
	"retryCount" int4 NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"processedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NULL,
	"errorLog" text NULL,
	CONSTRAINT "Outbox_pkey" PRIMARY KEY (id)
);


-- partner_service."Partner" definition

-- Drop table

-- DROP TABLE partner_service."Partner";

CREATE TABLE partner_service."Partner" (
	id text NOT NULL,
	code text NOT NULL,
	"name" jsonb NOT NULL,
	"taxId" text NOT NULL,
	"partnerType" text NOT NULL,
	"companyType" text NOT NULL,
	"sapCode" text NOT NULL,
	"pointCost" text NOT NULL,
	categories jsonb NOT NULL,
	address1 jsonb NOT NULL,
	address2 text NULL,
	attachments jsonb NOT NULL,
	contact jsonb NOT NULL,
	status text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"createdBy" jsonb NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"updatedBy" jsonb NOT NULL,
	"type" text DEFAULT 'EXTERNAL'::text NOT NULL,
	CONSTRAINT "Partner_pkey" PRIMARY KEY (id)
);
CREATE UNIQUE INDEX "Partner_code_key" ON partner_service."Partner" USING btree (code);


-- partner_service."PaymentEligibility" definition

-- Drop table

-- DROP TABLE partner_service."PaymentEligibility";

CREATE TABLE partner_service."PaymentEligibility" (
	id text NOT NULL,
	"refId" text NOT NULL,
	"refType" text NOT NULL,
	eligibility jsonb NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "PaymentEligibility_pkey" PRIMARY KEY (id)
);


-- partner_service."PaymentMethod" definition

-- Drop table

-- DROP TABLE partner_service."PaymentMethod";

CREATE TABLE partner_service."PaymentMethod" (
	id text NOT NULL,
	code text NOT NULL,
	"name" text NOT NULL,
	"type" text NOT NULL,
	description text NOT NULL,
	settings jsonb NOT NULL,
	"createdBy" text NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedBy" text NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"deletedAt" timestamp(3) NULL,
	CONSTRAINT "PaymentMethod_code_unique" UNIQUE (code),
	CONSTRAINT "PaymentMethod_pkey" PRIMARY KEY (id)
);


-- partner_service."SalesTransactionCouponUseLater" definition

-- Drop table

-- DROP TABLE partner_service."SalesTransactionCouponUseLater";

CREATE TABLE partner_service."SalesTransactionCouponUseLater" (
	id text NOT NULL,
	"salesTransactionId" int8 NOT NULL,
	"type" text NOT NULL,
	"subType" text NOT NULL,
	"memberCouponId" text NOT NULL,
	"promoCode" text NOT NULL,
	"sapCode" text NULL,
	value numeric(16, 2) NULL,
	settings jsonb NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	status text NULL,
	CONSTRAINT "SalesTransactionCouponUseLater_pkey" PRIMARY KEY (id)
);


-- partner_service."Branch" definition

-- Drop table

-- DROP TABLE partner_service."Branch";

CREATE TABLE partner_service."Branch" (
	id text NOT NULL,
	"partnerId" text NOT NULL,
	"brandId" text NOT NULL,
	code text NOT NULL,
	"name" jsonb NOT NULL,
	"branchType" text NOT NULL,
	latitude float8 NOT NULL,
	longitude float8 NOT NULL,
	"location" text NOT NULL,
	status text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"createdBy" jsonb NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"updatedBy" jsonb NOT NULL,
	"deletedAt" timestamp(3) NULL,
	"costCenterCode" text NULL,
	"businessAreaCode" text NULL,
	"branchCodeNo" text NULL,
	CONSTRAINT "Branch_pkey" PRIMARY KEY (id),
	CONSTRAINT "Branch_brandId_fkey" FOREIGN KEY ("brandId") REFERENCES partner_service."Brand"(id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT "Branch_partnerId_fkey" FOREIGN KEY ("partnerId") REFERENCES partner_service."Partner"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE UNIQUE INDEX "Branch_code_key" ON partner_service."Branch" USING btree (code);


-- partner_service."CostCenter" definition

-- Drop table

-- DROP TABLE partner_service."CostCenter";

CREATE TABLE partner_service."CostCenter" (
	id text NOT NULL,
	"partnerId" text NOT NULL,
	code text NOT NULL,
	"name" text NOT NULL,
	"businessAreaCode" text NOT NULL,
	"businessAreaName" text NOT NULL,
	"createdBy" text NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedBy" text NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"deletedAt" timestamp(3) NULL,
	CONSTRAINT "CostCenter_pkey" PRIMARY KEY (id),
	CONSTRAINT "CostCenter_partnerId_fkey" FOREIGN KEY ("partnerId") REFERENCES partner_service."Partner"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- partner_service."PartnerBrand" definition

-- Drop table

-- DROP TABLE partner_service."PartnerBrand";

CREATE TABLE partner_service."PartnerBrand" (
	id text NOT NULL,
	"partnerId" text NOT NULL,
	"brandId" text NOT NULL,
	"createdBy" jsonb NOT NULL,
	"updatedBy" jsonb NOT NULL,
	CONSTRAINT "PartnerBrand_pkey" PRIMARY KEY (id),
	CONSTRAINT "PartnerBrand_brandId_fkey" FOREIGN KEY ("brandId") REFERENCES partner_service."Brand"(id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT "PartnerBrand_partnerId_fkey" FOREIGN KEY ("partnerId") REFERENCES partner_service."Partner"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- partner_service."PartnerEligibility" definition

-- Drop table

-- DROP TABLE partner_service."PartnerEligibility";

CREATE TABLE partner_service."PartnerEligibility" (
	id text NOT NULL,
	"partnerId" text NOT NULL,
	"brandId" text NOT NULL,
	"refId" text NOT NULL,
	"refType" text NOT NULL,
	eligibility jsonb NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "PartnerEligibility_pkey" PRIMARY KEY (id),
	CONSTRAINT "PartnerEligibility_Brand_fkey" FOREIGN KEY ("brandId") REFERENCES partner_service."Brand"(id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT "PartnerEligibility_Partner_fkey" FOREIGN KEY ("partnerId") REFERENCES partner_service."Partner"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- partner_service."Product" definition

-- Drop table

-- DROP TABLE partner_service."Product";

CREATE TABLE partner_service."Product" (
	id text NOT NULL,
	"brandId" text NOT NULL,
	sku text NOT NULL,
	"name" text NOT NULL,
	"categoryCode" text NOT NULL,
	"brandCode" text NULL,
	settings jsonb NOT NULL,
	status text NOT NULL,
	"createdBy" text NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedBy" text NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"deletedAt" timestamp(3) NULL,
	CONSTRAINT "Product_pkey" PRIMARY KEY (id),
	CONSTRAINT "Product_brandId_fkey" FOREIGN KEY ("brandId") REFERENCES partner_service."Brand"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- partner_service."ProductBrand" definition

-- Drop table

-- DROP TABLE partner_service."ProductBrand";

CREATE TABLE partner_service."ProductBrand" (
	id text NOT NULL,
	"brandId" text NOT NULL,
	code text NOT NULL,
	"name" text NOT NULL,
	settings jsonb NOT NULL,
	"createdBy" text NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedBy" text NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"deletedAt" timestamp(3) NULL,
	CONSTRAINT "ProductBrand_pkey" PRIMARY KEY (id),
	CONSTRAINT "ProductBrand_Brand_fkey" FOREIGN KEY ("brandId") REFERENCES partner_service."Brand"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- partner_service."ProductCategory" definition

-- Drop table

-- DROP TABLE partner_service."ProductCategory";

CREATE TABLE partner_service."ProductCategory" (
	id text NOT NULL,
	"brandId" text NOT NULL,
	code text NOT NULL,
	"name" text NOT NULL,
	settings jsonb NOT NULL,
	"createdBy" text NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedBy" text NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"deletedAt" timestamp(3) NULL,
	CONSTRAINT "ProductCategory_pkey" PRIMARY KEY (id),
	CONSTRAINT "ProductCategory_Brand_fkey" FOREIGN KEY ("brandId") REFERENCES partner_service."Brand"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- partner_service."SalesTransaction" definition

-- Drop table

-- DROP TABLE partner_service."SalesTransaction";

CREATE TABLE partner_service."SalesTransaction" (
	id int8 DEFAULT nextval('partner_service.salestransaction_id_seq'::regclass) NOT NULL,
	"memberId" text NOT NULL,
	"gwlNo" text NOT NULL,
	"externalId" text NOT NULL,
	"taxInvoice" text NULL,
	"partnerId" text NOT NULL,
	"brandId" text NOT NULL,
	"branchId" text NOT NULL,
	"netTotalAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"totalOriginalPrice" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"totalCaratEarnableAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"totalCashbackEarnableAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"totalAccumSpendableAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"importTaxAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"shippingAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	status text NOT NULL,
	settings jsonb NOT NULL,
	"rawRequest" jsonb NULL,
	"completedAt" timestamp(3) NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"memberShoppingCardId" text NOT NULL,
	"refundStatus" text NULL,
	detail jsonb NULL,
	"documentDate" timestamp(3) NULL,
	CONSTRAINT "SalesTransaction_pkey" PRIMARY KEY (id),
	CONSTRAINT salestransaction_branch_fk FOREIGN KEY ("branchId") REFERENCES partner_service."Branch"(id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT salestransaction_brand_fk FOREIGN KEY ("brandId") REFERENCES partner_service."Brand"(id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT salestransaction_partner_fk FOREIGN KEY ("partnerId") REFERENCES partner_service."Partner"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE INDEX "MANUAL_SalesTransaction_branchId" ON partner_service."SalesTransaction" USING btree ("branchId");
CREATE INDEX "MANUAL_SalesTransaction_externalId" ON partner_service."SalesTransaction" USING btree ("externalId");
CREATE INDEX "MANUAL_SalesTransaction_memberId" ON partner_service."SalesTransaction" USING btree ("memberId");
CREATE INDEX salestransaction_gwlno_idx ON partner_service."SalesTransaction" USING btree ("gwlNo");


-- partner_service."SalesTransactionBurnPayment" definition

-- Drop table

-- DROP TABLE partner_service."SalesTransactionBurnPayment";

CREATE TABLE partner_service."SalesTransactionBurnPayment" (
	id text NOT NULL,
	"walletCode" text NOT NULL,
	"burnAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"beforeAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"afterAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"paymentAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	settings jsonb NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"salesTransactionId" int8 NOT NULL,
	"burnPurpose" text DEFAULT 'ITEM'::text NOT NULL,
	CONSTRAINT "SalesTransactionBurnPayment_pkey" PRIMARY KEY (id),
	CONSTRAINT salestransactionburnpayment_salestransaction_fk FOREIGN KEY ("salesTransactionId") REFERENCES partner_service."SalesTransaction"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- partner_service."SalesTransactionCoupon" definition

-- Drop table

-- DROP TABLE partner_service."SalesTransactionCoupon";

CREATE TABLE partner_service."SalesTransactionCoupon" (
	id text NOT NULL,
	"salesTransactionId" int8 NOT NULL,
	"couponSourceType" text NOT NULL,
	"type" text NULL,
	"subType" text NULL,
	"memberCouponId" text NULL,
	"promoCode" text NOT NULL,
	sapcode text NULL,
	value numeric(16, 2) DEFAULT 0.00 NULL,
	settings jsonb NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	status text NULL,
	CONSTRAINT "SalesTransactionCoupon_pkey" PRIMARY KEY (id),
	CONSTRAINT salestransactioncoupon_salestransaction_fk FOREIGN KEY ("salesTransactionId") REFERENCES partner_service."SalesTransaction"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- partner_service."SalesTransactionItem" definition

-- Drop table

-- DROP TABLE partner_service."SalesTransactionItem";

CREATE TABLE partner_service."SalesTransactionItem" (
	id text NOT NULL,
	"productId" text NULL,
	quantity int4 NOT NULL,
	"netAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"caratEarnableAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"normalPointEarned" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"burnPaymentAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	settings jsonb NOT NULL,
	"paymentDetail" jsonb NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"originalPrice" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"salesTransactionId" int8 NOT NULL,
	"cashbackEarnableAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"tierExtraPointEarned" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	sku text NULL,
	"couponExtraPointEarned" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	CONSTRAINT "SalesTransactionItem_pkey" PRIMARY KEY (id),
	CONSTRAINT salestransactionitem_salestransaction_fk FOREIGN KEY ("salesTransactionId") REFERENCES partner_service."SalesTransaction"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE INDEX "MANUAL_SalesTransactionItem_salesTransactionId" ON partner_service."SalesTransactionItem" USING btree ("salesTransactionId");


-- partner_service."SalesTransactionItemBurnPayment" definition

-- Drop table

-- DROP TABLE partner_service."SalesTransactionItemBurnPayment";

CREATE TABLE partner_service."SalesTransactionItemBurnPayment" (
	id text NOT NULL,
	"itemId" text NOT NULL,
	"burnAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"paymentAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	settings jsonb NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"burnPaymentId" text NOT NULL,
	CONSTRAINT "SalesTransactionItemBurnPayment_pkey" PRIMARY KEY (id),
	CONSTRAINT "SalesTransactionItemBurnPayment_Item_fkey" FOREIGN KEY ("itemId") REFERENCES partner_service."SalesTransactionItem"(id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT "SalesTransactionItemBurnPayment_burnPaymentId_fkey" FOREIGN KEY ("burnPaymentId") REFERENCES partner_service."SalesTransactionBurnPayment"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- partner_service."SalesTransactionItemCoupon" definition

-- Drop table

-- DROP TABLE partner_service."SalesTransactionItemCoupon";

CREATE TABLE partner_service."SalesTransactionItemCoupon" (
	id text NOT NULL,
	"itemId" text NOT NULL,
	amount numeric(16, 2) DEFAULT 0.00 NOT NULL,
	detail jsonb NOT NULL,
	"salesTransactionCouponId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "SalesTransactionItemCoupon_pkey" PRIMARY KEY (id),
	CONSTRAINT "SalesTransactionItemCoupon_item_fkey" FOREIGN KEY ("itemId") REFERENCES partner_service."SalesTransactionItem"(id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT "SalesTransactionItemCoupon_salesTransactionCouponId_fkey" FOREIGN KEY ("salesTransactionCouponId") REFERENCES partner_service."SalesTransactionCoupon"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- partner_service."SalesTransactionPayment" definition

-- Drop table

-- DROP TABLE partner_service."SalesTransactionPayment";

CREATE TABLE partner_service."SalesTransactionPayment" (
	id text NOT NULL,
	"paymentMethodId" text NOT NULL,
	amount numeric(16, 2) DEFAULT 0.00 NOT NULL,
	settings jsonb NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"salesTransactionId" int8 NOT NULL,
	CONSTRAINT "SalesTransactionPayment_pkey" PRIMARY KEY (id),
	CONSTRAINT salestransactionpayment_paymentmethod_fk FOREIGN KEY ("paymentMethodId") REFERENCES partner_service."PaymentMethod"(id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT salestransactionpayment_salestransaction_fk FOREIGN KEY ("salesTransactionId") REFERENCES partner_service."SalesTransaction"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- partner_service."SalesTransactionWalletActivity" definition

-- Drop table

-- DROP TABLE partner_service."SalesTransactionWalletActivity";

CREATE TABLE partner_service."SalesTransactionWalletActivity" (
	"salesTransactionId" int8 NOT NULL,
	"type" text NOT NULL,
	"activityId" text NOT NULL,
	detail jsonb NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	id text NOT NULL,
	CONSTRAINT "SalesTransactionWalletActivity_pkey" PRIMARY KEY (id),
	CONSTRAINT "salestransactionwalletactivity_activityId_unique" UNIQUE ("activityId"),
	CONSTRAINT salestransactionwalletactivity_salestransaction_fk FOREIGN KEY ("salesTransactionId") REFERENCES partner_service."SalesTransaction"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- partner_service."RefundSalesTransaction" definition

-- Drop table

-- DROP TABLE partner_service."RefundSalesTransaction";

CREATE TABLE partner_service."RefundSalesTransaction" (
	id text NOT NULL,
	"salesTransactionId" int8 NOT NULL,
	"type" text NOT NULL,
	"taxInvoices" text NULL,
	"externalId" text NULL,
	reason text NULL,
	"caratRefundAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"caratRevokeAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"cashbackRefundAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"cashbackRevokeAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"chargeBackAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"refundedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	detail jsonb NULL,
	"approvedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "RefundSalesTransaction_pkey" PRIMARY KEY (id),
	CONSTRAINT refundsalestransaction_salestransaction_fk FOREIGN KEY ("salesTransactionId") REFERENCES partner_service."SalesTransaction"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE INDEX "RefundSalesTransaction_type_idx" ON partner_service."RefundSalesTransaction" USING btree (type);


-- partner_service."RefundSalesTransactionItem" definition

-- Drop table

-- DROP TABLE partner_service."RefundSalesTransactionItem";

CREATE TABLE partner_service."RefundSalesTransactionItem" (
	id text NOT NULL,
	"refundSalesTransactionId" text NOT NULL,
	"salesTransactionItemId" text NOT NULL,
	quantity int4 NOT NULL,
	"refundWallets" jsonb NOT NULL,
	"revokeWallets" jsonb NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "RefundSalesTransactionItem_pkey" PRIMARY KEY (id),
	CONSTRAINT "RefundSalesTransactionItem_unique" UNIQUE ("refundSalesTransactionId", "salesTransactionItemId"),
	CONSTRAINT "RefundSalesTransactionItem_refundSalesTransactionId_fkey" FOREIGN KEY ("refundSalesTransactionId") REFERENCES partner_service."RefundSalesTransaction"(id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT "RefundSalesTransactionItem_salesTransactionItemId_fkey" FOREIGN KEY ("salesTransactionItemId") REFERENCES partner_service."SalesTransactionItem"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- partner_service."RefundSalesTransactionWalletActivity" definition

-- Drop table

-- DROP TABLE partner_service."RefundSalesTransactionWalletActivity";

CREATE TABLE partner_service."RefundSalesTransactionWalletActivity" (
	id text NOT NULL,
	"refundSalesTransactionId" text NOT NULL,
	"type" text NOT NULL,
	"activityId" text NOT NULL,
	detail jsonb NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "RefundSalesTransactionWalletActivity_pkey" PRIMARY KEY (id),
	CONSTRAINT "RefundSalesTransactionWalletActivity_RefundSalesTransaction_fke" FOREIGN KEY ("refundSalesTransactionId") REFERENCES partner_service."RefundSalesTransaction"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- partner_service."SalesTransactionCouponAdjustment" definition

-- Drop table

-- DROP TABLE partner_service."SalesTransactionCouponAdjustment";

CREATE TABLE partner_service."SalesTransactionCouponAdjustment" (
	id text NOT NULL,
	"refundSalesTransactionId" text NOT NULL,
	"type" text NOT NULL,
	"memberCouponId" text NOT NULL,
	"promoCode" text NOT NULL,
	detail jsonb NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "SalesTransactionCouponAdjustment_pkey" PRIMARY KEY (id),
	CONSTRAINT "SalesTransactionCouponAdjustment_RefundSalesTransaction_fkey" FOREIGN KEY ("refundSalesTransactionId") REFERENCES partner_service."RefundSalesTransaction"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- partner_service."WalletEligibleBranch" definition

-- Drop table

-- DROP TABLE partner_service."WalletEligibleBranch";

CREATE TABLE partner_service."WalletEligibleBranch" (
	id text NOT NULL,
	"branchCode" text NOT NULL,
	"walletCode" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "WalletEligibleBranch_pkey" PRIMARY KEY (id)
);
CREATE UNIQUE INDEX "WalletEligibleBranch_branchCode_walletCode_key" ON partner_service."WalletEligibleBranch" USING btree ("branchCode", "walletCode");


-- partner_service."WalletPaymentMethod" definition

-- Drop table

-- DROP TABLE partner_service."WalletPaymentMethod";

CREATE TABLE partner_service."WalletPaymentMethod" (
	"walletTypeCode" text NOT NULL,
	"paymentMethodCode" text NOT NULL,
	CONSTRAINT "WalletPaymentMethod_pkey" PRIMARY KEY ("walletTypeCode")
);
CREATE UNIQUE INDEX "WalletPaymentMethod_walletTypeCode_key" ON partner_service."WalletPaymentMethod" USING btree ("walletTypeCode");


-- partner_service."WalletEligibleBranch" foreign keys

ALTER TABLE partner_service."WalletEligibleBranch" ADD CONSTRAINT "WalletEligibleBranch_branchCode_fkey" FOREIGN KEY ("branchCode") REFERENCES partner_service."Branch"(code) ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE partner_service."WalletEligibleBranch" ADD CONSTRAINT "WalletEligibleBranch_walletCode_fkey" FOREIGN KEY ("walletCode") REFERENCES point_service."Wallet"(code) ON DELETE RESTRICT ON UPDATE CASCADE;


-- partner_service."WalletPaymentMethod" foreign keys

ALTER TABLE partner_service."WalletPaymentMethod" ADD CONSTRAINT "WalletPaymentMethod_paymentMethodCode_fkey" FOREIGN KEY ("paymentMethodCode") REFERENCES partner_service."PaymentMethod"(code) ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE partner_service."WalletPaymentMethod" ADD CONSTRAINT walletpaymentmethod_wallettype_fk FOREIGN KEY ("walletTypeCode") REFERENCES point_service."WalletType"(code);



-- DROP FUNCTION partner_service.lo_manage();

CREATE OR REPLACE FUNCTION partner_service.lo_manage()
 RETURNS trigger
 LANGUAGE c
AS '$libdir/lo', $function$lo_manage$function$
;

-- DROP FUNCTION partner_service.lo_oid(partner_service."lo");

CREATE OR REPLACE FUNCTION partner_service.lo_oid(partner_service.lo)
 RETURNS oid
 LANGUAGE sql
 IMMUTABLE PARALLEL SAFE STRICT
AS $function$SELECT $1::pg_catalog.oid$function$
;




-- get correct sequence tables to list here

"Partner"
"Brand"
"Branch"
"PartnerBrand"
"PaymentMethod"
"WalletPaymentMethod"
"SalesTransactionItemBurnPayment"
"SalesTransactionCoupon"
"SalesTransactionItemCoupon"
"WalletEligibleBranch"
"CostCenter"
"ImportHistory"
"RefundSalesTransactionWalletActivity"
"ProductCategory"
"ProductBrand"
"Product"
"Outbox"
"PartnerEligibility"
"PaymentEligibility"
"SalesTransactionCouponAdjustment"
"SalesTransactionCouponUseLater"
"SalesTransactionWalletActivity"
"SalesTransaction"
"RefundSalesTransaction"
"SalesTransactionItem"
"SalesTransactionBurnPayment"
"SalesTransactionPayment"
"RefundSalesTransactionItem"

-- table from gwl
Branch
Brand
CostCenter
ImportHistory
Outbox
Partner
PartnerBrand
PartnerEligibility
PaymentEligibility
PaymentMethod
Product
ProductBrand
ProductCategory
RefundSalesTransaction
RefundSalesTransactionItem
RefundSalesTransactionWalletActivity
SalesTransaction
SalesTransactionBurnPayment
SalesTransactionCoupon
SalesTransactionCouponAdjustment
SalesTransactionCouponUseLater
SalesTransactionItem
SalesTransactionItemBurnPayment
SalesTransactionItemCoupon
SalesTransactionPayment
SalesTransactionWalletActivity
WalletEligibleBranch
WalletPaymentMethod




-- create sql statement to insert data from gwl to staging
-- which columns and dblink like example

-- Partner
INSERT INTO "Partner"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."Partner"') AS 
t1(id text,
    code text,
    "name" jsonb,
    "taxId" text,
    "partnerType" text,
    "companyType" text,
    "sapCode" text,
    "pointCost" text,
    categories jsonb,
    address1 jsonb,
    address2 text,
    attachments jsonb,
    contact jsonb,
    status text,
    "createdAt" timestamp(3),
    "createdBy" jsonb,
    "updatedAt" timestamp(3),
    "updatedBy" jsonb,
    "type" text)
ON CONFLICT (code) DO UPDATE SET
    id = EXCLUDED.id,
    "name" = EXCLUDED."name",
    "taxId" = EXCLUDED."taxId",
    "partnerType" = EXCLUDED."partnerType",
    "companyType" = EXCLUDED."companyType",
    "sapCode" = EXCLUDED."sapCode",
    "pointCost" = EXCLUDED."pointCost",
    categories = EXCLUDED.categories,
    address1 = EXCLUDED.address1,
    address2 = EXCLUDED.address2,
    attachments = EXCLUDED.attachments,
    contact = EXCLUDED.contact,
    status = EXCLUDED.status,
    "createdAt" = EXCLUDED."createdAt",
    "createdBy" = EXCLUDED."createdBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "type" = EXCLUDED."type";


-- Brand
INSERT INTO "Brand"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."Brand"') AS 
t1(id text,
    code text,
    "name" jsonb,
    categories text,
    status text,
    "createdAt" timestamp(3),
    "createdBy" jsonb,
    "updatedAt" timestamp(3),
    "updatedBy" jsonb,
    logo jsonb,
    settings jsonb,
    "deletedAt" timestamp(3),
    description text)
ON CONFLICT (code) DO UPDATE SET
    id = EXCLUDED.id,
    "name" = EXCLUDED."name",
    categories = EXCLUDED.categories,
    status = EXCLUDED.status,
    "createdAt" = EXCLUDED."createdAt",
    "createdBy" = EXCLUDED."createdBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    logo = EXCLUDED.logo,
    settings = EXCLUDED.settings,
    "deletedAt" = EXCLUDED."deletedAt",
    description = EXCLUDED.description;


-- Branch
INSERT INTO "Branch"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."Branch"') AS 
t1(id text,
    "partnerId" text,
    "brandId" text,
    code text,
    "name" jsonb,
    "branchType" text,
    latitude float8,
    longitude float8,
    "location" text,
    status text,
    "createdAt" timestamp(3),
    "createdBy" jsonb,
    "updatedAt" timestamp(3),
    "updatedBy" jsonb,
    "deletedAt" timestamp(3),
    "costCenterCode" text,
    "businessAreaCode" text,
    "branchCodeNo" text)
ON CONFLICT (code) DO UPDATE SET
    id = EXCLUDED.id,
    "partnerId" = EXCLUDED."partnerId",
    "brandId" = EXCLUDED."brandId",
    "name" = EXCLUDED."name",
    "branchType" = EXCLUDED."branchType",
    latitude = EXCLUDED.latitude,
    longitude = EXCLUDED.longitude,
    "location" = EXCLUDED."location",
    status = EXCLUDED.status,
    "createdAt" = EXCLUDED."createdAt",
    "createdBy" = EXCLUDED."createdBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "deletedAt" = EXCLUDED."deletedAt",
    "costCenterCode" = EXCLUDED."costCenterCode",
    "businessAreaCode" = EXCLUDED."businessAreaCode",
    "branchCodeNo" = EXCLUDED."branchCodeNo";


-- "PartnerBrand"
INSERT INTO "PartnerBrand"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."PartnerBrand"') AS 
t1("partnerId" text,
    "brandId" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT ("partnerId", "brandId") DO UPDATE SET
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";


-- PaymentMethod
INSERT INTO "PaymentMethod"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."PaymentMethod"') AS 
t1(id text,
    code text,
    "name" text,
    "type" text,
    description text,
    settings jsonb,
    "createdBy" text,
    "createdAt" timestamp(3),
    "updatedBy" text,
    "updatedAt" timestamp(3),
    "deletedAt" timestamp(3))
ON CONFLICT (code) DO UPDATE SET
    id = EXCLUDED.id,
    "name" = EXCLUDED."name",
    "type" = EXCLUDED."type",
    description = EXCLUDED.description,
    settings = EXCLUDED.settings,
    "createdBy" = EXCLUDED."createdBy",
    "createdAt" = EXCLUDED."createdAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt";


-- WalletPaymentMethod
INSERT INTO "WalletPaymentMethod"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."WalletPaymentMethod"') AS 
t1("walletCode" text,
    "paymentMethodId" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT ("walletCode", "paymentMethodId") DO UPDATE SET
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";


-- SalesTransactionItemBurnPayment
INSERT INTO "SalesTransactionItemBurnPayment"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."SalesTransactionItemBurnPayment"') AS 
t1(id text,
    "itemId" text,
    "burnAmount" numeric(16, 2),
    "paymentAmount" numeric(16, 2),
    settings jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "burnPaymentId" text)
ON CONFLICT (id) DO UPDATE SET
    "itemId" = EXCLUDED."itemId",
    "burnAmount" = EXCLUDED."burnAmount",
    "paymentAmount" = EXCLUDED."paymentAmount",
    settings = EXCLUDED.settings,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "burnPaymentId" = EXCLUDED."burnPaymentId";


-- SalesTransactionCoupon
INSERT INTO "SalesTransactionCoupon"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."SalesTransactionCoupon"') AS 
t1(id text,
    "salesTransactionId" int8,
    "type" text,
    "subType" text,
    "memberCouponId" text,
    "promoCode" text,
    "sapCode" text,
    value numeric(16, 2),
    settings jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    status text)
ON CONFLICT (id) DO UPDATE SET
    "salesTransactionId" = EXCLUDED."salesTransactionId",
    "type" = EXCLUDED."type",
    "subType" = EXCLUDED."subType",
    "memberCouponId" = EXCLUDED."memberCouponId",
    "promoCode" = EXCLUDED."promoCode",
    "sapCode" = EXCLUDED."sapCode",
    value = EXCLUDED.value,
    settings = EXCLUDED.settings,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    status = EXCLUDED.status;


-- SalesTransactionItemCoupon
INSERT INTO "SalesTransactionItemCoupon"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."SalesTransactionItemCoupon"') AS 
t1(id text,
    "salesTransactionItemId" text,
    "couponId" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "salesTransactionItemId" = EXCLUDED."salesTransactionItemId",
    "couponId" = EXCLUDED."couponId",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";


-- WalletEligibleBranch
INSERT INTO "WalletEligibleBranch"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."WalletEligibleBranch"') AS 
t1(id text,
    "branchCode" text,
    "walletCode" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "branchCode" = EXCLUDED."branchCode",
    "walletCode" = EXCLUDED."walletCode",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";


-- CostCenter
INSERT INTO "CostCenter"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."CostCenter"') AS 
t1(id text,
    "partnerId" text,
    code text,
    "name" text,
    "businessAreaCode" text,
    "businessAreaName" text,
    "createdBy" text,
    "createdAt" timestamp(3),
    "updatedBy" text,
    "updatedAt" timestamp(3),
    "deletedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "partnerId" = EXCLUDED."partnerId",
    code = EXCLUDED.code,
    "name" = EXCLUDED."name",
    "businessAreaCode" = EXCLUDED."businessAreaCode",
    "businessAreaName" = EXCLUDED."businessAreaName",
    "createdBy" = EXCLUDED."createdBy",
    "createdAt" = EXCLUDED."createdAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt";

-- ImportHistory
INSERT INTO "ImportHistory"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."ImportHistory"') AS 
t1(id text,
    detail jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    detail = EXCLUDED.detail,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- RefundSalesTransactionWalletActivity
INSERT INTO "RefundSalesTransactionWalletActivity"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."RefundSalesTransactionWalletActivity"') AS 
t1(id text,
    "refundSalesTransactionId" text,
    "type" text,
    "activityId" text,
    detail jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "refundSalesTransactionId" = EXCLUDED."refundSalesTransactionId",
    "type" = EXCLUDED."type",
    "activityId" = EXCLUDED."activityId",
    detail = EXCLUDED.detail,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- ProductCategory
INSERT INTO "ProductCategory"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."ProductCategory"') AS 
t1(id text,
    "brandId" text,
    code text,
    "name" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "brandId" = EXCLUDED."brandId",
    code = EXCLUDED.code,
    "name" = EXCLUDED."name",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- ProductBrand
INSERT INTO "ProductBrand"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."ProductBrand"') AS 
t1(id text,
    "productId" text,
    "brandId" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "productId" = EXCLUDED."productId",
    "brandId" = EXCLUDED."brandId",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- Product
INSERT INTO "Product"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."Product"') AS 
t1(id text,
    code text,
    "brandId" text,
    "categoryId" text,
    "name" text,
    description text,
    status text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    code = EXCLUDED.code,
    "brandId" = EXCLUDED."brandId",
    "categoryId" = EXCLUDED."categoryId",
    "name" = EXCLUDED."name",
    description = EXCLUDED.description,
    status = EXCLUDED.status,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- Outbox
INSERT INTO "Outbox"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."Outbox"') AS 
t1(id text,
    "eventId" text,
    payload jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "eventId" = EXCLUDED."eventId",
    payload = EXCLUDED.payload,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- PartnerEligibility
INSERT INTO "PartnerEligibility"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."PartnerEligibility"') AS 
t1(id text,
    "partnerId" text,
    eligibility jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "partnerId" = EXCLUDED."partnerId",
    eligibility = EXCLUDED.eligibility,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- PaymentEligibility
INSERT INTO "PaymentEligibility"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."PaymentEligibility"') AS 
t1(id text,
    "refId" text,
    "refType" text,
    eligibility jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "refId" = EXCLUDED."refId",
    "refType" = EXCLUDED."refType",
    eligibility = EXCLUDED.eligibility,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- SalesTransactionCouponAdjustment
INSERT INTO "SalesTransactionCouponAdjustment"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."SalesTransactionCouponAdjustment"') AS 
t1(id text,
    "refundSalesTransactionId" text,
    "type" text,
    "memberCouponId" text,
    "promoCode" text,
    detail jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "refundSalesTransactionId" = EXCLUDED."refundSalesTransactionId",
    "type" = EXCLUDED."type",
    "memberCouponId" = EXCLUDED."memberCouponId",
    "promoCode" = EXCLUDED."promoCode",
    detail = EXCLUDED.detail,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- SalesTransactionCouponUseLater
INSERT INTO "SalesTransactionCouponUseLater"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."SalesTransactionCouponUseLater"') AS 
t1(id text,
    "salesTransactionId" int8,
    "type" text,
    "subType" text,
    "memberCouponId" text,
    "promoCode" text,
    "sapCode" text,
    value numeric(16, 2),
    settings jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    status text)
ON CONFLICT (id) DO UPDATE SET
    "salesTransactionId" = EXCLUDED."salesTransactionId",
    "type" = EXCLUDED."type",
    "subType" = EXCLUDED."subType",
    "memberCouponId" = EXCLUDED."memberCouponId",
    "promoCode" = EXCLUDED."promoCode",
    "sapCode" = EXCLUDED."sapCode",
    value = EXCLUDED.value,
    settings = EXCLUDED.settings,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    status = EXCLUDED.status;

-- SalesTransactionWalletActivity
INSERT INTO "SalesTransactionWalletActivity"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."SalesTransactionWalletActivity"') AS 
t1(id text,
    "salesTransactionId" int8,
    "type" text,
    "activityId" text,
    detail jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "salesTransactionId" = EXCLUDED."salesTransactionId",
    "type" = EXCLUDED."type",
    "activityId" = EXCLUDED."activityId",
    detail = EXCLUDED.detail,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- SalesTransaction
INSERT INTO "SalesTransaction"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."SalesTransaction"') AS 
t1(id int8,
    "partnerId" text,
    "branchId" text,
    "transactionDate" timestamp(3),
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "partnerId" = EXCLUDED."partnerId",
    "branchId" = EXCLUDED."branchId",
    "transactionDate" = EXCLUDED."transactionDate",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- RefundSalesTransaction
INSERT INTO "RefundSalesTransaction"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."RefundSalesTransaction"') AS 
t1(id text,
    "salesTransactionId" int8,
    "type" text,
    "taxInvoices" text,
    "externalId" text,
    reason text,
    "caratRefundAmount" numeric(16, 2),
    "caratRevokeAmount" numeric(16, 2),
    "cashbackRefundAmount" numeric(16, 2),
    "cashbackRevokeAmount" numeric(16, 2),
    "chargeBackAmount" numeric(16, 2),
    "refundedAt" timestamp(3),
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    detail jsonb,
    "approvedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "salesTransactionId" = EXCLUDED."salesTransactionId",
    "type" = EXCLUDED."type",
    "taxInvoices" = EXCLUDED."taxInvoices",
    "externalId" = EXCLUDED."externalId",
    reason = EXCLUDED.reason,
    "caratRefundAmount" = EXCLUDED."caratRefundAmount",
    "caratRevokeAmount" = EXCLUDED."caratRevokeAmount",
    "cashbackRefundAmount" = EXCLUDED."cashbackRefundAmount",
    "cashbackRevokeAmount" = EXCLUDED."cashbackRevokeAmount",
    "chargeBackAmount" = EXCLUDED."chargeBackAmount",
    "refundedAt" = EXCLUDED."refundedAt",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    detail = EXCLUDED.detail,
    "approvedAt" = EXCLUDED."approvedAt";

-- SalesTransactionItem
INSERT INTO "SalesTransactionItem"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."SalesTransactionItem"') AS 
t1(id text,
    "productId" text,
    quantity int4,
    "netAmount" numeric(16, 2),
    "caratEarnableAmount" numeric(16, 2),
    "normalPointEarned" numeric(16, 2),
    "burnPaymentAmount" numeric(16, 2),
    settings jsonb,
    "paymentDetail" jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "originalPrice" numeric(16, 2),
    "salesTransactionId" int8,
    "cashbackEarnableAmount" numeric(16, 2),
    "tierExtraPointEarned" numeric(16, 2),
    sku text,
    "couponExtraPointEarned" numeric(16, 2))
ON CONFLICT (id) DO UPDATE SET
    "productId" = EXCLUDED."productId",
    quantity = EXCLUDED.quantity,
    "netAmount" = EXCLUDED."netAmount",
    "caratEarnableAmount" = EXCLUDED."caratEarnableAmount",
    "normalPointEarned" = EXCLUDED."normalPointEarned",
    "burnPaymentAmount" = EXCLUDED."burnPaymentAmount",
    settings = EXCLUDED.settings,
    "paymentDetail" = EXCLUDED."paymentDetail",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "originalPrice" = EXCLUDED."originalPrice",
    "salesTransactionId" = EXCLUDED."salesTransactionId",
    "cashbackEarnableAmount" = EXCLUDED."cashbackEarnableAmount",
    "tierExtraPointEarned" = EXCLUDED."tierExtraPointEarned",
    sku = EXCLUDED.sku,
    "couponExtraPointEarned" = EXCLUDED."couponExtraPointEarned";

-- SalesTransactionBurnPayment
INSERT INTO "SalesTransactionBurnPayment"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."SalesTransactionBurnPayment"') AS 
t1(id text,
    "walletCode" text,
    "burnAmount" numeric(16, 2),
    "beforeAmount" numeric(16, 2),
    "afterAmount" numeric(16, 2),
    "paymentAmount" numeric(16, 2),
    settings jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "salesTransactionId" int8,
    "burnPurpose" text)
ON CONFLICT (id) DO UPDATE SET
    "walletCode" = EXCLUDED."walletCode",
    "burnAmount" = EXCLUDED."burnAmount",
    "beforeAmount" = EXCLUDED."beforeAmount",
    "afterAmount" = EXCLUDED."afterAmount",
    "paymentAmount" = EXCLUDED."paymentAmount",
    settings = EXCLUDED.settings,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "salesTransactionId" = EXCLUDED."salesTransactionId",
    "burnPurpose" = EXCLUDED."burnPurpose";

-- SalesTransactionPayment
INSERT INTO "SalesTransactionPayment"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."SalesTransactionPayment"') AS 
t1(id text,
    "paymentMethodId" text,
    amount numeric(16, 2),
    settings jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "salesTransactionId" int8)
ON CONFLICT (id) DO UPDATE SET
    "paymentMethodId" = EXCLUDED."paymentMethodId",
    amount = EXCLUDED.amount,
    settings = EXCLUDED.settings,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "salesTransactionId" = EXCLUDED."salesTransactionId";

-- RefundSalesTransactionItem
INSERT INTO "RefundSalesTransactionItem"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."RefundSalesTransactionItem"') AS 
t1(id text,
    "refundSalesTransactionId" text,
    "salesTransactionItemId" text,
    quantity int4,
    "refundWallets" jsonb,
    "revokeWallets" jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "refundSalesTransactionId" = EXCLUDED."refundSalesTransactionId",
    "salesTransactionItemId" = EXCLUDED."salesTransactionItemId",
    quantity = EXCLUDED.quantity,
    "refundWallets" = EXCLUDED."refundWallets",
    "revokeWallets" = EXCLUDED."revokeWallets",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";
