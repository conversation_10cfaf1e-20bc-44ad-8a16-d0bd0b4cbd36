INSERT into public."RefundSalesTransactionItem" (
    "id",
    "refundSalesTransactionId",
    "salesTransactionItemId",
    "quantity",
    "refundWallets",
    "revokeWallets",
    "createdAt",
    "updatedAt"
)
SELECT
    t1."id",
    t1."refundSalesTransactionId",
    t1."salesTransactionItemId",
    t1."quantity",
    t1."refundWallets",
    t1."revokeWallets",
    t1."createdAt",
    t1."updatedAt"
FROM dblink(
    'my_connection', 
    'SELECT 
        *
     FROM partner_service."RefundSalesTransactionItem"'
) AS t1 (
    "id"	text,
    "refundSalesTransactionId"	text,
    "salesTransactionItemId"	text,
    "quantity"	int4,
    "refundWallets"	jsonb,
    "revokeWallets"	jsonb,
    "createdAt"	timestamp(3),
    "updatedAt"	timestamp(3)
)
INNER JOIN public."RefundSalesTransaction" rst ON rst.id = t1."refundSalesTransactionId"

ON CONFLICT (id) DO UPDATE SET
    "refundSalesTransactionId" = EXCLUDED."refundSalesTransactionId",
    "salesTransactionItemId" = EXCLUDED."salesTransactionItemId",
    "quantity" = EXCLUDED."quantity",
    "refundWallets" = EXCLUDED."refundWallets",
    "revokeWallets" = EXCLUDED."revokeWallets",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";