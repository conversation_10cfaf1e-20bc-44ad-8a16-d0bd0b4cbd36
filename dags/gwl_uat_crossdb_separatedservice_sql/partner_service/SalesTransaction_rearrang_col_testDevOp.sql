INSERT into public."SalesTransaction" (
    "memberId"
    ,"gwlNo"
    ,"externalId"
    ,"taxInvoice"
    ,"partnerId"
    ,"brandId"
    ,"branchId"
    ,"netTotalAmount"
    ,"totalCaratEarnableAmount"
    ,"totalAccumSpendableAmount"
    ,"status"
    ,"settings"
    ,"rawRequest"
    ,"createdAt"
    ,"updatedAt"
    ,"totalOriginalPrice"
    ,"completedAt"
    ,"id"
    ,"shippingAmount"
    ,"detail"
    ,"totalCashbackEarnableAmount"
    ,"memberShoppingCardId"
    ,"refundStatus"
    ,"documentDate"
    ,"importTaxAmount"
    ,"createdBy"
    ,"updatedBy"
)
SELECT
    "memberId"
    ,"gwlNo"
    ,"externalId"
    ,"taxInvoice"
    ,"partnerId"
    ,"brandId"
    ,"branchId"
    ,"netTotalAmount"
    ,"totalCaratEarnableAmount"
    ,"totalAccumSpendableAmount"
    ,"status"
    ,"settings"
    ,"rawRequest"
    ,"createdAt"
    ,"updatedAt"
    ,"totalOriginalPrice"
    ,"completedAt"
    ,"id"
    ,"shippingAmount"
    ,"detail"
    ,"totalCashbackEarnableAmount"
    ,"memberShoppingCardId"
    ,"refundStatus"
    ,"documentDate"
    ,"importTaxAmount"

    ,jsonb_build_object(
        'id', NULL,
        'name', 'SYSTEM',
        'email', NULL
    ) AS "createdBy"
    ,jsonb_build_object(
        'id', NULL,
            'name', 'SYSTEM',
            'email', NULL
    ) AS "updatedBy"
FROM dblink(
    'my_connection', 
    'SELECT
        *
    FROM partner_service."SalesTransaction"'
)  AS t1 (
    "id"	bigint,
    "memberId"	text,
    "gwlNo"	text,
    "externalId"	text,
    "taxInvoice"	text,
    "partnerId"	text,
    "brandId"	text,
    "branchId"	text,
    "netTotalAmount"	numeric(16,2),
    "totalOriginalPrice"	numeric(16,2),
    "totalCaratEarnableAmount"	numeric(16,2),
    "totalCashbackEarnableAmount"	numeric(16,2),
    "totalAccumSpendableAmount"	numeric(16,2),
    "importTaxAmount"	numeric(16,2),
    "shippingAmount"	numeric(16,2),
    "status"	text,
    "settings"	jsonb,
    "rawRequest"	jsonb,
    "completedAt"	timestamp(3),
    "createdAt"	timestamp(3),
    "updatedAt"	timestamp(3),
    "memberShoppingCardId"	text,
    "refundStatus"	text,
    "detail"	jsonb,
    "documentDate"	timestamp(3)
)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "gwlNo" = EXCLUDED."gwlNo",
    "externalId" = EXCLUDED."externalId",
    "taxInvoice" = EXCLUDED."taxInvoice",
    "partnerId" = EXCLUDED."partnerId",
    "brandId" = EXCLUDED."brandId",
    "branchId" = EXCLUDED."branchId",
    "netTotalAmount" = EXCLUDED."netTotalAmount",
    "totalCaratEarnableAmount" = EXCLUDED."totalCaratEarnableAmount",
    "totalAccumSpendableAmount" = EXCLUDED."totalAccumSpendableAmount",
    "status" = EXCLUDED."status",
    "settings" = EXCLUDED."settings",
    "rawRequest" = EXCLUDED."rawRequest",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "totalOriginalPrice" = EXCLUDED."totalOriginalPrice",
    "completedAt" = EXCLUDED."completedAt",
    "shippingAmount" = EXCLUDED."shippingAmount",
    "detail" = EXCLUDED."detail",
    "totalCashbackEarnableAmount" = EXCLUDED."totalCashbackEarnableAmount",
    "memberShoppingCardId" = EXCLUDED."memberShoppingCardId",
    "refundStatus" = EXCLUDED."refundStatus",
    "documentDate" = EXCLUDED."documentDate",
    "importTaxAmount" = EXCLUDED."importTaxAmount",
    "createdBy" = EXCLUDED."createdBy",
    "updatedBy" = EXCLUDED."updatedBy";
