INSERT into public."PaymentMethod" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."PaymentMethod"')  AS 
t1(id text,
    code text,
    "name" text,
    "type" text,
    description text,
    settings jsonb,
    "createdBy" text,
    "createdAt" timestamp(3),
    "updatedBy" text,
    "updatedAt" timestamp(3),
    "deletedAt" timestamp(3),
    "setting_properties_isEarnable" bool,
    "setting_properties_isAccumSpendable" bool,
    "setting_effectiveAt" timestamp(3),
    "setting_createdAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    code = EXCLUDED.code,
    "name" = EXCLUDED."name",
    "type" = EXCLUDED."type",
    description = EXCLUDED.description,
    settings = EXCLUDED.settings,
    "createdBy" = EXCLUDED."createdBy",
    "createdAt" = EXCLUDED."createdAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "deletedAt" = EXCLUDED."deletedAt",
    "setting_properties_isEarnable" = EXCLUDED."setting_properties_isEarnable",
    "setting_properties_isAccumSpendable" = EXCLUDED."setting_properties_isAccumSpendable",
    "setting_effectiveAt" = EXCLUDED."setting_effectiveAt",
    "setting_createdAt" = EXCLUDED."setting_createdAt";
