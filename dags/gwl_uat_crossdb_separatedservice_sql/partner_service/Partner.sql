INSERT into public."Partner"
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."Partner"') AS
t1(id text,
    code text,
    "name" text,
    "taxId" text,
    "partnerType" text,
    "companyType" text,
    "sapCode" text,
    "pointCost" text,
    categories jsonb,
    address1 jsonb,
    address2 text,
    attachments jsonb,
    contact jsonb,
    status text,
    "createdAt" timestamp(3),
    "createdBy" jsonb,
    "updatedAt" timestamp(3),
    "updatedBy" jsonb,
    "type" text)
ON CONFLICT (id) DO UPDATE SET
    code = EXCLUDED.code,
    "name" = EXCLUDED."name",
    "taxId" = EXCLUDED."taxId",
    "partnerType" = EXCLUDED."partnerType",
    "companyType" = EXCLUDED."companyType",
    "sapCode" = EXCLUDED."sapCode",
    "pointCost" = EXCLUDED."pointCost",
    categories = EXCLUDED.categories,
    address1 = EXCLUDED.address1,
    address2 = EXCLUDED.address2,
    attachments = EXCLUDED.attachments,
    contact = EXCLUDED.contact,
    status = EXCLUDED.status,
    "createdAt" = EXCLUDED."createdAt",
    "createdBy" = EXCLUDED."createdBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "type" = EXCLUDED."type";

