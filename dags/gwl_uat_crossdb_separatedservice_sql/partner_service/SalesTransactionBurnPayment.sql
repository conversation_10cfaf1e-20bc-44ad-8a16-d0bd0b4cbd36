INSERT into public."SalesTransactionBurnPayment" (
    "id",
    "walletCode",
    "burnAmount",
    "beforeAmount",
    "afterAmount",
    "paymentAmount",
    "settings",
    "createdAt",
    "updatedAt",
    "salesTransactionId",
    "burnPurpose"
)
SELECT 
    t1."id",
    t1."walletCode",
    t1."burnAmount",
    t1."beforeAmount",
    t1."afterAmount",
    t1."paymentAmount",
    t1."settings",
    t1."createdAt",
    t1."updatedAt",
    t1."salesTransactionId",
    t1."burnPurpose"
FROM dblink(
    'my_connection',
    'SELECT
        *
    FROM partner_service."SalesTransactionBurnPayment"'
)  AS t1 (
    id text,
    "walletCode" text,
    "burnAmount" numeric(16, 2),
    "beforeAmount" numeric(16, 2),
    "afterAmount" numeric(16, 2),
    "paymentAmount" numeric(16, 2),
    settings jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "salesTransactionId" bigint,
    "burnPurpose" text
)
INNER JOIN public."SalesTransaction" st ON st.id = t1."salesTransactionId"

ON CONFLICT (id) DO UPDATE SET
    "walletCode" = EXCLUDED."walletCode",
    "burnAmount" = EXCLUDED."burnAmount",
    "beforeAmount" = EXCLUDED."beforeAmount",
    "afterAmount" = EXCLUDED."afterAmount",
    "paymentAmount" = EXCLUDED."paymentAmount",
    settings = EXCLUDED.settings,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "salesTransactionId" = EXCLUDED."salesTransactionId",
    "burnPurpose" = EXCLUDED."burnPurpose";
