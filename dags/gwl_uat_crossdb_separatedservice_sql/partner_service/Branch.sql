INSERT into public."Branch" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."Branch"')  AS 
t1(id text,
    "partnerId" text,
    "brandId" text,
    code text,
    "name" jsonb,
    "branchType" text,
    latitude float8,
    longitude float8,
    "location" text,
    status text,
    "createdAt" timestamp(3),
    "createdBy" jsonb,
    "updatedAt" timestamp(3),
    "updatedBy" jsonb,
    "deletedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "partnerId" = EXCLUDED."partnerId",
    "brandId" = EXCLUDED."brandId",
    code = EXCLUDED.code,
    "name" = EXCLUDED."name",
    "branchType" = EXCLUDED."branchType",
    latitude = EXCLUDED.latitude,
    longitude = EXCLUDED.longitude,
    "location" = EXCLUDED."location",
    status = EXCLUDED.status,
    "createdAt" = EXCLUDED."createdAt",
    "createdBy" = EXCLUDED."createdBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "deletedAt" = EXCLUDED."deletedAt";

