INSERT into public."WalletEligibleBranch" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM partner_service."WalletEligibleBranch"')  AS 
t1(id text,
    "branchCode" text,
    "walletCode" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "branchCode" = EXCLUDED."branchCode",
    "walletCode" = EXCLUDED."walletCode",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

