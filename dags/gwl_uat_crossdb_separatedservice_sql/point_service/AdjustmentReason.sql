INSERT into public."AdjustmentReason" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM point_service."AdjustmentReason"')  AS 
t1(code text,
    "name" text,
    "type" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (code) DO UPDATE SET
    "name" = EXCLUDED."name",
    "type" = EXCLUDED."type",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

