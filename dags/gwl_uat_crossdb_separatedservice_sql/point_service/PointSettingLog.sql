-- PointSettingLog
INSERT INTO public."PointSettingLog"
SELECT * FROM dblink('my_connection', 'SELECT * FROM point_service."PointSettingLog"') AS 
t1(id text,
    "updatedBy" jsonb,
    "oldData" jsonb,
    "newData" jsonb,
    "pointSettingId" text,
    detail text,
    "effectiveDate" timestamp(3),
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "updatedBy" = EXCLUDED."updatedBy",
    "oldData" = EXCLUDED."oldData",
    "newData" = EXCLUDED."newData",
    "pointSettingId" = EXCLUDED."pointSettingId",
    detail = EXCLUDED.detail,
    "effectiveDate" = EXCLUDED."effectiveDate",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";