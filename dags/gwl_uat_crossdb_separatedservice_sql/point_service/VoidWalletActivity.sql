-- VoidWalletActivity
INSERT INTO public."VoidWalletActivity"
SELECT * FROM dblink('my_connection', 'SELECT * FROM point_service."VoidWalletActivity"') AS 
t1(id text,
    "VoidWalletActivityId" text,
    "parentWalletActivityId" text,
    "createdAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "VoidWalletActivityId" = EXCLUDED."VoidWalletActivityId",
    "parentWalletActivityId" = EXCLUDED."parentWalletActivityId",
    "createdAt" = EXCLUDED."createdAt";