-- WalletActivity
INSERT INTO public."WalletActivity" (
    "id",    
    "memberId",
    "walletCode",
    "type",
    "refType",
    "refId",
    "externalId",
    "amount",
    "partnerCode",
    "brandCode",
    "branchCode",
    "detail",
    "createdAt",
    "afterBalance",
    "beforeBalance",
    "deletedAt",
    "documentDate",
    "remark",
    "updatedAt"
    -- "pointCost"
)
SELECT
    "id",    
    "memberId",
    "walletCode",
    "type",
    "refType",
    "refId",
    "externalId",
    "amount",
    "partnerCode",
    "brandCode",
    "branchCode",
    "detail",
    "createdAt",
    "afterBalance",
    "beforeBalance",
    "deletedAt",
    "documentDate",
    "remark",
    "updatedAt"
    -- "pointCost"
FROM dblink('my_connection', 'SELECT * FROM point_service."WalletActivity"') AS 
t1 (
    id text,
    "memberId" text,
    "walletCode" text,
    "type" text,
    "refType" text,
    "refId" text,
    "externalId" text,
    amount numeric(16, 2),
    "partnerCode" text,
    "brandCode" text,
    "branchCode" text,
    detail jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "documentDate" timestamp(3),
    "beforeBalance" numeric(16, 2),
    "afterBalance" numeric(16, 2),
    remark text,
    "deletedAt" timestamp(3)
)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "walletCode" = EXCLUDED."walletCode",
    "type" = EXCLUDED."type",
    "refType" = EXCLUDED."refType",
    "refId" = EXCLUDED."refId",
    "externalId" = EXCLUDED."externalId",
    amount = EXCLUDED.amount,
    "partnerCode" = EXCLUDED."partnerCode",
    "brandCode" = EXCLUDED."brandCode",
    "branchCode" = EXCLUDED."branchCode",
    detail = EXCLUDED.detail,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "documentDate" = EXCLUDED."documentDate",
    "beforeBalance" = EXCLUDED."beforeBalance",
    "afterBalance" = EXCLUDED."afterBalance",
    remark = EXCLUDED.remark,
    "deletedAt" = EXCLUDED."deletedAt"
    -- "pointCost" = EXCLUDED."pointCost"
    ;
