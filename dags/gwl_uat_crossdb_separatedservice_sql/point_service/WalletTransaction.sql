-- WalletTransaction
INSERT INTO public."WalletTransaction" (
    "id",
    "memberId",
    "walletActivityId",
    "balanceId",
    "type",
    "walletCode",
    "amount",
    "expiredAt",
    "createdAt"
)
SELECT
    t1."id",
    t1."memberId",
    t1."walletActivityId",
    t1."balanceId",
    t1."type",
    t1."walletCode",
    t1."amount",
    t1."expiredAt",
    t1."createdAt"
FROM dblink('my_connection', 'SELECT * FROM point_service."WalletTransaction"') AS 
t1(
    id text,
    "memberId" text,
    "walletActivityId" text,
    "balanceId" text,
    "type" text,
    "walletCode" text,
    amount numeric(16, 2),
    "expiredAt" timestamp(3),
    "createdAt" timestamp(3)
)
INNER JOIN public."WalletBalance" wb ON t1."balanceId" = wb.id
INNER JOIN public."WalletActivity" wa ON t1."walletActivityId" = wa.id

ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "walletActivityId" = EXCLUDED."walletActivityId",
    "balanceId" = EXCLUDED."balanceId",
    "type" = EXCLUDED."type",
    "walletCode" = EXCLUDED."walletCode",
    amount = EXCLUDED.amount,
    "expiredAt" = EXCLUDED."expiredAt",
    "createdAt" = EXCLUDED."createdAt";
