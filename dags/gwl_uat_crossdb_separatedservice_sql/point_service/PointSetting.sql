-- PointSettingUpdateRequest
INSERT INTO public."PointSettingUpdateRequest"
SELECT * FROM dblink('my_connection', 'SELECT * FROM point_service."PointSettingUpdateRequest"') AS 
t1(id text,
    "oldData" jsonb,
    "newData" jsonb,
    "pointSettingId" text,
    detail text,
    "effectiveDate" timestamp(3),
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "updatedBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "oldData" = EXCLUDED."oldData",
    "newData" = EXCLUDED."newData",
    "pointSettingId" = EXCLUDED."pointSettingId",
    detail = EXCLUDED.detail,
    "effectiveDate" = EXCLUDED."effectiveDate",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy";