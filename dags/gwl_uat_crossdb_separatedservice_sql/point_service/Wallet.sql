-- Wallet
INSERT INTO public."Wallet"
SELECT * FROM dblink('my_connection', 'SELECT * FROM point_service."Wallet"') AS 
t1(id text,
    "runningId" int4,
    code text,
    "name" text,
    "walletTypeCode" text,
    status text,
    description text,
    currency text,
    image jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "updatedBy" jsonb)
ON CONFLICT (id, code) DO UPDATE SET
    "runningId" = EXCLUDED."runningId",
    "name" = EXCLUDED."name",
    "walletTypeCode" = EXCLUDED."walletTypeCode",
    status = EXCLUDED.status,
    description = EXCLUDED.description,
    currency = EXCLUDED.currency,
    image = EXCLUDED.image,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy";