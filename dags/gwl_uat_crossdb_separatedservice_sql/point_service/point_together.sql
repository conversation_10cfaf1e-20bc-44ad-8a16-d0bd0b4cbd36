-- from DDL of gwl
-- database name : point_service
-- insert correct seqencne ...

-- Drop table

-- DROP TABLE "AdjustmentReason";

CREATE TABLE "AdjustmentReason" (
	code text NOT NULL,
	"name" text NOT NULL,
	"type" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "AdjustmentReason_pkey" PRIMARY KEY (code)
);


-- "Outbox" definition

-- Drop table

-- DROP TABLE "Outbox";

CREATE TABLE "Outbox" (
	id text NOT NULL,
	"aggregateId" text NULL,
	"aggregateType" text NULL,
	topic text NOT NULL,
	payload text NOT NULL,
	status text NOT NULL,
	"retryCount" int4 NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"processedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NULL,
	"errorLog" text NULL,
	CONSTRAINT "Outbox_pkey" PRIMARY KEY (id)
);


-- "PointSetting" definition

-- Drop table

-- DROP TABLE "PointSetting";

CREATE TABLE "PointSetting" (
	id text NOT NULL,
	"nameEn" text NOT NULL,
	"nameTh" text NOT NULL,
	"unitEn" text NOT NULL,
	"unitTh" text NULL,
	earn jsonb NOT NULL,
	burn jsonb NOT NULL,
	"expiryPeriodYear" int4 NOT NULL,
	"cost" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	image jsonb NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedBy" jsonb NOT NULL,
	CONSTRAINT "PointSetting_pkey" PRIMARY KEY (id)
);


-- "WalletType" definition

-- Drop table

-- DROP TABLE "WalletType";

CREATE TABLE "WalletType" (
	code text NOT NULL,
	"name" text NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "WalletType_pkey" PRIMARY KEY (code)
);
CREATE UNIQUE INDEX "WalletType_code_key" ON "WalletType" USING btree (code);


-- "PointSettingLog" definition

-- Drop table

-- DROP TABLE "PointSettingLog";

CREATE TABLE "PointSettingLog" (
	id text NOT NULL,
	"updatedBy" jsonb NOT NULL,
	"oldData" jsonb NOT NULL,
	"newData" jsonb NOT NULL,
	"pointSettingId" text NOT NULL,
	detail _text NULL,
	"effectiveDate" timestamp(3) NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "PointSettingLog_pkey" PRIMARY KEY (id),
	CONSTRAINT "PointSettingLog_pointSettingId_fkey" FOREIGN KEY ("pointSettingId") REFERENCES "PointSetting"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- "PointSettingUpdateRequest" definition

-- Drop table

-- DROP TABLE "PointSettingUpdateRequest";

CREATE TABLE "PointSettingUpdateRequest" (
	id text NOT NULL,
	"oldData" jsonb NOT NULL,
	"newData" jsonb NOT NULL,
	"pointSettingId" text NOT NULL,
	detail _text NULL,
	"effectiveDate" timestamp(3) NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedBy" jsonb NOT NULL,
	CONSTRAINT "PointSettingUpdateRequest_pkey" PRIMARY KEY (id),
	CONSTRAINT "PointSettingUpdateRequest_pointSettingId_fkey" FOREIGN KEY ("pointSettingId") REFERENCES "PointSetting"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE UNIQUE INDEX "PointSettingUpdateRequest_pointSettingId_key" ON "PointSettingUpdateRequest" USING btree ("pointSettingId");


-- "Wallet" definition

-- Drop table

-- DROP TABLE "Wallet";

CREATE TABLE "Wallet" (
	id text NOT NULL,
	"runningId" int4 NOT NULL,
	code text NOT NULL,
	"name" text NOT NULL,
	"walletTypeCode" text NOT NULL,
	status text NOT NULL,
	description text NULL,
	currency text NOT NULL,
	image jsonb NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedBy" jsonb NOT NULL,
	CONSTRAINT "Wallet_code_unique" UNIQUE (code),
	CONSTRAINT "Wallet_pkey" PRIMARY KEY (id, code),
	CONSTRAINT "Wallet_walletTypeCode_fkey" FOREIGN KEY ("walletTypeCode") REFERENCES "WalletType"(code) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- "WalletActivity" definition

-- Drop table

-- DROP TABLE "WalletActivity";

CREATE TABLE "WalletActivity" (
	id text NOT NULL,
	"memberId" text NOT NULL,
	"walletCode" text NOT NULL,
	"type" text NULL,
	"refType" text NULL,
	"refId" text NULL,
	"externalId" text NULL,
	amount numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"partnerCode" text NULL,
	"brandCode" text NULL,
	"branchCode" text NULL,
	detail jsonb NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"documentDate" timestamp(3) NOT NULL,
	"beforeBalance" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"afterBalance" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	remark text NULL,
	"deletedAt" timestamp(3) NULL,
	CONSTRAINT "WalletActivity_pkey" PRIMARY KEY (id),
	CONSTRAINT "WalletActivity_walletCode_fkey" FOREIGN KEY ("walletCode") REFERENCES "Wallet"(code) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- "WalletAdjustmentReason" definition

-- Drop table

-- DROP TABLE "WalletAdjustmentReason";

CREATE TABLE "WalletAdjustmentReason" (
	"reasonCode" text NOT NULL,
	"walletTypeCode" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "WalletAdjustmentReason_pkey" PRIMARY KEY ("reasonCode", "walletTypeCode", "createdAt"),
	CONSTRAINT "WalletAdjustmentReason_reasonCode_fkey" FOREIGN KEY ("reasonCode") REFERENCES "AdjustmentReason"(code) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT "WalletAdjustmentReason_walletTypeCode_fkey" FOREIGN KEY ("walletTypeCode") REFERENCES "WalletType"(code) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- "WalletAdjustmentTransaction" definition

-- Drop table

-- DROP TABLE "WalletAdjustmentTransaction";

CREATE TABLE "WalletAdjustmentTransaction" (
	id text NOT NULL,
	"memberId" text NOT NULL,
	"walletCode" text NOT NULL,
	"reasonCode" text NOT NULL,
	amount numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"type" text NOT NULL,
	remark text NOT NULL,
	"createdBy" jsonb NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"refNo" text NULL,
	"expiredAt" timestamp(3) NULL,
	"businessAreaBranchCode" text NULL,
	CONSTRAINT "WalletAdjustmentTransaction_pkey" PRIMARY KEY (id),
	CONSTRAINT "WalletAdjustmentTransaction_reasonCode_fkey" FOREIGN KEY ("reasonCode") REFERENCES "AdjustmentReason"(code) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT "WalletAdjustmentTransaction_walletCode_fkey" FOREIGN KEY ("walletCode") REFERENCES "Wallet"(code) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- "WalletBalance" definition

-- Drop table

-- DROP TABLE "WalletBalance";

CREATE TABLE "WalletBalance" (
	id text NOT NULL,
	"memberId" text NOT NULL,
	"walletCode" text NOT NULL,
	amount numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"expiredAt" timestamp(3) NULL,
	CONSTRAINT "WalletBalance_pkey" PRIMARY KEY (id),
	CONSTRAINT "WalletBalance_unique" UNIQUE ("memberId", "walletCode", "expiredAt"),
	CONSTRAINT "WalletBalance_walletCode_fkey" FOREIGN KEY ("walletCode") REFERENCES "Wallet"(code) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- "WalletLog" definition

-- Drop table

-- DROP TABLE "WalletLog";

CREATE TABLE "WalletLog" (
	id text NOT NULL,
	"oldData" jsonb NOT NULL,
	"newData" jsonb NOT NULL,
	detail _text NULL,
	status text NOT NULL,
	"effectiveDate" timestamp(3) NOT NULL,
	"updatedBy" jsonb NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"walletCode" text NOT NULL,
	CONSTRAINT "WalletLog_pkey" PRIMARY KEY (id),
	CONSTRAINT "WalletLog_walletCode_fkey" FOREIGN KEY ("walletCode") REFERENCES "Wallet"(code) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- "WalletTransaction" definition

-- Drop table

-- DROP TABLE "WalletTransaction";

CREATE TABLE "WalletTransaction" (
	id text NOT NULL,
	"memberId" text NOT NULL,
	"walletActivityId" text NOT NULL,
	"balanceId" text NOT NULL,
	"type" text NOT NULL,
	"walletCode" text NOT NULL,
	amount numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"expiredAt" timestamp(3) NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "WalletTransaction_pkey" PRIMARY KEY (id),
	CONSTRAINT "WalletTransaction_WalletActivity_fkey" FOREIGN KEY ("walletActivityId") REFERENCES "WalletActivity"(id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT "WalletTransaction_WalletBalance_fkey" FOREIGN KEY ("balanceId") REFERENCES "WalletBalance"(id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT "WalletTransaction_walletCode_fkey" FOREIGN KEY ("walletCode") REFERENCES "Wallet"(code) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- "VoidWalletActivity" definition

-- Drop table

-- DROP TABLE "VoidWalletActivity";

CREATE TABLE "VoidWalletActivity" (
	id text NOT NULL,
	"VoidWalletActivityId" text NOT NULL,
	"parentWalletActivityId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "VoidWalletActivity_pkey" PRIMARY KEY (id),
	CONSTRAINT "VoidWalletActivity_WalletActivity_fkey1" FOREIGN KEY ("VoidWalletActivityId") REFERENCES "WalletActivity"(id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT "VoidWalletActivity_WalletActivity_fkey2" FOREIGN KEY ("parentWalletActivityId") REFERENCES "WalletActivity"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);



-- get correct sequence tables to list here

"PointSetting"
"AdjustmentReason"
"WalletAdjustmentReason"
"WalletType"
"Wallet"
"WalletLog"
"VoidWalletActivity"
"PointSettingLog"
"PointSettingUpdateRequest"
"WalletAdjustmentTransaction"
"WalletBalance"
"WalletActivity"
"WalletTransaction"
"Outbox"

-- table from gwl
PointSetting
AdjustmentReason
WalletAdjustmentReason
WalletType
Wallet
WalletLog
VoidWalletActivity
PointSettingLog
PointSettingUpdateRequest
WalletAdjustmentTransaction
WalletBalance
WalletActivity
WalletTransaction
Outbox





-- create sql statement to insert data from gwl to staging
-- which columns and dblink like example

-- PointSetting
INSERT INTO "PointSetting"
SELECT * FROM dblink('my_connection', 'SELECT * FROM point_service."PointSetting"') AS 
t1(id text,
    "nameEn" text,
    "nameTh" text,
    "unitEn" text,
    "unitTh" text,
    earn jsonb,
    burn jsonb,
    "expiryPeriodYear" int4,
    "cost" numeric(16, 2),
    image jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "updatedBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "nameEn" = EXCLUDED."nameEn",
    "nameTh" = EXCLUDED."nameTh",
    "unitEn" = EXCLUDED."unitEn",
    "unitTh" = EXCLUDED."unitTh",
    earn = EXCLUDED.earn,
    burn = EXCLUDED.burn,
    "expiryPeriodYear" = EXCLUDED."expiryPeriodYear",
    "cost" = EXCLUDED."cost",
    image = EXCLUDED.image,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy";

-- AdjustmentReason
INSERT INTO "AdjustmentReason"
SELECT * FROM dblink('my_connection', 'SELECT * FROM point_service."AdjustmentReason"') AS 
t1(code text,
    "name" text,
    "type" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (code) DO UPDATE SET
    "name" = EXCLUDED."name",
    "type" = EXCLUDED."type",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- WalletAdjustmentReason
INSERT INTO "WalletAdjustmentReason"
SELECT * FROM dblink('my_connection', 'SELECT * FROM point_service."WalletAdjustmentReason"') AS 
t1("reasonCode" text,
    "walletTypeCode" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT ("reasonCode", "walletTypeCode") DO UPDATE SET
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- WalletType
INSERT INTO "WalletType"
SELECT * FROM dblink('my_connection', 'SELECT * FROM point_service."WalletType"') AS 
t1(code text,
    "name" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (code) DO UPDATE SET
    "name" = EXCLUDED."name",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- Wallet
INSERT INTO "Wallet"
SELECT * FROM dblink('my_connection', 'SELECT * FROM point_service."Wallet"') AS 
t1(id text,
    "runningId" int4,
    code text,
    "name" text,
    "walletTypeCode" text,
    status text,
    description text,
    currency text,
    image jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "updatedBy" jsonb)
ON CONFLICT (id, code) DO UPDATE SET
    "runningId" = EXCLUDED."runningId",
    "name" = EXCLUDED."name",
    "walletTypeCode" = EXCLUDED."walletTypeCode",
    status = EXCLUDED.status,
    description = EXCLUDED.description,
    currency = EXCLUDED.currency,
    image = EXCLUDED.image,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy";

-- WalletLog
INSERT INTO "WalletLog"
SELECT * FROM dblink('my_connection', 'SELECT * FROM point_service."WalletLog"') AS 
t1(id text,
    "oldData" jsonb,
    "newData" jsonb,
    detail text,
    status text,
    "effectiveDate" timestamp(3),
    "updatedBy" jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "walletCode" text)
ON CONFLICT (id) DO UPDATE SET
    "oldData" = EXCLUDED."oldData",
    "newData" = EXCLUDED."newData",
    detail = EXCLUDED.detail,
    status = EXCLUDED.status,
    "effectiveDate" = EXCLUDED."effectiveDate",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "walletCode" = EXCLUDED."walletCode";

-- VoidWalletActivity
INSERT INTO "VoidWalletActivity"
SELECT * FROM dblink('my_connection', 'SELECT * FROM point_service."VoidWalletActivity"') AS 
t1(id text,
    "VoidWalletActivityId" text,
    "parentWalletActivityId" text,
    "createdAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "VoidWalletActivityId" = EXCLUDED."VoidWalletActivityId",
    "parentWalletActivityId" = EXCLUDED."parentWalletActivityId",
    "createdAt" = EXCLUDED."createdAt";

-- PointSettingLog
INSERT INTO "PointSettingLog"
SELECT * FROM dblink('my_connection', 'SELECT * FROM point_service."PointSettingLog"') AS 
t1(id text,
    "updatedBy" jsonb,
    "oldData" jsonb,
    "newData" jsonb,
    "pointSettingId" text,
    detail text,
    "effectiveDate" timestamp(3),
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "updatedBy" = EXCLUDED."updatedBy",
    "oldData" = EXCLUDED."oldData",
    "newData" = EXCLUDED."newData",
    "pointSettingId" = EXCLUDED."pointSettingId",
    detail = EXCLUDED.detail,
    "effectiveDate" = EXCLUDED."effectiveDate",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- PointSettingUpdateRequest
INSERT INTO "PointSettingUpdateRequest"
SELECT * FROM dblink('my_connection', 'SELECT * FROM point_service."PointSettingUpdateRequest"') AS 
t1(id text,
    "oldData" jsonb,
    "newData" jsonb,
    "pointSettingId" text,
    detail text,
    "effectiveDate" timestamp(3),
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "updatedBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "oldData" = EXCLUDED."oldData",
    "newData" = EXCLUDED."newData",
    "pointSettingId" = EXCLUDED."pointSettingId",
    detail = EXCLUDED.detail,
    "effectiveDate" = EXCLUDED."effectiveDate",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy";

-- WalletAdjustmentTransaction
INSERT INTO "WalletAdjustmentTransaction"
SELECT * FROM dblink('my_connection', 'SELECT * FROM point_service."WalletAdjustmentTransaction"') AS 
t1(id text,
    "memberId" text,
    "walletCode" text,
    "reasonCode" text,
    amount numeric(16, 2),
    "type" text,
    remark text,
    "createdBy" jsonb,
    "createdAt" timestamp(3),
    "refNo" text,
    "expiredAt" timestamp(3),
    "businessAreaBranchCode" text)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "walletCode" = EXCLUDED."walletCode",
    "reasonCode" = EXCLUDED."reasonCode",
    amount = EXCLUDED.amount,
    "type" = EXCLUDED."type",
    remark = EXCLUDED.remark,
    "createdBy" = EXCLUDED."createdBy",
    "createdAt" = EXCLUDED."createdAt",
    "refNo" = EXCLUDED."refNo",
    "expiredAt" = EXCLUDED."expiredAt",
    "businessAreaBranchCode" = EXCLUDED."businessAreaBranchCode";

-- WalletBalance
INSERT INTO "WalletBalance"
SELECT * FROM dblink('my_connection', 'SELECT * FROM point_service."WalletBalance"') AS 
t1(id text,
    "memberId" text,
    "walletCode" text,
    amount numeric(16, 2),
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "expiredAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "walletCode" = EXCLUDED."walletCode",
    amount = EXCLUDED.amount,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "expiredAt" = EXCLUDED."expiredAt";

-- WalletActivity
INSERT INTO "WalletActivity"
SELECT * FROM dblink('my_connection', 'SELECT * FROM point_service."WalletActivity"') AS 
t1(id text,
    "memberId" text,
    "walletCode" text,
    "type" text,
    "refType" text,
    "refId" text,
    "externalId" text,
    amount numeric(16, 2),
    "partnerCode" text,
    "brandCode" text,
    "branchCode" text,
    detail jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "documentDate" timestamp(3),
    "beforeBalance" numeric(16, 2),
    "afterBalance" numeric(16, 2),
    remark text,
    "deletedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "walletCode" = EXCLUDED."walletCode",
    "type" = EXCLUDED."type",
    "refType" = EXCLUDED."refType",
    "refId" = EXCLUDED."refId",
    "externalId" = EXCLUDED."externalId",
    amount = EXCLUDED.amount,
    "partnerCode" = EXCLUDED."partnerCode",
    "brandCode" = EXCLUDED."brandCode",
    "branchCode" = EXCLUDED."branchCode",
    detail = EXCLUDED.detail,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "documentDate" = EXCLUDED."documentDate",
    "beforeBalance" = EXCLUDED."beforeBalance",
    "afterBalance" = EXCLUDED."afterBalance",
    remark = EXCLUDED.remark,
    "deletedAt" = EXCLUDED."deletedAt";

-- WalletTransaction
INSERT INTO "WalletTransaction"
SELECT * FROM dblink('my_connection', 'SELECT * FROM point_service."WalletTransaction"') AS 
t1(id text,
    "memberId" text,
    "walletActivityId" text,
    "balanceId" text,
    "type" text,
    "walletCode" text,
    amount numeric(16, 2),
    "expiredAt" timestamp(3),
    "createdAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "walletActivityId" = EXCLUDED."walletActivityId",
    "balanceId" = EXCLUDED."balanceId",
    "type" = EXCLUDED."type",
    "walletCode" = EXCLUDED."walletCode",
    amount = EXCLUDED.amount,
    "expiredAt" = EXCLUDED."expiredAt",
    "createdAt" = EXCLUDED."createdAt";


-- Outbox
INSERT INTO "Outbox"
SELECT * FROM dblink('my_connection', 'SELECT * FROM point_service."Outbox"') AS 
t1(id text,
    "aggregateId" text,
    "aggregateType" text,
    topic text,
    payload text,
    status text,
    "retryCount" int4,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "processedAt" timestamp(3),
    "errorLog" text)
ON CONFLICT (id) DO UPDATE SET
    "aggregateId" = EXCLUDED."aggregateId",
    "aggregateType" = EXCLUDED."aggregateType",
    topic = EXCLUDED.topic,
    payload = EXCLUDED.payload,
    status = EXCLUDED.status,
    "retryCount" = EXCLUDED."retryCount",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "processedAt" = EXCLUDED."processedAt",
    "errorLog" = EXCLUDED."errorLog";
