-- WalletAdjustmentReason
INSERT INTO public."WalletAdjustmentReason"
SELECT * FROM dblink('my_connection', 'SELECT * FROM point_service."WalletAdjustmentReason"') AS 
t1("reasonCode" text,
    "walletTypeCode" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT ("reasonCode", "walletTypeCode") DO UPDATE SET
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";