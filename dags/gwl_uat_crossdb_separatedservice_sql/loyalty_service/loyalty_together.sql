CREATE TABLE public."Bank" (
	code text NOT NULL,
	"name" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "Bank_pk" PRIMARY KEY (code)
);




-- loyalty_service."CardType" definition

-- Drop table

-- DROP TABLE loyalty_service."CardType";

CREATE TABLE loyalty_service."CardType" (
	code text NOT NULL,
	"name" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "CardType_pkey" PRIMARY KEY (code)
);


-- loyalty_service."MemberCoBrandCardImport" definition

-- Drop table

-- DROP TABLE loyalty_service."MemberCoBrandCardImport";

CREATE TABLE loyalty_service."MemberCoBrandCardImport" (
	id text NOT NULL,
	file jsonb NULL,
	status text NOT NULL,
	error text NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"errorLogFile" jsonb NULL,
	CONSTRAINT "MemberCoBrandCardImport_pkey" PRIMARY KEY (id)
);


-- loyalty_service."Outbox" definition

-- Drop table

-- DROP TABLE loyalty_service."Outbox";

CREATE TABLE loyalty_service."Outbox" (
	id text NOT NULL,
	"aggregateId" text NULL,
	"aggregateType" text NULL,
	topic text NOT NULL,
	payload text NOT NULL,
	status text NOT NULL,
	"retryCount" int4 NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"processedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NULL,
	"errorLog" text NULL,
	CONSTRAINT "Outbox_pkey" PRIMARY KEY (id)
);


-- loyalty_service."RegisterChannel" definition

-- Drop table

-- DROP TABLE loyalty_service."RegisterChannel";

CREATE TABLE loyalty_service."RegisterChannel" (
	code text NOT NULL,
	"name" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "RegisterChannel_pkey" PRIMARY KEY (code)
);


-- loyalty_service."RegisterLocation" definition

-- Drop table

-- DROP TABLE loyalty_service."RegisterLocation";

CREATE TABLE loyalty_service."RegisterLocation" (
	code text NOT NULL,
	"name" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "RegisterLocation_pkey" PRIMARY KEY (code)
);


-- loyalty_service."ReportEmailTemplate" definition

-- Drop table

-- DROP TABLE loyalty_service."ReportEmailTemplate";

CREATE TABLE loyalty_service."ReportEmailTemplate" (
	id text NOT NULL,
	code text NOT NULL,
	"name" text NOT NULL,
	subject text NOT NULL,
	"htmlFile" text NULL,
	status text NULL,
	settings jsonb NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "ReportEmailTemplate_pkey" PRIMARY KEY (id)
);


-- loyalty_service."StaffCompany" definition

-- Drop table

-- DROP TABLE loyalty_service."StaffCompany";

CREATE TABLE loyalty_service."StaffCompany" (
	code text NOT NULL,
	"name" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "StaffCompany_pkey" PRIMARY KEY (code)
);


-- loyalty_service."StaffLevel" definition

-- Drop table

-- DROP TABLE loyalty_service."StaffLevel";

CREATE TABLE loyalty_service."StaffLevel" (
	code text NOT NULL,
	"name" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "StaffLevel_pkey" PRIMARY KEY (code)
);


-- loyalty_service."SubSegment" definition

-- Drop table

-- DROP TABLE loyalty_service."SubSegment";

CREATE TABLE loyalty_service."SubSegment" (
	code text NOT NULL,
	"name" text NOT NULL,
	status text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"type" text NOT NULL,
	CONSTRAINT "SubSegment_pkey" PRIMARY KEY (code)
);


-- loyalty_service."Tier" definition

-- Drop table

-- DROP TABLE loyalty_service."Tier";

CREATE TABLE loyalty_service."Tier" (
	id text NOT NULL,
	code text NOT NULL,
	"name" text NOT NULL,
	"earnRate" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"isActive" bool DEFAULT false NOT NULL,
	"minimumSpending" numeric(16, 2) NULL,
	"maintainSpending" numeric(16, 2) NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	description text NULL,
	image jsonb NULL,
	"type" text NULL,
	CONSTRAINT "Tier_pkey" PRIMARY KEY (id)
);
CREATE UNIQUE INDEX "Tier_code_key" ON loyalty_service."Tier" USING btree (code);
CREATE UNIQUE INDEX "Tier_minimumSpending_key" ON loyalty_service."Tier" USING btree ("minimumSpending");
CREATE UNIQUE INDEX "Tier_name_key" ON loyalty_service."Tier" USING btree (name);


-- loyalty_service."UpgradeGroup" definition

-- Drop table

-- DROP TABLE loyalty_service."UpgradeGroup";

CREATE TABLE loyalty_service."UpgradeGroup" (
	code text NOT NULL,
	"name" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "UpgradeGroup_pkey" PRIMARY KEY (code)
);


-- loyalty_service."UpgradeReason" definition

-- Drop table

-- DROP TABLE loyalty_service."UpgradeReason";

CREATE TABLE loyalty_service."UpgradeReason" (
	code text NOT NULL,
	"name" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "UpgradeReason_pkey" PRIMARY KEY (code)
);


-- loyalty_service."CoBrand" definition

-- Drop table

-- DROP TABLE loyalty_service."CoBrand";

CREATE TABLE loyalty_service."CoBrand" (
	id text NOT NULL,
	code text NOT NULL,
	"name" text NOT NULL,
	"bankCode" text NOT NULL,
	"cardTypeCode" text NOT NULL,
	"cardBin" text NOT NULL,
	"cardImage" jsonb DEFAULT jsonb_build_object() NOT NULL,
	"minimumTierCode" text NULL,
	status text DEFAULT false NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	detail jsonb NULL,
	"updatedBy" jsonb NULL,
	CONSTRAINT "CoBrand_pkey" PRIMARY KEY (id),
	CONSTRAINT "CoBrand_cardTypeCode_fkey" FOREIGN KEY ("cardTypeCode") REFERENCES loyalty_service."CardType"(code) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT cobrand_bankcode_fkey FOREIGN KEY ("bankCode") REFERENCES loyalty_service."Bank"(code) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- loyalty_service."CoBrandLog" definition

-- Drop table

-- DROP TABLE loyalty_service."CoBrandLog";

CREATE TABLE loyalty_service."CoBrandLog" (
	id text NOT NULL,
	"oldData" jsonb NOT NULL,
	"newData" jsonb NOT NULL,
	"coBrandId" text NOT NULL,
	detail _text NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedBy" jsonb NULL,
	CONSTRAINT "CoBrandLog_pkey" PRIMARY KEY (id),
	CONSTRAINT "CoBrandLog_coBrandId_fkey" FOREIGN KEY ("coBrandId") REFERENCES loyalty_service."CoBrand"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- loyalty_service."CoBrandPrivilege" definition

-- Drop table

-- DROP TABLE loyalty_service."CoBrandPrivilege";

CREATE TABLE loyalty_service."CoBrandPrivilege" (
	id text NOT NULL,
	"coBrandId" text NOT NULL,
	"privilegeId" text NOT NULL,
	"privilegeEligibleTiers" jsonb NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	CONSTRAINT "CoBrandPrivilege_pkey" PRIMARY KEY (id),
	CONSTRAINT "CoBrandPrivilege_CoBrandID_fkey" FOREIGN KEY ("coBrandId") REFERENCES loyalty_service."CoBrand"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- loyalty_service."Member" definition

-- Drop table

-- DROP TABLE loyalty_service."Member";

CREATE TABLE loyalty_service."Member" (
	id text NOT NULL,
	"gwlNo" text NOT NULL,
	"embossNo" text NULL,
	email text NULL,
	"emailVerifiedAt" timestamp(3) NULL,
	phone text NULL,
	"phoneVerifiedAt" timestamp(3) NULL,
	"registeredAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"deletedAt" timestamp(3) NULL,
	"tierId" text NOT NULL,
	"minimumTierId" text NULL,
	"tierStartedAt" timestamp(3) NOT NULL,
	"tierEndedAt" timestamp(3) NOT NULL,
	"accumulateSpending" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"lifeTimeSpending" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"isActive" bool DEFAULT true NOT NULL,
	reason text NULL,
	"picRemark" text NULL,
	"referralCode" text NULL,
	"upgradeGroupCode" text NULL,
	"upgradeReasonCode" text NULL,
	"registrationChannelCode" text NOT NULL,
	"registrationLocationCode" text NOT NULL,
	"accumulateMaintainSpending" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"shoppingCardId" text NULL,
	"onepassId" text NULL,
	"phoneCode" text NULL,
	"isCoBrandNonMember" bool DEFAULT false NOT NULL,
	"emailHash" text NULL,
	"phoneHash" text NULL,
	"minimumTierInvitedId" text NULL,
	"updatedBy" jsonb NOT NULL,
	CONSTRAINT "Member_pkey" PRIMARY KEY (id),
	CONSTRAINT "Member_registrationChannelCode_fkey" FOREIGN KEY ("registrationChannelCode") REFERENCES loyalty_service."RegisterChannel"(code) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT "Member_registrationLocationCode_fkey" FOREIGN KEY ("registrationLocationCode") REFERENCES loyalty_service."RegisterLocation"(code) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT "Member_tierId_fkey" FOREIGN KEY ("tierId") REFERENCES loyalty_service."Tier"(id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT "Member_upgradeGroupCode_fkey" FOREIGN KEY ("upgradeGroupCode") REFERENCES loyalty_service."UpgradeGroup"(code) ON DELETE SET NULL ON UPDATE CASCADE,
	CONSTRAINT "Member_upgradeReasonCode_fkey" FOREIGN KEY ("upgradeReasonCode") REFERENCES loyalty_service."UpgradeReason"(code) ON DELETE SET NULL ON UPDATE CASCADE,
	CONSTRAINT member_tier_fk FOREIGN KEY ("minimumTierId") REFERENCES loyalty_service."Tier"(id) ON DELETE SET NULL ON UPDATE CASCADE
);
CREATE UNIQUE INDEX member_gwlno_idx ON loyalty_service."Member" USING btree ("gwlNo");


-- loyalty_service."MemberCoBrandCard" definition

-- Drop table

-- DROP TABLE loyalty_service."MemberCoBrandCard";

CREATE TABLE loyalty_service."MemberCoBrandCard" (
	id text NOT NULL,
	"memberId" text NOT NULL,
	"coBrandId" text NOT NULL,
	"cardNo" text NOT NULL,
	"memberCoBrandCardImportId" text NOT NULL,
	status text NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"createdBy" jsonb NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedBy" jsonb NULL,
	"cardHolderName" text NOT NULL,
	"cardHolderNameHash" text NULL,
	remark text NULL,
	"cardReason" text NOT NULL,
	CONSTRAINT "MemberCoBrandCard_pkey" PRIMARY KEY (id),
	CONSTRAINT "MemberCoBrandCard_CoBrand_fkey" FOREIGN KEY ("coBrandId") REFERENCES loyalty_service."CoBrand"(id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT "MemberCoBrandCard_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES loyalty_service."Member"(id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT membercobrandcard_membercobrandcardimport_fk FOREIGN KEY ("memberCoBrandCardImportId") REFERENCES loyalty_service."MemberCoBrandCardImport"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE INDEX "MemberCoBrandCard_memberId_idx" ON loyalty_service."MemberCoBrandCard" USING btree ("memberId");


-- loyalty_service."MemberCoBrandCardLog" definition

-- Drop table

-- DROP TABLE loyalty_service."MemberCoBrandCardLog";

CREATE TABLE loyalty_service."MemberCoBrandCardLog" (
	id text NOT NULL,
	"memberCoBrandCardId" text NOT NULL,
	"type" text NOT NULL,
	"name" text NOT NULL,
	description _text NULL,
	"oldData" jsonb NOT NULL,
	"newData" jsonb NOT NULL,
	"createdBy" jsonb NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "MemberCoBrandCardLog_pkey" PRIMARY KEY (id),
	CONSTRAINT "MemberCoBrandCardLog_memberCoBrandCard_fkey" FOREIGN KEY ("memberCoBrandCardId") REFERENCES loyalty_service."MemberCoBrandCard"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- loyalty_service."MemberLegacyCoBrandHistory" definition

-- Drop table

-- DROP TABLE loyalty_service."MemberLegacyCoBrandHistory";

CREATE TABLE loyalty_service."MemberLegacyCoBrandHistory" (
	id text NOT NULL,
	"memberId" text NOT NULL,
	"cardTypeCode" text NOT NULL,
	description text NOT NULL,
	"embossNo" text NULL,
	"tierStartedAt" timestamp(3) NOT NULL,
	"tierEndedAt" timestamp(3) NOT NULL,
	"cardStatus" text NULL,
	"cardReason" text NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "MemberLegacyCobrandHistory_pkey" PRIMARY KEY (id),
	CONSTRAINT "MemberLegacyCobrandHistory_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES loyalty_service."Member"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE INDEX "MemberLegacyCobrandHistory_memberId_idx" ON loyalty_service."MemberLegacyCoBrandHistory" USING btree ("memberId");


-- loyalty_service."MemberLegacyTierHistory" definition

-- Drop table

-- DROP TABLE loyalty_service."MemberLegacyTierHistory";

CREATE TABLE loyalty_service."MemberLegacyTierHistory" (
	id text NOT NULL,
	"memberId" text NOT NULL,
	"cardTypeCode" text NOT NULL,
	description text NOT NULL,
	"embossNo" text NULL,
	"tierStartedAt" timestamp(3) NOT NULL,
	"tierEndedAt" timestamp(3) NOT NULL,
	"cardStatus" text NULL,
	"cardReason" text NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "MemberLegacyTierHistory_pkey" PRIMARY KEY (id),
	CONSTRAINT "MemberLegacyTierHistory_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES loyalty_service."Member"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE INDEX "MemberLegacyTierHistory_memberId_idx" ON loyalty_service."MemberLegacyTierHistory" USING btree ("memberId");


-- loyalty_service."MemberLog" definition

-- Drop table

-- DROP TABLE loyalty_service."MemberLog";

CREATE TABLE loyalty_service."MemberLog" (
	id text NOT NULL,
	"memberId" text NOT NULL,
	"type" text NOT NULL,
	"name" text NOT NULL,
	description _text NOT NULL,
	"oldData" jsonb NULL,
	"newData" jsonb NOT NULL,
	"createdBy" jsonb NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "MemberLog_pkey" PRIMARY KEY (id),
	CONSTRAINT "MemberLog_Member_fkey" FOREIGN KEY ("memberId") REFERENCES loyalty_service."Member"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- loyalty_service."MemberProfile" definition

-- Drop table

-- DROP TABLE loyalty_service."MemberProfile";

CREATE TABLE loyalty_service."MemberProfile" (
	id text NOT NULL,
	"memberId" text NOT NULL,
	"firstName" text NOT NULL,
	"firstNameTh" text NULL,
	"middleName" text NULL,
	"middleNameTh" text NULL,
	"lastName" text NOT NULL,
	"lastNameTh" text NULL,
	cid text NULL,
	"passportNo" text NULL,
	"passportExpiryDate" date NULL,
	"dateOfBirth" text NOT NULL,
	gender text NULL,
	"addressLine" text NULL,
	"subDistrict" text NULL,
	district text NULL,
	province text NULL,
	city text NULL,
	"postalCode" text NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	occupation int4 NULL,
	title int4 NULL,
	"countryCode" text NULL,
	"nationalityCode" text NOT NULL,
	"firstNameHash" text NULL,
	"firstNameThHash" text NULL,
	"middleNameHash" text NULL,
	"middleNameThHash" text NULL,
	"lastNameHash" text NULL,
	"lastNameThHash" text NULL,
	"cidHash" text NULL,
	"passportNoHash" text NULL,
	"dateOfBirthHash" text NULL,
	"genderHash" text NULL,
	"addressLineHash" text NULL,
	CONSTRAINT "MemberProfile_pkey" PRIMARY KEY (id),
	CONSTRAINT "MemberProfile_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES loyalty_service."Member"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- loyalty_service."MemberSubSegment" definition

-- Drop table

-- DROP TABLE loyalty_service."MemberSubSegment";

CREATE TABLE loyalty_service."MemberSubSegment" (
	id text NOT NULL,
	"memberId" text NOT NULL,
	"subSegmentCode" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "MemberSubSegment_pkey" PRIMARY KEY (id),
	CONSTRAINT "MemberSubSegment_subSegmentCode_fkey" FOREIGN KEY ("subSegmentCode") REFERENCES loyalty_service."SubSegment"(code) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT membersubsegment_member_fk FOREIGN KEY ("memberId") REFERENCES loyalty_service."Member"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE UNIQUE INDEX "MemberSubSegment_memberId_subSegmentCode_key" ON loyalty_service."MemberSubSegment" USING btree ("memberId", "subSegmentCode");


-- loyalty_service."MemberTierHistory" definition

-- Drop table

-- DROP TABLE loyalty_service."MemberTierHistory";

CREATE TABLE loyalty_service."MemberTierHistory" (
	id text NOT NULL,
	"memberId" text NOT NULL,
	"fromTierId" text NOT NULL,
	"toTierId" text NOT NULL,
	"tierStartedAt" timestamp(3) NOT NULL,
	"tierEndedAt" timestamp(3) NOT NULL,
	"accumulateSpending" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "MemberTierHistory_pkey" PRIMARY KEY (id),
	CONSTRAINT "MemberTierHistory_Member_fkey" FOREIGN KEY ("memberId") REFERENCES loyalty_service."Member"(id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT "MemberTierHistory_fromTierId_fkey" FOREIGN KEY ("fromTierId") REFERENCES loyalty_service."Tier"(id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT "MemberTierHistory_toTierId_fkey" FOREIGN KEY ("toTierId") REFERENCES loyalty_service."Tier"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- loyalty_service."PrivilegeTier" definition

-- Drop table

-- DROP TABLE loyalty_service."PrivilegeTier";

CREATE TABLE loyalty_service."PrivilegeTier" (
	id text NOT NULL,
	"tierId" text NOT NULL,
	"privilegeId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "PrivilegeTier_pkey" PRIMARY KEY (id),
	CONSTRAINT "PrivilegeTier_tierId_fkey" FOREIGN KEY ("tierId") REFERENCES loyalty_service."Tier"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE UNIQUE INDEX "PrivilegeTier_tierId_privilegeId_key" ON loyalty_service."PrivilegeTier" USING btree ("tierId", "privilegeId");


-- loyalty_service."SalesTransaction" definition

-- Drop table

-- DROP TABLE loyalty_service."SalesTransaction";

CREATE TABLE loyalty_service."SalesTransaction" (
	id text NOT NULL,
	"memberId" text NOT NULL,
	"completedAt" timestamp(3) NOT NULL,
	"externalId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"netTotalAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"totalAccumSpendableAmount" numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"branchCode" text NULL,
	"brandCode" text NULL,
	"partnerCode" text NULL,
	CONSTRAINT "SalesTransaction_pkey" PRIMARY KEY (id),
	CONSTRAINT "SalesTransaction_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES loyalty_service."Member"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE INDEX salestransaction_memberid_idx ON loyalty_service."SalesTransaction" USING btree ("memberId");


-- loyalty_service."StaffProfile" definition

-- Drop table

-- DROP TABLE loyalty_service."StaffProfile";

CREATE TABLE loyalty_service."StaffProfile" (
	id text NOT NULL,
	"memberId" text NOT NULL,
	"staffLevelCode" text NOT NULL,
	"companyCode" text NOT NULL,
	"staffNo" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"staffPosition" text NULL,
	"staffDivision" text NULL,
	"staffJobLevel" text NULL,
	CONSTRAINT "StaffProfile_pkey" PRIMARY KEY (id),
	CONSTRAINT "StaffProfile_companyCode_fkey" FOREIGN KEY ("companyCode") REFERENCES loyalty_service."StaffCompany"(code) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT "StaffProfile_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES loyalty_service."Member"(id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT "StaffProfile_staffLevelCode_fkey" FOREIGN KEY ("staffLevelCode") REFERENCES loyalty_service."StaffLevel"(code) ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE UNIQUE INDEX "StaffProfile_memberId_key" ON loyalty_service."StaffProfile" USING btree ("memberId");


-- loyalty_service."TierLog" definition

-- Drop table

-- DROP TABLE loyalty_service."TierLog";

CREATE TABLE loyalty_service."TierLog" (
	id text NOT NULL,
	"updatedBy" jsonb NOT NULL,
	"oldData" jsonb NOT NULL,
	"newData" jsonb NOT NULL,
	"tierId" text NOT NULL,
	detail _text NULL,
	"effectiveDate" timestamp(3) NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "TierLog_pkey" PRIMARY KEY (id),
	CONSTRAINT "TierLog_tierId_fkey" FOREIGN KEY ("tierId") REFERENCES loyalty_service."Tier"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);


-- loyalty_service."TierUpdateRequest" definition

-- Drop table

-- DROP TABLE loyalty_service."TierUpdateRequest";

CREATE TABLE loyalty_service."TierUpdateRequest" (
	id text NOT NULL,
	"updatedBy" jsonb NOT NULL,
	"oldData" jsonb NOT NULL,
	"newData" jsonb NOT NULL,
	"effectiveDate" timestamp(3) NOT NULL,
	"tierId" text NOT NULL,
	detail _text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"privileges" _text NULL,
	CONSTRAINT "TierUpdateRequest_pkey" PRIMARY KEY (id),
	CONSTRAINT "TierUpdateRequest_tierId_fkey" FOREIGN KEY ("tierId") REFERENCES loyalty_service."Tier"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE UNIQUE INDEX "TierUpdateRequest_tierId_key" ON loyalty_service."TierUpdateRequest" USING btree ("tierId");


-- loyalty_service."RefundSalesTransaction" definition

-- Drop table

-- DROP TABLE loyalty_service."RefundSalesTransaction";

CREATE TABLE loyalty_service."RefundSalesTransaction" (
	id text NOT NULL,
	"memberId" text NOT NULL,
	"type" text NOT NULL,
	"salesTransactionId" text NOT NULL,
	"externalId" text NULL,
	"refundedAt" timestamp(3) NULL,
	"refundAmount" numeric(16, 2) NOT NULL,
	"revokeAccumSpendableAmount" numeric(16, 2) NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "RefundSalesTransaction_pkey" PRIMARY KEY (id),
	CONSTRAINT "RefundSalesTransaction_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES loyalty_service."Member"(id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT refundsalestransaction_salestransaction_fk FOREIGN KEY ("salesTransactionId") REFERENCES loyalty_service."SalesTransaction"(id) ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE INDEX "RefundSalesTransaction_memberId_idx" ON loyalty_service."RefundSalesTransaction" USING btree ("memberId");














-- Bank table
INSERT into loyalty_service."Bank" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."Bank"') AS 
t1( code text,
    name text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";


-- CardType
INSERT into loyalty_service."CardType" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."CardType"') AS 
t1( code text,
    name text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";


-- MemberCoBrandCardImport
INSERT into loyalty_service."MemberCoBrandCardImport" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."MemberCoBrandCardImport"') AS 
t1( id text,
    file jsonb,
    status text,
    error text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "errorLogFile" jsonb)
ON CONFLICT (id) DO UPDATE SET
    file = EXCLUDED.file,
    status = EXCLUDED.status,
    error = EXCLUDED.error,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "errorLogFile" = EXCLUDED."errorLogFile";

-- Outbox
INSERT into loyalty_service."Outbox" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."Outbox"') AS 
t1( id text,
    "aggregateId" text,
    "aggregateType" text,
    topic text,
    payload text,
    status text,
    "retryCount" int4,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "processedAt" timestamp(3),
    "errorLog" text)
ON CONFLICT (id) DO UPDATE SET
    "aggregateId" = EXCLUDED."aggregateId",
    "aggregateType" = EXCLUDED."aggregateType",
    topic = EXCLUDED.topic,
    payload = EXCLUDED.payload,
    status = EXCLUDED.status,
    "retryCount" = EXCLUDED."retryCount",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "processedAt" = EXCLUDED."processedAt",
    "errorLog" = EXCLUDED."errorLog";

-- RegisterChannel
INSERT into loyalty_service."RegisterChannel" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."RegisterChannel"') AS 
t1( code text,
    name text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- RegisterLocation
INSERT into loyalty_service."RegisterLocation" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."RegisterLocation"') AS 
t1( code text,
    name text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- ReportEmailTemplate
INSERT into loyalty_service."ReportEmailTemplate" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."ReportEmailTemplate"') AS 
t1( id text,
    code text,
    name text,
    subject text,
    "htmlFile" text,
    status text,
    settings jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    code = EXCLUDED.code,
    name = EXCLUDED.name,
    subject = EXCLUDED.subject,
    "htmlFile" = EXCLUDED."htmlFile",
    status = EXCLUDED.status,
    settings = EXCLUDED.settings,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- StaffCompany
INSERT into loyalty_service."StaffCompany" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."StaffCompany"') AS 
t1( code text,
    name text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- StaffLevel
INSERT into loyalty_service."StaffLevel" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."StaffLevel"') AS 
t1( code text,
    name text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- SubSegment
INSERT into loyalty_service."SubSegment" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."SubSegment"') AS 
t1( code text,
    name text,
    status text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "type" text)
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    status = EXCLUDED.status,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "type" = EXCLUDED."type";

-- Tier
INSERT into loyalty_service."Tier" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."Tier"') AS 
t1( id text,
    code text,
    name text,
    "earnRate" numeric(16, 2),
    "isActive" bool,
    "minimumSpending" numeric(16, 2),
    "maintainSpending" numeric(16, 2),
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    description text,
    image jsonb,
    "type" text)
ON CONFLICT (id) DO UPDATE SET
    code = EXCLUDED.code,
    name = EXCLUDED.name,
    "earnRate" = EXCLUDED."earnRate",
    "isActive" = EXCLUDED."isActive",
    "minimumSpending" = EXCLUDED."minimumSpending",
    "maintainSpending" = EXCLUDED."maintainSpending",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    description = EXCLUDED.description,
    image = EXCLUDED.image,
    "type" = EXCLUDED."type";

-- UpgradeGroup
INSERT into loyalty_service."UpgradeGroup" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."UpgradeGroup"') AS 
t1( code text,
    name text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- UpgradeReason
INSERT into loyalty_service."UpgradeReason" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."UpgradeReason"') AS 
t1( code text,
    name text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- CoBrand
INSERT into loyalty_service."CoBrand" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."CoBrand"') AS 
t1( id text,
    code text,
    name text,
    "bankCode" text,
    "cardTypeCode" text,
    "cardBin" text,
    "cardImage" jsonb,
    "minimumTierCode" text,
    status text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    detail jsonb,
    "updatedBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    code = EXCLUDED.code,
    name = EXCLUDED.name,
    "bankCode" = EXCLUDED."bankCode",
    "cardTypeCode" = EXCLUDED."cardTypeCode",
    "cardBin" = EXCLUDED."cardBin",
    "cardImage" = EXCLUDED."cardImage",
    "minimumTierCode" = EXCLUDED."minimumTierCode",
    status = EXCLUDED.status,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    detail = EXCLUDED.detail,
    "updatedBy" = EXCLUDED."updatedBy";

-- CoBrandLog
INSERT into loyalty_service."CoBrandLog" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."CoBrandLog"') AS 
t1( id text,
    "oldData" jsonb,
    "newData" jsonb,
    "coBrandId" text,
    detail _text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "updatedBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "oldData" = EXCLUDED."oldData",
    "newData" = EXCLUDED."newData",
    "coBrandId" = EXCLUDED."coBrandId",
    detail = EXCLUDED.detail,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy";

-- CoBrandPrivilege
INSERT into loyalty_service."CoBrandPrivilege" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."CoBrandPrivilege"') AS 
t1( id text,
    "coBrandId" text,
    "privilegeId" text,
    "privilegeEligibleTiers" jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "coBrandId" = EXCLUDED."coBrandId",
    "privilegeId" = EXCLUDED."privilegeId",
    "privilegeEligibleTiers" = EXCLUDED."privilegeEligibleTiers",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- Member
INSERT into loyalty_service."Member" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."Member"') AS 
t1( id text,
    "gwlNo" text,
    "embossNo" text,
    email text,
    "emailVerifiedAt" timestamp(3),
    phone text,
    "phoneVerifiedAt" timestamp(3),
    "registeredAt" timestamp(3),
    "deletedAt" timestamp(3),
    "tierId" text,
    "minimumTierId" text,
    "tierStartedAt" timestamp(3),
    "tierEndedAt" timestamp(3),
    "accumulateSpending" numeric(16, 2),
    "lifeTimeSpending" numeric(16, 2),
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "isActive" bool,
    reason text,
    "picRemark" text,
    "referralCode" text,
    "upgradeGroupCode" text,
    "upgradeReasonCode" text,
    "registrationChannelCode" text,
    "registrationLocationCode" text,
    "accumulateMaintainSpending" numeric(16, 2),
    "shoppingCardId" text,
    "onepassId" text,
    "phoneCode" text,
    "isCoBrandNonMember" bool,
    "emailHash" text,
    "phoneHash" text,
    "minimumTierInvitedId" text,
    "updatedBy" jsonb,
    remark text)
ON CONFLICT (id) DO UPDATE SET
    "gwlNo" = EXCLUDED."gwlNo",
    "embossNo" = EXCLUDED."embossNo",
    email = EXCLUDED.email,
    "emailVerifiedAt" = EXCLUDED."emailVerifiedAt",
    phone = EXCLUDED.phone,
    "phoneVerifiedAt" = EXCLUDED."phoneVerifiedAt",
    "registeredAt" = EXCLUDED."registeredAt",
    "deletedAt" = EXCLUDED."deletedAt",
    "tierId" = EXCLUDED."tierId",
    "minimumTierId" = EXCLUDED."minimumTierId",
    "tierStartedAt" = EXCLUDED."tierStartedAt",
    "tierEndedAt" = EXCLUDED."tierEndedAt",
    "accumulateSpending" = EXCLUDED."accumulateSpending",
    "lifeTimeSpending" = EXCLUDED."lifeTimeSpending",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "isActive" = EXCLUDED."isActive",
    reason = EXCLUDED.reason,
    "picRemark" = EXCLUDED."picRemark",
    "referralCode" = EXCLUDED."referralCode",
    "upgradeGroupCode" = EXCLUDED."upgradeGroupCode",
    "upgradeReasonCode" = EXCLUDED."upgradeReasonCode",
    "registrationChannelCode" = EXCLUDED."registrationChannelCode",
    "registrationLocationCode" = EXCLUDED."registrationLocationCode",
    "accumulateMaintainSpending" = EXCLUDED."accumulateMaintainSpending",
    "shoppingCardId" = EXCLUDED."shoppingCardId",
    "onepassId" = EXCLUDED."onepassId",
    "phoneCode" = EXCLUDED."phoneCode",
    "isCoBrandNonMember" = EXCLUDED."isCoBrandNonMember",
    "emailHash" = EXCLUDED."emailHash",
    "phoneHash" = EXCLUDED."phoneHash",
    "minimumTierInvitedId" = EXCLUDED."minimumTierInvitedId",
    "updatedBy" = EXCLUDED."updatedBy"
    remark = EXCLUDED.remark;


-- MemberCoBrandCard
INSERT INTO loyalty_service."MemberCoBrandCard"
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."MemberCoBrandCard"') AS 
t1(id text,
   "memberId" text,
   "coBrandId" text,
   "cardNo" text,
   "memberCoBrandCardImportId" text,
   status text,
   "createdAt" timestamp(3),
   "createdBy" jsonb,
   "updatedAt" timestamp(3),
   "updatedBy" jsonb,
   "cardHolderName" text,
   "cardHolderNameHash" text,
   remark text,
   "cardReason" text)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "coBrandId" = EXCLUDED."coBrandId",
    "cardNo" = EXCLUDED."cardNo",
    "memberCoBrandCardImportId" = EXCLUDED."memberCoBrandCardImportId",
    status = EXCLUDED.status,
    "createdAt" = EXCLUDED."createdAt",
    "createdBy" = EXCLUDED."createdBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "cardHolderName" = EXCLUDED."cardHolderName",
    "cardHolderNameHash" = EXCLUDED."cardHolderNameHash",
    remark = EXCLUDED.remark,
    "cardReason" = EXCLUDED."cardReason";

-- MemberCoBrandCardLog
INSERT INTO loyalty_service."MemberCoBrandCardLog"
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."MemberCoBrandCardLog"') AS 
t1(id text,
   "memberCoBrandCardId" text,
   "type" text,
   "name" text,
   description _text,
   "oldData" jsonb,
   "newData" jsonb,
   "createdBy" jsonb,
   "createdAt" timestamp(3),
   "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "memberCoBrandCardId" = EXCLUDED."memberCoBrandCardId",
    "type" = EXCLUDED."type",
    "name" = EXCLUDED."name",
    description = EXCLUDED.description,
    "oldData" = EXCLUDED."oldData",
    "newData" = EXCLUDED."newData",
    "createdBy" = EXCLUDED."createdBy",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- MemberLegacyCoBrandHistory
INSERT INTO loyalty_service."MemberLegacyCoBrandHistory"
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."MemberLegacyCoBrandHistory"') AS 
t1(id text,
   "memberId" text,
   "cardTypeCode" text,
   description text,
   "embossNo" text,
   "tierStartedAt" timestamp(3),
   "tierEndedAt" timestamp(3),
   "cardStatus" text,
   "cardReason" text,
   "createdAt" timestamp(3),
   "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "cardTypeCode" = EXCLUDED."cardTypeCode",
    description = EXCLUDED.description,
    "embossNo" = EXCLUDED."embossNo",
    "tierStartedAt" = EXCLUDED."tierStartedAt",
    "tierEndedAt" = EXCLUDED."tierEndedAt",
    "cardStatus" = EXCLUDED."cardStatus",
    "cardReason" = EXCLUDED."cardReason",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- MemberLegacyTierHistory
INSERT INTO loyalty_service."MemberLegacyTierHistory"
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."MemberLegacyTierHistory"') AS 
t1(id text,
   "memberId" text,
   "cardTypeCode" text,
   description text,
   "embossNo" text,
   "tierStartedAt" timestamp(3),
   "tierEndedAt" timestamp(3),
   "cardStatus" text,
   "cardReason" text,
   "createdAt" timestamp(3),
   "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "cardTypeCode" = EXCLUDED."cardTypeCode",
    description = EXCLUDED.description,
    "embossNo" = EXCLUDED."embossNo",
    "tierStartedAt" = EXCLUDED."tierStartedAt",
    "tierEndedAt" = EXCLUDED."tierEndedAt",
    "cardStatus" = EXCLUDED."cardStatus",
    "cardReason" = EXCLUDED."cardReason",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";


-- MemberLog
INSERT into staging_loyalty_service."MemberLog" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."MemberLog"') AS 
t1(id text,
    "memberId" text,
    "type" text,
    "name" text,
    description _text,
    "oldData" jsonb,
    "newData" jsonb,
    "createdBy" jsonb,
    "createdAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "type" = EXCLUDED."type",
    "name" = EXCLUDED."name",
    description = EXCLUDED.description,
    "oldData" = EXCLUDED."oldData",
    "newData" = EXCLUDED."newData",
    "createdBy" = EXCLUDED."createdBy",
    "createdAt" = EXCLUDED."createdAt";


-- MemberProfile
INSERT into staging_loyalty_service."MemberProfile" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."MemberProfile"') AS 
t1(id text,
    "memberId" text,
    "firstName" text,
    "firstNameTh" text,
    "middleName" text,
    "middleNameTh" text,
    "lastName" text,
    "lastNameTh" text,
    cid text,
    "passportNo" text,
    "passportExpiryDate" date,
    "dateOfBirth" text,
    gender text,
    "addressLine" text,
    "subDistrict" text,
    district text,
    province text,
    city text,
    "postalCode" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    occupation int4,
    title int4,
    "countryCode" text,
    "nationalityCode" text,
    "firstNameHash" text,
    "firstNameThHash" text,
    "middleNameHash" text,
    "middleNameThHash" text,
    "lastNameHash" text,
    "lastNameThHash" text,
    "cidHash" text,
    "passportNoHash" text,
    "dateOfBirthHash" text,
    "genderHash" text,
    "addressLineHash" text)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "firstName" = EXCLUDED."firstName",
    "firstNameTh" = EXCLUDED."firstNameTh",
    "middleName" = EXCLUDED."middleName",
    "middleNameTh" = EXCLUDED."middleNameTh",
    "lastName" = EXCLUDED."lastName",
    "lastNameTh" = EXCLUDED."lastNameTh",
    cid = EXCLUDED.cid,
    "passportNo" = EXCLUDED."passportNo",
    "passportExpiryDate" = EXCLUDED."passportExpiryDate",
    "dateOfBirth" = EXCLUDED."dateOfBirth",
    gender = EXCLUDED.gender,
    "addressLine" = EXCLUDED."addressLine",
    "subDistrict" = EXCLUDED."subDistrict",
    district = EXCLUDED.district,
    province = EXCLUDED.province,
    city = EXCLUDED.city,
    "postalCode" = EXCLUDED."postalCode",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    occupation = EXCLUDED.occupation,
    title = EXCLUDED.title,
    "countryCode" = EXCLUDED."countryCode",
    "nationalityCode" = EXCLUDED."nationalityCode",
    "firstNameHash" = EXCLUDED."firstNameHash",
    "firstNameThHash" = EXCLUDED."firstNameThHash",
    "middleNameHash" = EXCLUDED."middleNameHash",
    "middleNameThHash" = EXCLUDED."middleNameThHash",
    "lastNameHash" = EXCLUDED."lastNameHash",
    "lastNameThHash" = EXCLUDED."lastNameThHash",
    "cidHash" = EXCLUDED."cidHash",
    "passportNoHash" = EXCLUDED."passportNoHash",
    "dateOfBirthHash" = EXCLUDED."dateOfBirthHash",
    "genderHash" = EXCLUDED."genderHash",
    "addressLineHash" = EXCLUDED."addressLineHash";




-- MemberSubSegment
INSERT into staging_loyalty_service."MemberSubSegment" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."MemberSubSegment"') AS 
t1(id text,
    "memberId" text,
    "subSegmentCode" text,
    "createdAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "subSegmentCode" = EXCLUDED."subSegmentCode",
    "createdAt" = EXCLUDED."createdAt";


-- MemberTierHistory
INSERT into staging_loyalty_service."MemberTierHistory" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."MemberTierHistory"') AS 
t1(id text,
    "memberId" text,
    "fromTierId" text,
    "toTierId" text,
    "tierStartedAt" timestamp(3),
    "tierEndedAt" timestamp(3),
    "accumulateSpending" numeric(16,2),
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "fromTierId" = EXCLUDED."fromTierId",
    "toTierId" = EXCLUDED."toTierId",
    "tierStartedAt" = EXCLUDED."tierStartedAt",
    "tierEndedAt" = EXCLUDED."tierEndedAt",
    "accumulateSpending" = EXCLUDED."accumulateSpending",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";


-- PrivilegeTier
INSERT into staging_loyalty_service."PrivilegeTier" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."PrivilegeTier"') AS 
t1(id text,
    "tierId" text,
    "privilegeId" text,
    "createdAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "tierId" = EXCLUDED."tierId",
    "privilegeId" = EXCLUDED."privilegeId",
    "createdAt" = EXCLUDED."createdAt";


-- SalesTransaction
INSERT into staging_loyalty_service."SalesTransaction" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."SalesTransaction"') AS 
t1(id text,
    "memberId" text,
    "completedAt" timestamp(3),
    "externalId" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "netTotalAmount" numeric(16,2),
    "totalAccumSpendableAmount" numeric(16,2),
    "branchCode" text,
    "brandCode" text,
    "partnerCode" text)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "completedAt" = EXCLUDED."completedAt",
    "externalId" = EXCLUDED."externalId",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "netTotalAmount" = EXCLUDED."netTotalAmount",
    "totalAccumSpendableAmount" = EXCLUDED."totalAccumSpendableAmount",
    "branchCode" = EXCLUDED."branchCode",
    "brandCode" = EXCLUDED."brandCode",
    "partnerCode" = EXCLUDED."partnerCode";



-- StaffProfile
INSERT into loyalty_service."StaffProfile" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."StaffProfile"') AS 
t1(id text,
    "memberId" text,
    "staffLevelCode" text,
    "companyCode" text,
    "staffNo" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "staffPosition" text,
    "staffDivision" text,
    "staffJobLevel" text)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "staffLevelCode" = EXCLUDED."staffLevelCode",
    "companyCode" = EXCLUDED."companyCode",
    "staffNo" = EXCLUDED."staffNo",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "staffPosition" = EXCLUDED."staffPosition",
    "staffDivision" = EXCLUDED."staffDivision",
    "staffJobLevel" = EXCLUDED."staffJobLevel";

-- TierLog
INSERT into loyalty_service."TierLog" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."TierLog"') AS 
t1(id text,
    "updatedBy" jsonb,
    "oldData" jsonb,
    "newData" jsonb,
    "tierId" text,
    detail _text,
    "effectiveDate" timestamp(3),
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "updatedBy" = EXCLUDED."updatedBy",
    "oldData" = EXCLUDED."oldData",
    "newData" = EXCLUDED."newData",
    "tierId" = EXCLUDED."tierId",
    detail = EXCLUDED.detail,
    "effectiveDate" = EXCLUDED."effectiveDate",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- TierUpdateRequest
INSERT into loyalty_service."TierUpdateRequest" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."TierUpdateRequest"') AS 
t1(id text,
    "updatedBy" jsonb,
    "oldData" jsonb,
    "newData" jsonb,
    "effectiveDate" timestamp(3),
    "tierId" text,
    detail _text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "privileges" _text)
ON CONFLICT (id) DO UPDATE SET
    "updatedBy" = EXCLUDED."updatedBy",
    "oldData" = EXCLUDED."oldData",
    "newData" = EXCLUDED."newData",
    "effectiveDate" = EXCLUDED."effectiveDate",
    "tierId" = EXCLUDED."tierId",
    detail = EXCLUDED.detail,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "privileges" = EXCLUDED."privileges";



-- RefundSalesTransaction
INSERT into loyalty_service."RefundSalesTransaction" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."RefundSalesTransaction"') AS 
t1(id text,
    "memberId" text,
    "type" text,
    "salesTransactionId" text,
    "externalId" text,
    "refundedAt" timestamp(3),
    "refundAmount" numeric(16, 2),
    "revokeAccumSpendableAmount" numeric(16, 2),
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "type" = EXCLUDED."type",
    "salesTransactionId" = EXCLUDED."salesTransactionId",
    "externalId" = EXCLUDED."externalId",
    "refundedAt" = EXCLUDED."refundedAt",
    "refundAmount" = EXCLUDED."refundAmount",
    "revokeAccumSpendableAmount" = EXCLUDED."revokeAccumSpendableAmount",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

