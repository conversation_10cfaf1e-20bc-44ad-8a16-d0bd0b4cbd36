-- <PERSON><PERSON><PERSON>
INSERT into public."CoBrand" 
SELECT
    "id"
    ,"code"
    ,"name"
    ,"bankCode"
    ,"cardTypeCode"
    ,"cardBin"
    ,"cardImage"
    ,"minimumTierCode"
    ,"status"
    ,"createdAt"
    ,"updatedAt"
    ,"updatedBy"
    ,"detail"
    ,jsonb_build_object(
                        'id', NULL, 
                        'name', 'SYSTEM', 
                        'email', NULL
                        ) AS "createdBy"

FROM dblink('my_connection', 'SELECT * FROM loyalty_service."CoBrand"') AS 
t1( id text,
    code text,
    name text,
    "bankCode" text,
    "cardTypeCode" text,
    "cardBin" text,
    "cardImage" jsonb,
    "minimumTierCode" text,
    status text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    detail jsonb,
    "updatedBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    code = EXCLUDED.code,
    name = EXCLUDED.name,
    "bankCode" = EXCLUDED."bankCode",
    "cardTypeCode" = EXCLUDED."cardTypeCode",
    "cardBin" = EXCLUDED."cardBin",
    "cardImage" = EXCLUDED."cardImage",
    "minimumTierCode" = EXCLUDED."minimumTierCode",
    status = EXCLUDED.status,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    detail = EXCLUDED.detail,
    "updatedBy" = EXCLUDED."updatedBy";