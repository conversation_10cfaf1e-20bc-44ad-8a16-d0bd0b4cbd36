-- CoBrandPrivilege
INSERT into public."CoBrandPrivilege" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."CoBrandPrivilege"') AS 
t1( id text,
    "coBrandId" text,
    "privilegeId" text,
    "privilegeEligibleTiers" jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "coBrandId" = EXCLUDED."coBrandId",
    "privilegeId" = EXCLUDED."privilegeId",
    "privilegeEligibleTiers" = EXCLUDED."privilegeEligibleTiers",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";