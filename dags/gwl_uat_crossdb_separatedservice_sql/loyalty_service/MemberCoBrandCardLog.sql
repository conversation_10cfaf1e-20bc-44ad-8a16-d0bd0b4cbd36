-- MemberCoBrandCardLog
INSERT INTO public."MemberCoBrandCardLog"
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."MemberCoBrandCardLog"') AS 
t1(id text,
   "memberCoBrandCardId" text,
   "type" text,
   "name" text,
   description _text,
   "oldData" jsonb,
   "newData" jsonb,
   "createdBy" jsonb,
   "createdAt" timestamp(3),
   "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "memberCoBrandCardId" = EXCLUDED."memberCoBrandCardId",
    "type" = EXCLUDED."type",
    "name" = EXCLUDED."name",
    description = EXCLUDED.description,
    "oldData" = EXCLUDED."oldData",
    "newData" = EXCLUDED."newData",
    "createdBy" = EXCLUDED."createdBy",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";