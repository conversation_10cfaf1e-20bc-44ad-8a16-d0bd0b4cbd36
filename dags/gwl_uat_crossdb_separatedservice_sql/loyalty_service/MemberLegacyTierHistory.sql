-- MemberLegacyTierHistory
INSERT INTO public."MemberLegacyTierHistory" (
   "id"
   ,"memberId"
   ,"cardTypeCode"
   ,"embossNo"
   ,"description"
   ,"tierStartedAt"
   ,"tierEndedAt"
   ,"cardStatus"
   ,"cardReason"
   ,"createdAt"
   ,"updatedAt"
)
SELECT
   "id"
   ,"memberId"
   ,"cardTypeCode"
   ,"embossNo"
   ,"description"
   ,"tierStartedAt"
   ,"tierEndedAt"
   ,"cardStatus"
   ,"cardReason"
   ,"createdAt"
   ,"updatedAt"
    -- source_runno in staging no have in prod
    -- source_cardtypecode in staging no have in prod

FROM dblink('my_connection', 'SELECT * FROM loyalty_service."MemberLegacyTierHistory"') AS 
t1(id text,
   "memberId" text,
   "cardTypeCode" text,
   description text,
   "embossNo" text,
   "tierStartedAt" timestamp(3),
   "tierEndedAt" timestamp(3),
   "cardStatus" text,
   "cardReason" text,
   "createdAt" timestamp(3),
   "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "cardTypeCode" = EXCLUDED."cardTypeCode",
    description = EXCLUDED.description,
    "embossNo" = EXCLUDED."embossNo",
    "tierStartedAt" = EXCLUDED."tierStartedAt",
    "tierEndedAt" = EXCLUDED."tierEndedAt",
    "cardStatus" = EXCLUDED."cardStatus",
    "cardReason" = EXCLUDED."cardReason",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";
    -- diff is from alter columns between prod gwl and prod loyalty_service
-- Note: The "createdBy" and "updatedBy" fields are not included in the insert statement as they are not present in the source table.
-- fixed to be same sequence as prod gwl