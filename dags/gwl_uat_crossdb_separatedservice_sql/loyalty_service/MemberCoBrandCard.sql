-- MemberCoBrandCard
INSERT INTO public."MemberCoBrandCard"(
   "id"
   ,"memberId"
   ,"coBrandId"
   ,"cardNo"
   ,"memberCoBrandCardImportId"
   ,"status"
   ,"createdAt"
   ,"createdBy"
   ,"updatedAt"
   ,"updatedBy"
   ,"remark"
   ,"cardHolderName"
   ,"cardHolderNameHash"
   ,"cardReason"
)
SELECT
   "id"
   ,"memberId"
   ,"coBrandId"
   ,"cardNo"
   ,"memberCoBrandCardImportId"
   ,"status"
   ,"createdAt"
   ,"createdBy" -- existing jsonb from staging table and from smc
   ,"updatedAt"
   ,"updatedBy" -- existing jsonb from staging table and from smc
   ,"remark"
   ,"cardHolderName"
   ,"cardHolderNameHash"
   ,"cardReason"

FROM dblink('my_connection', 'SELECT * FROM loyalty_service."MemberCoBrandCard"') AS 
t1(id text,
   "memberId" text,
   "coBrandId" text,
   "cardNo" text,
   "memberCoBrandCardImportId" text,
   status text,
   "createdAt" timestamp(3),
   "createdBy" jsonb,
   "updatedAt" timestamp(3),
   "updatedBy" jsonb,
   "cardHolderName" text,
   "cardHolderNameHash" text,
   remark text,
   "cardReason" text)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "coBrandId" = EXCLUDED."coBrandId",
    "cardNo" = EXCLUDED."cardNo",
    "memberCoBrandCardImportId" = EXCLUDED."memberCoBrandCardImportId",
    status = EXCLUDED.status,
    "createdAt" = EXCLUDED."createdAt",
    "createdBy" = EXCLUDED."createdBy",
    "updatedAt" = EXCLUDED."updatedAt",
    "updatedBy" = EXCLUDED."updatedBy",
    "cardHolderName" = EXCLUDED."cardHolderName",
    "cardHolderNameHash" = EXCLUDED."cardHolderNameHash",
    remark = EXCLUDED.remark,
    "cardReason" = EXCLUDED."cardReason";