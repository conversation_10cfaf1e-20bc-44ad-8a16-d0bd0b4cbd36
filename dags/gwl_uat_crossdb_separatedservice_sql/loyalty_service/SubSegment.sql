-- SubSegment
INSERT into public."SubSegment" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."SubSegment"') AS 
t1( code text,
    name text,
    status text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "type" text)
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    status = EXCLUDED.status,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "type" = EXCLUDED."type";