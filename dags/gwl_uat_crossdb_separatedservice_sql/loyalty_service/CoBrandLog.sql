-- CoBrandLog
INSERT into public."CoBrandLog" 
SELECT
,"id"
,"oldData"
,"newData"
,"coBrandId"
,"detail"
,"createdAt"
,"updatedAt"
-- ,"createdBy"
-- ,"effectiveDate"
-- ,"status"

FROM dblink('my_connection', 'SELECT * FROM loyalty_service."CoBrandLog"') AS 
t1( id text,
    "oldData" jsonb,
    "newData" jsonb,
    "coBrandId" text,
    detail _text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3)
    )
    -- ,"updatedBy" jsonb)
ON CONFLICT (id) DO UPDATE SET
    "oldData" = EXCLUDED."oldData",
    "newData" = EXCLUDED."newData",
    "coBrandId" = EXCLUDED."coBrandId",
    detail = EXCLUDED.detail,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";
    -- ,"updatedBy" = EXCLUDED."updatedBy";