-- MemberCoBrandCardImport
INSERT into public."MemberCoBrandCardImport" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."MemberCoBrandCardImport"') AS 
t1( id text,
    file jsonb,
    status text,
    error text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "errorLogFile" jsonb)
ON CONFLICT (id) DO UPDATE SET
    file = EXCLUDED.file,
    status = EXCLUDED.status,
    error = EXCLUDED.error,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "errorLogFile" = EXCLUDED."errorLogFile";