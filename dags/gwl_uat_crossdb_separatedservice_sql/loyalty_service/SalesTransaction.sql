-- SalesTransaction
-- same order sequence as prod
INSERT into public."SalesTransaction" (
    "id"
    ,"memberId"
    ,"completedAt"
    ,"externalId"
    ,"createdAt"
    ,"updatedAt"
    ,"netTotalAmount"
    ,"totalAccumSpendableAmount"
    ,"branchCode"
    ,"brandCode"
    ,"partnerCode"
)
SELECT
    "id"
    ,"memberId"
    ,"completedAt"
    ,"externalId"
    ,"createdAt"
    ,"updatedAt"
    ,"netTotalAmount"
    ,"totalAccumSpendableAmount"
    ,"branchCode"
    ,"brandCode"
    ,"partnerCode"

FROM dblink('my_connection', 'SELECT * FROM loyalty_service."SalesTransaction"') AS 
t1(id text,
    "memberId" text,
    "completedAt" timestamp(3),
    "externalId" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "netTotalAmount" numeric(16,2),
    "totalAccumSpendableAmount" numeric(16,2),
    "branchCode" text,
    "brandCode" text,
    "partnerCode" text)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "completedAt" = EXCLUDED."completedAt",
    "externalId" = EXCLUDED."externalId",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "netTotalAmount" = EXCLUDED."netTotalAmount",
    "totalAccumSpendableAmount" = EXCLUDED."totalAccumSpendableAmount",
    "branchCode" = EXCLUDED."branchCode",
    "brandCode" = EXCLUDED."brandCode",
    "partnerCode" = EXCLUDED."partnerCode";