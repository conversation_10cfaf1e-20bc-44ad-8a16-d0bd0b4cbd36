-- MemberLegacyCoBrandHistory
-- same order sequence as prod
INSERT INTO public."MemberLegacyCoBrandHistory"(
   "id"
   ,"memberId"
   ,"cardTypeCode"
   ,"description"
   ,"embossNo"
   ,"startedAt"
   ,"endedAt"
   ,"cardStatus"
   ,"cardReason"
   ,"createdAt"
   ,"updatedAt"
)
SELECT
   "id"
   ,"memberId"
   ,"cardTypeCode"
   ,"description"
   ,"embossNo"
   ,"startedAt"
   ,"endedAt"
   ,"cardStatus"
   ,"cardReason"
   ,"createdAt"
   ,"updatedAt"

FROM dblink('my_connection', 'SELECT * FROM loyalty_service."MemberLegacyCoBrandHistory"') AS 
t1(id text,
   "memberId" text,
   "cardTypeCode" text,
   description text,
   "embossNo" text,
   "startedAt" timestamp(3),
   "endedAt" timestamp(3),
   "cardStatus" text,
   "cardReason" text,
   "createdAt" timestamp(3),
   "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "cardTypeCode" = EXCLUDED."cardTypeCode",
    description = EXCLUDED.description,
    "embossNo" = EXCLUDED."embossNo",
    "startedAt" = EXCLUDED."startedAt",
    "endedAt" = EXCLUDED."endedAt",
    "cardStatus" = EXCLUDED."cardStatus",
    "cardReason" = EXCLUDED."cardReason",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";