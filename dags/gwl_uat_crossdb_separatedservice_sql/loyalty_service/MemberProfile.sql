-- MemberProfile
-- adjust sequence to match prod (if select col less than prod, it will insert to prod with null value)
INSERT into public."MemberProfile" (
    "id"
    ,"memberId"
    ,"firstName"
    ,"firstNameTh"
    ,"middleName"
    ,"middleNameTh"
    ,"lastName"
    ,"lastNameTh"
    ,"cid"
    ,"passportNo"
    ,"passportExpiryDate"
    ,"dateOfBirth"
    ,"gender"
    ,"addressLine"
    ,"subDistrict"
    ,"district"
    ,"province"
    ,"city"
    ,"postalCode"
    ,"createdAt"
    ,"updatedAt"
    ,"occupation"
    ,"title"
    ,"countryCode"
    ,"nationalityCode"
    ,"addressLineHash"
    ,"cidHash"
    ,"firstNameHash"
    ,"firstNameThHash"
    ,"genderHash"
    ,"lastNameHash"
    ,"lastNameThHash"
    ,"middleNameHash"
    ,"middleNameThHash"
    ,"passportNoHash"
    ,"dateOfBirthHash"
    ,"updatedBy"
    ,"createdBy"
)
SELECT
    "id"
    ,"memberId"
    ,"firstName"
    ,"firstNameTh"
    ,"middleName"
    ,"middleNameTh"
    ,"lastName"
    ,"lastNameTh"
    ,"cid"
    ,"passportNo"
    ,"passportExpiryDate"
    ,"dateOfBirth"
    ,"gender"
    ,"addressLine"
    ,"subDistrict"
    ,"district"
    ,"province"
    ,"city"
    ,"postalCode"
    ,"createdAt"
    ,"updatedAt"
    ,"occupation"
    ,"title"
    ,"countryCode"
    ,"nationalityCode"
    ,"addressLineHash"
    ,"cidHash"
    ,"firstNameHash"
    ,"firstNameThHash"
    ,"genderHash"
    ,"lastNameHash"
    ,"lastNameThHash"
    ,"middleNameHash"
    ,"middleNameThHash"
    ,"passportNoHash"
    ,"dateOfBirthHash"
    ,"updatedBy"
    ,"createdBy"

FROM dblink('my_connection', 'SELECT * FROM loyalty_service."MemberProfile"') AS 
t1(id text,
    "memberId" text,
    "firstName" text,
    "firstNameTh" text,
    "middleName" text,
    "middleNameTh" text,
    "lastName" text,
    "lastNameTh" text,
    cid text,
    "passportNo" text,
    "passportExpiryDate" date,
    "dateOfBirth" text,
    gender text,
    "addressLine" text,
    "subDistrict" text,
    district text,
    province text,
    city text,
    "postalCode" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    occupation int4,
    title int4,
    "countryCode" text,
    "nationalityCode" text,
    "firstNameHash" text,
    "firstNameThHash" text,
    "middleNameHash" text,
    "middleNameThHash" text,
    "lastNameHash" text,
    "lastNameThHash" text,
    "cidHash" text,
    "passportNoHash" text,
    "dateOfBirthHash" text,
    "genderHash" text,
    "addressLineHash" text,
    "updatedBy" jsonb,
    "createdBy" jsonb)
ON CONFLICT ("memberId") DO UPDATE SET
    id = EXCLUDED.id,
    "firstName" = EXCLUDED."firstName",
    "firstNameTh" = EXCLUDED."firstNameTh",
    "middleName" = EXCLUDED."middleName",
    "middleNameTh" = EXCLUDED."middleNameTh",
    "lastName" = EXCLUDED."lastName",
    "lastNameTh" = EXCLUDED."lastNameTh",
    cid = EXCLUDED.cid,
    "passportNo" = EXCLUDED."passportNo",
    "passportExpiryDate" = EXCLUDED."passportExpiryDate",
    "dateOfBirth" = EXCLUDED."dateOfBirth",
    gender = EXCLUDED.gender,
    "addressLine" = EXCLUDED."addressLine",
    "subDistrict" = EXCLUDED."subDistrict",
    district = EXCLUDED.district,
    province = EXCLUDED.province,
    city = EXCLUDED.city,
    "postalCode" = EXCLUDED."postalCode",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    occupation = EXCLUDED.occupation,
    title = EXCLUDED.title,
    "countryCode" = EXCLUDED."countryCode",
    "nationalityCode" = EXCLUDED."nationalityCode",
    "firstNameHash" = EXCLUDED."firstNameHash",
    "firstNameThHash" = EXCLUDED."firstNameThHash",
    "middleNameHash" = EXCLUDED."middleNameHash",
    "middleNameThHash" = EXCLUDED."middleNameThHash",
    "lastNameHash" = EXCLUDED."lastNameHash",
    "lastNameThHash" = EXCLUDED."lastNameThHash",
    "cidHash" = EXCLUDED."cidHash",
    "passportNoHash" = EXCLUDED."passportNoHash",
    "dateOfBirthHash" = EXCLUDED."dateOfBirthHash",
    "genderHash" = EXCLUDED."genderHash",
    "addressLineHash" = EXCLUDED."addressLineHash",
    "updatedBy" = EXCLUDED."updatedBy",
    "createdBy" = EXCLUDED."createdBy";