-- RefundSalesTransaction
-- same order sequence as prod
INSERT into public."RefundSalesTransaction"(
    "id"
    ,"memberId"
    ,"type"
    ,"salesTransactionId"
    ,"externalId"
    ,"refundedAt"
    ,"refundAmount"
    ,"revokeAccumSpendableAmount"
    ,"createdAt"
    ,"updatedAt"
)
SELECT
    "id"
    ,"memberId"
    ,"type"
    ,"salesTransactionId"
    ,"externalId"
    ,"refundedAt"
    ,"refundAmount"
    ,"revokeAccumSpendableAmount"
    ,"createdAt"
    ,"updatedAt"

FROM dblink('my_connection', 'SELECT * FROM loyalty_service."RefundSalesTransaction"') AS 
t1(id text,
    "memberId" text,
    "type" text,
    "salesTransactionId" text,
    "externalId" text,
    "refundedAt" timestamp(3),
    "refundAmount" numeric(16, 2),
    "revokeAccumSpendableAmount" numeric(16, 2),
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "type" = EXCLUDED."type",
    "salesTransactionId" = EXCLUDED."salesTransactionId",
    "externalId" = EXCLUDED."externalId",
    "refundedAt" = EXCLUDED."refundedAt",
    "refundAmount" = EXCLUDED."refundAmount",
    "revokeAccumSpendableAmount" = EXCLUDED."revokeAccumSpendableAmount",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";