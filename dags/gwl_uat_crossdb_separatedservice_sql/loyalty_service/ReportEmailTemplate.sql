-- ReportEmailTemplate
INSERT into public."ReportEmailTemplate" 
SELECT * FROM dblink('my_connection', 'SELECT * FROM loyalty_service."ReportEmailTemplate"') AS 
t1( id text,
    code text,
    name text,
    subject text,
    "htmlFile" text,
    status text,
    settings jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3))
ON CONFLICT (id) DO UPDATE SET
    code = EXCLUDED.code,
    name = EXCLUDED.name,
    subject = EXCLUDED.subject,
    "htmlFile" = EXCLUDED."htmlFile",
    status = EXCLUDED.status,
    settings = EXCLUDED.settings,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";