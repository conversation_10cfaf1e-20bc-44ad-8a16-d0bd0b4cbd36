from common_helpers.database_services import <PERSON>g<PERSON><PERSON><PERSON><PERSON>

from common_helpers.logging import get_logger

from airflow import DAG
from airflow.operators.python_operator import PythonOperator

logger = get_logger()


def terminate_temp_db_processes():
    postgresql_handler = PostgresHandler(conn_id="temp_db_connection_id")

    logger.info("verifying active processes...")
    logger.info(
        postgresql_handler.extract_data(
            """
                SELECT
                    pid,
                    usename,
                    query,
                    state,
                    age(clock_timestamp(), query_start) AS duration
                FROM pg_stat_activity
                WHERE datname = current_database();
            """
        )
    )

    logger.info("terminating all processes...")
    logger.info(
        postgresql_handler.extract_data(
            """
                SELECT pg_terminate_backend(pid)
                FROM pg_stat_activity
                WHERE datname = current_database()
                AND pid <> pg_backend_pid();
            """
        )
    )

    logger.info("listing activities after termination...")
    logger.info(
        postgresql_handler.extract_data(
            """
                SELECT * FROM pg_stat_activity WHERE datname = current_database();
            """
        )
    )


with DAG(
    "terminate_temp_db_processes",
    default_args={
        "owner": "airflow",
    },
    schedule_interval=None,
    catchup=False,
) as terminate_temp_db_processes_dag:
    PythonOperator(
        task_id="terminate_temp_db_processes_task",
        python_callable=terminate_temp_db_processes,
    )
