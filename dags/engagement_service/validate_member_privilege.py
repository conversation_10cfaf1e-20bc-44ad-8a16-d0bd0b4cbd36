from datetime import datetime, timezone

from airflow.exceptions import AirflowException

from common_helpers.database_services import <PERSON>S<PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.logging import get_logger
from common_helpers.utils import insert_validation_logs
from constants import (
    SAMPLE_DATA_VALIDATION,
    SAMPLE_DATA_VALIDATION_ERROR,
)


logger = get_logger()


class MemberPrivilegeValidation:
    def __init__(
        self,
        service_name: str,
        mssql_handler: MSSQLHandler,
        postgresql_handler: PostgresHandler,
    ) -> None:
        self.service_name = service_name
        self.mssql_handler = mssql_handler
        self.postgresql_handler = postgresql_handler
        self.YOB = 2025
        self.errors = []

        current_utc = datetime.now(timezone.utc)
        formatted_date = current_utc.strftime("%Y%m%d")
        source_table = f"snapshot_lv_birthday_{formatted_date}"

        self.source_count_query_string = f"""
            SELECT
                COUNT(*)
            FROM {source_table} lvb
            JOIN LVTrans lvt ON lvt.LVTransKey = lvb.LVTransKey
            JOIN LVHeader lvh ON lvh.LVHeaderKey = lvt.LVHeaderKey
            JOIN LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
            WHERE YOB = {self.YOB};
        """
        self.source_select_query_string = f"""
            SELECT
                lvb.LVBirthdayKey AS id,
                lvb.LVTransKey AS lv_trans_key,
                lvb.LVNumber AS member_id,
                0 AS is_unlimited,
                DATEADD (HOUR, -7, lvh.AddDT) AS granted_at,
                DATEADD (HOUR, -7, lvh.AddDT) AS created_at,
                DATEADD (HOUR, -7,
                    CASE 
                        WHEN lvh.FinishDT IS NOT NULL THEN lvh.FinishDT
                        ELSE lvh.AddDT
                    END
                ) AS updated_at,
                CASE 
                    WHEN lvd.[ExpireDate] = CAST(lvd.[ExpireDate] AS DATE) 
                    THEN DATEADD(
                        MILLISECOND,
                        61199999, 
                        CAST(lvd.[ExpireDate] AS DATETIME2(3))
                    )
                    ELSE DATEADD (HOUR, -7, lvd.[ExpireDate])
                END AS expired_at,
                'ACTIVE' as status
            FROM {source_table} lvb
            JOIN LVTrans lvt ON lvt.LVTransKey = lvb.LVTransKey
            JOIN LVHeader lvh ON lvh.LVHeaderKey = lvt.LVHeaderKey
            JOIN LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
            WHERE YOB = {self.YOB}
        """

    def transform_record(
        self,
        record: tuple,
    ) -> tuple:
        """
        Transform a record queried from source table to destination table schema format.

        Args:
            record (tuple): A record queried from source table.

        Returns:
            tuple: A record in destination table schema format.
        """
        (
            id,
            lv_trans_key,
            member_id,
            is_unlimited,
            granted_at,
            created_at,
            updated_at,
            expired_at,
            status,
        ) = record
        id = f"{id}_{lv_trans_key}" if lv_trans_key else id
        is_unlimited = bool(is_unlimited)

        return (
            id,
            member_id,
            self.service_name,
            is_unlimited,
            granted_at,
            created_at,
            updated_at,
            expired_at,
            status,
        )

    def validate_sample_data(self):
        transformed_source_records = [
            self.transform_record(record)
            for record in self.mssql_handler.extract_data(
                self.source_select_query_string
            )
        ]

        transformed_source_record_ids = [
            record[0] for record in transformed_source_records
        ]

        destination_sample_query_string = f"""
            SELECT
                "id",
                "memberId",
                "privilegeId",
                "isUnlimited",
                "grantedAt",
                "createdAt",
                "updatedAt",
                "expiredAt",
                "status"
            FROM engagement_service."MemberPrivilege" WHERE id IN ({", ".join(["'" + str(id) + "'" for id in transformed_source_record_ids])})
        """

        postgresql_connection = self.postgresql_handler.hook.get_conn()

        with postgresql_connection.cursor() as cursor:
            cursor.execute(destination_sample_query_string, (self.service_name,))
            destination_records = cursor.fetchall()

        source_dict = {
            record[0]: {
                "member_id": record[1],
                "privilege_id": record[2],
                "is_unlimited": bool(record[3]),
                "granted_at": record[4],
                "created_at": record[5],
                "updated_at": record[6],
                "expired_at": record[7],
                "status": record[8],
            }
            for record in transformed_source_records
        }

        destination_dict = {
            record[0]: {
                "member_id": record[1],
                "privilege_id": record[2],
                "is_unlimited": record[3],
                "granted_at": record[4],
                "created_at": record[5],
                "updated_at": record[6],
                "expired_at": record[7],
                "status": record[8],
            }
            for record in destination_records
        }

        source_ids = set(source_dict.keys())
        destination_ids = set(destination_dict.keys())
        missing_ids = source_ids - destination_ids
        extra_ids = destination_ids - source_ids

        data_mismatches = []
        common_ids = source_ids.intersection(destination_ids)

        for record_id in common_ids:
            source_data = source_dict[record_id]
            dest_data = destination_dict[record_id]

            mismatches = []

            fields_to_compare = [
                "member_id",
                "privilege_id",
                "is_unlimited",
                "granted_at",
                "created_at",
                "updated_at",
                "expired_at",
                "status",
            ]

            for field in fields_to_compare:
                if source_data[field] != dest_data[field]:
                    mismatches.append(
                        f"{field}: source={source_data[field]}, dest={dest_data[field]}"
                    )

            if mismatches:
                data_mismatches.append((record_id, mismatches))

        if missing_ids:
            logger.warning(
                f"found {len(missing_ids)} records missing in destination table"
            )
            logger.warning(f"missing record IDs: {', '.join(sorted(missing_ids))}")
        else:
            logger.info("no missing records found in destination table")

        if extra_ids:
            logger.warning(f"found {len(extra_ids)} extra records in destination table")
            logger.warning(f"extra record IDs: {', '.join(sorted(extra_ids))}")
        else:
            logger.info("no extra records found in destination table")

        if data_mismatches:
            logger.warning(f"found {len(data_mismatches)} records with data mismatches")
            for record_id, mismatches in data_mismatches:
                logger.warning(f"data mismatches for record {record_id}:")
                for mismatch in mismatches:
                    logger.warning(f"  - {mismatch}")
        else:
            logger.info("no data mismatches found in matching records")

        if not missing_ids and not extra_ids and not data_mismatches:
            logger.info(f"sample data validation passed: all sample records match")
        else:
            self.errors.append(SAMPLE_DATA_VALIDATION_ERROR)

    def validate(self, temp_db_handler: PostgresHandler, service: str) -> None:
        logger.info("started sample data validation...")
        self.validate_sample_data()
        logger.info("finished sample data validation.")

        insert_validation_logs(
            errors=self.errors,
            db_handler=temp_db_handler,
            service=service,
            source_table="LVBirthday",
            destination_table="MemberPrivilege",
            validation_types=[
                {
                    "name": SAMPLE_DATA_VALIDATION,
                    "error": SAMPLE_DATA_VALIDATION_ERROR,
                },
            ],
        )

        if len(self.errors):
            raise AirflowException("validation(s) failed.")
