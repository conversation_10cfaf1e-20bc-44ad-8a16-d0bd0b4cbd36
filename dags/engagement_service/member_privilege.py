import math
import pytz
import threading
from concurrent.futures import as_completed, ThreadPoolExecutor
from datetime import datetime, timedelta, timezone

from psycopg2.extensions import connection as postgres_connection

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.logging import get_logger
from common_helpers.utils import get_query_offsets, insert_migration_result

logger = get_logger()


class MemberPrivilege:
    def __init__(
        self,
        batch_size: int,
        executor_max_workers: int,
        mssql_handler: MSSQLHandler,
        postgresql_handler: PostgresHandler,
        service_name: str,
    ) -> None:
        self.batch_size = batch_size
        self.executor_max_workers = executor_max_workers
        self.mssql_handler = mssql_handler
        self.postgresql_handler = postgresql_handler
        self.service_name = service_name
        self.YOB = 2025

        current_utc = datetime.now(timezone.utc)
        formatted_date = current_utc.strftime("%Y%m%d")
        source_table = f"snapshot_lv_birthday_{formatted_date}"

        self.count_query_string = f"""
            SELECT
                COUNT(*)
            FROM
                {source_table} lvb
                JOIN LVTrans lvt ON lvt.LVTransKey = lvb.LVTransKey
                JOIN LVHeader lvh ON lvh.LVHeaderKey = lvt.LVHeaderKey
                JOIN LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
            WHERE "YOB" = {self.YOB};
        """
        self.select_query_string = f"""
            SELECT
                lvb.LVBirthdayKey AS id,
                lvb.LVTransKey AS lv_trans_key,
                lvb.LVNumber AS member_id,
                0 AS is_unlimited,
                DATEADD (HOUR, -7, lvh.AddDT) AS granted_at,
                DATEADD (HOUR, -7, lvh.AddDT) AS created_at,
                DATEADD (HOUR, -7,
                    CASE 
                        WHEN lvh.FinishDT IS NOT NULL THEN lvh.FinishDT
                        ELSE lvh.AddDT
                    END
                ) AS updated_at,
                CASE 
                    WHEN lvd.[ExpireDate] = CAST(lvd.[ExpireDate] AS DATE) 
                    THEN DATEADD(
                        MILLISECOND,
                        61199999, 
                        CAST(lvd.[ExpireDate] AS DATETIME2(3))
                    )
                    ELSE DATEADD (HOUR, -7, lvd.[ExpireDate])
                END AS expired_at,
                'ACTIVE' as status
            FROM {source_table} lvb
            JOIN LVTrans lvt ON lvt.LVTransKey = lvb.LVTransKey
            JOIN LVHeader lvh ON lvh.LVHeaderKey = lvt.LVHeaderKey
            JOIN LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
            WHERE
                "YOB" = {self.YOB}
            ORDER BY
                id,
                lv_trans_key
            OFFSET
                %s ROWS
            FETCH NEXT
                %s ROWS ONLY;
        """
        self.destination_insert_query = """
            INSERT INTO "engagement_service"."MemberPrivilege" (
                "id",
                "memberId",
                "privilegeId",
                "isUnlimited",
                "grantedAt",
                "createdAt",
                "updatedAt",
                "expiredAt",
                "status"
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT ("id") DO NOTHING;
        """

    def transform_record(
        self,
        record: tuple,
    ) -> tuple:
        """
        Transform a record queried from source table to destination table schema format.

        Args:
            record (tuple): A record queried from source table.

        Returns:
            tuple: A record in destination table schema format.
        """
        (
            id,
            lv_trans_key,
            member_id,
            is_unlimited,
            granted_at,
            created_at,
            updated_at,
            expired_at,
            status,
        ) = record
        id = f"{id}_{lv_trans_key}" if lv_trans_key else id
        is_unlimited = bool(is_unlimited)

        return (
            id,
            member_id,
            self.service_name,
            is_unlimited,
            granted_at,
            created_at,
            updated_at,
            expired_at,
            status,
        )

    def insert_batch_to_destination(
        self,
        connection: postgres_connection,
        batch: list[tuple],
    ) -> None:
        """
        Insert a batch to destination table.

        Args:
            connection (postgres_connection): A PostgreSQL connection.
            batch (list[tuple]): A list of records to insert to destination table.

        Returns:
            None
        """
        self.postgresql_handler.execute_with_rollback(
            connection, self.destination_insert_query, batch
        )

    def process_batch(
        self,
        connection: postgres_connection,
        batch: list[tuple],
        batch_no: int,
        total_batches: int,
        total_records: int,
    ) -> None:
        """
        Transform queried result from source table and insert them to a new table.

        Args:
            connection (postgres_connection): A PostgreSQL connection.
            batch (list[tuple]): A batch to process.
            batch_no (int): The current batch's number, used only for logging.
            total_batches (int): The total number of batches to process, used only for logging.
            total_records (int): The total number of records being processed, used for batch tracking.

        Returns:
            None
        """
        logger.info(
            f"started transforming and inserting batch {batch_no}/{total_batches} (size {len(batch)})..."
        )
        transformed_batch = [self.transform_record(record=record) for record in batch]
        self.insert_batch_to_destination(
            connection=connection,
            batch=transformed_batch,
        )
        logger.info(
            f"successfully transformed and inserted batch {batch_no}/{total_batches} (size {len(batch)})."
        )
        self.postgresql_handler.update_batch_tracker(
            connection=connection,
            service_name="engagement_service",
            table_name="MemberPrivilege",
            total_records=total_records,
            batch_no=batch_no,
        )

    def migrate(self, is_full_dump: bool = True) -> None:
        """
        The main function for MemberPrivilege migration flow.

        Args:
            is_full_dump (bool): Whether this is a full dump migration vs incremental.

        Returns:
            None
        """
        created_at = datetime.now()
        incremental_date = (
            None
            if is_full_dump
            else (
                datetime.now(pytz.timezone("Asia/Bangkok")).date() - timedelta(days=1)
            ).strftime("%Y-%m-%d")
        )

        mssql_connection = self.mssql_handler.hook.get_conn()
        postgresql_connection = self.postgresql_handler.hook.get_conn()

        tracker = self.postgresql_handler.get_latest_batch_info(
            connection=postgresql_connection,
            service_name="engagement_service",
            table_name="MemberPrivilege",
        )

        total_records = (
            self.mssql_handler.get_table_total_records(self.count_query_string)
            if tracker is None
            else tracker[0]
        )
        total_batches = math.ceil(total_records / self.batch_size)
        offsets = get_query_offsets(
            total_records=total_records,
            batch_size=self.batch_size,
            starting_offset=(
                0 if tracker is None else (tracker[1] - 1) * self.batch_size
            ),
        )
        completed_batches = tracker[2] if tracker is not None else []

        is_migration_succeeded = False

        try:
            futures = []

            batch_generator = self.mssql_handler.generate_batches(
                connection=mssql_connection,
                query_string=self.select_query_string,
                total_records=total_records,
                batch_size=self.batch_size,
                offsets=offsets,
                completed_batches=completed_batches,
            )

            with ThreadPoolExecutor(max_workers=self.executor_max_workers) as executor:
                semaphore = threading.Semaphore(self.executor_max_workers)

                while True:
                    semaphore.acquire()

                    try:
                        batch, batch_no = next(batch_generator)
                    except StopIteration:
                        break

                    future = executor.submit(
                        self.process_batch,
                        postgresql_connection,
                        batch,
                        batch_no,
                        total_batches,
                        total_records,
                    )
                    futures.append(future)
                    future.add_done_callback(lambda _: semaphore.release())

                for future in as_completed(futures):
                    future.result()

            logger.info(
                f"succesfully processed {total_records} records into MemberPrivilege"
            )

            logger.info(f"started cleaning up batch tracker...")
            self.postgresql_handler.cleanup_batch_tracker(
                connection=postgresql_connection,
                service_name="engagement_service",
                table_name="MemberPrivilege",
            )
            logger.info(f"finished cleaning up batch tracker.")

            is_migration_succeeded = True

        except Exception as error:
            logger.error(f"an error has occured: {error}")

            if postgresql_connection:
                logger.info("a postgresql connection is found, rolling back...")
                postgresql_connection.rollback()
                logger.info("successfully rolled back.")

            raise error
        finally:
            logger.info("started inserting migration result log...")
            if is_migration_succeeded:
                insert_migration_result(
                    postgresql_handler=self.postgresql_handler,
                    dag_name="engagement_service_migration",
                    migration_type="FULL_DUMP" if is_full_dump else "INCREMENTAL",
                    source_table="LVBirthday",
                    source_table_count=total_records,
                    destination_table="MemberPrivilege",
                    destination_table_count=total_records,
                    validation_type="COMPLETENESS",
                    validation_result=100,
                    created_at=created_at,
                    incremental_date=incremental_date,
                )
            else:
                tracker = self.postgresql_handler.get_latest_batch_info(
                    connection=postgresql_connection,
                    service_name="engagement_service",
                    table_name="MemberPrivilege",
                )

                destination_table_count = (
                    0
                    if tracker is None
                    else (
                        ((tracker[1] - 1) * self.batch_size)
                        if (tracker[1] - 1) * self.batch_size <= total_records
                        else total_records
                    )
                )

                total_processed = (
                    0 if tracker is None else len(tracker[2]) * self.batch_size
                )

                if tracker is not None and total_batches in tracker[2]:
                    total_processed -= self.batch_size + total_records % self.batch_size

                insert_migration_result(
                    postgresql_handler=self.postgresql_handler,
                    dag_name="engagement_service_migration",
                    migration_type="FULL_DUMP" if is_full_dump else "INCREMENTAL",
                    source_table="LVBirthday",
                    source_table_count=total_records,
                    destination_table="MemberPrivilege",
                    destination_table_count=destination_table_count,
                    validation_type="COMPLETENESS",
                    validation_result=(
                        0 if tracker is None else total_processed / total_records * 100
                    ),
                    created_at=created_at,
                    incremental_date=incremental_date,
                )
            logger.info("finished inserting migration result log.")

            if mssql_connection:
                mssql_connection.close()
            if postgresql_connection:
                postgresql_connection.close()
