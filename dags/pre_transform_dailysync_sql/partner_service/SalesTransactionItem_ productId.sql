SELECT
    p.id AS "productId"
    ,st."brandId"
    ,b."name"->'en' AS "brandNameEn"
    ,b."name"->'th' AS "brandNameTh"
    ,sti.sku
    ,p.name AS "productName"
    ,st.id as "salesTransactionId"
FROM staging_partner_service."SalesTransactionItem" sti
INNER JOIN partner_service."SalesTransaction"   AS st   ON sti."salesTransactionId" = st.id
LEFT JOIN partner_service."Product" AS p ON (st."brandId" = p."brandId" AND sti.sku = p.sku)
LEFT JOIN partner_service."Brand" AS b ON st."brandId" = b.id