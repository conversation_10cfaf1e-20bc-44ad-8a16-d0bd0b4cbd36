-- DROP TABLE IF EXISTS public.member_reason CASCADE

CREATE TABLE IF NOT EXISTS public.member_reason (
    "memberId" text not null PRIMARY KEY,
    reason text null DEFAULT NULL
);


WITH cardtypecode_tier AS
(SELECT 'N_NY' AS cardTypeCode, 10 AS VALUE
    UNION ALL
    SELECT 'P_CR', 10
    UNION ALL
    SELECT 'N_SL', 9
    UNION ALL
    SELECT 'OV_SL', 9
    UNION ALL
    SELECT 'OT_SL', 9
    UNION ALL
    SELECT 'N_CR', 8
    UNION ALL
    SELECT 'N_OX', 7
    UNION ALL
    SELECT 'OT_OX', 7
    UNION ALL
    SELECT 'OV_OX', 7
    UNION ALL
    SELECT 'IN_V1', 6
    UNION ALL
    SELECT 'IN_V2', 5
    UNION ALL
    SELECT 'IN_V3', 5
    UNION ALL
    SELECT 'IN_K', 4
    UNION ALL
    SELECT 'VEGA', 3
    UNION ALL
    SELECT 'NVVIP', 2
    UNION ALL
    SELECT 'C_STF', 1
    UNION ALL
    SELECT 'CS_MS', 1
    )
,
max_cardtypecode_tier AS

(SELECT DISTINCT ON (mlth."memberId")
    mlth."memberId",
    mlth."cardTypeCode",
    ct.value
FROM
    staging_loyalty_service."MemberLegacyTierHistory" AS mlth
LEFT JOIN
    cardtypecode_tier AS ct ON mlth."cardTypeCode" = ct.cardTypeCode
-- WHERE    mlth."cardStatus" = 'ACTIVE'
ORDER BY
    mlth."memberId", ct.value ASC
)
--SELECT * FROM max_cardtypecode_tier
--SELECT count(*) FROM max_cardtypecode_tier--1531316

,
distinct_member_mlth AS (
				SELECT DISTINCT "memberId" --true as "isActive"
				FROM staging_loyalty_service."MemberLegacyTierHistory"
                    )

,
active_member_mlth AS (
				SELECT DISTINCT "memberId"
				FROM staging_loyalty_service."MemberLegacyTierHistory"
                    WHERE "cardStatus" IN ('ACTIVE')
                    --WHERE "cardStatus" NOT IN ('ACTIVE')
                    )
         

--SELECT count(*) FROM isactive_member_mlth --distinct 1372798 , inactive 1179861 , total distinct 2552659, ok correct
--SELECT (1372798+1179861) -- 2552659
             -- SELECT distinct "cardStatus" FROM isactive_member_mlth      
,
inactive_member_mlth AS (
				SELECT DISTINCT "memberId", "cardStatus" --true as "isActive"
				FROM staging_loyalty_service."MemberLegacyTierHistory"
                    --WHERE "cardStatus" IN ('ACTIVE')
                    WHERE "cardStatus" NOT IN ('ACTIVE')
                    )
--SELECT count(*) FROM inactive_member_mlth --1179861
,
isactive_member_mlch AS (
				SELECT DISTINCT "memberId" --true as "isActive"
				FROM 
					(
                    SELECT "memberId", "cardStatus" 
                    FROM staging_loyalty_service."MemberLegacyCoBrandHistory"
                    WHERE "cardStatus" IN ('ACTIVE')
                    )
)

,scb_inact_act AS 
(
-- scb inactive and actitve
SELECT "memberId"
FROM staging_loyalty_service."MemberLegacyCoBrandHistory"
WHERE ("cardTypeCode" NOT IN ('BVP05', 'BVP10', 'BVP15', 'BVP20', 'BVS', 'BA', 'BG')  OR source_cardtypecode NOT IN ('KBANK'))
--and "cardStatus" = 'ACTIVE'
GROUP BY "memberId"
)

, kbank_wo_scb_kbankonly AS
(SELECT DISTINCT mlch."memberId"
FROM staging_loyalty_service."MemberLegacyCoBrandHistory" mlch
LEFT JOIN scb_inact_act AS sia ON mlch."memberId" = sia."memberId"
WHERE sia."memberId" IS NULL
) --135,318
--SELECT count(*) FROM kbank_wo_scb_kbankonly -- 86352

-- left exclude join to get only kbank - no lv ever -no scb
,kbank_wo_scb_kbankonly_no_lv_ever AS
(SELECT kwos_only."memberId"
FROM kbank_wo_scb_kbankonly kwos_only
LEFT JOIN distinct_member_mlth dmm ON kwos_only."memberId" = dmm."memberId"
WHERE dmm."memberId" IS NULL
)
--SELECT count(*) FROM kbank_wo_scb_kbankonly_no_lv_ever--67227

, member_scb_kbank_ina_a_that_no_lv_ever AS (
SELECT distinct mlcbh."memberId"
FROM staging_loyalty_service."MemberLegacyCoBrandHistory" mlcbh 
LEFT JOIN distinct_member_mlth dmm on mlcbh."memberId" = dmm."memberId"
WHERE dmm."memberId" IS NULL
)

,
member_data AS (
SELECT
    t."memberId"
    --,t."isActive"
    ,isactive_member_mlch."memberId" AS isam_mlch_memberid--IS NOT NULL THEN true ELSE false end AS "isActive"
    ,CASE WHEN distinct_member_mlth."memberId" IS NOT NULL THEN true ELSE false END AS distinct_member_mlth
    ,CASE WHEN active_member_mlth."memberId" IS NOT NULL THEN true ELSE false END AS active_member_mlth
    ,CASE WHEN isactive_member_mlch."memberId" IS NOT NULL THEN true ELSE false END AS isactive_member_mlch -- no use case kbank only leak
    ,CASE WHEN susp."memberId" IS NOT NULL THEN true ELSE false END AS is_blacklisted
	,CASE WHEN mexp."memberId" IS NOT NULL THEN true ELSE false END AS is_expired_notmigrate
    ,CASE WHEN mskiatnle."memberId" IS NOT NULL THEN true ELSE false END as mskiatnle
    ,CASE WHEN other_bank."memberId" IS NOT NULL THEN true ELSE false END as has_other_cobrand_mix
    ,CASE WHEN other_bank_active."memberId" IS NOT NULL THEN true ELSE false END as has_other_cobrand_active
    ,CASE WHEN other_bank_inactive."memberId" IS NOT NULL THEN true ELSE false END as has_other_cobrand_inactive
	,CASE WHEN mct."memberId" IS NULL then false ELSE true END AS lv
    ,t."createdAt"
    ,t."updatedAt"
--FROM    public.tiertransformed t
FROM    (SELECT id AS "memberId", "createdAt", "updatedAt" FROM staging_loyalty_service."Member") t
LEFT JOIN
    public.members_blacklist susp ON t."memberId" = susp."memberId"
LEFT JOIN
    public.members_expired_notmigrate mexp ON t."memberId" = mexp."memberId"
LEFT JOIN member_scb_kbank_ina_a_that_no_lv_ever mskiatnle ON t."memberId" = mskiatnle."memberId"
LEFT JOIN (
    SELECT "memberId"
	FROM staging_loyalty_service."MemberLegacyCoBrandHistory"
	WHERE ("cardTypeCode" NOT IN ('BVP05', 'BVP10', 'BVP15', 'BVP20', 'BVS', 'BA', 'BG')  or source_cardtypecode NOT IN ('KBANK'))
        --and "cardStatus" IN ('ACTIVE')
	GROUP BY "memberId"
) other_bank
ON t."memberId" = other_bank."memberId"
LEFT JOIN (
    SELECT "memberId"
	FROM staging_loyalty_service."MemberLegacyCoBrandHistory"
	WHERE ("cardTypeCode" NOT IN ('BVP05', 'BVP10', 'BVP15', 'BVP20', 'BVS', 'BA', 'BG')  or source_cardtypecode NOT IN ('KBANK'))
        and "cardStatus" IN ('ACTIVE')
	GROUP BY "memberId"
) other_bank_active
ON t."memberId" = other_bank_active."memberId"
LEFT JOIN (
    SELECT "memberId"
	FROM staging_loyalty_service."MemberLegacyCoBrandHistory"
	WHERE ("cardTypeCode" NOT IN ('BVP05', 'BVP10', 'BVP15', 'BVP20', 'BVS')  or source_cardtypecode NOT IN ('KBANK'))
        and "cardStatus" NOT IN ('ACTIVE')
	GROUP BY "memberId"
) other_bank_inactive
ON t."memberId" = other_bank_inactive."memberId"
LEFT JOIN
    max_cardtypecode_tier mct ON t."memberId" = mct."memberId"
LEFT JOIN distinct_member_mlth ON t."memberId" = distinct_member_mlth."memberId"    
LEFT JOIN active_member_mlth ON t."memberId" = active_member_mlth."memberId"
LEFT JOIN
    isactive_member_mlch ON t."memberId" = isactive_member_mlch."memberId"
)
--SELECT count(*) FROM member_data WHERE not_lv = true --224814
--SELECT count(*) FROM member_data WHERE has_other_cobrand = true --135121
--SELECT * FROM member_data limit 100
--SELECT count(*) FROM member_data WHERE active_member_mlth = true
,
label_reason AS (
    SELECT
        md."memberId",
        CASE
            WHEN is_blacklisted THEN 'Suspended' --1
            --WHEN ("isActive" = FALSE AND is_expired_notmigrate ) THEN 'NOTMIGRATE EXCLUDED LIST' --2
            WHEN ( active_member_mlth = false AND is_expired_notmigrate ) THEN 'NOTMIGRATE_EXCLUDED_LIST' --2  ok .....
            
            WHEN ( ( distinct_member_mlth = false ) AND (has_other_cobrand_mix = false )) THEN 'NOTMIGRATE_NO_LV_NO_COBRAND' -- distinct_member_mlth check no have LV ever + no SCB ever
            
            WHEN ( distinct_member_mlth = true AND active_member_mlth = false AND has_other_cobrand_mix = false ) THEN 'MIGRATE_LV_INACTIVE_NO_COBRAND' -- has LV history but inactive + no cobrand
            
            WHEN ( distinct_member_mlth = false AND has_other_cobrand_active) THEN 'MIGRATE_NO_LV_COBRAND_ACTIVE' --no have LV ever + have SCB active
            WHEN ( distinct_member_mlth = false AND has_other_cobrand_inactive) THEN 'MIGRATE_NO_LV_COBRAND_INACTIVE' --no have LV ever + have SCB inactive
            WHEN (  active_member_mlth ) THEN 'MIGRATE_LV_ACTIVE' --have lv active
            WHEN (  active_member_mlth = false AND has_other_cobrand_active) THEN 'MIGRATE_LV_INACTIVE_COBRAND_ACTIVE' --have lv inactive + have SCB active
            WHEN (  active_member_mlth = false AND has_other_cobrand_inactive) THEN 'MIGRATE_LV_INACTIVE_COBRAND_INACTIVE' --have lv inactive + have SCB inactive

            ELSE NULL --5 change NULL to 'MIGRATE OTHER'
        END AS reason,
        "createdAt",
        "updatedAt"
    FROM member_data md
)
--select count(*) FROM label_reason
--select * FROM label_reason limit 10

--select count(*) FROM label_reason WHERE  reason = 'Suspended' -- 37

--select count(*) FROM label_reason WHERE  reason = 'NOTMIGRATE_EXCLUDED_LIST' --137997

--select count(*) FROM label_reason WHERE  reason = 'MIGRATE_LV_INACTIVE_NO_COBRAND' --4724

--select count(*) FROM label_reason WHERE  reason = 'NOTMIGRATE_NO_LV_NO_COBRAND' --67704

--select count(*) FROM label_reason WHERE  reason = 'MIGRATE_NO_LV_COBRAND_ACTIVE' --71865

--select count(*) FROM label_reason WHERE  reason = 'MIGRATE_NO_LV_COBRAND_INACTIVE'--85245

--select count(*) FROM label_reason WHERE  reason = 'MIGRATE_LV_ACTIVE' --1372236

--select count(*) FROM label_reason WHERE  reason = 'MIGRATE_LV_INACTIVE_COBRAND_ACTIVE' --14425

--select count(*) FROM label_reason WHERE  reason = 'MIGRATE_LV_INACTIVE_COBRAND_INACTIVE' --1897



--select count(*) FROM label_reason WHERE  reason IS NULL --4723 -->> 0 after fixed
--,null_member as (select * FROM label_reason WHERE  reason IS NULL)
--select * FROM null_member

--select * FROM staging_loyalty_service."Member" m  
--LEFT JOIN loyalty_service."MemberLegacyCoBrandHistory" mlcbh on m.id = mlcbh."memberId"
--WHERE m."id" in (select "memberId" FROM null_member) and mlcbh."memberId" is not null


INSERT INTO public.member_reason ("memberId", reason, "createdAt", "updatedAt")
SELECT * FROM label_reason
ON CONFLICT ("memberId") DO UPDATE set
reason = EXCLUDED.reason,
"createdAt" = EXCLUDED."createdAt",
"updatedAt" = EXCLUDED."updatedAt";