-- staging_point_service."WalletActivityListJson" definition

-- Drop table

-- DROP TABLE staging_point_service."WalletActivityListJson";

CREATE TABLE IF NOT EXISTS staging_point_service."WalletActivityListJson" (
	"WalletActivityId" text NOT NULL,
	"ListJsonValues" jsonb NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "WalletActivityListJson_pkey" PRIMARY KEY ("WalletActivityId")
);

WITH stg_walj AS 
(
SELECT
    stg_waplj."WalletActivityId",
    json_agg(stg_waplj.transaction_json_values) AS "ListJsonValues",
    stg_waplj."createdAt",
    stg_waplj."updatedAt"
FROM
    staging_point_service."WalletActivityPriorListJson" stg_waplj
--WHERE stg_waplj."WalletActivityId" like '5426897_%'
-- WHERE "createdAt" = '2020-10-28'
GROUP BY
    stg_waplj."WalletActivityId",
    stg_waplj."createdAt",
    stg_waplj."updatedAt"
)


INSERT INTO staging_point_service."WalletActivityListJson" (
    "WalletActivityId",
    "ListJsonValues",
    "createdAt",
    "updatedAt"
)

SELECT
    "WalletActivityId",
    "ListJsonValues",
    "createdAt",
    "updatedAt"
FROM stg_walj
ON CONFLICT ("WalletActivityId") DO UPDATE SET
    "ListJsonValues" = EXCLUDED."ListJsonValues",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";
--  Updated Rows	21458533    Execute time	7m 43s