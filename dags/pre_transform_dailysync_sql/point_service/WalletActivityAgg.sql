-- staging_point_service."WalletActivityAgg" definition

-- Drop table

-- DROP TABLE staging_point_service."WalletActivityAgg";

CREATE TABLE IF NOT EXISTS staging_point_service."WalletActivityAgg" (
	id text NOT NULL,
	"memberId" text NOT NULL,
	"walletCode" text NOT NULL,
	"type" text NOT NULL,
	"refType" text NOT NULL,
	"refId" text NOT NULL,
	"externalId" text NULL,
	amount numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"partnerCode" text NULL,
	"brandCode" text NULL,
	"branchCode" text NULL,
	remark text NULL,
	detail jsonb NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"documentDate" timestamp(3) DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT "WalletActivityAgg_pkey" PRIMARY KEY (id)
);
--CREATE INDEX WalletActivityAgg_refid_idx ON staging_point_service."WalletActivity" USING btree ("refId");


WITH waagg AS (
    SELECT 
        max(wa.id) AS id,
        wa."memberId" AS "memberId",
        wa."walletCode" AS "walletCode",
        wa.type AS type,
        wa."refType" AS "refType",
        wa."refId" AS "refId",
        wa."externalId" AS "externalId",
        sum(wa.amount) AS amount,
        wa."partnerCode" AS "partnerCode",
        wa."brandCode" AS "brandCode",
        wa."branchCode" AS "branchCode",
        wa.remark AS remark,
        wa.detail AS detail,
        max(wa."createdAt") AS "createdAt",
        max(wa."updatedAt") AS "updatedAt",
        max(wa."documentDate") AS "documentDate"     

    FROM staging_point_service."WalletActivity" AS wa
    INNER JOIN loyalty_service."Member" AS sm     ON wa."memberId" = sm."gwlNo"
    INNER JOIN partner_service."SalesTransaction" AS st     ON wa."refId" = st.id::text

    -- WHERE "walletCode" = 'CASH_WALLET' AND wa."refId" = '10000011'
    GROUP BY wa."memberId", wa."walletCode", wa.type, wa."refType", wa."refId", wa."externalId",  wa."partnerCode", wa."brandCode", wa."branchCode", wa.remark, wa.detail, wa."createdAt", wa."updatedAt", wa."documentDate"
)
INSERT INTO staging_point_service."WalletActivityAgg" (
    "id",
    "memberId",
    "walletCode",
    "type",
    "refType",
    "refId",
    "externalId",
    amount,
    "partnerCode",
    "brandCode",
    "branchCode",
    remark,
    detail,
    "createdAt",
    "updatedAt",
    "documentDate"
)
SELECT
    id,
    "memberId",
    "walletCode",
    "type",
    "refType",
    "refId",
    "externalId",
    amount,
    "partnerCode",
    "brandCode",
    "branchCode",
    remark,
    detail,
    "createdAt",
    "updatedAt",
    "documentDate"
FROM waagg
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "walletCode" = EXCLUDED."walletCode",
    "type" = EXCLUDED."type",
    "refType" = EXCLUDED."refType",
    "refId" = EXCLUDED."refId",
    "externalId" = EXCLUDED."externalId",
    amount = EXCLUDED.amount,
    "partnerCode" = EXCLUDED."partnerCode",
    "brandCode" = EXCLUDED."brandCode",
    "branchCode" = EXCLUDED."branchCode",
    remark = EXCLUDED.remark,
    detail = EXCLUDED.detail,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "documentDate" = EXCLUDED."documentDate";
-- 9 minutes
--