WITH member_nocashwallet AS 
(
--self join but filter on walletcode= 'CASH_WALLET'
--if this is no member it will insert nothing
SELECT 
	m.id as "memberId",
    m."createdAt",
    m."updatedAt"
from staging_loyalty_service."Member" m 
LEFT JOIN (
	select *
	from staging_point_service."WalletBalanceTemp"
	where "walletCode" = 'CASH_WALLET'
) wbt 
ON m."id" = wbt."memberId" 
WHERE wbt."memberId" IS null
group by m.id
)
--select count(*) from member_nocashwallet -- 454261 no cash wallet

INSERT INTO staging_point_service."PreWalletBalanceTemp" (id, "memberId", "walletCode", amount, "createdAt", "updatedAt", "expiredAt")
SELECT
    concat(m."memberId",'_','CASH_WALLET') as id,
    m."memberId",
    'CASH_WALLET',
    0.00,
    "createdAt",
    "updatedAt",
    NULL
FROM member_nocashwallet AS m
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "walletCode" = EXCLUDED."walletCode",
    amount = EXCLUDED.amount,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "expiredAt" = EXCLUDED."expiredAt";
