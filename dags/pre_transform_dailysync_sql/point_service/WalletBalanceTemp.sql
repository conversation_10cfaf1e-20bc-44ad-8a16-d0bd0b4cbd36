-- CREATE TABLE IF NOT EXISTS staging_point_service."WalletBalanceTemp" (
-- 	id text NOT NULL,
-- 	"memberId" text NOT NULL,
-- 	"walletCode" text NOT NULL,
-- 	amount numeric(16, 2) DEFAULT 0.00 NOT NULL,
-- 	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
-- 	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
-- 	"expiredAt" timestamp(3) NULL,
-- 	CONSTRAINT "WalletBalanceTemp_pkey" PRIMARY KEY (id)
-- );

INSERT INTO staging_point_service."WalletBalanceTemp" (
    "id",
    "memberId",
    "walletCode",
    "amount",
    "createdAt",
    "updatedAt",
    "expiredAt"
)

WITH ranked_wallets AS (
  SELECT
    wb.*,
    ROW_NUMBER() OVER (
      PARTITION BY wb."memberId"
      ORDER BY wb."expiredAt" DESC
    ) AS rn
  FROM (SELECT * FROM staging_point_service."WalletBalance"
        UNION ALL 
        SELECT * FROM staging_point_service."PreWalletBalanceTemp") AS wb
  WHERE wb."walletCode" = 'CASH_WALLET'
    AND wb."expiredAt" >= '2025-07-01'
    AND wb."expiredAt" < '2099-01-01'
    AND wb."amount" <> 0
)

,replace_exp_date_group AS
(SELECT 
id
, "memberId"
,"walletCode"
,amount
,rn
, "expiredAt"
, DATE '2099-12-30' - (rn - 1) * INTERVAL '1 day' AS "new_expiredAt"
FROM ranked_wallets
)

--SELECT * FROM replace_exp_date_group --WHERE  "memberId" = '2000683' WHERE  "memberId" = '1524610'
,walletbalance AS
(SELECT
    wb.id ,
	wb."memberId" ,
	wb."walletCode" ,
	CASE WHEN wb."walletCode" = 'CARAT_WALLET' then wb.amount*4
         ELSE wb.amount
    END AS amount,
	wb."createdAt" ,
	wb."updatedAt" ,
	CASE 
        WHEN wb."walletCode" = 'CARAT_WALLET' and EXTRACT(YEAR FROM wb."expiredAt") = 2099 THEN TO_TIMESTAMP('2007-01-01 16:59:59.999', 'YYYY-MM-DD HH24:MI:SS.MS')
        WHEN wb."walletCode" = 'CASH_WALLET' THEN COALESCE(redg."new_expiredAt", wb."expiredAt")
        WHEN wb."walletCode" = 'CARAT_WALLET' and wb."expiredAt" > now() 
            THEN TO_TIMESTAMP(EXTRACT(YEAR FROM wb."expiredAt") || '-12-31 16:59:59.999', 'YYYY-MM-DD HH24:MI:SS.MS')
        ELSE wb."expiredAt"
    END AS "expiredAt" ,
    redg."new_expiredAt",
    wb."expiredAt" AS "old_expiredAt"
FROM (SELECT * FROM staging_point_service."WalletBalance"
        UNION ALL 
        SELECT * FROM staging_point_service."PreWalletBalanceTemp") AS wb
LEFT JOIN replace_exp_date_group redg ON wb.id = redg.id  
-- where wb."expiredAt" is not null and redg."new_expiredAt" is not null and wb."memberId" = '1524610'
-- WHERE  wb."memberId" = '1524610' and wb."walletCode"='CASH_WALLET'
)

SELECT 
    "id",
    "memberId",
    "walletCode",
    "amount",
    "createdAt",
    "updatedAt",
    "expiredAt"
FROM walletbalance
--WHERE ((wb."createdAt" BETWEEN 'start_timestamps' AND 'end_timestamps') OR (wb."updatedAt" BETWEEN 'start_timestamps' AND 'end_timestamps'))


ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "walletCode" = EXCLUDED."walletCode",
    amount = EXCLUDED.amount,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "expiredAt" = EXCLUDED."expiredAt";
