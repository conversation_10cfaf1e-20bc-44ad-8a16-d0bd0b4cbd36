WITH ranked_wallets AS (
  SELECT
    wb.*,
    ROW_NUMBER() OVER (
      PARTITION BY wb."memberId"
      ORDER BY wb."expiredAt" DESC
    ) AS rn
  FROM (SELECT * FROM staging_point_service."WalletBalance"
        UNION ALL 
        SELECT * FROM staging_point_service."PreWalletBalanceTemp") AS wb
  WHERE wb."walletCode" = 'CASH_WALLET'
    AND wb."expiredAt" >= '2025-07-01'
    AND wb."expiredAt" < '2099-01-01'
    AND wb."amount" <> 0
)

,replace_exp_date_group AS
(SELECT 
id
, "memberId"
,"walletCode"
,amount
,rn
, "expiredAt"
, DATE '2099-12-30' - (rn - 1) * INTERVAL '1 day' AS "new_expiredAt"
FROM ranked_wallets
)

--SELECT * FROM replace_exp_date_group --WHERE  "memberId" = '2000683' WHERE  "memberId" = '1524610'
,walletbalance_0 AS
(SELECT
    wb.id ,
	wb."memberId" ,
	wb."walletCode" ,
	CASE WHEN wb."walletCode" = 'CARAT_WALLET' then wb.amount*4
         ELSE wb.amount
    END AS amount,
	wb."createdAt" ,
	wb."updatedAt" ,
	
   CASE 
        WHEN wb."walletCode" = 'CARAT_WALLET' and ("expiredAt" >= '2099-01-01' AND "expiredAt" < '2100-01-01') THEN TO_TIMESTAMP('2007-01-01 16:59:59.999', 'YYYY-MM-DD HH24:MI:SS.MS')
        -- WHEN wb."walletCode" = 'CASH_WALLET' THEN COALESCE(redg."new_expiredAt", wb."expiredAt")
        WHEN wb."walletCode" = 'CARAT_WALLET' and wb."expiredAt" > now() 
            THEN TO_TIMESTAMP(EXTRACT(YEAR FROM wb."expiredAt") || '-12-31 16:59:59.999', 'YYYY-MM-DD HH24:MI:SS.MS')
        ELSE wb."expiredAt"
    END AS "expiredAt" ,

    redg."new_expiredAt"
    -- ,wb."expiredAt" AS "old_expiredAt"
FROM (SELECT * FROM staging_point_service."WalletBalance"
        UNION ALL 
        SELECT * FROM staging_point_service."PreWalletBalanceTemp") AS wb
LEFT JOIN replace_exp_date_group redg ON wb.id = redg.id  
-- where wb."expiredAt" is not null and redg."new_expiredAt" is not null and wb."memberId" = '1524610'
-- WHERE  wb."memberId" = '1524610' and wb."walletCode"='CASH_WALLET'
)

--SELECT    * FROM walletbalance_0 -- 12987

,walletbalance AS
(SELECT 
    id,
    "memberId",
    "walletCode",
    coalesce("amount", 0.00) as "amount",
    "createdAt",
    "updatedAt",
    "expiredAt"
FROM (
    SELECT 
        ulid.ulid_id as id,
        sm.id as "memberId",
        wb."walletCode",
        agg.total_amount as "amount",
        wb."createdAt",
        wb."updatedAt",
        agg."expiredAt",
        ROW_NUMBER() OVER (PARTITION BY wb."memberId", wb."walletCode", agg."expiredAt" ORDER BY wb."createdAt", wb.id) AS rn
    FROM walletbalance_0 AS wb
    LEFT JOIN (
        SELECT 
            "memberId",
            "walletCode",
            "expiredAt",
            SUM(amount) AS total_amount
        FROM walletbalance_0
        GROUP BY "memberId", "walletCode", "expiredAt"
    ) agg 
    ON wb."memberId" = agg."memberId"
    AND wb."walletCode" = agg."walletCode"
    AND coalesce(wb."expiredAt", TIMESTAMP '2099-01-01 00:00:00') = coalesce(agg."expiredAt", TIMESTAMP '2099-01-01 00:00:00')
    LEFT JOIN staging_point_service."ulid_WalletBalance" ulid
    ON wb.id = ulid.id
    INNER JOIN loyalty_service."Member" AS sm
    ON wb."memberId" = sm."gwlNo"
) ranked
WHERE rn = 1
)

SELECT
    id,
    "memberId",
    "walletCode",
    "amount",
    "createdAt",
    "updatedAt",
    "expiredAt"
FROM walletbalance


