-- Combined SQL Script with CTEs for Wallet Balance Processing
-- CTE 1: walletbalance_0 - Transform expiredAt column based on wallet type
-- CTE 2: walletbalance - Final aggregation and member mapping

WITH ranked_wallets AS (
    SELECT
        wb.*,
        ROW_NUMBER() OVER (
            PARTITION BY wb."memberId"
            ORDER BY wb."expiredAt" DESC
        ) AS rn
    FROM (
        SELECT * FROM staging_point_service."WalletBalance"
        UNION ALL
        SELECT * FROM staging_point_service."PreWalletBalanceTemp"
    ) AS wb
    WHERE wb."walletCode" = 'CASH_WALLET'
        AND wb."expiredAt" >= '2025-07-01'
        AND wb."expiredAt" < '2099-01-01'
        AND wb."amount" <> 0
),

replace_exp_date_group AS (
    SELECT
        id,
        "memberId",
        "walletCode",
        amount,
        rn,
        "expiredAt",
        DATE '2099-12-30' - (rn - 1) * INTERVAL '1 day' AS "new_expiredAt"
    FROM ranked_wallets
),

-- CTE 1: walletbalance_0 - Transform expiredAt column based on wallet type
walletbalance_0 AS (
    SELECT
        wb.id,
        wb."memberId",
        wb."walletCode",
        CASE
            WHEN wb."walletCode" = 'CARAT_WALLET' THEN wb.amount * 4
            ELSE wb.amount
        END AS amount,
        wb."createdAt",
        wb."updatedAt",
        -- Transform expiredAt based on wallet type and conditions
        CASE
            WHEN wb."walletCode" = 'CARAT_WALLET'
                AND (wb."expiredAt" >= '2099-01-01' AND wb."expiredAt" < '2100-01-01')
                THEN TO_TIMESTAMP('2007-01-01 16:59:59.999', 'YYYY-MM-DD HH24:MI:SS.MS')
            WHEN wb."walletCode" = 'CARAT_WALLET'
                AND wb."expiredAt" > now()
                THEN TO_TIMESTAMP(EXTRACT(YEAR FROM wb."expiredAt") || '-12-31 16:59:59.999', 'YYYY-MM-DD HH24:MI:SS.MS')
            ELSE wb."expiredAt"
        END AS "expiredAt",
        redg."new_expiredAt"
    FROM (
        SELECT * FROM staging_point_service."WalletBalance"
        UNION ALL
        SELECT * FROM staging_point_service."PreWalletBalanceTemp"
    ) AS wb
    LEFT JOIN replace_exp_date_group redg ON wb.id = redg.id
),

-- CTE 2: walletbalance - Final aggregation and member mapping
walletbalance AS (
    SELECT
        id,
        "memberId",
        "walletCode",
        COALESCE("amount", 0.00) AS "amount",
        "createdAt",
        "updatedAt",
        "expiredAt"
    FROM (
        SELECT
            ulid.ulid_id AS id,
            sm.id AS "memberId",
            wb."walletCode",
            agg.total_amount AS "amount",
            wb."createdAt",
            wb."updatedAt",
            agg."expiredAt",
            ROW_NUMBER() OVER (
                PARTITION BY wb."memberId", wb."walletCode", agg."expiredAt"
                ORDER BY wb."createdAt", wb.id
            ) AS rn
        FROM walletbalance_0 AS wb
        LEFT JOIN (
            SELECT
                "memberId",
                "walletCode",
                "expiredAt",
                SUM(amount) AS total_amount
            FROM walletbalance_0
            GROUP BY "memberId", "walletCode", "expiredAt"
        ) agg
        ON wb."memberId" = agg."memberId"
        AND wb."walletCode" = agg."walletCode"
        AND COALESCE(wb."expiredAt", TIMESTAMP '2099-01-01 00:00:00') = COALESCE(agg."expiredAt", TIMESTAMP '2099-01-01 00:00:00')
        LEFT JOIN staging_point_service."ulid_WalletBalance" ulid
        ON wb.id = ulid.id
        INNER JOIN loyalty_service."Member" AS sm
        ON wb."memberId" = sm."gwlNo"
    ) ranked
    WHERE rn = 1
)

-- Final SELECT statement
SELECT
    id,
    "memberId",
    "walletCode",
    "amount",
    "createdAt",
    "updatedAt",
    "expiredAt"
FROM walletbalance;


