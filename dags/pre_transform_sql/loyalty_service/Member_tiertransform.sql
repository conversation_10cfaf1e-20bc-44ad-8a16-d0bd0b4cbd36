-- modify P_CR order same as N_CR, so, change from 10 to 8
-- have to add at the very first rule: all inactive members are Navy
-- don't consider cobrand if it is inactive


-- create new public.tiertransformed table for join in xform

-- DROP TABLE IF EXISTS public.tiertransformed CASCADE;

CREATE TABLE IF NOT EXISTS public.tiertransformed (
    "memberId" text not null PRIMARY KEY,
    "tierId" text not null DEFAULT 'Navy',
    "minimumTierId" text null,
    "minimumTierInvitedId" text null,
    "accumulateSpending" numeric(16,2) NULL DEFAULT 0.00,
    "isActive" boolean null,
    "createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);





-- insert data into public.tiertransformed table

WITH cardtypecode_cobrand AS (
    SELECT 'SVP05' AS cardTypeCode, 5 AS value
    UNION ALL
    --SELECT 'BVP05', 5
    --UNION ALL
    SELECT 'SVP10', 4
    UNION ALL
    --SELECT 'BVS', 3
    --UNION ALL
    SELECT 'SVP15', 3
    UNION ALL
    SELECT 'SVP20', 3
    UNION ALL
    SELECT 'STRIB', 2
    UNION ALL
    SELECT 'SPLD', 2
    UNION ALL
    SELECT 'SVP30', 1
)

,
max_cardtypecode_cobrand as 
(SELECT DISTINCT ON (mlch."memberId")
    		mlch."memberId",
    		mlch."cardTypeCode",
    		coalesce(cc.value,0) as value
FROM 	staging_loyalty_service."MemberLegacyCoBrandHistory" AS mlch
LEFT JOIN   cardtypecode_cobrand AS cc ON mlch."cardTypeCode" = cc.cardTypeCode
WHERE    	("cardTypeCode" not in ('BVP05', 'BVP10', 'BVP15', 'BVP20', 'BVS', 'BA', 'BG')  or source_cardtypecode not in ('KBANK'))
AND mlch."cardStatus" = 'ACTIVE' 
ORDER BY    mlch."memberId", cc.value ASC

	)
--select count(*) from max_cardtypecode_cobrand -- only SCB 269142 (distincted)
--select * from max_cardtypecode_cobrand --0000089 BVP05 , all kbank+NO code in Master table will be value NULL
--select count(*) from (select distinct "memberId" from max_cardtypecode_cobrand)

,
max_cardtypecode_cobrand_active as 
(SELECT DISTINCT ON (mlch."memberId")
    		mlch."memberId",
    		mlch."cardTypeCode",
    		coalesce(cc.value,0) as value
FROM 	staging_loyalty_service."MemberLegacyCoBrandHistory" AS mlch
LEFT JOIN   cardtypecode_cobrand AS cc ON mlch."cardTypeCode" = cc.cardTypeCode
WHERE     	mlch."cardStatus" = 'ACTIVE' AND  ("cardTypeCode" not in ('BVP05', 'BVP10', 'BVP15', 'BVP20', 'BVS', 'BA', 'BG')  or source_cardtypecode not in ('KBANK'))
ORDER BY    mlch."memberId", cc.value ASC
	
	)
--select count(*) from max_cardtypecode_cobrand_active -- only SCB 135121 (distincted)

,
cardtypecode_tier as
(SELECT 'N_NY' AS cardTypeCode, 10 AS value
    UNION ALL
    SELECT 'N_SL', 9
    UNION ALL
    SELECT 'OV_SL', 9
    UNION ALL
    SELECT 'OT_SL', 9
    UNION ALL
    SELECT 'P_CR', 8 --- modified
    UNION ALL
    SELECT 'N_CR', 8
    UNION ALL
    SELECT 'N_OX', 7
    UNION ALL
    SELECT 'OT_OX', 7
    UNION ALL
    SELECT 'OV_OX', 7
    UNION ALL
    SELECT 'IN_V1', 6
    UNION ALL
    SELECT 'IN_V2', 5
    UNION ALL
    SELECT 'IN_V3', 5
    UNION ALL
    SELECT 'IN_K', 4
    UNION ALL
    SELECT 'VEGA', 3
    UNION ALL
    SELECT 'NVVIP', 2
    UNION ALL
    SELECT 'C_STF', 1
    UNION ALL
    SELECT 'CS_MS', 1
    )
,
max_cardtypecode_tier as
(SELECT DISTINCT ON (mlth."memberId")
    mlth."memberId",
    mlth."cardTypeCode",
    COALESCE(ct.value, 0) as value
FROM staging_loyalty_service."MemberLegacyTierHistory" AS mlth
LEFT JOIN    cardtypecode_tier AS ct ON mlth."cardTypeCode" = ct.cardTypeCode
--WHERE    mlth."cardStatus" = 'ACTIVE'
ORDER BY   mlth."memberId", ct.value ASC
)
--select * from max_cardtypecode_tier--1531316
--select count(*) from max_cardtypecode_tier --1531316
--select count(*) from (select distinct "memberId" from max_cardtypecode_tier)

,

distinct_member_mlth AS (
				select DISTINCT "memberId" --true as "isActive"
				FROM staging_loyalty_service."MemberLegacyTierHistory"
                    )
--select count(*) from distinct_member_mlth --1531316

,
active_member_mlth AS (
				select DISTINCT "memberId"
				FROM staging_loyalty_service."MemberLegacyTierHistory"
                    WHERE "cardStatus" IN ('ACTIVE')
                    --WHERE "cardStatus" NOT IN ('ACTIVE')
                    )
--select count(*) from active_member_mlth
--select * from active_member_mlth -- 1372237
,
max_cardtypecode_tier_active as
(SELECT DISTINCT ON (mlth."memberId")
    mlth."memberId",
    mlth."cardTypeCode",
    COALESCE(ct.value, 0) as value
FROM staging_loyalty_service."MemberLegacyTierHistory" AS mlth
LEFT JOIN    cardtypecode_tier AS ct ON mlth."cardTypeCode" = ct.cardTypeCode
WHERE    mlth."cardStatus" IN ('ACTIVE')
ORDER BY   mlth."memberId", ct.value ASC
)

--select count(*) from max_cardtypecode_tier_active --1372237
--select * from max_cardtypecode_tier_active --1372237
	

,
isactive_member_either_lv_cobrand AS (
				select DISTINCT "memberId", true as "isActive"
				from 
					(
                    SELECT "memberId" --, "cardStatus"
                    FROM active_member_mlth
                    
                    UNION ALL
                    SELECT "memberId" --, "cardStatus" 
                    FROM max_cardtypecode_cobrand_active
                    
                    )
)
--select * from isactive_member_either_lv_cobrand --1458,528 remove new card 1458,497

,
reactivate_list AS (
	select * FROM public.fulldump_smc_expired_list
	-- WHERE ("updatedAt" BETWEEN '2025-06-12 00:00:20.368' AND '2025-06-13 00:00:20.368') -- hard code for one time use migration later change to WHERE (rl."updatedAt" BETWEEN 'start_timestamps' AND 'end_timestamps')- 
)
--select * from reactivate_list 
--select "memberId" from reactivate_list  


,
member_accumulatespending_change AS 
(SELECT m."id",m."accumulateSpending" AS old_accumulateSpending, m."createdAt", m."updatedAt", rm."accumulateSpending", rm."createdAt", rm."updatedAt"
FROM staging_loyalty_service."Member" m
JOIN (
    SELECT id, "accumulateSpending", "createdAt", "updatedAt"
    FROM dblink('my_connection',
    'SELECT id, "accumulateSpending", "createdAt", "updatedAt" FROM loyalty_service."Member"')
    AS remote_member(id text, "accumulateSpending" numeric(16, 2), "createdAt" timestamp(3), "updatedAt" timestamp(3))
) AS rm ON m.id = rm.id
WHERE m."accumulateSpending" <> rm."accumulateSpending"
)


,
accumulateSpending_rlreset AS
(SELECT 

CASE WHEN m.id = rl."memberId"  THEN 0.00                 -- (New 1st condition) midnight 4 th june to cutoff CASE WHEN (m.id = rl."memberId" and (m."createdAt" < '2025-06-03 17:00:00 +0000' or m."updatedAt" < '2025-06-04 17:00:00 +0000')) THEN 0.00
    	 --WHEN isam."isActive" = true THEN m."accumulateSpending" -- (New 2nd condition)
    	 ELSE m."accumulateSpending"  END AS "accumulateSpending_rlreset",
    m."gwlNo" AS "gwlNo_arlr",
    m."accumulateSpending" AS "m_accumulateSpending_arlr",
    m."createdAt" AS "creatdAt_arlr",
    m."updatedAt" AS "updatedAt_arlr",
    true    AS "isActive_arlr"

FROM    staging_loyalty_service."Member" AS m
INNER JOIN reactivate_list rl on m.id = rl."memberId"

)
--SELECT * FROM accumulateSpending_rlreset -- full list is 401396 where = 401380

,
accumulateSpending_rl_noreset AS
(SELECT 
	--CASE WHEN (m.id = rl."memberId" AND (m."createdAt" > '2025-06-03' or m."updatedAt" > '2025-06-03')) THEN m."accumulateSpending"                 -- (New 1st condition) midnight 4 th june to cutoff CASE WHEN (m.id = rl."memberId" and (m."createdAt" < '2025-06-03 17:00:00 +0000' or m."updatedAt" < '2025-06-04 17:00:00 +0000')) THEN 0.00
    --	 ELSE 0.99999999  END AS "accumulateSpending_rlreset",
	m."accumulateSpending" AS "accumulateSpending_rl_noreset",
	m."gwlNo" AS "gwlNo_arlr",
    m."accumulateSpending" AS "m_accumulateSpending_arlr",
    m."createdAt" AS "creatdAt_arlr",
    m."updatedAt" AS "updatedAt_arlr",
    true    AS "isActive_arlr"
FROM    staging_loyalty_service."Member" AS m
INNER JOIN reactivate_list rl on m.id = rl."memberId"
--WHERE (m."createdAt" > '2025-06-04') --AND acc    
)
--SELECT * FROM accumulateSpending_rl_noreset --16

,
tiertransformed AS
(SELECT
	m.id AS "memberId",
	m."accumulateSpending" AS stg_accumulatespending,
	rlr."accumulateSpending_rlreset" AS rlr_accumulatespending_reset,
	rlr."isActive_arlr",
	isam."isActive" AS "isActive_mlch_mlth",
    CASE
        --WHEN isam."isActive" <> true THEN 'Navy'
        --WHEN rlr."isActive_arlr" IS NOT NULL THEN 'Navy(reactivated_reset)' -- 
        WHEN isam."isActive" <> true THEN 'Navy'
        WHEN TRIM(mct."cardTypeCode") IN ('C_STF', 'CS_MS') THEN 'Crystal'
        WHEN TRIM(mct."cardTypeCode") IN ('NVVIP') THEN 'VVIP'
        WHEN TRIM(mcc."cardTypeCode") IN ('SVP30') THEN 'VVIP'
        WHEN COALESCE(rlr."accumulateSpending_rlreset", m."accumulateSpending") >= 2000000 THEN 'Vega'
        WHEN TRIM(UPPER(mct."cardTypeCode")) = 'VEGA' AND COALESCE(rlr."accumulateSpending_rlreset", m."accumulateSpending") < 2000000 THEN 'Vega(GP)'
        WHEN TRIM(mcc."cardTypeCode") IN ('STRIB', 'SPLD') THEN 'Crown'
        WHEN COALESCE(rlr."accumulateSpending_rlreset", m."accumulateSpending") >= 300000 AND COALESCE(rlr."accumulateSpending_rlreset", m."accumulateSpending") < 2000000 THEN 'Crown'
        
        WHEN TRIM(mct."cardTypeCode") IN ('IN_K') AND m."upgradeGroupCode" = 'DOCTOR' THEN 'Crown(GP)'
        WHEN TRIM(mct."cardTypeCode") IN ('P_CR', 'N_CR', 'N_OX', 'OT_OX', 'OV_OX', 'IN_V1', 'IN_V2', 'IN_V3') THEN 'Crown(GP)'
        WHEN TRIM(mcc."cardTypeCode") IN ('SVP15', 'SVP20') THEN 'Crown(GP)'
        WHEN COALESCE(rlr."accumulateSpending_rlreset", m."accumulateSpending") >= 40000 AND COALESCE(rlr."accumulateSpending_rlreset", m."accumulateSpending") < 300000 THEN 'Scarlet'
        WHEN TRIM(mcc."cardTypeCode") IN ('SVP10') THEN 'Scarlet(GP-SVP10)'
        WHEN COALESCE(rlr."accumulateSpending_rlreset", m."accumulateSpending") < 40000 AND mct."cardTypeCode" IN ('IN_K', 'N_SL', 'OT_SL', 'OV_SL') THEN 'Scarlet(GP-L4000)'
        WHEN COALESCE(rlr."accumulateSpending_rlreset", m."accumulateSpending") < 40000 AND mct."cardTypeCode" IN ('N_NY') THEN 'Navy'
        ELSE 'Navy'
    END AS "tierId",

    CASE
        --WHEN rlr."isActive_arlr" IS NOT NULL THEN 'Navy' -- 
        WHEN isam."isActive" <> true THEN 'Navy'
        WHEN TRIM(mct."cardTypeCode") IN ('C_STF', 'CS_MS', 'NVVIP') THEN NULL
        WHEN TRIM(mcc."cardTypeCode") IN ('SVP30') THEN NULL
        WHEN TRIM(UPPER(mct."cardTypeCode")) = 'VEGA' THEN 'VEGA'
        
        WHEN TRIM(mct."cardTypeCode") IN ('IN_K') AND m."upgradeGroupCode" = 'DOCTOR' THEN 'CROWN'
        WHEN TRIM(mct."cardTypeCode") IN ('P_CR', 'N_CR', 'N_OX', 'OT_OX', 'OV_OX', 'IN_V1', 'IN_V2', 'IN_V3') THEN 'CROWN'
        WHEN TRIM(mcc."cardTypeCode") IN ('BVS', 'SVP15', 'SVP20', 'STRIB', 'SPLD') THEN 'CROWN'
        WHEN TRIM(mcc."cardTypeCode") IN ('SVP10') THEN 'SCARLET'
		WHEN mct."cardTypeCode" IN ('IN_K', 'N_SL', 'OT_SL', 'OV_SL') THEN 'SCARLET'
        ELSE NULL
    END AS "minimumTierId",

    CASE
        --WHEN rlr."isActive_arlr" IS NOT NULL THEN 'Navy' -- 
        WHEN isam."isActive" <> true THEN 'Navy'
        WHEN TRIM(mct."cardTypeCode") IN ('C_STF', 'CS_MS', 'NVVIP', 'N_NY') THEN NULL
        WHEN TRIM(UPPER(mct."cardTypeCode")) = 'VEGA' THEN 'VEGA'
        
        WHEN TRIM(mct."cardTypeCode") IN ('IN_K') AND m."upgradeGroupCode" = 'DOCTOR' THEN 'CROWN'
        WHEN TRIM(mct."cardTypeCode") IN ('P_CR', 'N_CR', 'N_OX', 'OT_OX', 'OV_OX', 'IN_V1', 'IN_V2', 'IN_V3') THEN 'CROWN'
		WHEN mct."cardTypeCode" IN ('IN_K', 'N_SL', 'OT_SL', 'OV_SL') THEN 'SCARLET'
        ELSE NULL
    END AS "minimumTierInvitedId",

    CASE WHEN (isam."isActive" = true OR rlr."isActive_arlr" = true ) THEN COALESCE(rlr."accumulateSpending_rlreset", m."accumulateSpending") -- (New 2nd condition)
    --CASE WHEN isam."isActive" = true THEN COALESCE(rlr."accumulateSpending_rlreset", m."accumulateSpending") -- (New 2nd condition)	 
    	 ELSE 0.00  END AS "accumulateSpending",

	--CASE WHEN isam."isActive" = true or rlr."isActive_arlr" = true THEN true end as "isActive" --switch off
	CASE WHEN isam."isActive" = true THEN isam."isActive" --1458520
         ELSE false END AS "isActive",
    m."createdAt",
    CURRENT_TIMESTAMP AS "updatedAt"
FROM
    staging_loyalty_service."Member" AS m
    LEFT JOIN accumulateSpending_rlreset rlr on m.id = rlr."gwlNo_arlr"

    LEFT JOIN max_cardtypecode_tier_active AS mct ON m.id = mct."memberId"
    LEFT JOIN max_cardtypecode_cobrand_active AS mcc ON m.id = mcc."memberId"
    LEFT JOIN isactive_member_either_lv_cobrand AS isam ON m.id = isam."memberId"
)

--select count(*) from tiertransformed
INSERT INTO public.tiertransformed (
    "memberId", 
    "tierId", 
    "minimumTierId",
    "minimumTierInvitedId", 
    "accumulateSpending", 
    "isActive",
    "createdAt",
    "updatedAt"
)
SELECT 
    "memberId", 
    "tierId", 
    "minimumTierId",
    "minimumTierInvitedId", 
    "accumulateSpending", 
    "isActive",
    "createdAt",
    "updatedAt"
FROM tiertransformed
ON CONFLICT ("memberId") DO UPDATE SET
    "tierId" = EXCLUDED."tierId",
    "minimumTierId" = EXCLUDED."minimumTierId",
    "minimumTierInvitedId" = EXCLUDED."minimumTierInvitedId",
    "accumulateSpending" = EXCLUDED."accumulateSpending",
    "isActive" = EXCLUDED."isActive",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";