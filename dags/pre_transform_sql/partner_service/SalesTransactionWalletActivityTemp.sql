CREATE TABLE IF NOT EXISTS staging_partner_service."SalesTransactionWalletActivityTemp" (
    id TEXT NOT NULL PRIMARY KEY,
	"salesTransactionId" TEXT,
    "type" TEXT,
    "activityId" TEXT,
    detail JSONB,
    "createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL ,
    "updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
); 

WITH amount_presum AS
(
  SELECT
	wls."WalletActivityId" ,
    (jsonb_array_elements("ListJsonValues"::jsonb)->>'amount')::numeric AS amount
FROM
  staging_point_service."WalletActivityListJson" wls
)

,
sum_amount AS
(
select
"WalletActivityId" ,
sum(amount) AS sum_amount_in_listjson
from   amount_presum
group by "WalletActivityId" 
)

,
stwat AS (
SELECT
    concat(wa."refId", '-', wa.id) AS id,
    wa."refId" AS "salesTransactionId",
    --CAST(wa."refId" AS int8)  AS "salesTransactionId",
    --wa.id AS "WalletActivityId",
    wa."type" AS "type",
    wa.id AS "activityId",
    jsonb_build_object(
        -- 'id', wa.id,
        -- 'memberId', wa."memberId",
        'type', wa."type",
        'walletCode', wa."walletCode",
        'walletName', w.name,
        'walletType', w."walletTypeCode",
        'afterBalance', sum_amount.sum_amount_in_listjson,
        'beforeBalance', 0,
        'transactions', walj."ListJsonValues"
                           
    )::jsonb AS detail, -- Explicitly cast to jsonb,
        
    wa."createdAt" AS "createdAt",
    wa."updatedAt" AS "updatedAt"
FROM
    staging_point_service."WalletActivity" wa
    LEFT JOIN staging_point_service."WalletActivityListJson" walj ON wa.id = walj."WalletActivityId"  
    LEFT JOIN sum_amount ON wa.id = sum_amount."WalletActivityId"
    LEFT JOIN point_service."Wallet" w ON wa."walletCode" = w.code -- this is existing master table must use master table in gwl
    --INNER JOIN staging_partner_service."SalesTransaction" st ON wa."refId"::bigint  = st.id
    
WHERE
    wa."refType" = 'SALES_TRANSACTION' 
    --and wa.id = '5426897_USE_CR001_0171078'
--LIMIT 100
)
-- for count(*)
-- 19598245 no inner
-- no inner Finalize Aggregate  (cost=730571.00..730571.01 rows=1 width=8) (actual time=6348.861..6348.923 rows=1 loops=1)
-- 14355810 with inner
-- with inner Finalize Aggregate  (cost=1226010.98..1226010.99 rows=1 width=8) (actual time=7881.091..8226.400 rows=1 loops=1)



-- for select * limit 1 m
-- no inner Limit  (cost=3.39..305522.22 rows=1000000 width=133) (actual time=0.055..6581.201 rows=1000000 loops=1)
-- with inner Limit  (cost=1003.85..593936.50 rows=1000000 width=133) (actual time=8.346..6756.158 rows=1000000 loops=1)


INSERT INTO staging_partner_service."SalesTransactionWalletActivityTemp" 
( 
	id,
    "salesTransactionId",
    "type",
    "activityId",
    detail,
    "createdAt",
    "updatedAt"   
)
SELECT
    id, 
    "salesTransactionId",
    "type",
    "activityId",
    detail,
    "createdAt",
    "updatedAt"
from stwat

ON CONFLICT (id) DO UPDATE SET
    "salesTransactionId" = EXCLUDED."salesTransactionId",
    "type" = EXCLUDED."type",
    "activityId" = EXCLUDED."activityId",
    detail = EXCLUDED.detail,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";
-- Updated Rows	14304056
-- Execute time	12m 50s