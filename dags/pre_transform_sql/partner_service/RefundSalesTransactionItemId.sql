-- CREATE TABLE IF NOT EXISTS public."RefundSalesTransactionItemId" (
-- 	"id" text NOT NULL,
--     "refundSalesTransactionId" text NOT NULL,
--     "salesTransactionItemId" text NOT NULL,
--     "createdAt" timestamp(3)
-- 	CONSTRAINT "RefundSalesTransactionId_unique" UNIQUE ("refundSalesTransactionId", "salesTransactionItemId")
-- );

INSERT INTO public."RefundSalesTransactionItemId" (
    "id",
    "refundSalesTransactionId",
    "salesTransactionItemId",
    "createdAt"
)
SELECT
    concat(rst.id, '_', sti.id) as id,
    rst.id as "refundSalesTransactionId",
	sti.id as "salesTransactionItemId",
	rst."createdAt"
FROM staging_partner_service."SalesTransactionItem" sti     
INNER JOIN staging_partner_service."RefundSalesTransaction" rst
ON sti."salesTransactionId" = rst."salesTransactionId"
ON CONFLICT ("refundSalesTransactionId", "salesTransactionItemId") DO NOTHING;