from datetime import datetime
from common_helpers.database_services import <PERSON>gresHand<PERSON>
from common_helpers.logging import get_logger

from airflow import DAG
from airflow.operators.python_operator import PythonOperator

logger = get_logger()


class EngagementServiceCleanup:
    def __init__(self):
        self.postgresql_handler = PostgresHandler(conn_id="temp_db_connection_id")

    def cleanup_member_privilege(self):
        """
        Cleanup data from table MemberPrivilege.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up MemberPrivilege table...")
            cursor.execute('TRUNCATE TABLE engagement_service."MemberPrivilege";')
            logger.info(f"finished cleaning up MemberPrivilege table.")

        self.cleanup(table_cleanup)

    def cleanup(self, func):
        """
        Cleanup data from Engagement Service table.

        Args:
            func: A function that truncate the table.

        Returns:
            None
        """

        try:
            postgresql_connection = self.postgresql_handler.hook.get_conn()

            with postgresql_connection.cursor() as cursor:
                func(cursor)

            postgresql_connection.commit()
        except Exception as error:
            logger.error(f"an error has occured: {error}")

            if postgresql_connection:
                logger.info("a postgresql connection is found, rolling back...")
                postgresql_connection.rollback()
                logger.info("successfully rolled back.")

            raise error
        finally:
            if postgresql_connection:
                postgresql_connection.close()


with DAG(
    "engagement_service_member_privilege_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL MemberPrivilege migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 2, 28),
    catchup=False,
    tags=["engagement_service", "member_privilege", "cleanup"],
) as engagement_service_member_privilege_cleanup_dag:
    PythonOperator(
        task_id="cleanup_member_privilege_task",
        python_callable=EngagementServiceCleanup().cleanup_member_privilege,
    )
