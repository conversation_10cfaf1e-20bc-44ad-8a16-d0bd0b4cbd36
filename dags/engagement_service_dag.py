from datetime import datetime, timedelta, timezone

from airflow import DAG
from airflow.models import Variable
from airflow.operators.python_operator import PythonOperator
from psycopg2.extensions import connection as postgres_connection

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.logging import get_logger
from common_helpers.utils import create_migration_result_table
from engagement_service.member_privilege import MemberPrivilege

logger = get_logger()


class EngagementService:
    def __init__(self) -> None:
        self.service_name = "engagement_service"
        self.batch_size = int(
            Variable.get(f"{self.service_name}.batch_size", default_var=10000),
        )
        self.executor_max_workers = int(
            Variable.get(f"{self.service_name}.executor_max_workers", default_var=5),
        )
        self.loyalty_value_handler = MSSQLHandler(
            conn_id="loyalty_value_smc_db_connection_id"
        )
        self.postgresql_handler = PostgresHandler(conn_id="temp_db_connection_id")

    def create_lv_birthday_snapshot(self) -> None:
        current_utc = datetime.now(timezone.utc)
        formatted_date = current_utc.strftime("%Y%m%d")

        query_string = f"""
            IF OBJECT_ID('snapshot_lv_birthday_{formatted_date}', 'U') IS NULL
            BEGIN
                SELECT *
                INTO snapshot_lv_birthday_{formatted_date}
                FROM LVBirthday;
            END
        """

        loyalty_value_connection = self.loyalty_value_handler.hook.get_conn()

        try:
            logger.info(f"started creating LVBirthday snapshot...")
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=query_string,
            )
            logger.info(f"finished creating LVBirthday snapshot.")
        finally:
            loyalty_value_connection.close()

    def prepare_privilege_table_for_migration(
        self,
        connection: postgres_connection,
    ) -> None:
        """
        Insert a row into engagement_service.Privilege, with id of ENGAGEMENT_SERVICE_MIGRATION,
        which is needed for engagement_service.MemberPrivilege migration.

        Args:
            None

        Returns:
            None
        """
        insert_query = """
            INSERT INTO "engagement_service"."Privilege" (
                "id",
                "nameEn",
                "nameTh",
                "typeCode",
                "subTypeCode",
                "renewalFrequency",
                "usageCondition",
                "createdAt",
                "updatedAt",
                "status",
                "effectiveDate"
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s, NOW () AT TIME ZONE 'UTC', NOW () AT TIME ZONE 'UTC', %s, NOW () AT TIME ZONE 'UTC')
            ON CONFLICT ("id") DO NOTHING;
        """

        self.postgresql_handler.execute_with_rollback(
            connection=connection,
            query_string=insert_query,
            record=(
                self.service_name,
                self.service_name,
                self.service_name,
                self.service_name,
                self.service_name,
                self.service_name,
                "{}",
                self.service_name,
            ),
        )

    def prepare_tables(self):
        """
        Prepare mandatory table(s) for Engagement Service migration.

        Args:
            None

        Returns:
            None
        """
        loyalty_value_connection = self.loyalty_value_handler.hook.get_conn()
        postgresql_connection = self.postgresql_handler.hook.get_conn()

        try:
            logger.info(f"started preparing Privilege table for migration...")
            self.prepare_privilege_table_for_migration(postgresql_connection)
            logger.info(f"finished preparing Privilege table for migration.")
        except Exception as error:
            logger.error(f"an error has occured: {error}")

            if postgresql_connection:
                logger.info("a postgresql connection is found, rolling back...")
                postgresql_connection.rollback()
                logger.info("successfully rolled back.")

            raise error
        finally:
            if loyalty_value_connection:
                loyalty_value_connection.close()
            if postgresql_connection:
                postgresql_connection.close()

    def prepare_batch_tracker_table(self):
        """
        Prepare a table to track the migration process.

        Args:
            None

        Returns:
            None
        """
        create_table_query_string = """
            CREATE TABLE IF NOT EXISTS engagement_service.batch_tracker (
                table_name VARCHAR(100) PRIMARY KEY,
                total_records INT NOT NULL,
                completed_batches JSONB NOT NULL DEFAULT '[]'::jsonb,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """
        postgresql_connection = self.postgresql_handler.hook.get_conn()

        try:
            logger.info(f"started preparing batch tracker table for migration...")
            self.postgresql_handler.execute_with_rollback(
                connection=postgresql_connection,
                query_string=create_table_query_string,
            )
            logger.info(f"finished preparing batch tracker table for migration.")
        finally:
            postgresql_connection.close()

    def migrate_member_privilege_full_dump(self):
        """
        The main function for MemberPrivilege full dump migration task.

        Args:
            None

        Returns:
            None
        """
        MemberPrivilege(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
            service_name=self.service_name,
        ).migrate()

    def migrate_member_privilege_incremental(self):
        """
        The main function for MemberPrivilege incremental migration task.

        Args:
            None

        Returns:
            None
        """
        MemberPrivilege(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
            service_name=self.service_name,
        ).migrate(is_full_dump=False)


with DAG(
    "engagement_service_full_dump_migration_member_privilege",
    default_args={
        "owner": "airflow",
    },
    description="A dag for Engagement Service full dump migration.",
    schedule_interval=None,
    start_date=datetime(2025, 5, 28),
    catchup=False,
    tags=["engagement_service", "full_dump", "member_privilege"],
) as engagement_service_migration_dag:
    engagement_service = EngagementService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_batch_tracker_table_task = PythonOperator(
        task_id="prepare_batch_tracker_table_task",
        python_callable=engagement_service.prepare_batch_tracker_table,
    )

    prepare_tables_task = PythonOperator(
        task_id="prepare_tables_task",
        python_callable=engagement_service.prepare_tables,
    )

    migrate_member_privilege_task = PythonOperator(
        task_id="migrate_member_privilege_task",
        retries=3,
        retry_delay=timedelta(minutes=5),
        python_callable=engagement_service.migrate_member_privilege_full_dump,
    )

    (
        [
            prepare_migration_result_table_task,
            prepare_batch_tracker_table_task,
            prepare_tables_task,
        ]
        >> migrate_member_privilege_task
    )

with DAG(
    "engagement_service_incremental_migration_member_privilege",
    default_args={
        "owner": "airflow",
    },
    description="A dag for Engagement Service incremental migration.",
    schedule_interval="0 17 * * *",
    start_date=datetime(2025, 6, 22, 17, 0),
    catchup=False,
    tags=["engagement_service", "incremental", "member_privilege"],
) as engagement_service_migration_dag:
    engagement_service = EngagementService()

    create_lv_birthday_snapshot_task = PythonOperator(
        task_id="create_snapshot_task",
        python_callable=engagement_service.create_lv_birthday_snapshot,
    )

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_batch_tracker_table_task = PythonOperator(
        task_id="prepare_batch_tracker_table_task",
        python_callable=engagement_service.prepare_batch_tracker_table,
    )

    prepare_tables_task = PythonOperator(
        task_id="prepare_tables_task",
        python_callable=engagement_service.prepare_tables,
    )

    migrate_member_privilege_task = PythonOperator(
        task_id="migrate_member_privilege_task",
        retries=3,
        retry_delay=timedelta(minutes=5),
        python_callable=engagement_service.migrate_member_privilege_incremental,
    )

    (
        [
            create_lv_birthday_snapshot_task,
            prepare_migration_result_table_task,
            prepare_batch_tracker_table_task,
            prepare_tables_task,
        ]
        >> migrate_member_privilege_task
    )

