from airflow import D<PERSON>
from datetime import datetime, timedelta
from airflow.operators.python import PythonOperator
from common_helpers.utils_memberprofile import get_country_mapping


def log_nationality_code():
    get_country_mapping()


# full dump dag
# with DAG(
#     "log_nationality_code_mapping",
#     description="Update Nationality Code Mapping According Updated Business Requirements",
#     schedule_interval=None,
#     start_date=datetime(2023, 1, 1),
#     catchup=False,
#     tags=["loyalty_service", "debug_nationality_code"],
# ) as dag:

#     update_nationality_code_task = PythonOperator(
#         task_id=f"debug_nationality_code",
#         python_callable=log_nationality_code,
#         retries=3,
#         retry_delay=timedelta(minutes=5),
#     )

#     update_nationality_code_task
