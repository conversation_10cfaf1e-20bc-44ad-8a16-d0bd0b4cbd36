from airflow import <PERSON><PERSON>
from datetime import datetime, timed<PERSON><PERSON>
from airflow.operators.python import Python<PERSON>perator
from common_helpers.utils import get_logger, calc_total_batches, calc_last_batch_size, calc_offset, is_last_batch, log_start_process_batch, get_df
from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>g<PERSON><PERSON><PERSON><PERSON>
from constants import NEWMEMBER_CONN_ID, TEMP_CONN_ID, BATCH_SIZE
from common_helpers.logging import get_logger

logger = get_logger()
TABLE = "SalesTransaction"

def create_temp_sales_table():
    """
    Prepare indexed temporary tables for SalesTransaction migration.

    Args:
        is_full_dump (bool): The migration type.

    Returns:
        None
    """
    create_temp_accum_table = (
        f"""
            -- Drop temporary tables if they exist
            IF OBJECT_ID (N'TmpT0_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT0_192_168_1_103;
            END
            IF OBJECT_ID (N'TmpT1_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT1_192_168_1_103;
            END
            IF OBJECT_ID (N'TmpT2_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT2_192_168_1_103;
            END
            IF OBJECT_ID (N'TmpT3_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT3_192_168_1_103;
            END
            IF OBJECT_ID (N'TmpT4_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT4_192_168_1_103;
            END
            IF OBJECT_ID (N'TmpT5_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT5_192_168_1_103;
            END
            IF OBJECT_ID (N'temp_t5_sales_tnx_accum') IS NOT NULL 
            BEGIN
                DROP TABLE temp_t5_sales_tnx_accum;
            END

            -- TmpT0
            SELECT DISTINCT 
                CAST(t1.key_search AS CHAR(30)) COLLATE thai_ci_as AS KeySearch 
            INTO TmpT0_192_168_1_103
            FROM SMCSalesHeader t1 WITH (NOLOCK)
            INNER JOIN SMCSalesTrans t2 WITH (NOLOCK)
                ON t1.key_search = t2.key_search 
                AND lineCancel = 0 
                AND CancelStatus = 0
            INNER JOIN SMCSalesPayment smcp WITH (NOLOCK)
                ON t1.key_search = smcp.key_search
            INNER JOIN mast_NonAccCarat WITH (NOLOCK)
                ON smcp.MethodCode = mast_NonAccCarat.code 
                AND MAST_NonAccCarat.type = 'P' 
                AND MAST_NonAccCarat.IsCancel = 0
            INNER JOIN df_member WITH (NOLOCK)
                ON CAST(t1.member_id AS CHAR(8)) = df_member.member_id 
            WHERE t1.saleStatus <> 'R';

            CREATE NONCLUSTERED INDEX ix_tmpT0 ON TmpT0_192_168_1_103(keysearch);

            -- TmpT1
            SELECT 
                CAST(L1.KeySearch AS CHAR(30)) COLLATE thai_ci_as AS keysearch,
                SUM(-1 * l2.amount) AS net  
            INTO TmpT1_192_168_1_103
            FROM LoyaltyValue.dbo.LVHeader L1 WITH (NOLOCK)
            INNER JOIN LoyaltyValue.dbo.LVTrans L2 WITH (NOLOCK)
                ON L1.LVHeaderKey = L2.LVHeaderKey
            INNER JOIN LoyaltyValue.dbo.LVdata L3 WITH (NOLOCK)
                ON L2.LVMainKey = L3.LVMainKey
            INNER JOIN df_member WITH (NOLOCK)
                ON l2.LVNumber COLLATE thai_ci_as = df_member.member_id COLLATE thai_ci_as
            INNER JOIN TmpT0_192_168_1_103 L4
                ON CAST(l1.keysearch AS CHAR(30)) COLLATE thai_ci_as = l4.KeySearch
            WHERE L3.valuecode IN ('ep001','ep007','ep008','ep009','EP010','KPC01','KPO02')
            AND l2.movementcode = 'USE'
            GROUP BY CAST(L1.KeySearch AS CHAR(30));

            CREATE NONCLUSTERED INDEX ix_tmpT1 ON TmpT1_192_168_1_103(keysearch);

            -- TmpT2
            SELECT 
                t1.BranchNo,
                CAST(t1.key_search AS CHAR(30)) COLLATE thai_ci_as AS key_search,
                t1.salesBranch,
                CAST(t1.member_id AS CHAR(8)) AS member_id,
                SUM(t2.qty) AS qty 
            INTO TmpT2_192_168_1_103
            FROM SMCSalesHeader t1 WITH (NOLOCK)
            INNER JOIN SMCSalesTrans t2 WITH (NOLOCK)
                ON t1.key_search = t2.key_search 
                AND lineCancel = 0 
                AND CancelStatus = 0
            INNER JOIN df_member WITH (NOLOCK)
                ON CAST(t1.member_id AS CHAR(8)) = df_member.member_id 
            WHERE t1.saleStatus <> 'R'
            GROUP BY t1.BranchNo, t1.key_search, t1.salesBranch, t1.member_id;

            CREATE NONCLUSTERED INDEX ix_tmpT2 ON TmpT2_192_168_1_103(key_search);

            -- TmpT3
            SELECT *
            INTO TmpT3_192_168_1_103
            FROM TmpT2_192_168_1_103 T2
            INNER JOIN TmpT1_192_168_1_103 T1
                ON T2.key_search = T1.KeySearch;

            CREATE NONCLUSTERED INDEX ix_tmpT3 ON TmpT3_192_168_1_103(key_search);

            -- TmpT4
            SELECT 
                x.member_id,
                x.key_search,
                SUM(t3.net) AS total
            INTO TmpT4_192_168_1_103
            FROM (
                SELECT 
                    t1.key_search,
                    CAST(t1.member_id AS CHAR(8)) AS member_id
                FROM SMCSalesHeader t1 WITH (NOLOCK)
                INNER JOIN SMCSalesTrans t2 WITH (NOLOCK)
                    ON t1.key_search = t2.key_search
                    AND t2.lineCancel = 0 
                    AND t2.CancelStatus = 0
                INNER JOIN df_member WITH (NOLOCK)
                    ON CAST(t1.member_id AS CHAR(8)) = df_member.member_id
                WHERE t1.saleStatus <> 'R'
                GROUP BY t1.BranchNo, t1.key_search, t1.salesBranch, t1.member_id
            ) x
            INNER JOIN SMCSalesPayment t3 WITH (NOLOCK)
                ON x.key_search = t3.key_search
                AND t3.MethodCode NOT IN (
                    SELECT Code 
                    FROM mast_NonAccCarat 
                    WHERE type = 'P' AND iscancel = 0
                )
            GROUP BY x.key_search, x.member_id;

            CREATE NONCLUSTERED INDEX ix_tmpT4 ON TmpT4_192_168_1_103(key_search);

            -- TmpT5
            SELECT 
                z.member_id,
                z.key_search,
                z.totalEarnableAmount,
                z.totalAccumSpendableAmount
            INTO TmpT5_192_168_1_103
            FROM (
                SELECT 
                    member_id, 
                    key_search,
                    net AS totalEarnableAmount,
                    net AS totalAccumSpendableAmount
                FROM TmpT3_192_168_1_103

                UNION

                SELECT 
                    member_id, 
                    key_search,
                    total AS totalEarnableAmount,
                    total AS totalAccumSpendableAmount
                FROM TmpT4_192_168_1_103
            ) z;

            CREATE NONCLUSTERED INDEX ix_tmpT5 ON TmpT5_192_168_1_103(key_search);
            
            -- Final result
            SELECT 
                key_search,
                SUM(totalAccumSpendableAmount) AS totalAccumSpendableAmount,
                SUM(totalEarnableAmount) AS totalEarnableAmount
            INTO temp_t5_sales_tnx_accum
            FROM TmpT5_192_168_1_103
            GROUP BY key_search;

            -- Drop temporary tables after use
            IF OBJECT_ID (N'TmpT0_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT0_192_168_1_103;
            END
            IF OBJECT_ID (N'TmpT1_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT1_192_168_1_103;
            END
            IF OBJECT_ID (N'TmpT2_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT2_192_168_1_103;
            END
            IF OBJECT_ID (N'TmpT3_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT3_192_168_1_103;
            END
            IF OBJECT_ID (N'TmpT4_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT4_192_168_1_103;
            END
            IF OBJECT_ID (N'TmpT5_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT5_192_168_1_103;
            END
        """)
    newmember_handler = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    newmember_connection = newmember_handler.hook.get_conn()

    try:
        logger.info(f"started preparing accum temp table for migration...")
        newmember_handler.execute_query_string(
            connection=newmember_connection,
            query_string=create_temp_accum_table,
        )
        logger.info(f"finished preparing accum temp table for migration.")

    finally:
        newmember_connection.close()

def drop_temp_tables() -> None:
    drop_temp_tables = (
        f"""
        IF OBJECT_ID (N'temp_t5_sales_tnx_accum') IS NOT NULL 
        BEGIN
            DROP TABLE temp_t5_sales_tnx_accum;
        END
        """
    )
    newmember_handler = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    newmember_connection = newmember_handler.hook.get_conn()

    newmember_handler = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    newmember_connection = newmember_handler.hook.get_conn()

    try:
        logger.info(f"started dropping accum temp table for migration...")
        newmember_handler.execute_query_string(
            connection=newmember_connection,
            query_string=drop_temp_tables,
        )
        logger.info(f"finished dropping accum temp table for migration.")

    finally:
        newmember_connection.close()

def set_accum_to_be_zero(postgres: PostgresHandler):
    query = f"""
    UPDATE loyalty_service."{TABLE}"
    SET "totalAccumSpendableAmount" = 0, 
        "totalEarnableAmount" = 0;
    """
    postgres.execute_query(query)

def update_query(
    id: str,
    accumulate_spendable_spending: float,
    accumulate_earnable_spending: float,
) -> str:
    query = f"""
        UPDATE loyalty_service."{TABLE}"
        SET "totalAccumSpendableAmount" = {accumulate_spendable_spending}, 
            "totalEarnableAmount" = {accumulate_earnable_spending}
        WHERE id = '{id}';
    """
    return query

def update_sales_accum_in_db(df, conn):
    # Loop through DataFrame and execute an update query for each row
    for _, row in df.iterrows():
        query = update_query(
            row["id"],
            row["totalAccumSpendableAmount"],
            row["totalEarnableAmount"],
        )

        with conn.cursor() as cursor:
            cursor.execute(query)
    
    conn.commit()


def get_total_rows(postgres: PostgresHandler) -> int:
    query = f"""
    SELECT COUNT(*) FROM loyalty_service."{TABLE}";
    """
    return postgres.extract_data(query)[0][0]


def update_sales_accum():
    postgres = PostgresHandler(conn_id=TEMP_CONN_ID)
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    logger = get_logger()
    conn = postgres.hook.get_conn()

    logger.info("Starting update sales accum...")

    # # set accum to be zero
    # logger.info("Setting accum to be zero...")
    # set_accum_to_be_zero(postgres)

    total_rows = get_total_rows(postgres)
    total_batches = calc_total_batches(total_rows, BATCH_SIZE)
    last_batch_size = calc_last_batch_size(total_rows, BATCH_SIZE)
    logger.info(f"Total rows: {total_rows}, Total batches: {total_batches}")

    dest_count = 0

    for batch_num in range(0, total_batches):
        log_start_process_batch("Member", batch_num, total_batches)

        offset = calc_offset(batch_num, BATCH_SIZE)
        batch_size = (
            last_batch_size if is_last_batch(batch_num, total_batches) else BATCH_SIZE
        )
        # get SalesTransaction data 
        query = f"""
            SELECT 
                st.id,
                st."externalId"
            FROM loyalty_service."{TABLE}" st
            ORDER BY st."id" OFFSET {offset} ROWS FETCH NEXT {batch_size} ROWS ONLY;
        """
        df = get_df(query, postgres)
        key_searchs = df["externalId"].to_list()

        # get totalAccumSpendableAmount, totalEarnableAmount from temp_t5_sales_tnx_accum
        query = f"""
            SELECT 
                key_search as externalId,
                totalAccumSpendableAmount,
                totalEarnableAmount
            FROM temp_t5_sales_tnx_accum
            WHERE key_search IN ({",".join([f"'{k}'" for k in key_searchs])})
        """
        accum_df = get_df(query, mssql)

        df = df.merge(accum_df, on="externalId", how="inner")

        update_sales_accum_in_db(df, conn)
        logger.info(f"Successfully updated {len(df)} rows, Batch size: {batch_size}")
        
        dest_count += len(df)
    

    logger.info(f"Successfully updated `totalAccumSpendableAmount` and `totalEarnableAmount` in {TABLE} (total rows: {dest_count}/{total_rows})")


with DAG(
    "loyalty_service_update_sales_accum",
    description="Update totalAccumSpendableAmount and totalEarnableAmount in SalesTransaction Table",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=[
        "loyalty_service",
        "sales_accum",
    ],
) as dag:

    # create_temp_sales_table_task = PythonOperator(
    #     task_id=f"create_temp_sales_table_task",
    #     python_callable=create_temp_sales_table,
    # )

    update_sales_accum_task = PythonOperator(
        task_id="update_sales_accum_task",
        python_callable=update_sales_accum,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    # drop_temp_tables_task = PythonOperator(
    #     task_id="drop_temp_tables_task",
    #     python_callable=drop_temp_tables,
    # )

    # create_temp_sales_table_task >> update_sales_accum_task
    
    update_sales_accum_task
