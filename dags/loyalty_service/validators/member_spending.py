# dag for running validaiton script on loyalty service

import pandas as pd
from airflow import DAG
from datetime import datetime, timedelta
from airflow.operators.python import PythonOperator
from common_helpers.logging import get_logger
from common_helpers.utils import get_df
from constants import TEMP_CONN_ID, VALIDATION_CUTOFF_DATE
from common_helpers.database_services import PostgresHandler


def insert_validation_summary(cur, table: str, is_valid: bool, total_rules: int, failed_rules: int, summary_message: str) -> int:
    """Insert validation summary and return run_id"""
    cur.execute(
        """
        INSERT INTO public.validation_summary 
        (service, table_name, is_valid, total_rules, failed_rules, summary_message)
        VALUES ('LoyaltyService', %s, %s, %s, %s, %s)
        RETURNING run_id;
        """,
        (table, is_valid, total_rules, failed_rules, summary_message)
    )
    return cur.fetchone()[0]


def insert_accum_spending_validation(cur, run_id: int, validation_df: pd.DataFrame):
    """Insert accumulated spending validation result"""
    difference = validation_df["difference"].iloc[0]
    total_member = validation_df["total_member_accummulate_spending"].iloc[0]
    total_sales = validation_df["total_sales_spending_in_last_2_years"].iloc[0]
    total_refund = validation_df["total_refund_spending_in_last_2_years"].iloc[0]
    
    message = (
        f"Member accumulated spending: {total_member:,.2f}, "
        f"Sales spending (2 years): {total_sales:,.2f}, "
        f"Refund spending (2 years): {total_refund:,.2f}, "
        f"Difference: {difference:,.2f}"
    )
    
    cur.execute(
        """
        INSERT INTO public.validation_results 
        (run_id, service, source_table, destination_table, rule_name, is_valid, message)
        VALUES (%s, 'LoyaltyService', '-', 'Member', 'check_accumulated_spending', %s, %s)
        """,
        (run_id, bool(difference == 0), message)
    )


def insert_lifetime_spending_validation(cur, run_id: int, validation_df: pd.DataFrame):
    """Insert lifetime spending validation result"""
    difference = validation_df["difference"].iloc[0]
    total_member = validation_df["total_member_life_time_spending"].iloc[0]
    total_sales = validation_df["total_sales_spending"].iloc[0]
    total_refund = validation_df["total_refund_spending"].iloc[0]
    
    message = (
        f"Member lifetime spending: {total_member:,.2f}, "
        f"Total sales: {total_sales:,.2f}, "
        f"Total refund: {total_refund:,.2f}, "
        f"Difference: {difference:,.2f}"
    )
    
    cur.execute(
        """
        INSERT INTO public.validation_results 
        (run_id, service, source_table, destination_table, rule_name, is_valid, message)
        VALUES (%s, 'LoyaltyService', '-', 'Member', 'check_life_time_spending', %s, %s)
        """,
        (run_id, bool(difference == 0), message)
    )


def get_accum_spending_query() -> str:
    """
    Generates an SQL query to validate accumulated spending of member.
    """
    if VALIDATION_CUTOFF_DATE:
        last_2_years = f"SELECT DATE_TRUNC('month', DATE '{VALIDATION_CUTOFF_DATE}') - INTERVAL '2 years' AS start_date"
    else:
        last_2_years = "SELECT DATE_TRUNC('month', NOW() - INTERVAL '1 day') - INTERVAL '2 years' AS start_date"

    return f"""
    WITH last_2_years AS (
        {last_2_years}
    )
    SELECT 
        -- Total member spending
        (SELECT SUM("accumulateSpending") 
        FROM loyalty_service."Member") AS total_member_accummulate_spending,

        -- Total sales spending
        (SELECT COALESCE(SUM("totalAccumSpendableAmount"), 0) 
        FROM loyalty_service."SalesTransaction"
        WHERE "createdAt" >= (SELECT start_date FROM last_2_years)) AS total_sales_spending_in_last_2_years,

        -- Total refund spending
        (SELECT COALESCE(SUM("revokeAccumSpendableAmount"), 0) 
        FROM loyalty_service."RefundSalesTransaction"
        WHERE "createdAt" >= (SELECT start_date FROM last_2_years)) AS total_refund_spending_in_last_2_years,

        -- Difference calculation
        (
            (SELECT SUM("accumulateSpending") 
            FROM loyalty_service."Member") 
            - 
            (
                COALESCE(
                    (SELECT SUM("totalAccumSpendableAmount") 
                    FROM loyalty_service."SalesTransaction"
                    WHERE "createdAt" >= (SELECT start_date FROM last_2_years)), 0
                ) 
                + 
                COALESCE(
                    (SELECT SUM("revokeAccumSpendableAmount") 
                    FROM loyalty_service."RefundSalesTransaction"
                    WHERE "createdAt" >= (SELECT start_date FROM last_2_years)), 0
                )
            )
        ) AS difference;
    """


def get_life_spending_query() -> str:
    """
    Generates an SQL query to validate life time spending of member.
    """
    return """
    SELECT 
        (SELECT COALESCE(SUM("lifeTimeSpending"), 0) 
        FROM loyalty_service."Member") AS total_member_life_time_spending,
        
        (SELECT COALESCE(SUM("netTotalAmount"), 0) 
        FROM loyalty_service."SalesTransaction") AS total_sales_spending,
        
        (SELECT COALESCE(SUM("refundAmount"), 0) 
        FROM loyalty_service."RefundSalesTransaction") AS total_refund_spending,
        
        (
            (SELECT COALESCE(SUM("lifeTimeSpending"), 0) 
            FROM loyalty_service."Member") 
            - 
            (
                COALESCE((SELECT SUM("netTotalAmount") 
                        FROM loyalty_service."SalesTransaction"), 0) 
                - 
                COALESCE((SELECT SUM("refundAmount") 
                        FROM loyalty_service."RefundSalesTransaction"), 0)
            )
        ) AS difference;
    """


def log_dataframe_values(df: pd.DataFrame) -> None:
    """
    Logs the column name and value of each column in the DataFrame.

    Parameters:
    df (pd.DataFrame): The DataFrame whose column names and values need to be logged.
    """
    logger = get_logger()

    # Iterate over each column in the DataFrame
    for column in df.columns:
        value = df[column].iloc[0]  # Get the first value of the column
        if isinstance(value, (int, float)):
            value = f"{value:,}"

        # Log the column name and its value
        logger.info(f"{column}: {value}")
        

def validate_member_spending() -> None:
    """
    Validates member spending data and logs results to validation tables
    """
    logger = get_logger()
    temp_postgres = PostgresHandler(conn_id=TEMP_CONN_ID)
    
    try:
        # Get validation data
        accum_spending_df = get_df(get_accum_spending_query(), temp_postgres)
        lifetime_spending_df = get_df(get_life_spending_query(), temp_postgres)
        
        # Log raw values
        logger.info("=== Accumulated Spending Validation ===")
        log_dataframe_values(accum_spending_df)
        logger.info("\n=== Lifetime Spending Validation ===")
        log_dataframe_values(lifetime_spending_df)
        
        # Calculate validation status
        accum_difference = accum_spending_df["difference"].iloc[0]
        lifetime_difference = lifetime_spending_df["difference"].iloc[0]
        
        total_rules = 2  # Accumulated spending and Lifetime spending
        failed_rules = 0
        validation_messages = []
        
        # Rule 1: Accumulated spending check
        if accum_difference != 0:
            failed_rules += 1
            validation_messages.append(f"Accumulated spending mismatch: {accum_difference:,.2f}")
            logger.error("Validation No.1 failed: Member's accumulated spending does not match sales and refund transactions.")
        else:
            logger.success("Validation No.1 successful: Member's accumulated spending matches sales and refund transactions in the last 2 year.")
            
        # Rule 2: Lifetime spending check
        if lifetime_difference != 0:
            failed_rules += 1
            validation_messages.append(f"Lifetime spending mismatch: {lifetime_difference:,.2f}")
            logger.error("Validation No.2 failed: Member's life time spending does not match sales and refund transactions.")
        else:
            logger.success("Validation No.2 successful: Member's life time spending matches sales and refund transactions.")
        
        is_valid = failed_rules == 0
        summary_message = "Validation successful" if is_valid else "; ".join(validation_messages)
        
        # Log final validation summary
        logger.info("\n=== Validation Summary ===")
        logger.info(f"Total Rules: {total_rules}")
        logger.info(f"Failed Rules: {failed_rules}")
        logger.info(f"Status: {'PASSED' if is_valid else 'FAILED'}")
        logger.info(f"Summary: {summary_message}")
        
        # Insert results into validation tables
        with temp_postgres.hook.get_conn() as conn:
            with conn.cursor() as cur:
                # Insert validation summary
                run_id = insert_validation_summary(
                    cur, 
                    "Member (AccummulateSpending, lifeTimeSpending)", 
                    is_valid, 
                    total_rules, 
                    failed_rules, 
                    summary_message
                )
                
                # Insert individual validation results
                insert_accum_spending_validation(cur, run_id, accum_spending_df)
                insert_lifetime_spending_validation(cur, run_id, lifetime_spending_df)
                
            conn.commit()
            
        logger.info(f"Validation results stored in database with Run ID: {run_id}")
        
    except Exception as err:
        logger.error(f"An error occurred during spending validation: {err}")
        raise err

