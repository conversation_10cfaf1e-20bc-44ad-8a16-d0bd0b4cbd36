from typing import Dict, Any
import pandas as pd
import numpy as np
from common_helpers.database_services import <PERSON>S<PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.utils import get_logger, get_df, cutoff_date_condition_mssql, incremental_date_condition
from constants import NEWMEMBER_CONN_ID, TEMP_CONN_ID, VALIDATION_CUTOFF_DATE, VALIDATION_SAMPLE_SIZE as SAMPLE_SIZE

TABLE = "MemberLegacyCobrandHistory"


def get_source_data(mssql: MSSQLHandler) -> pd.DataFrame:
    """Get source data with key fields for validation."""
    filter_condition = f"""
        AND {cutoff_date_condition_mssql('dc.add_datetime', VALIDATION_CUTOFF_DATE)}
        AND {cutoff_date_condition_mssql('dc.update_datetime', VALIDATION_CUTOFF_DATE)}
    """

    source_query = f"""
    SELECT 
        dc.member_id as "memberId",
        dc.runno as source_runno,
        dc."CardTypeCode" as source_cardtypecode,
        dc.card_type_id as "cardTypeCode",
        dc.emboss_id as "embossNo",
        CAST(r.reason_desc AS NVARCHAR(max)) AS "cardReason",
        cs.StatusName as "cardStatus",
        ct.description
    FROM df_cardhist dc 
    JOIN df_member dm ON dc.member_id = dm.member_id
    LEFT JOIN mst_card_type ct ON dc.card_type_id = ct.card_type_code
    LEFT JOIN mst_reason r ON dc.reason_id = r.reason_id
    LEFT JOIN MAST_CardStatus cs ON dc.card_status = cs.CardStatus
    WHERE dc.CardTypeCode IN ('SCB', 'KBANK') 
    AND dm.del_flag = ' '
    AND {cutoff_date_condition_mssql('dm.add_datetime', VALIDATION_CUTOFF_DATE)}
        {filter_condition}
    ORDER BY dc.member_id, dc.runno, dc.card_type_id
    """
    source_df = get_df(source_query, mssql)
    # Strip whitespace from string columns
    source_df = source_df.apply(lambda x: x.str.strip() if x.dtype == "object" else x)

    return source_df


def get_destination_data(postgres: PostgresHandler) -> pd.DataFrame:
    """Get destination data."""
    dest_query = f"""
    SELECT 
        mlch."memberId",
        mlch.source_runno,
        mlch.source_cardtypecode,
        mlch."cardTypeCode",
        mlch."embossNo",
        mlch.description,
        mlch."cardStatus",
        mlch."cardReason"
    FROM loyalty_service."{TABLE}" mlch
    JOIN loyalty_service."Member" m ON mlch."memberId" = m."gwlNo"
    WHERE m."deletedAt" IS NULL
    ORDER BY mlch."memberId", mlch.source_runno, mlch.source_cardtypecode
    """
    return get_df(dest_query, postgres)


def check_data_correctness(
    source_df: pd.DataFrame, 
    dest_df: pd.DataFrame,
):
    """
    Check data correctness for fields using sampling.

    Args:
        source_df: Source dataframe containing the data to validate
        dest_df: Destination dataframe to validate against
        sample_size: Number of records to sample for validation

    Returns:
        Tuple containing:
        - DataFrame of mismatched records
        - Boolean indicating if mismatches were found
        - Message describing the validation result
    """
    total_mismatches = 0
    mismatch_messages = []
    all_mismatches = []
    field_mappings = [
        {
            "source_field": "cardTypeCode_x",
            "dest_field": "cardTypeCode_y",
            "expected_value_field": "cardTypeCode_x",
        },
        {
            "source_field": "embossNo_x",
            "dest_field": "embossNo_y",
            "expected_value_field": "embossNo_x",
        },
        {
            "source_field": "description_x",
            "dest_field": "description_y",
            "expected_value_field": "description_x",
        },
        {
            "source_field": "cardStatus_x",
            "dest_field": "cardStatus_y",
            "expected_value_field": "cardStatus_x",
        },
        {
            "source_field": "cardReason_x",
            "dest_field": "cardReason_y",
            "expected_value_field": "cardReason_x",
        },
    ]

    merged_df = pd.merge(
        source_df, 
        dest_df, 
        on=["memberId", "source_runno", "source_cardtypecode"], 
        how="inner",
    )

    merged_df.replace(np.nan, None, inplace=True)
    merged_df.replace("", None, inplace=True)

    logger = get_logger()
    logger.info(f"merged_df: {merged_df.columns}")

     # Check each field pair
    for mapping in field_mappings:
        source_field = mapping["source_field"]
        dest_field = mapping["dest_field"]

        # Compare destination value with expected value, properly handling NULL values
        field_mismatches = merged_df[
            ~(
                ((merged_df[source_field].isna()) & (merged_df[dest_field].isna())) |
                (merged_df[source_field] == merged_df[dest_field])
            )    
        ]

        if not field_mismatches.empty:
            mismatch_count = len(field_mismatches)
            total_mismatches += mismatch_count
            mismatch_messages.append(
                f"{mismatch_count} {source_field}->{dest_field} mismatches"
            )

            # Add field information to mismatches
            for _, row in field_mismatches.iterrows():
                mismatch_info = {
                    "source_record_id": f"{row['memberId']}_{row['source_runno']}_{row['source_cardtypecode']}",
                    "source_field": source_field,
                    "source_value": str(row[source_field]),
                    "destination_field": dest_field,
                    "destination_value": str(row[dest_field]),
                    "expected_value": str(row[source_field]),
                }
                all_mismatches.append(mismatch_info)

    has_mismatches = len(all_mismatches) > 0

    if has_mismatches:
        message = f"Found {total_mismatches} total mismatches in sample of {len(merged_df)} records: {'; '.join(mismatch_messages)}"
    else:
        message = f"All values match in sample of {len(merged_df)} records"

    # Convert mismatches to DataFrame
    mismatches_df = pd.DataFrame(all_mismatches) if all_mismatches else pd.DataFrame()

    return mismatches_df, has_mismatches, message



def insert_validation_summary(
    cur: Any,
    table: str,
    is_valid: bool,
    total_rules: int,
    failed_rules: int,
    summary_message: str,
) -> int:
    """Insert validation summary and return run_id."""
    cur.execute(
        """
        INSERT INTO public.validation_summary 
        (service, table_name, is_valid, total_rules, failed_rules, summary_message)
        VALUES ('LoyaltyService', %s, %s, %s, %s, %s)
        RETURNING run_id;
        """,
        (table, is_valid, total_rules, failed_rules, summary_message),
    )
    return cur.fetchone()[0]


def insert_validation_result(
    cur: Any, 
    run_id: int, 
    rule_name: str, 
    is_valid: bool, 
    message: str
) -> int:
    """Insert validation result and return result_id."""
    cur.execute(
        """
        INSERT INTO public.validation_results 
        (run_id, service, source_table, destination_table, rule_name, is_valid, message)
        VALUES (%s, 'LoyaltyService', 'df_cardhist', %s, %s, %s, %s)
        RETURNING id;
        """,
        (run_id, TABLE, rule_name, is_valid, message),
    )
    return cur.fetchone()[0]


def insert_record_differences(
    cur: Any, validation_result_id: int, mismatches_df: pd.DataFrame
) -> None:
    """Insert record differences for mismatched records."""
    if not mismatches_df.empty:
        for _, row in mismatches_df.iterrows():
            cur.execute(
                """
                INSERT INTO public.record_differences
                (validation_result_id, source_record_id, source_field, source_value, 
                destination_field, destination_value, expected_value)
                VALUES (%s, %s, %s, %s, %s, %s, %s);
                """,
                (
                    validation_result_id,
                    row["source_record_id"],
                    row["source_field"],
                    row["source_value"],
                    row["destination_field"],
                    row["destination_value"],
                    row["expected_value"],
                ),
            )


def adhoc_check_all_mlcobrandhistory() -> Dict:
    """
    Compare data between source and destination databases to ensure data integrity.
    Validates:
    1. Data correctness for all fields (using sample of {SAMPLE_SIZE} records)
    """
    logger = get_logger()
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    postgres = PostgresHandler(conn_id=TEMP_CONN_ID)


    # Initialize validation results
    total_rules = 1
    failed_rules = 0
    validation_messages = []

    # Rule 1: Check data correctness for all fields
    # Get data from source and destination
    source_df = get_source_data(mssql)
    dest_df = get_destination_data(postgres)
    mismatches_df, has_mismatches, mismatch_message = check_data_correctness(source_df, dest_df)

    logger.info(f"mismatches_df: {len(mismatches_df)}")

    if has_mismatches:
        failed_rules += 1
        validation_messages.append(mismatch_message)
        logger.warning(mismatch_message)
    else:
        logger.info(mismatch_message)


    # Create final summary
    is_valid = failed_rules == 0
    summary_message = (
        "Validation successful" if is_valid else "; ".join(validation_messages)
    )

    # Insert results into validation tables
    with postgres.hook.get_conn() as conn:
        with conn.cursor() as cur:
            # Insert validation summary
            run_id = insert_validation_summary(
                cur, TABLE, is_valid, total_rules, failed_rules, summary_message
            )

            # Insert transformation issues validation result
            result_id = insert_validation_result(
                cur, run_id, "data_correctness", not has_mismatches, mismatch_message
            )

            # Store mismatches in record_differences table if any field has mismatches
            if has_mismatches:
                insert_record_differences(cur, result_id, mismatches_df)

        conn.commit()

    logger.info(f"Validation completed - Run ID: {run_id}")
    logger.info(f"Total Rules: {total_rules}, Failed Rules: {failed_rules}")
    logger.info(f"Summary: {summary_message}")

    return {
        "run_id": run_id,
        "is_valid": is_valid,
        "failed_rules": failed_rules,
        "summary_message": summary_message,
    }
