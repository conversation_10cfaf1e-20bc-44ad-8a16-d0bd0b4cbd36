from constants import (
    NEWMEMBER_CONN_ID,
    TEMP_CONN_ID,
    VALIDATION_CUTOFF_DATE,
    SALESTRANS_BATCH_SIZE as BATCH_SIZE,
)
from common_helpers.database_services import <PERSON>S<PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.utils import (
    get_logger,
    get_df,
    cutoff_date_condition_mssql,
    calc_total_batches,
    calc_last_batch_size,
    calc_offset,
    is_last_batch,
)

TABLE = "SalesTransaction"


def create_source_temp_table(mssql: MSSQLHandler) -> None:
    logger = get_logger()

    create_source_temp_table = f"""
    IF OBJECT_ID('temp_source_sales_tnx_validation', 'U') IS NOT NULL
    BEGIN
        DROP TABLE temp_source_sales_tnx_validation;
    END

    SELECT 
        s.key_search
    INTO temp_source_sales_tnx_validation
    FROM 
        Newmember.dbo.SMCSalesHeader s
    INNER JOIN SMCSalesTrans st ON s.key_search = st.key_search and st.lineCancel = 0 and st.CancelStatus = 0
    INNER JOIN df_member dm on s.member_id = dm.member_id and dm.del_flag = ''
    LEFT JOIN LoyaltyValue.dbo.LVHeader l ON s.key_search COLLATE Thai_CI_AS = l.KeySearch
    WHERE s.SaleStatus <> 'R'
    AND l.CancelHeaderKey is null
    AND {cutoff_date_condition_mssql('s.DataDate', VALIDATION_CUTOFF_DATE)}
    GROUP BY s.key_search
    """

    logger.info(
        f"started preparing source temp table for SalesTransaction validation..."
    )
    mssql.execute_query_string(
        connection=mssql.hook.get_conn(),
        query_string=create_source_temp_table,
    )
    logger.info(
        f"Finished preparing source temp table for SalesTransaction validation..."
    )


def create_dest_temp_table(postgres: PostgresHandler) -> None:
    logger = get_logger()

    create_dest_temp_table = f"""
    DROP TABLE IF EXISTS public.temp_dest_sales_tnx_validation;

    SELECT 
        st."externalId"
    INTO public.temp_dest_sales_tnx_validation
    FROM loyalty_service."SalesTransaction" st
    JOIN loyalty_service."Member" m 
        ON m."gwlNo" = st."memberId"
    WHERE m."deletedAt" IS NULL; 
    """

    logger.info(f"Start creating temp table: `temp_dest_sales_tnx_validation`...")
    postgres.execute_query(create_dest_temp_table)
    logger.info(f"Finished creating temp table: `temp_dest_sales_tnx_validation`...")


def drop_temp_tables(mssql: MSSQLHandler, postgres: PostgresHandler) -> None:
    logger = get_logger()
    logger.info(f"started dropping temp tables for SalesTransaction validation...")
    mssql.execute_query_string(
        connection=mssql.hook.get_conn(),
        query_string=f"DROP TABLE IF EXISTS temp_source_sales_tnx_validation",
    )
    mssql.execute_query_string(
        connection=mssql.hook.get_conn(),
        query_string=f"DROP TABLE IF EXISTS temp_dest_sales_tnx_validation  ",
    )
    postgres.execute_query(
        f"DROP TABLE IF EXISTS public.temp_dest_sales_tnx_validation"
    )
    logger.info(f"Finished dropping temp tables for SalesTransaction validation...")


def get_source_count(mssql: MSSQLHandler):
    """Get the count of records in the source table."""
    query = f"""
    SELECT 
        count(key_search)
    FROM 
        temp_source_sales_tnx_validation
    """
    return mssql.extract_data(query)[0][0]


def get_dest_count(postgres: PostgresHandler) -> int:
    """Get the count of records in the destination table."""
    query = f"""
    SELECT 
        COUNT(*) as total_rows
    FROM 
        public.temp_dest_sales_tnx_validation
    """
    return postgres.extract_data(query)[0][0]


def fetch_source_key_searchs(
    mssql: MSSQLHandler,
    offset: int,
    batch_size: int,
) -> list[str]:
    query = f"""
    select key_search 
    FROM temp_source_sales_tnx_validation
    order by key_search
    OFFSET {offset} ROWS FETCH NEXT {batch_size} ROWS ONLY;
    """
    df = get_df(query, mssql)
    return df["key_search"].to_list()


def find_missing_records_in_destination(
    postgres: PostgresHandler,
    key_searchs: list[str],
) -> set[str]:
    query = f"""
        select "externalId" 
        from public.temp_dest_sales_tnx_validation 
        where "externalId" in ({', '.join([f"'{k}'" for k in key_searchs])})
    """
    key_searchs_in_dest = get_df(query, postgres)["externalId"].to_list()

    missing_key_searchs = set(key_searchs) - set(key_searchs_in_dest)
    return missing_key_searchs


def get_missing_records_in_destination(
    mssql: MSSQLHandler,
    postgres: PostgresHandler,
) -> set[str]:
    logger = get_logger()

    total_rows = get_source_count(mssql)
    total_batches = calc_total_batches(total_rows, BATCH_SIZE)
    last_batch_size = calc_last_batch_size(total_rows, BATCH_SIZE)
    all_missing_records = set()

    for batch_num in range(0, total_batches):
        offset = calc_offset(batch_num, BATCH_SIZE)
        batch_size = (
            last_batch_size if is_last_batch(batch_num, total_batches) else BATCH_SIZE
        )

        key_searchs = fetch_source_key_searchs(mssql, offset, batch_size)

        missing_records = find_missing_records_in_destination(postgres, key_searchs)
        if missing_records:
            logger.info(
                f"Found {len(missing_records)} missing records in destination: {sorted(missing_records)}"
            )
            all_missing_records.update(missing_records)

        logger.info(
            f"Processing batch {batch_num}/{total_batches}: {len(key_searchs)} records"
        )

    return all_missing_records


def validate_sales_transaction_migration():
    """
    Compare data between source and destination databases to ensure data integrity.
    Stores validation results in validation_summary, validation_results, and record_differences tables.
    """
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    postgres = PostgresHandler(conn_id=TEMP_CONN_ID)
    logger = get_logger()

    # Create temp tables
    create_source_temp_table(mssql)
    create_dest_temp_table(postgres)

    # Initialize validation results
    total_rules = 2  # Count check, missing records checks
    failed_rules = 0
    validation_messages = []

    # Create validation summary entry
    summary_query = """
        INSERT INTO public.validation_summary 
        (service, table_name, is_valid, total_rules, failed_rules, summary_message)
        VALUES ('LoyaltyService', %s, %s, %s, %s, %s)
        RETURNING run_id;
    """

    # Rule 1: Check record counts
    source_count = get_source_count(mssql)
    dest_count = get_dest_count(postgres)
    count_match = source_count == dest_count

    if not count_match:
        failed_rules += 1
        validation_messages.append(
            f"Record count mismatch: Source={source_count}, Destination={dest_count}"
        )

    # Rule 2: Check for missing records in destination
    missing_records = get_missing_records_in_destination(mssql, postgres)
    if missing_records:
        failed_rules += 1
        logger.info(
            f"Records missing in destination ({len(missing_records)} records): {sorted(missing_records)}"
        )
        validation_messages.append(
            f"Missing records in destination: {len(missing_records)} records"
        )

    # Drop temp tables
    drop_temp_tables(mssql, postgres)

    # Create final summary
    is_valid = failed_rules == 0
    summary_message = (
        "Validation successful" if is_valid else "; ".join(validation_messages)
    )
    logger.info(f"Validation summary: {summary_message}")

    # Insert results into validation tables
    with postgres.hook.get_conn() as conn:
        with conn.cursor() as cur:
            # Insert validation summary
            cur.execute(
                summary_query,
                (TABLE, is_valid, total_rules, failed_rules, summary_message),
            )
            run_id = cur.fetchone()[0]

            # Insert count validation result
            cur.execute(
                """
                INSERT INTO public.validation_results 
                (run_id, service, source_table, destination_table, rule_name, is_valid, message)
                VALUES (%s, 'LoyaltyService', 'df_member', %s, 'record_count', %s, %s)
                RETURNING id;
                """,
                (
                    run_id,
                    TABLE,
                    count_match,
                    f"Source: {source_count}, Destination: {dest_count}",
                ),
            )

            # Insert missing records validation results (source -> destination)
            missing_records = sorted(list(missing_records))
            cur.execute(
                """"
                INSERT INTO public.validation_results 
                (run_id, service, source_table, destination_table, rule_name, is_valid, message)
                VALUES (%s, 'LoyaltyService', 'df_member', %s, 'missing_in_destination', %s, %s)
                RETURNING id;
                """,
                (
                    run_id,
                    TABLE,
                    len(missing_records) == 0,
                    (
                        "No missing records in destination"
                        if len(missing_records) == 0
                        else f"Records found in source but missing in destination ({len(missing_records)} records): {missing_records}"
                    ),
                ),
            )
            if missing_records:
                logger.warning(f"Records missing in destination: {missing_records}")
            else:
                logger.info("No records missing in destination")

        conn.commit()

    logger.info(f"Validation completed - Run ID: {run_id}")
    logger.info(f"Total Rules: {total_rules}, Failed Rules: {failed_rules}")
    logger.info(f"Summary: {summary_message}")

    return {
        "run_id": run_id,
        "is_valid": is_valid,
        "failed_rules": failed_rules,
        "summary_message": summary_message,
    }
