import numpy as np
import pandas as pd

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.utils import (
    cast_nvarchar,
    convert_bangkok_to_utc,
    get_logger,
    get_df,
    cutoff_date_condition_mssql,
    cutoff_date_condition_postgres,
    incremental_date_condition,
)
from common_helpers.utils_member import (
    get_subprogram_mapping,
    valid_thai_phone,
    email_cleansing_df,
)
from constants import (
    ENCRYPT_KEY,
    NEWMEMBER_CONN_ID,
    TEMP_CONN_ID,
    SUBPROGRAM_PATH,
    VALIDATION_CUTOFF_DATE,
    VALIDATION_SAMPLE_SIZE as SAMPLE_SIZE,
    EMAIL_MAPPING_PATH
)

logger = get_logger()
TABLE = "Member"

def get_sample_size(mssql: MSSQLHandler, is_full_dump: bool = True):
    if is_full_dump:
        filter_condition = f"""
            AND {cutoff_date_condition_mssql('m.add_datetime', VALIDATION_CUTOFF_DATE)}
            AND {cutoff_date_condition_mssql('m.update_datetime', VALIDATION_CUTOFF_DATE)}
        """
    else:
        filter_condition = f"""
            AND {cutoff_date_condition_mssql('m.update_datetime', VALIDATION_CUTOFF_DATE)} -- suppose be the record that is no updated after the validation cutoff date
            AND ({incremental_date_condition('m.add_datetime', VALIDATION_CUTOFF_DATE)}
            OR {incremental_date_condition('m.update_datetime', VALIDATION_CUTOFF_DATE)})
        """
    total_count_query = f"""
        SELECT COUNT(*) as total
        FROM df_member m
        WHERE m.del_flag = ' '
            {filter_condition}
    """
    total_count = get_df(total_count_query, mssql).iloc[0]['total']

    return min(total_count, SAMPLE_SIZE)


def get_source_data(mssql: MSSQLHandler, sample_size: int, is_full_dump: bool = True) -> pd.DataFrame:
    if is_full_dump:
        filter_condition = f"""
            AND {cutoff_date_condition_mssql('add_datetime', VALIDATION_CUTOFF_DATE)}
            AND {cutoff_date_condition_mssql('update_datetime', VALIDATION_CUTOFF_DATE)}
        """
    else:
        filter_condition = f"""
            AND {cutoff_date_condition_mssql('update_datetime', VALIDATION_CUTOFF_DATE)} -- suppose be the record that is no updated after the validation cutoff date
            AND ({incremental_date_condition('add_datetime', VALIDATION_CUTOFF_DATE)}
            OR {incremental_date_condition('update_datetime', VALIDATION_CUTOFF_DATE)})
        """
    
    source_query = f"""
        SELECT TOP {sample_size}
            TRIM(member_id) as member_id,
            {cast_nvarchar("remark")},
            emboss_id,
            shopping_card,
            TRIM(staff_source) as staff_source,
            add_datetime,
            update_datetime, 
            subprogram_id
        FROM
            df_member
        WHERE
            del_flag = ' '
            {filter_condition}
        ORDER BY NEWID(); -- Random order
    """
    source_df = get_df(source_query, mssql)
    source_df = source_df.map(lambda x: x.str.strip() if pd.api.types.is_string_dtype(x) else x)
    source_df = source_df.map(lambda x: x.strip() if isinstance(x, str) else x)
    source_df = source_df.replace({r"\x00": ""}, regex=True)
    source_df["member_id"] = source_df["member_id"].str.strip()
    source_df = source_df.set_index("member_id", drop=False)

    return source_df


def get_dest_data(postgres: PostgresHandler, member_ids: list[str]) -> pd.DataFrame:
    dest_query = f"""
        SELECT
            m."gwlNo" AS "memberId",
            pgp_sym_decrypt(m."embossNo"::bytea, '{ENCRYPT_KEY}') AS "embossNo",
            m."referralCode",
            m."registeredAt",
            m."registrationChannelCode",
            m."registrationLocationCode",
            m."remark",
            m."shoppingCardId",
            m."tierId",
            m."upgradeGroupCode",
            m."upgradeReasonCode",
            m."tierStartedAt",
            m."tierEndedAt",
            m."deletedAt"
        FROM
            loyalty_service."{TABLE}" m
        WHERE
            m."deletedAt" IS NULL AND
            m."gwlNo" IN ({', '.join(f"'{member_id}'" for member_id in member_ids)})
    """
    dest_df = get_df(dest_query, postgres)
    dest_df = dest_df.set_index('memberId', drop=False)
    dest_df["registeredAt"] = dest_df["registeredAt"].astype(str)
    dest_df["tierStartedAt"] = dest_df["tierStartedAt"].dt.strftime('%H:%M:%S').astype(str)
    dest_df["tierEndedAt"] = dest_df["tierEndedAt"].dt.strftime('%H:%M:%S').astype(str)

    return dest_df


def get_source_count(mssql: MSSQLHandler):
    query = f"""
        SELECT
            TRIM(member_id) as member_id
        FROM
            df_member m
        WHERE del_flag = ' '
        AND {cutoff_date_condition_mssql('add_datetime', VALIDATION_CUTOFF_DATE)}
    """
    df = get_df(query, mssql)
    count = len(df)
    source_member_ids = set(df['member_id'].to_list())

    return count, source_member_ids


def get_dest_count(postgres: PostgresHandler, mssql: MSSQLHandler):
    query = f"""
        SELECT
            "id"
        FROM
            loyalty_service."{TABLE}"
        WHERE
            "deletedAt" IS NULL
            AND {cutoff_date_condition_postgres('"registeredAt"', VALIDATION_CUTOFF_DATE)}
    """
    df = get_df(query, postgres)

    # Remove inactive members from destination table 
    if VALIDATION_CUTOFF_DATE:
        date_condition = f"dm.update_datetime >= DATEADD(DAY, 1, CAST('{VALIDATION_CUTOFF_DATE}' AS DATETIME))"
    else:
        date_condition = f"CAST(dm.update_datetime AS DATE) >= CAST(GETDATE() AS DATE)"

    source_query = f"""
    SELECT 
        TRIM(dm.member_id) as member_id
    FROM df_member dm 
    WHERE dm.del_flag = 'X'
    AND {date_condition}
    """
    source_df = get_df(source_query, mssql)

    df = df.merge(source_df, left_on='id', right_on='member_id', how='left')

    # debugging log: log member ids whom is already inactive
    inactive_members = sorted(df[df['member_id'].notna()]["id"].to_list())
    if inactive_members:
        logger.info(f"Total inactive members: {len(inactive_members)} records. Inactive members: {', '.join(inactive_members[:10])}... - All of these member will mark `deletedAt` in destination table later (in the next migration)")
    # end debugging log

    # remove already inactive members from destination count
    df = df[df['member_id'].isna()]

    count = len(df)
    dest_member_ids = set(df['id'].to_list())

    return count, dest_member_ids


def check_data_correctness(source_df: pd.DataFrame, dest_df: pd.DataFrame) -> list[dict]:
    # 2.3.1 Transform source data
    source_df["remark"] = source_df["remark"].str.strip().str.lstrip('\n')
    source_df["staff_source"] = source_df["staff_source"].replace("", None)
    source_df["add_datetime"] = source_df["add_datetime"].apply(convert_bangkok_to_utc).dt.strftime('%Y-%m-%d %H:%M:%S').astype(str)

    subprogram_mapping = get_subprogram_mapping(SUBPROGRAM_PATH)
    source_df["shopping_card"] = source_df["shopping_card"].replace("", None)

    source_df["tierStartedAt"] = "17:00:00"
    source_df["tierEndedAt"] = "16:59:59"

    # Map subprogram codes
    ug_mapping, ur_mapping, rc_mapping, rl_mapping = subprogram_mapping
    source_df["upgradeGroupCode"] = source_df["subprogram_id"].map(ug_mapping).replace({np.nan: None})
    source_df["upgradeReasonCode"] = source_df["subprogram_id"].map(ur_mapping).replace({np.nan: None})
    source_df["registrationChannelCode"] = source_df["subprogram_id"].map(rc_mapping)
    source_df["registrationLocationCode"] = source_df["subprogram_id"].map(rl_mapping).replace(np.nan, "NA")

    # 2.3.2 Compare fields
    transform_issues = []
    field_mappings = {
        'emboss_id': 'embossNo',
        'member_id': 'memberId',
        'staff_source': 'referralCode',
        'add_datetime': 'registeredAt',
        'upgradeGroupCode': 'upgradeGroupCode',
        'upgradeReasonCode': 'upgradeReasonCode',
        'remark': 'remark',
        'shopping_card': 'shoppingCardId',
        'registrationChannelCode': 'registrationChannelCode',
        'registrationLocationCode': 'registrationLocationCode',
        'tierStartedAt': 'tierStartedAt',
        'tierEndedAt': 'tierEndedAt',
    }

    source_member_ids = set(source_df.index)
    dest_member_ids = set(dest_df.index)

    common_member_ids = source_member_ids.intersection(dest_member_ids)
    for member_id in common_member_ids:
        source_row = source_df.loc[member_id]
        dest_row = dest_df.loc[member_id]

        for source_field, dest_field in field_mappings.items():
            source_value = source_row[source_field]
            dest_value = dest_row[dest_field]
            
            if pd.isna(source_value):
                source_value = None
            if pd.isna(dest_value):
                dest_value = None
                
            if source_value != dest_value:
                transform_issues.append({
                    'member_id': member_id,
                    'source_field': source_field,
                    'source_value': source_value,
                    'destination_field': dest_field,
                    'destination_value': dest_value
                })

    return transform_issues


def check_phone_format(phone: str) -> bool:
    """
    Check if the phone number is valid.
    """
    return phone.isdigit()


def validate_phone_format(postgres: PostgresHandler) -> tuple[bool, str]:
    """
    Validate phone format of member.
    """
    query = f"""
        SELECT
            "gwlNo",
            pgp_sym_decrypt("phone"::bytea, '{ENCRYPT_KEY}') AS "phone"
        FROM loyalty_service."{TABLE}"
        WHERE "deletedAt" IS NULL
        AND {cutoff_date_condition_postgres('"registeredAt"', VALIDATION_CUTOFF_DATE)}
        AND "phone" IS NOT NULL
    """
    df = get_df(query, postgres)
    df["is_valid_phone"] = df["phone"].apply(check_phone_format)

    check_count = len(df)
    logger.info(f"Start validating phone format of {len(df)} records")

    invalid_df = df[~df["is_valid_phone"]]
    invalid_phones = invalid_df["phone"].to_list()
    member_ids = invalid_df["gwlNo"].to_list()

    if invalid_phones:
        message = f"Found {len(invalid_phones)} invalid phone format."
        logger.warning(message)
        logger.info(f"Example invalid phone numbers: {', '.join(invalid_phones[:20])}...")
        logger.info(f"Example of member IDs: {', '.join(member_ids[:20])}...")
        return False, message
    else:
        message = f"All phone numbers are valid. ({check_count} records)"
        logger.info(message)
        return True, message


def validate_thai_phone_format(postgres: PostgresHandler) -> tuple[bool, str]:
    """
    Validate thai phone format of member.
    """
    query = f"""
        SELECT
            m."gwlNo",
            pgp_sym_decrypt(m."phone"::bytea, '{ENCRYPT_KEY}') AS "phone"
        FROM loyalty_service."{TABLE}" m
        JOIN loyalty_service."MemberProfile" mp ON m."gwlNo" = mp."memberId"
        WHERE "deletedAt" IS NULL
        AND m."phone" IS NOT NULL AND mp."nationalityCode" = 'THA'
    """
    df = get_df(query, postgres)
    df["is_valid_thai_phone"] = df["phone"].apply(valid_thai_phone)

    check_count = len(df)
    logger.info(f"Start validating thai phone format of {len(df)} records")

    invalid_df = df[~df["is_valid_thai_phone"]]
    invalid_thai_phones = invalid_df["phone"].to_list()
    member_ids = invalid_df["gwlNo"].to_list()
    
    if invalid_thai_phones:
        message = f"Found {len(invalid_thai_phones)} invalid thai phone format. "
        logger.warning(message)
        logger.info(f"Example of invalid thai phone numbers: {', '.join(invalid_thai_phones[:20])}...")
        logger.info(f"Example of member IDs: {', '.join(member_ids[:20])}...")
        return False, message
    else:
        message = f"All thai phone numbers are valid. ({check_count} records)"
        logger.info(message)
        return True, message


def get_domain(email: str) -> str | None:
    if email and "@" in email:
        return email.split("@")[-1]
    
    return None


def is_valid_email_domain(domain: str, typo_domains: set[str]) -> bool:
    """
    Check if the email domain is valid.
    """
    if domain in typo_domains:
        return False
    
    return True


def validate_email_cleaning(postgres: PostgresHandler):
    """
    Validate all invalid email domain is cleaned according to email_cleansing.csv.
    """
    query = f"""
        SELECT
            "gwlNo",
            pgp_sym_decrypt("email"::bytea, '{ENCRYPT_KEY}') AS "email"
        FROM loyalty_service."{TABLE}"
        WHERE "deletedAt" IS NULL AND "email" IS NOT NULL
    """
    df = get_df(query, postgres)

    check_count = len(df)
    logger.info(f"Start validating email cleaning of {len(df)} records")

    # Read email_cleansing.csv
    email_df = email_cleansing_df(EMAIL_MAPPING_PATH)[["Suggested Domain", "Typo Domain"]]
    typo_domains = set(email_df["Typo Domain"].to_list())

    df["email"] = df["email"].str.lower()
    df["domain"] = df["email"].apply(get_domain)

    # get only the email with domain
    df = df[df["domain"].notna()]
    df["is_valid_domain"] = df["domain"].apply(lambda domain: is_valid_email_domain(domain, typo_domains))

    invalid_df = df[~df["is_valid_domain"]]
    invalid_domains = invalid_df["email"].to_list()
    member_ids = invalid_df["gwlNo"].to_list()

    if invalid_domains:
        message = f"Found {len(invalid_domains)} invalid email domain."
        logger.warning(message)
        logger.info(f"Example of invalid email domains: {', '.join(invalid_domains[:20])}...")
        logger.info(f"Example of member IDs: {', '.join(member_ids[:20])}...")
        return False, message
    else:
        message = f"All email domains are valid according to email_cleansing.csv. ({check_count} records)"
        logger.info(message)
        return True, message


def validate_member_migration(is_full_dump: bool = True):
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    postgres = PostgresHandler(conn_id=TEMP_CONN_ID)

    # 0. Get total count
    sample_size = get_sample_size(mssql, is_full_dump)
    logger.info(f"Sample size for data correctness validation: {sample_size} records")

    # 1. Get data
    # 1.1 Get source data
    source_df = get_source_data(mssql, sample_size, is_full_dump)

    # 1.2 Get destination data
    sample_member_ids = source_df.index.to_list()
    dest_df = get_dest_data(postgres, sample_member_ids)

    # 2. Initialize validation results
    total_rules = 5  # Count check, missing records checks, transformation check, phone format check, and email cleansing check
    failed_rules = 0
    validation_messages = []

    # 2.1 Rule 1: Check record counts
    source_count, source_member_ids = get_source_count(mssql)
    dest_count, dest_member_ids = get_dest_count(postgres, mssql)
    count_match = source_count == dest_count

    if not count_match:
        failed_rules += 1
        validation_messages.append(f"Record count mismatch: Source={source_count}, Destination={dest_count}")

    # 2.2 Rule 2: Check for missing records
    missing_in_dest = source_member_ids - dest_member_ids
    
    if missing_in_dest:
        failed_rules += 1
        missing_dest_ids = sorted(list(missing_in_dest))
        validation_messages.append(f"Records missing in destination: {missing_dest_ids}")

    # 2.3 Rule 3: Check data correctness
    transform_issues = check_data_correctness(source_df, dest_df)
    transform_valid = len(transform_issues) == 0
    if not transform_valid:
        failed_rules += 1
        validation_messages.append(f"Found {len(transform_issues)} transformation issues")

    # 2.4 Rule 4: Phone Format checking 
    is_valid_phone_format, phone_format_message = validate_phone_format(postgres)
    is_valid_thai_phone_format, thai_phone_format_message = validate_thai_phone_format(postgres)

    if not (is_valid_phone_format and is_valid_thai_phone_format):
        failed_rules += 1
        validation_messages.append("Phone format validation failed")

    # 2.5 Rule 5: Email Cleaning checking
    is_valid_email_cleaning, email_cleaning_message = validate_email_cleaning(postgres)
    if not is_valid_email_cleaning:
        failed_rules += 1
        validation_messages.append(email_cleaning_message)

    # 3. Create final summary
    is_valid = failed_rules == 0
    summary_message = "Validation successful" if is_valid else "; ".join(validation_messages)

    # Insert results into validation tables
    with postgres.hook.get_conn() as conn:
        with conn.cursor() as cur:
            # Insert validation summary
            cur.execute(
                """
                INSERT INTO public.validation_summary 
                    (service, table_name, is_valid, total_rules, failed_rules, summary_message)
                VALUES
                    ('LoyaltyService', %s, %s, %s, %s, %s)
                RETURNING run_id;
                """,
                (TABLE, is_valid, total_rules, failed_rules, summary_message)
            )
            run_id = cur.fetchone()[0]

            # Insert count validation result
            cur.execute(
                """
                INSERT INTO public.validation_results 
                (run_id, service, source_table, destination_table, rule_name, is_valid, message)
                VALUES (%s, 'LoyaltyService', 'df_member', %s, 'record_count', %s, %s)
                RETURNING id;
                """,
                (run_id, TABLE, count_match, f"Source: {source_count}, Destination: {dest_count}")
            )

            # Insert missing records validation results
            missing_ids = sorted(list(missing_in_dest))
            cur.execute(
                """
                INSERT INTO public.validation_results 
                (run_id, service, source_table, destination_table, rule_name, is_valid, message)
                VALUES (%s, 'LoyaltyService', 'df_member', %s, 'missing_in_destination', %s, %s)
                RETURNING id;
                """,
                (
                    run_id, 
                    TABLE, 
                    len(missing_ids) == 0,
                    "No missing records in destination" if len(missing_ids) == 0 else f"Records found in source but missing in destination ({len(missing_ids)} records): {missing_ids}"
                )
            )
            if missing_ids:
                logger.warning(f"Records missing in destination: {missing_ids}")
            else:
                logger.info("No records missing in destination")

            # Insert data correctness validation result
            cur.execute(
                """
                INSERT INTO public.validation_results 
                (run_id, service, source_table, destination_table, rule_name, is_valid, message)
                VALUES (%s, 'LoyaltyService', 'df_member', %s, 'data_correctness', %s, %s)
                RETURNING id;
                """,
                (run_id, TABLE, transform_valid, f"Found {len(transform_issues)} transformation issues")
            )
            transform_result_id = cur.fetchone()[0]

            # Insert phone format validation result
            cur.execute(
                """
                INSERT INTO public.validation_results 
                (run_id, service, source_table, destination_table, rule_name, is_valid, message)
                VALUES (%s, 'LoyaltyService', 'df_member', %s, 'phone_format', %s, %s)
                RETURNING id;
                """,
                (run_id, TABLE, is_valid_phone_format and is_valid_thai_phone_format, f"{phone_format_message}; {thai_phone_format_message};")
            )

            # Insert email cleansing validation result
            cur.execute(
                """
                INSERT INTO public.validation_results 
                (run_id, service, source_table, destination_table, rule_name, is_valid, message)
                VALUES (%s, 'LoyaltyService', 'df_member', %s, 'email_cleansing', %s, %s)
                RETURNING id;
                """,
                (run_id, TABLE, is_valid_email_cleaning, email_cleaning_message)
            )

            # Insert transformation issues into record_differences
            if transform_issues:
                for issue in transform_issues:
                    cur.execute(
                        """
                        INSERT INTO public.record_differences 
                        (validation_result_id, source_record_id, source_field, source_value, destination_field, destination_value, expected_value)
                        VALUES (%s, %s, %s, %s, %s, %s, %s);
                        """,
                        (
                            transform_result_id,
                            issue['member_id'],
                            issue['source_field'],
                            str(issue['source_value']),
                            issue['destination_field'],
                            str(issue['destination_value']),
                            str(issue['source_value'])
                        )
                    )

        conn.commit()

    logger.info(f"Validation completed - Run ID: {run_id}")
    logger.info(f"Total Rules: {total_rules}, Failed Rules: {failed_rules}")
    logger.info(f"Summary: {summary_message}")

    return {
        'run_id': run_id,
        'is_valid': is_valid,
        'failed_rules': failed_rules,
        'summary_message': summary_message
    }
