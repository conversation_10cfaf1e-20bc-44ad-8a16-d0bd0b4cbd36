import pandas as pd
from typing import Dict, <PERSON><PERSON>, Any
from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresHand<PERSON>
from common_helpers.utils import (
    get_logger,
    get_df,
    cutoff_date_condition_mssql,
    incremental_date_condition,
)
from constants import (
    CUTOFF_DATE_ACCUMULATE_SPENDING,
    NEWMEMBER_CONN_ID,
    TEMP_CONN_ID,
    VALIDATION_CUTOFF_DATE,
    VALIDATION_SAMPLE_SIZE as SAMPLE_SIZE
)

TABLE = "MemberLegacyTierHistory"

def get_sample_size(mssql: MSSQLHandler, is_full_dump: bool = True):
    if is_full_dump:
        filter_condition = f"""
            AND {cutoff_date_condition_mssql('dc.add_datetime', VALIDATION_CUTOFF_DATE)}
            AND {cutoff_date_condition_mssql('dc.update_datetime', VALIDATION_CUTOFF_DATE)}
        """
    else:
        filter_condition = f"""
            AND {cutoff_date_condition_mssql('dc.update_datetime', VALIDATION_CUTOFF_DATE)} -- suppose be the record that is no updated after the validation cutoff date
            AND (
                {incremental_date_condition('dc.add_datetime', VALIDATION_CUTOFF_DATE)}
                OR {incremental_date_condition('dc.update_datetime', VALIDATION_CUTOFF_DATE)}
            )
        """
    total_count_query = f"""
        SELECT 
            COUNT(*) as total
        FROM df_cardhist dc
        JOIN df_member dm ON dc.member_id = dm.member_id
        WHERE dc.CardTypeCode = 'LV' AND dm.del_flag = ' '
        AND {cutoff_date_condition_mssql('dm.add_datetime', VALIDATION_CUTOFF_DATE)}
        {filter_condition}
    """
    total_count = get_df(total_count_query, mssql).iloc[0]['total']

    return min(total_count, SAMPLE_SIZE)


def get_source_data(mssql: MSSQLHandler, sample_size: int, is_full_dump: bool = True) -> pd.DataFrame:
    if is_full_dump:
        filter_condition = f"""
            AND {cutoff_date_condition_mssql('dc.add_datetime', VALIDATION_CUTOFF_DATE)}
            AND {cutoff_date_condition_mssql('dc.update_datetime', VALIDATION_CUTOFF_DATE)}
        """
    else:
        filter_condition = f""" 
            AND {cutoff_date_condition_mssql('dc.update_datetime', VALIDATION_CUTOFF_DATE)} -- suppose be the record that is no updated after the validation cutoff date
            AND ({incremental_date_condition('dc.add_datetime', VALIDATION_CUTOFF_DATE)}
            OR {incremental_date_condition('dc.update_datetime', VALIDATION_CUTOFF_DATE)}
            )
        """

    source_query = f"""
    SELECT 
        TOP {sample_size}
        dc.member_id,
        dc.runno,
        dc."CardTypeCode",
        dc.card_type_id,
        dc.emboss_id,
        mct.description,
        mcs.StatusName,
        CAST(mr.reason_desc AS NVARCHAR(255)) as reason_desc
    FROM df_cardhist dc 
    LEFT JOIN df_member dm ON dc.member_id = dm.member_id
    LEFT JOIN mst_card_type mct ON dc.card_type_id = mct.card_type_code
    LEFT JOIN MAST_CardStatus mcs ON dc.card_status = mcs.CardStatus
    LEFT JOIN mst_reason mr ON dc.reason_id = mr.reason_id
    WHERE dc.CardTypeCode = 'LV' 
    AND dm.del_flag = ' '
    AND {cutoff_date_condition_mssql('dm.add_datetime', VALIDATION_CUTOFF_DATE)}
    {filter_condition}
    ORDER BY NEWID(); -- Random order
    """
    source_df = get_df(source_query, mssql)

    # Strip whitespace from string columns
    source_df = source_df.apply(lambda x: x.str.strip() if x.dtype == "object" else x)

    return source_df


def get_dest_data(postgres: PostgresHandler) -> pd.DataFrame:
    dest_query = f"""
    SELECT 
        mlch."memberId",
        mlch.source_runno,
        mlch.source_cardtypecode,
        mlch."cardTypeCode",
        mlch."embossNo",
        mlch.description,
        mlch."cardStatus",
        mlch."cardReason"
    FROM loyalty_service."{TABLE}" mlch
    JOIN loyalty_service."Member" m ON mlch."memberId" = m."gwlNo"
    WHERE m."deletedAt" IS NULL
    """
    dest_df = get_df(dest_query, postgres)

    return dest_df


def check_data_correctness(source_df: pd.DataFrame, dest_df: pd.DataFrame) -> Tuple[pd.DataFrame, bool, str]:
    """
    Check data correctness for fields using sampling.

    Args:
        source_df: Source dataframe containing the data to validate
        dest_df: Destination dataframe to validate against
        sample_size: Number of records to sample for validation

    Returns:
        Tuple containing:
        - DataFrame of mismatched records
        - Boolean indicating if mismatches were found
        - Message describing the validation result
    """
    # Create a merged dataframe for comparing values
    merged_df = source_df.merge(
        dest_df,
        left_on=["member_id", "runno", "CardTypeCode"],
        right_on=["memberId", "source_runno", "source_cardtypecode"],
        how="inner",
    )

    # Define field mappings for validation
    field_mappings = [
        {
            "source_field": "member_id",
            "dest_field": "memberId",
            "expected_value_field": "member_id",
        },
        {
            "source_field": "card_type_id",
            "dest_field": "cardTypeCode",
            "expected_value_field": "card_type_id",
        },
        {
            "source_field": "emboss_id",
            "dest_field": "embossNo",
            "expected_value_field": "emboss_id",
        },
        {
            "source_field": "description",
            "dest_field": "description",
            "expected_value_field": "description_x",  # Using _x suffix for source column
            "dest_value_field": "description_y",  # Using _y suffix for destination column
        },
        {
            "source_field": "StatusName",
            "dest_field": "cardStatus",
            "expected_value_field": "StatusName",
        },
        {
            "source_field": "reason_desc",
            "dest_field": "cardReason",
            "expected_value_field": "reason_desc",
        },
    ]

    all_mismatches = []
    mismatch_messages = []
    total_mismatches = 0

    # Check each field pair
    for mapping in field_mappings:
        source_field = mapping["source_field"]
        dest_field = mapping["dest_field"]
        expected_field = mapping["expected_value_field"]
        # Get the actual field name to compare against (handle _x/_y suffixes)
        dest_value_field = mapping.get("dest_value_field", dest_field)

        # Compare destination value with expected value, properly handling NULL values
        field_mismatches = merged_df[
            ~(
                # Both are NULL
                ((merged_df[expected_field].isna()) & (merged_df[dest_value_field].isna())) |
                # Neither is NULL and they are equal
                ((~merged_df[expected_field].isna()) & (~merged_df[dest_value_field].isna()) & 
                (merged_df[expected_field] == merged_df[dest_value_field]))
            )
        ]

        if not field_mismatches.empty:
            mismatch_count = len(field_mismatches)
            total_mismatches += mismatch_count
            mismatch_messages.append(
                f"{mismatch_count} {source_field}->{dest_field} mismatches"
            )

            # Add field information to mismatches
            for _, row in field_mismatches.iterrows():
                mismatch_info = {
                    "source_record_id": f"{row['member_id']}_{row['runno']}_{row['CardTypeCode']}",
                    "source_field": source_field,
                    "source_value": str(row[source_field]) if row[source_field] is not None else None,
                    "destination_field": dest_field,
                    "destination_value": str(row[dest_field]) if row[dest_field] is not None else None,
                    "expected_value": str(row[expected_field]) if row[expected_field] is not None else None,
                }
                all_mismatches.append(mismatch_info)

    has_mismatches = len(all_mismatches) > 0

    if has_mismatches:
        message = f"Found {total_mismatches} total mismatches in sample of {len(merged_df)} records: {'; '.join(mismatch_messages)}"
    else:
        message = f"All values match in sample of {len(merged_df)} records"

    # Convert mismatches to DataFrame
    mismatches_df = pd.DataFrame(all_mismatches) if all_mismatches else pd.DataFrame()

    return mismatches_df, has_mismatches, message


def insert_record_differences(
    cur: Any, validation_result_id: int, mismatches_df: pd.DataFrame
) -> None:
    """Insert record differences for mismatched records."""
    if not mismatches_df.empty:
        for _, row in mismatches_df.iterrows():
            cur.execute(
                """
                INSERT INTO public.record_differences
                (validation_result_id, source_record_id, source_field, source_value, 
                destination_field, destination_value, expected_value)
                VALUES (%s, %s, %s, %s, %s, %s, %s);
                """,
                (
                    validation_result_id,
                    row["source_record_id"],
                    row["source_field"],
                    row["source_value"],
                    row["destination_field"],
                    row["destination_value"],
                    row["expected_value"],
                ),
            )


def get_source_count(mssql: MSSQLHandler):
    """Get the count of records in the source table."""
    query = f"""
    SELECT 
        TRIM(dc.member_id) as member_id,
        dc.runno,
        dc."CardTypeCode"
    FROM df_cardhist dc
    LEFT JOIN df_member dm ON dc.member_id = dm.member_id
    WHERE dc.CardTypeCode = 'LV' 
    AND dm.del_flag = ' ' 
    AND {cutoff_date_condition_mssql('dm.add_datetime', VALIDATION_CUTOFF_DATE)}
    AND {cutoff_date_condition_mssql('dc.add_datetime', VALIDATION_CUTOFF_DATE)}
    """
    df = get_df(query, mssql)

    count = len(df)
    source_keys = set(zip(df['member_id'], df['runno'], df['CardTypeCode']))

    return count, source_keys


def get_dest_count(postgres: PostgresHandler, mssql: MSSQLHandler):
    logger = get_logger()

    """Get the count of records in the destination table."""
    query = f"""
    SELECT 
        mlch."memberId",
        mlch.source_runno,
        mlch.source_cardtypecode
    FROM loyalty_service."{TABLE}" mlch
    JOIN loyalty_service."Member" m ON mlch."memberId" = m."gwlNo"
    WHERE m."deletedAt" IS NULL
    """
    df = get_df(query, postgres) 

    # Remove inactive members from destination table 
    if VALIDATION_CUTOFF_DATE:
        date_condition = f"dm.update_datetime >= DATEADD(DAY, 1, CAST('{VALIDATION_CUTOFF_DATE}' AS DATETIME))"
    else:
        date_condition = f"CAST(dm.update_datetime AS DATE) >= CAST(GETDATE() AS DATE)"


    source_query = f"""
    SELECT 
        TRIM(dm.member_id) as member_id
    FROM df_member dm 
    WHERE dm.del_flag = 'X'
    AND {date_condition}
    """
    source_df = get_df(source_query, mssql)

    df = df.merge(source_df, left_on='memberId', right_on='member_id', how='left')

    # debugging log: log member ids whom is already inactive
    inactive_members = sorted(df[df['member_id'].notna()]["memberId"].to_list())
    if inactive_members:
        logger.info(f"Total inactive members: {len(inactive_members)} records. Inactive members: {', '.join(inactive_members[:10])}... - All of these member will mark `deletedAt` in destination table later (in the next migration)")
    # end debugging log
    
    # remove already inactive members from destination count
    df = df[df['member_id'].isna()]

    dest_count = len(df)
    dest_keys = set(zip(df['memberId'], df['source_runno'], df['source_cardtypecode']))

    return dest_count, dest_keys


def validate_mltierhistory_migration(is_full_dump: bool = True) -> Dict:
    """
    Compare data between source and destination databases to ensure data integrity.
    Validates:
    1. Total record count match
    2. Missing records in destination
    3. Data correctness for key fields
    Note: Only validates data up to yesterday (cutoff at dc.add_datetime)
    """
    logger = get_logger()
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    postgres = PostgresHandler(conn_id=TEMP_CONN_ID)

    # Get sample size for data correctness validation
    sample_size = get_sample_size(mssql, is_full_dump)
    logger.info(f"Sample size for data correctness validation: {sample_size} records")

    # Get source data and destination data
    source_df = get_source_data(mssql, sample_size, is_full_dump)
    dest_df = get_dest_data(postgres)

    # Initialize validation results
    total_rules = 3  # Count check, missing records checks, and data correctness
    failed_rules = 0
    validation_messages = []

    # Create validation summary entry
    summary_query = """
        INSERT INTO public.validation_summary 
        (service, table_name, is_valid, total_rules, failed_rules, summary_message)
        VALUES ('LoyaltyService', %s, %s, %s, %s, %s)
        RETURNING run_id;
    """

    # Rule 1: Check record counts
    source_count, source_keys = get_source_count(mssql)
    dest_count, dest_keys = get_dest_count(postgres, mssql)
    count_match = source_count == dest_count

    if not count_match:
        failed_rules += 1
        validation_messages.append(f"Record count mismatch: Source={source_count}, Destination={dest_count}")
        logger.warning(f"Record count mismatch: Source={source_count}, Destination={dest_count}")

    # Rule 2: Check for missing records in destination    
    missing_in_dest = source_keys - dest_keys
    missing_in_source = dest_keys - source_keys

    # debugging log: missing records in source
    if missing_in_source:
        logger.info("- - - - - - For debugging: missing records in source - - - - - -")
        missing_source_records = sorted(list(missing_in_source))
        example_records = missing_source_records[:10]
        total_missing = len(missing_source_records)
        logger.info(f"Total records missing in source: {total_missing} records. First 10 examples: {example_records}")
        logger.info("- - - - - - End of debugging: missing records in source - - - - - -")
    # end debugging log

    if missing_in_dest:
        failed_rules += 1
        missing_dest_records = sorted(list(missing_in_dest))
        example_records = missing_dest_records[:10]
        total_missing = len(missing_dest_records)
        
        example_message = f"Records missing in destination ({total_missing} records). First 10 examples: {example_records}"
        validation_messages.append(example_message)
        
        # Log first 10 missing records
        logger.warning(f"Total records missing in destination: {total_missing}")
        logger.warning(f"First 10 missing records: {example_records}")
    else:
        logger.info("No missing records in destination")

    # for debugging: when missing in source
    missing_in_source = dest_keys - source_keys
    if missing_in_source:
        missing_source_records = sorted(list(missing_in_source))
        example_records = missing_source_records[:10]
        total_missing = len(missing_source_records)

        # Log first 10 missing records
        logger.debug(f"Total records missing in source: {total_missing}")
        logger.debug(f"First 10 missing records: {example_records}")
        
    # Rule 3: Check data correctness for all fields
    mismatches_df, has_mismatches, mismatch_message = check_data_correctness(
        source_df, dest_df
    )
    if has_mismatches:
        failed_rules += 1
        validation_messages.append(mismatch_message)
        logger.warning(mismatch_message)
    else:
        logger.info(mismatch_message)

    # Create final summary
    is_valid = failed_rules == 0
    summary_message = "Validation successful" if is_valid else "; ".join(validation_messages)

    # Insert results into validation tables
    with postgres.hook.get_conn() as conn:
        with conn.cursor() as cur:
            # Insert validation summary
            cur.execute(summary_query, (TABLE, is_valid, total_rules, failed_rules, summary_message))
            run_id = cur.fetchone()[0]

            # Insert count validation result
            cur.execute(
                """
                INSERT INTO public.validation_results 
                (run_id, service, source_table, destination_table, rule_name, is_valid, message)
                VALUES (%s, 'LoyaltyService', 'df_cardhist', %s, 'record_count', %s, %s)
                RETURNING id;
                """,
                (run_id, TABLE, count_match, f"Source={source_count}, Destination={dest_count}")
            )

            # Insert missing records validation results
            if missing_in_dest:
                example_records = missing_dest_records[:10]
                message = f"Records found in source but missing in destination ({total_missing} records). First 10 examples: {example_records}"
                
                cur.execute(
                    """
                    INSERT INTO public.validation_results 
                    (run_id, service, source_table, destination_table, rule_name, is_valid, message)
                    VALUES (%s, 'LoyaltyService', 'df_cardhist', %s, 'missing_in_destination', %s, %s)
                    RETURNING id;
                    """,
                    (
                        run_id, 
                        TABLE, 
                        False,
                        message
                    )
                )
            else:
                # Insert success result when no records are missing
                cur.execute(
                    """
                    INSERT INTO public.validation_results 
                    (run_id, service, source_table, destination_table, rule_name, is_valid, message)
                    VALUES (%s, 'LoyaltyService', 'df_cardhist', %s, 'missing_in_destination', %s, %s)
                    RETURNING id;
                    """,
                    (
                        run_id, 
                        TABLE, 
                        True,
                        "No missing records in destination"
                    )
                )

            # Insert data correctness validation result and differences
            cur.execute(
                """
                INSERT INTO public.validation_results 
                (run_id, service, source_table, destination_table, rule_name, is_valid, message)
                VALUES (%s, 'LoyaltyService', 'df_cardhist', %s, 'data_correctness', %s, %s)
                RETURNING id;
                """,
                (run_id, TABLE, not has_mismatches, mismatch_message)
            )
            result_id = cur.fetchone()[0]  # Get the returned ID from the INSERT

            # Store mismatches in record_differences table if any field has mismatches
            if has_mismatches:
                insert_record_differences(cur, result_id, mismatches_df)

        conn.commit()

    logger.info(f"Validation completed - Run ID: {run_id}")
    logger.info(f"Total Rules: {total_rules}, Failed Rules: {failed_rules}")
    logger.info(f"Summary: {summary_message}")

    return {
        'run_id': run_id,
        'is_valid': is_valid,
        'failed_rules': failed_rules,
        'summary_message': summary_message
    }
