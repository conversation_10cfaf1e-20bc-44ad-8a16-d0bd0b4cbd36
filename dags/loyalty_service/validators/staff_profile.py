from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.utils import get_logger, get_df, cutoff_date_condition_mssql
from constants import NEWMEMBER_CONN_ID, TEMP_CONN_ID, VALIDATION_CUTOFF_DATE

logger = get_logger()

TABLE = "StaffProfile"

def get_source_count(mssql: MSSQLHandler):
    """Get the count of records in the source table."""
    query = f"""
    SELECT 
        TRIM(m.member_id) as member_id
    FROM df_member m
    WHERE m.subprogram_id in ('3333', '3377', '3388', '8866', '8877', '8888') 
    AND m.staff_flag = 1 
    AND m.del_flag = ' '
    AND {cutoff_date_condition_mssql('m.add_datetime', VALIDATION_CUTOFF_DATE)}
    AND {cutoff_date_condition_mssql('m.update_datetime', VALIDATION_CUTOFF_DATE)}
    """
    df = get_df(query, mssql)

    return len(df), set(df['member_id'].tolist())

def get_dest_count(postgres: PostgresHandler, mssql: MSSQLHandler):
    """Get the count of records in the destination table."""
    query = f"""
    SELECT 
        sp."memberId"
    FROM loyalty_service."{TABLE}" sp
    JOIN loyalty_service."Member" m ON sp."memberId" = m."gwlNo"
    WHERE m."deletedAt" IS NULL
    """
    df = get_df(query, postgres)
    member_ids = df['memberId'].tolist()

    # 1st: Filter only the members who are staff in StaffProfile before the cutoff date
    query = f"""
        SELECT 
            TRIM(dm.member_id) as "member_id"
        FROM df_member dm
        where dm.subprogram_id in ('3333', '3377', '3388', '8866', '8877', '8888')
        and dm.staff_flag = 1
        and dm.del_flag = ' '
        and {cutoff_date_condition_mssql('dm.update_datetime', VALIDATION_CUTOFF_DATE)}
    """
    source_df = get_df(query, mssql)

    # log staffs in StaffProfile before the cutoff date
    staffs = source_df["member_id"].to_list()
    logger.info(f"Staffs in StaffProfile before the cutoff date ({len(staffs)} records): {', '.join(staffs[:10])}...")

    # Filter the members who are staff in StaffProfile before the cutoff date
    df = df[df["memberId"].isin(staffs)]

    # Debugging: log no longer staffs in StaffProfile
    merged_df = df.merge(source_df, left_on='memberId', right_on='member_id', how='left')
    non_staffs = merged_df[merged_df['member_id'].isna()]["memberId"].to_list()
    if non_staffs:
        logger.info("- - - - - - - - For debugging - - - - - - - -")
        logger.info(f"Non staffs in StaffProfile ({len(non_staffs)} records): {', '.join(non_staffs[:10])}...")
        logger.info("- - - - - - - - End of debugging - - - - - - - -")

    # 2nd: remove count where member is already inactive 
    if VALIDATION_CUTOFF_DATE:
        date_condition = f"dm.update_datetime > DATEADD(DAY, 1, CAST('{VALIDATION_CUTOFF_DATE}' AS DATETIME))"
    else:
        date_condition = f"CAST(dm.update_datetime AS DATE) > CAST(GETDATE() AS DATE)"
    
    source_query = f"""
    SELECT 
        TRIM(dm.member_id) as member_id
    FROM df_member dm 
    WHERE dm.del_flag = 'X'
    AND {date_condition}
    """
    source_df = get_df(source_query, mssql)

    df = df.merge(source_df, left_on='memberId', right_on='member_id', how='left')

    # debugging log: log member ids whom is already inactive
    inactive_members = sorted(df[df['member_id'].notna()]["memberId"].to_list())
    if inactive_members:
        logger.info(f"Total inactive members: {len(inactive_members)} records. Inactive members: {', '.join(inactive_members[:10])}... - All of these members will mark `deletedAt` in Member table later (in the next migration)")
    # end debugging log
    
    # remove already inactive members from destination count
    df = df[df['member_id'].isna()]
    return len(df), set(df['memberId'].tolist())

def validate_staff_profile_migration():
    """
    Compare data between source and destination databases to ensure data integrity.
    Stores validation results in validation_summary, validation_results, and record_differences tables.
    """
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    postgres = PostgresHandler(conn_id=TEMP_CONN_ID)

    # Get source data with staff level and company information
    source_query = f"""
    SELECT 
        m.member_id, 
        m.subprogram_id, 
        m.w_position,
        CASE 
            WHEN m.subprogram_id IN ('3333', '3377', '3388') THEN 'OFFICER'
            WHEN m.subprogram_id IN ('8866', '8877', '8888') THEN 'MANAGEMENT'
        END as staff_level,
        CASE 
            WHEN m.subprogram_id IN ('3333', '8888') THEN ta.ValueString
            WHEN m.subprogram_id = '3377' THEN 'KPHM'
            WHEN m.subprogram_id = '3388' THEN 'KPMN'
            WHEN m.subprogram_id = '8866' THEN 'KPMN'
            WHEN m.subprogram_id = '8877' THEN 'KPHM'
        END as staff_company,
        ta2.ValueString as staff_id
    FROM df_member m
    LEFT JOIN TableAttribute ta ON 
        ta.TableName = 'df_member' 
        AND ta.TableKey = 'member_id'
        AND ta.AttributeName = 'staff_comp'
        AND ta.ValueCode = m.member_id
    LEFT JOIN TableAttribute ta2 ON 
        ta2.TableName = 'df_member' 
        AND ta2.TableKey = 'member_id'
        AND ta2.AttributeName = 'staff_id'
        AND ta2.ValueCode = m.member_id
    WHERE m.subprogram_id in ('3333', '3377', '3388', '8866', '8877', '8888') 
    AND m.staff_flag = 1 
    AND m.del_flag = ' '
    AND {cutoff_date_condition_mssql('m.add_datetime', VALIDATION_CUTOFF_DATE)} 
    AND {cutoff_date_condition_mssql('m.update_datetime', VALIDATION_CUTOFF_DATE)}
    """
    source_df = get_df(source_query, mssql)

    # Trim string data in source DataFrame
    source_df = source_df.apply(lambda x: x.str.strip() if x.dtype == "object" else x)
    
    # Convert V&A to V_AND_A in staff_company column
    source_df['staff_company'] = source_df['staff_company'].replace('V&A', 'V_AND_A')

    # Get destination data
    dest_query = f"""
    SELECT sp.*
    FROM loyalty_service."{TABLE}" sp
    JOIN loyalty_service."Member" m ON sp."memberId" = m."gwlNo"
    WHERE m."deletedAt" IS NULL
    """
    dest_df = get_df(dest_query, postgres)

    # Initialize validation results
    total_rules = 3  # Count check, missing records checks, and transformation check
    failed_rules = 0
    validation_messages = []

    # Create validation summary entry
    summary_query = """
        INSERT INTO public.validation_summary 
        (service, table_name, is_valid, total_rules, failed_rules, summary_message)
        VALUES ('LoyaltyService', %s, %s, %s, %s, %s)
        RETURNING run_id;
    """

    # Rule 1: Check record counts
    source_count, source_member_ids = get_source_count(mssql)
    dest_count, dest_member_ids = get_dest_count(postgres, mssql)
    count_match = source_count == dest_count

    if not count_match:
        failed_rules += 1
        validation_messages.append(f"Record count mismatch: Source={source_count}, Destination={dest_count}")

    # Rule 2: Check for missing records in both directions
    missing_in_dest = source_member_ids - dest_member_ids
    missing_in_source = dest_member_ids - source_member_ids
    logger.info(f"Missing in source: {len(missing_in_source)} records")
    logger.info(f"Missing in dest: {len(missing_in_dest)} records")
    
    if missing_in_dest:
        failed_rules += 1
        missing_dest_ids = sorted(list(missing_in_dest))
        validation_messages.append(f"Records missing in destination: {missing_dest_ids}")

    # for debugging: when missing in source
    if missing_in_source:
        missing_source_ids = sorted(list(missing_in_source))
        logger.info(f"Records missing in source: {missing_source_ids}")


    # Rule 3: Check data correctness
    transform_issues = []
    field_mappings = {
        'w_position': 'staffPosition',
        'staff_level': 'staffLevelCode',
        'staff_company': 'companyCode',
        'staff_id': 'staffNo'
    }

    common_member_ids = set(source_df["member_id"].to_list()).intersection(dest_df["memberId"].to_list())
    for member_id in common_member_ids:
        source_row = source_df[source_df['member_id'] == member_id].iloc[0]
        dest_row = dest_df[dest_df['memberId'] == member_id].iloc[0]
        
        for source_field, dest_field in field_mappings.items():
            if source_row[source_field] != dest_row[dest_field]:
                transform_issues.append({
                    'member_id': member_id,
                    'source_field': source_field,
                    'source_value': source_row[source_field],
                    'destination_field': dest_field,
                    'destination_value': dest_row[dest_field]
                })

    transform_valid = len(transform_issues) == 0
    if not transform_valid:
        failed_rules += 1
        validation_messages.append(f"Found {len(transform_issues)} transformation issues")

    # Create final summary
    is_valid = failed_rules == 0
    summary_message = "Validation successful" if is_valid else "; ".join(validation_messages)

    # Insert results into validation tables
    with postgres.hook.get_conn() as conn:
        with conn.cursor() as cur:
            # Insert validation summary
            cur.execute(summary_query, (TABLE, is_valid, total_rules, failed_rules, summary_message))
            run_id = cur.fetchone()[0]

            # Insert count validation result
            cur.execute(
                """
                INSERT INTO public.validation_results 
                (run_id, service, source_table, destination_table, rule_name, is_valid, message)
                VALUES (%s, 'LoyaltyService', 'df_member', %s, 'record_count', %s, %s)
                RETURNING id;
                """,
                (run_id, TABLE, count_match, f"Source: {source_count}, Destination: {dest_count}")
            )

            # Insert missing records validation results (source -> destination)
            missing_ids = sorted(list(missing_in_dest))
            cur.execute(
                """
                INSERT INTO public.validation_results 
                (run_id, service, source_table, destination_table, rule_name, is_valid, message)
                VALUES (%s, 'LoyaltyService', 'df_member', %s, 'missing_in_destination', %s, %s)
                RETURNING id;
                """,
                (
                    run_id, 
                    TABLE, 
                    len(missing_ids) == 0,
                    "No missing records in destination" if len(missing_ids) == 0 else f"Records found in source but missing in destination ({len(missing_ids)} records): {missing_ids}"
                )
            )
            if missing_ids:
                logger.warning(f"Records missing in destination: {missing_ids}")
            else:
                logger.info("No records missing in destination")

            # Insert data correctness validation result
            cur.execute(
                """
                INSERT INTO public.validation_results 
                (run_id, service, source_table, destination_table, rule_name, is_valid, message)
                VALUES (%s, 'LoyaltyService', 'df_member', %s, 'data_correctness', %s, %s)
                RETURNING id;
                """,
                (run_id, TABLE, transform_valid, f"Found {len(transform_issues)} transformation issues")
            )
            transform_result_id = cur.fetchone()[0]

            # Insert transformation issues into record_differences
            if transform_issues:
                for issue in transform_issues:
                    cur.execute(
                        """
                        INSERT INTO public.record_differences 
                        (validation_result_id, source_record_id, source_field, source_value, destination_field, destination_value, expected_value)
                        VALUES (%s, %s, %s, %s, %s, %s, %s);
                        """,
                        (
                            transform_result_id,
                            issue['member_id'],
                            issue['source_field'],
                            str(issue['source_value']),
                            issue['destination_field'],
                            str(issue['destination_value']),
                            str(issue['source_value'])  # For staff profile, source value is the expected value
                        )
                    )

        conn.commit()

    logger.info(f"Validation completed - Run ID: {run_id}")
    logger.info(f"Total Rules: {total_rules}, Failed Rules: {failed_rules}")
    logger.info(f"Summary: {summary_message}")

    return {
        'run_id': run_id,
        'is_valid': is_valid,
        'failed_rules': failed_rules,
        'summary_message': summary_message
    }
