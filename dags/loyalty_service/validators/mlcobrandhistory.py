from typing import Dict, Any
import pandas as pd
from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.utils import get_logger, get_df, cutoff_date_condition_mssql, incremental_date_condition
from constants import NEWMEMBER_CONN_ID, TEMP_CONN_ID, VALIDATION_CUTOFF_DATE, VALIDATION_SAMPLE_SIZE as SAMPLE_SIZE

TABLE = "MemberLegacyCobrandHistory"

def get_sample_size(mssql: MSSQLHandler, is_full_dump: bool = True):
    if is_full_dump:
        filter_condition = f"""
            AND {cutoff_date_condition_mssql('dc.add_datetime', VALIDATION_CUTOFF_DATE)}
            AND {cutoff_date_condition_mssql('dc.update_datetime', VALIDATION_CUTOFF_DATE)}
        """
    else:
        filter_condition = f"""
            AND {cutoff_date_condition_mssql('dc.update_datetime', VALIDATION_CUTOFF_DATE)} -- suppose be the record that is no updated after the validation cutoff date
            AND (
                {incremental_date_condition('dc.add_datetime', VALIDATION_CUTOFF_DATE)}
                OR {incremental_date_condition('dc.update_datetime', VALIDATION_CUTOFF_DATE)}
            )
        """
    total_count_query = f"""
        SELECT 
            COUNT(*) as total
        FROM df_cardhist dc
        JOIN df_member dm ON dc.member_id = dm.member_id
        WHERE dc.CardTypeCode IN ('SCB', 'KBANK') 
            AND dm.del_flag = ' '
            AND {cutoff_date_condition_mssql('dm.add_datetime', VALIDATION_CUTOFF_DATE)}
            {filter_condition}
    """
    total_count = get_df(total_count_query, mssql).iloc[0]['total']
    return min(total_count, SAMPLE_SIZE)


def get_source_count(mssql: MSSQLHandler):
    query = f"""
    SELECT
        TRIM(dc.member_id) as member_id,
        dc.runno,
        dc."CardTypeCode"
    FROM df_cardhist dc
    LEFT JOIN df_member dm ON dc.member_id = dm.member_id
    WHERE dc.CardTypeCode IN ('SCB', 'KBANK') 
    AND dm.del_flag = ' '
    AND {cutoff_date_condition_mssql('dc.add_datetime', VALIDATION_CUTOFF_DATE)}
    AND {cutoff_date_condition_mssql('dm.add_datetime', VALIDATION_CUTOFF_DATE)}
    """

    df = get_df(query, mssql)

    count = len(df)
    source_keys = set(zip(df['member_id'], df['runno'], df['CardTypeCode']))

    return count, source_keys


def get_dest_count(postgres: PostgresHandler, mssql: MSSQLHandler):
    logger = get_logger()
    query = f"""
    SELECT
        mlch."memberId",
        mlch.source_runno,
        mlch.source_cardtypecode
    FROM loyalty_service."{TABLE}" mlch
    JOIN loyalty_service."Member" m ON mlch."memberId" = m."gwlNo"
    WHERE m."deletedAt" IS NULL
    """

    df = get_df(query, postgres)

    # Remove inactive members from destination table 
    if VALIDATION_CUTOFF_DATE:
        date_condition = f"dm.update_datetime >= DATEADD(DAY, 1, CAST('{VALIDATION_CUTOFF_DATE}' AS DATETIME))"
    else:
        date_condition = f"CAST(dm.update_datetime AS DATE) >= CAST(GETDATE() AS DATE)"

    source_query = f"""
    SELECT 
        TRIM(dm.member_id) as member_id
    FROM df_member dm 
    WHERE dm.del_flag = 'X'
    AND {date_condition}
    """
    source_df = get_df(source_query, mssql)

    df = df.merge(source_df, left_on='memberId', right_on='member_id', how='left')

    # debugging log: log member ids whom is already inactive
    inactive_members = sorted(df[df['member_id'].notna()]["memberId"].to_list())
    if inactive_members:
        logger.info(f"Total inactive members: {len(inactive_members)} records. Inactive members: {', '.join(inactive_members[:10])}... - All of these member will mark `deletedAt` in destination table later (in the next migration)")
    # end debugging log
    
    # remove already inactive members from destination count
    df = df[df['member_id'].isna()]

    count = len(df)
    dest_keys = set(zip(df['memberId'], df['source_runno'], df['source_cardtypecode']))

    return count, dest_keys


def get_source_data(mssql: MSSQLHandler, sample_size: int, is_full_dump: bool = True) -> pd.DataFrame:
    """Get source data with key fields for validation."""
    if is_full_dump:
        filter_condition = f"""
            AND {cutoff_date_condition_mssql('dc.add_datetime', VALIDATION_CUTOFF_DATE)}
            AND {cutoff_date_condition_mssql('dc.update_datetime', VALIDATION_CUTOFF_DATE)}
        """
    else:
        filter_condition = f"""
            AND {cutoff_date_condition_mssql('dc.update_datetime', VALIDATION_CUTOFF_DATE)} -- suppose be the record that is no updated after the validation cutoff date
            AND
            (
                {incremental_date_condition('dc.add_datetime', VALIDATION_CUTOFF_DATE)}
                OR {incremental_date_condition('dc.update_datetime', VALIDATION_CUTOFF_DATE)}
            )
        """
    source_query = f"""
    SELECT 
        TOP {sample_size}
        dc.member_id,
        dc.runno,
        dc."CardTypeCode",
        dc.card_type_id,
        dc.emboss_id,
        dc.reason_id,
        dc.card_status
    FROM df_cardhist dc 
    LEFT JOIN df_member dm ON dc.member_id = dm.member_id
    WHERE dc.CardTypeCode IN ('SCB', 'KBANK') 
    AND dm.del_flag = ' '
    AND {cutoff_date_condition_mssql('dm.add_datetime', VALIDATION_CUTOFF_DATE)}
        {filter_condition}
    ORDER BY NEWID(); -- Random order
    """
    source_df = get_df(source_query, mssql)
    # Strip whitespace from string columns
    source_df = source_df.apply(lambda x: x.str.strip() if x.dtype == "object" else x)

    return source_df


def get_destination_data(postgres: PostgresHandler) -> pd.DataFrame:
    """Get destination data."""
    dest_query = f"""
    SELECT 
        mlch."memberId",
        mlch.source_runno,
        mlch.source_cardtypecode,
        mlch."cardTypeCode",
        mlch."embossNo",
        mlch.description,
        mlch."cardStatus",
        mlch."cardReason"
    FROM loyalty_service."{TABLE}" mlch
    JOIN loyalty_service."Member" m ON mlch."memberId" = m."gwlNo"
    WHERE m."deletedAt" IS NULL
    """
    return get_df(dest_query, postgres)


def check_data_correctness(
    mssql: MSSQLHandler,
    source_df: pd.DataFrame, 
    dest_df: pd.DataFrame,
):
    """
    Check data correctness for fields using sampling.

    Args:
        source_df: Source dataframe containing the data to validate
        dest_df: Destination dataframe to validate against
        sample_size: Number of records to sample for validation

    Returns:
        Tuple containing:
        - DataFrame of mismatched records
        - Boolean indicating if mismatches were found
        - Message describing the validation result
    """
    # 1. Transform fields
    transform_issues = []
    field_mappings = {
        'member_id': 'memberId',
        'card_type_id': 'cardTypeCode',
        'emboss_id': 'embossNo',
        'description': 'description',
        'StatusName': 'cardStatus',
        'reason_desc': 'cardReason'
    }

    # field: description
    query = "SELECT card_type_code, description FROM mst_card_type"
    ctype_df = get_df(query, mssql).map(lambda x: x.strip() if isinstance(x, str) else x)
    source_df = source_df.merge(
        ctype_df, left_on="card_type_id", right_on="card_type_code", how="left"
    )

    # field: reason_desc
    query = f"SELECT reason_id, CAST(reason_desc AS NVARCHAR(max)) AS reason_desc FROM mst_reason"
    reason_df = get_df(query, mssql).map(lambda x: x.strip() if isinstance(x, str) else x)
    source_df = source_df.merge(reason_df, on="reason_id", how="left")

    # field: card_status
    query = "SELECT StatusName, CardStatus FROM MAST_CardStatus"
    status_df = get_df(query, mssql)
    source_df = source_df.merge(status_df, left_on="card_status", right_on="CardStatus", how="left")

    source_df.drop(
        columns=["card_status", "reason_id", "card_type_code", "CardStatus"],
        axis=1,
        inplace=True,
    )

    # 2. Compare source and destination data
    source_keys = set(zip(source_df['member_id'], source_df['runno'], source_df['CardTypeCode']))
    dest_keys = set(zip(dest_df['memberId'], dest_df['source_runno'], dest_df['source_cardtypecode']))
    common_keys = source_keys.intersection(dest_keys)
    for member_id, runno, card_type_code in common_keys:
        # get source and destination row
        source_filter = (
            (source_df['member_id'] == member_id) &
            (source_df['CardTypeCode'] == card_type_code) &
            (source_df['runno'] == runno)
        )
        dest_filter = (
            (dest_df['memberId'] == member_id) & 
            (dest_df['source_runno'] == runno) & 
            (dest_df['source_cardtypecode'] == card_type_code)
        )
        source_row = source_df[source_filter].iloc[0]
        dest_row = dest_df[dest_filter].iloc[0]

        for source_field, dest_field in field_mappings.items():
            source_value = source_row[source_field]
            dest_value = dest_row[dest_field]
            
            if pd.isna(source_value) or source_value == '':
                source_value = None
            if pd.isna(dest_value) or dest_value == '':
                dest_value = None
                
            if source_value != dest_value:
                transform_issues.append({
                    'source_record_id': f"{member_id}_{runno}_{card_type_code}",
                    'source_field': source_field,
                    'source_value': source_value,
                    'destination_field': dest_field,
                    'destination_value': dest_value
                })

    is_valid = len(transform_issues) == 0
    message = f"Found {len(transform_issues)} transformation issues" if not is_valid else f"All sample values match"

    return is_valid, message, transform_issues

def insert_validation_summary(
    cur: Any,
    table: str,
    is_valid: bool,
    total_rules: int,
    failed_rules: int,
    summary_message: str,
) -> int:
    """Insert validation summary and return run_id."""
    cur.execute(
        """
        INSERT INTO public.validation_summary 
        (service, table_name, is_valid, total_rules, failed_rules, summary_message)
        VALUES ('LoyaltyService', %s, %s, %s, %s, %s)
        RETURNING run_id;
        """,
        (table, is_valid, total_rules, failed_rules, summary_message),
    )
    return cur.fetchone()[0]


def insert_validation_result(
    cur: Any, 
    run_id: int, 
    rule_name: str, 
    is_valid: bool, 
    message: str
) -> int:
    """Insert validation result and return result_id."""
    cur.execute(
        """
        INSERT INTO public.validation_results 
        (run_id, service, source_table, destination_table, rule_name, is_valid, message)
        VALUES (%s, 'LoyaltyService', 'df_cardhist', %s, %s, %s, %s)
        RETURNING id;
        """,
        (run_id, TABLE, rule_name, is_valid, message),
    )
    return cur.fetchone()[0]


def insert_record_differences(
    cur: Any, validation_result_id: int, mismatches_df: pd.DataFrame
) -> None:
    """Insert record differences for mismatched records."""
    if not mismatches_df.empty:
        for _, row in mismatches_df.iterrows():
            cur.execute(
                """
                INSERT INTO public.record_differences
                (validation_result_id, source_record_id, source_field, source_value, 
                destination_field, destination_value, expected_value)
                VALUES (%s, %s, %s, %s, %s, %s, %s);
                """,
                (
                    validation_result_id,
                    row["source_record_id"],
                    row["source_field"],
                    row["source_value"],
                    row["destination_field"],
                    row["destination_value"],
                    row["expected_value"],
                ),
            )


def validate_mlcobrandhistory_migration(is_full_dump: bool = True) -> Dict:
    """
    Compare data between source and destination databases to ensure data integrity.
    Validates:
    1. Total record count match
    2. Missing records in destination
    3. Data correctness for all fields (using sample of {SAMPLE_SIZE} records)
    """
    logger = get_logger()
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    postgres = PostgresHandler(conn_id=TEMP_CONN_ID)

    # Get sample size for data correctness validation
    sample_size = get_sample_size(mssql, is_full_dump)
    logger.info(f"Sample size for data correctness validation: {sample_size} records")

    # Initialize validation results
    total_rules = 3
    failed_rules = 0
    validation_messages = []

    # Rule 1: Check record counts
    source_count, source_keys = get_source_count(mssql)
    dest_count, dest_keys = get_dest_count(postgres, mssql)
    count_match = source_count == dest_count
    count_message = f"Source={source_count}, Destination={dest_count}"

    if not count_match:
        failed_rules += 1
        validation_messages.append(f"Record count mismatch: {count_message}")
        logger.warning(f"Record count mismatch: {count_message}")

    # Rule 2: Check for missing records
    missing_in_dest = source_keys - dest_keys
    if missing_in_dest:
        failed_rules += 1
        missing_dest_records = sorted(list(missing_in_dest))
        example_records = missing_dest_records[:10]
        missing_message = f"Records found in source but missing in destination ({len(missing_dest_records)} records). First 10 examples: {example_records}"
        validation_messages.append(missing_message)
    else:
        missing_message = "No missing records in destination"
    logger.warning(missing_message)

    # Rule 3: Check data correctness for all fields
    # Get data from source and destination
    source_df = get_source_data(mssql, sample_size, is_full_dump)
    dest_df = get_destination_data(postgres)
    transform_valid, transform_message, transform_issues = check_data_correctness(
        mssql, source_df, dest_df
    )
    if not transform_valid:
        failed_rules += 1
        validation_messages.append(transform_message)
        logger.warning(transform_message)
    else:
        logger.info(transform_message)

    # Create final summary
    is_valid = failed_rules == 0
    summary_message = (
        "Validation successful" if is_valid else "; ".join(validation_messages)
    )

    # Insert results into validation tables
    with postgres.hook.get_conn() as conn:
        with conn.cursor() as cur:
            # Insert validation summary
            run_id = insert_validation_summary(
                cur, TABLE, is_valid, total_rules, failed_rules, summary_message
            )

            # Insert count validation result
            result_id = insert_validation_result(
                cur, run_id, "record_count", count_match, count_message
            )

            # Insert missing records validation result
            result_id = insert_validation_result(
                cur, run_id, "missing_in_destination", not bool(missing_in_dest), missing_message
            )

            # Insert transformation issues validation result
            result_id = insert_validation_result(
                cur, run_id, "data_correctness", transform_valid, transform_message
            )

            # Insert data correctness validation result and differences
            # Insert transformation issues into record_differences
            if transform_issues:
                for issue in transform_issues:
                    cur.execute(
                        """
                        INSERT INTO public.record_differences 
                        (validation_result_id, source_record_id, source_field, source_value, destination_field, destination_value, expected_value)
                        VALUES (%s, %s, %s, %s, %s, %s, %s);
                        """,
                        (
                            result_id,
                            issue['source_record_id'],
                            issue['source_field'],
                            str(issue['source_value']),
                            issue['destination_field'],
                            str(issue['destination_value']),
                            str(issue['source_value'])
                        )
                    )

        conn.commit()

    logger.info(f"Validation completed - Run ID: {run_id}")
    logger.info(f"Total Rules: {total_rules}, Failed Rules: {failed_rules}")
    logger.info(f"Summary: {summary_message}")

    return {
        "run_id": run_id,
        "is_valid": is_valid,
        "failed_rules": failed_rules,
        "summary_message": summary_message,
    }
