import numpy as np
import pandas as pd

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.utils import (
    cast_nvarchar,
    get_logger,
    clean_id_card,
    get_df,
    cutoff_date_condition_mssql,
    cutoff_date_condition_postgres,
    incremental_date_condition,
)
from common_helpers.utils_memberprofile import (
    business_mapping_dict,
    get_address_df,
    get_address_mapping_dict,
    get_country_mapping,
    get_title_mapping,
    safe_int_convert,
    transform_address,
    transform_ename,
    transform_tname,
)
from constants import ENCRYPT_KEY, NEWMEMBER_CONN_ID, TEMP_CONN_ID, VALIDATION_CUTOFF_DATE, VALIDATION_SAMPLE_SIZE as SAMPLE_SIZE

logger = get_logger()

TABLE = "MemberProfile"


def get_source_count(mssql: MSSQLHandler):
    query = f"""
        SELECT
            TRIM(member_id) as member_id
        FROM
            df_member m
        WHERE del_flag = ' '
        AND {cutoff_date_condition_mssql('m.add_datetime', VALIDATION_CUTOFF_DATE)}
    """
    df = get_df(query, mssql)
    count = len(df)
    source_member_ids = set(df['member_id'].to_list())

    return count, source_member_ids


def get_dest_count(postgres: PostgresHandler, mssql: MSSQLHandler):
    query = f"""
        SELECT
            "memberId"
        FROM
            loyalty_service."{TABLE}" mp
        JOIN loyalty_service."Member" m ON mp."memberId" = m."gwlNo" 
        WHERE m."deletedAt" IS NULL
        AND {cutoff_date_condition_postgres('m."registeredAt"', VALIDATION_CUTOFF_DATE)}
    """
    df = get_df(query, postgres)

    # Remove inactive members from destination table 
    if VALIDATION_CUTOFF_DATE:
        date_condition = f"dm.update_datetime >= DATEADD(DAY, 1, CAST('{VALIDATION_CUTOFF_DATE}' AS DATETIME))"
    else:
        date_condition = f"CAST(dm.update_datetime AS DATE) >= CAST(GETDATE() AS DATE)"

    source_query = f"""
    SELECT 
        TRIM(dm.member_id) as member_id
    FROM df_member dm 
    WHERE dm.del_flag = 'X'
    AND {date_condition}
    """
    source_df = get_df(source_query, mssql)

    df = df.merge(source_df, left_on='memberId', right_on='member_id', how='left')

    # debugging log: log member ids whom is already inactive
    inactive_members = sorted(df[df['member_id'].notna()]["memberId"].to_list())
    if inactive_members:
        logger.info(f"Total inactive members: {len(inactive_members)} records. Inactive members: {', '.join(inactive_members[:10])}... - All of these member will mark `deletedAt` in destination table later (in the next migration)")
    # end debugging log

    # remove already inactive members from destination count
    df = df[df['member_id'].isna()]

    count = len(df)
    dest_member_ids = set(df['memberId'].to_list())

    return count, dest_member_ids


def get_sample_size(mssql: MSSQLHandler, is_full_dump: bool = True):
    total_count_query = f"""
        SELECT COUNT(*) as total FROM df_member m WHERE m.del_flag = ' '
    """
    if is_full_dump:
        total_count_query += f"""
            AND {cutoff_date_condition_mssql('m.add_datetime', VALIDATION_CUTOFF_DATE)}
            AND {cutoff_date_condition_mssql('m.update_datetime', VALIDATION_CUTOFF_DATE)}
        """
    else:
        total_count_query += f"""
            AND {cutoff_date_condition_mssql('update_datetime', VALIDATION_CUTOFF_DATE)} -- suppose be the record that is no updated after the validation cutoff date
            AND ({incremental_date_condition('m.add_datetime', VALIDATION_CUTOFF_DATE)}
            OR {incremental_date_condition('m.update_datetime', VALIDATION_CUTOFF_DATE)})
        """

    total_count = get_df(total_count_query, mssql).iloc[0]['total']

    return min(total_count, SAMPLE_SIZE)


def get_source_data(mssql: MSSQLHandler, sample_size: int, is_full_dump: bool = True) -> pd.DataFrame:
    if is_full_dump:
        filter_condition = f"""
            AND {cutoff_date_condition_mssql('add_datetime', VALIDATION_CUTOFF_DATE)}
            AND {cutoff_date_condition_mssql('update_datetime', VALIDATION_CUTOFF_DATE)}
        """
    else:
        filter_condition = f"""
            AND {cutoff_date_condition_mssql('update_datetime', VALIDATION_CUTOFF_DATE)} -- suppose be the record that is no updated after the validation cutoff date
            AND ({incremental_date_condition('add_datetime', VALIDATION_CUTOFF_DATE)}
            OR {incremental_date_condition('update_datetime', VALIDATION_CUTOFF_DATE)})
        """

    source_query = f"""
        SELECT TOP {sample_size}
            member_id,
            title_id,
            ename,
            {cast_nvarchar("tname")},
            id_card,
            passport_no,
            passport_expiry_date,
            date_of_birth,
            sex,
            bussiness_id,
            country_code,
            haddr_subdistrict,
            haddr_district,
            haddr_city,
            haddr_zip_code,
            caddr_subdistrict,
            caddr_district,
            caddr_city,
            caddr_zip_code,
            maddr_subdistrict,
            maddr_district,
            maddr_city,
            maddr_zip_code,
            staff_source
        FROM
            df_member
        WHERE
            del_flag = ' '
            {filter_condition}
        ORDER BY NEWID(); -- Random order
    """
    source_df = get_df(source_query, mssql)
    source_df = source_df.apply(lambda x: x.str.strip() if pd.api.types.is_string_dtype(x) else x)

    return source_df


def get_dest_data(postgres: PostgresHandler, sample_member_ids: list[str]) -> pd.DataFrame:
    dest_query = f"""
        SELECT
            mp."memberId",
            mp."title",
            pgp_sym_decrypt(mp."firstName"::bytea, '{ENCRYPT_KEY}') AS "firstName",
            pgp_sym_decrypt(mp."firstNameTh"::bytea, '{ENCRYPT_KEY}') AS "firstNameTh",
            pgp_sym_decrypt(mp."middleName"::bytea, '{ENCRYPT_KEY}') AS "middleName",
            pgp_sym_decrypt(mp."middleNameTh"::bytea, '{ENCRYPT_KEY}') AS "middleNameTh",
            pgp_sym_decrypt(mp."lastName"::bytea, '{ENCRYPT_KEY}') AS "lastName",
            pgp_sym_decrypt(mp."lastNameTh"::bytea, '{ENCRYPT_KEY}') AS "lastNameTh",
            pgp_sym_decrypt(mp."cid"::bytea, '{ENCRYPT_KEY}') AS "cid",
            pgp_sym_decrypt(mp."passportNo"::bytea, '{ENCRYPT_KEY}') AS "passportNo",
            mp."passportExpiryDate" ,
            pgp_sym_decrypt(mp."dateOfBirth"::bytea, '{ENCRYPT_KEY}') AS "dateOfBirth",
            pgp_sym_decrypt(mp."gender"::bytea, '{ENCRYPT_KEY}') AS "gender",
            mp."occupation",
            mp."nationalityCode",
            pgp_sym_decrypt(mp."addressLine"::bytea, '{ENCRYPT_KEY}') AS "addressLine",
            mp."subDistrict",
            mp."district",
            mp."province",
            mp."city",
            mp."postalCode"
        FROM
            loyalty_service."{TABLE}" mp
        JOIN loyalty_service."Member" m ON
            mp."memberId" = m."gwlNo"
        WHERE
            m."deletedAt" IS NULL AND
            mp."memberId" IN ({', '.join(f"'{member_id}'" for member_id in sample_member_ids)})
    """
    dest_df = get_df(dest_query, postgres)

    return dest_df


def check_data_correctness(source_df: pd.DataFrame, dest_df: pd.DataFrame) -> list[dict]:
    # 2.3.1 Transform fields
    transform_issues = []
    field_mappings = {
        'id_card': 'cid',
        'passport_no': 'passportNo',
        'passport_expiry_date': 'passportExpiryDate',
        'date_of_birth': 'dateOfBirth',
        'title_id': 'title',
        'bussiness_id': 'occupation',
        'sex': 'gender',
        'nationalityCode': 'nationalityCode',
        'firstNameTh': 'firstNameTh',
        'middleNameTh': 'middleNameTh',
        'lastNameTh': 'lastNameTh',
        'firstName': 'firstName',
        'middleName': 'middleName',
        'lastName': 'lastName',
        "addressLine": "addressLine",
        "subDistrict": "subDistrict",
        "district": "district",
        "province": "province",
        "postalCode": "postalCode"
    }

    title_mapping = get_title_mapping()
    country_mapping = get_country_mapping()
    address_mapping = get_address_mapping_dict()
    business_mapping = business_mapping_dict()

    member_ids = source_df['member_id'].tolist()
    address_df = get_address_df(member_ids)

    source_df["id_card"] = source_df["id_card"].apply(clean_id_card)
    source_df["passport_no"] = source_df["passport_no"].replace("", None)
    source_df["date_of_birth"] = source_df["date_of_birth"].apply(lambda d: d.date().strftime("%Y-%m-%d"))
    source_df["title_id"] = source_df["title_id"].apply(safe_int_convert).map(title_mapping) 
    source_df["bussiness_id"] = source_df["bussiness_id"].astype(int)
    source_df["bussiness_id"] = source_df["bussiness_id"].map(business_mapping).replace(np.nan, None)
    source_df["nationalityCode"] = source_df["country_code"].replace(country_mapping).replace("", "OTH").fillna("OTH")
    source_df[["firstNameTh", "middleNameTh", "lastNameTh"]] = source_df.apply(
        lambda row: pd.Series(transform_tname(row["tname"], row["nationalityCode"])),
        axis=1,
    )
    source_df[["firstName", "middleName", "lastName"]] = source_df.apply(
        lambda row: pd.Series(transform_ename(row["ename"], row["nationalityCode"])),
        axis=1,
    )
    source_df["staff_source"] = source_df["staff_source"].replace("", None)
    source_df[["addressLine", "subDistrict", "district", "province", "postalCode"]] = (
        source_df.apply(
            lambda row: pd.Series(
                transform_address(
                    row["member_id"],
                    row["staff_source"],
                    address_df,
                    address_mapping,
                )
            ),
            axis=1,
        )
    )

    # 2.3.2 Compare fields
    common_member_ids = set(source_df['member_id']).intersection(set(dest_df['memberId']))
    for member_id in common_member_ids:
        source_row = source_df[source_df['member_id'] == member_id].iloc[0]
        dest_row = dest_df[dest_df['memberId'] == member_id].iloc[0]

        for source_field, dest_field in field_mappings.items():
            source_value = source_row[source_field]
            dest_value = dest_row[dest_field]
            
            if pd.isna(source_value):
                source_value = None
            if pd.isna(dest_value):
                dest_value = None
                
            if source_value != dest_value:
                transform_issues.append({
                    'member_id': member_id,
                    'source_field': source_field,
                    'source_value': source_value,
                    'destination_field': dest_field,
                    'destination_value': dest_value
                })

    return transform_issues


def insert_validation_summary(
    conn: any,
    table_name: str,
    is_valid: bool,
    total_rules: int,
    failed_rules: int,
    summary_message: str
) -> int:
    summary_query = """
        INSERT INTO public.validation_summary 
            (service, table_name, is_valid, total_rules, failed_rules, summary_message)
        VALUES
            ('LoyaltyService', %s, %s, %s, %s, %s)
        RETURNING run_id;
    """

    # Insert results into validation tables
    with conn.cursor() as cur:
        # Insert validation summary
        cur.execute(
            summary_query, 
            (table_name, is_valid, total_rules, failed_rules, summary_message)
        )
        run_id = cur.fetchone()[0]
        return run_id


def insert_validation_results(
    conn: any,
    run_id: int,
    rule_name: str,
    is_valid: bool,
    message: str
) -> int:
    with conn.cursor() as cur:

        # Insert count validation result
        cur.execute(
            """
            INSERT INTO public.validation_results 
            (run_id, service, source_table, destination_table, rule_name, is_valid, message)
            VALUES (%s, 'LoyaltyService', 'df_member', %s, %s, %s, %s)
            RETURNING id;
            """,
            (run_id, TABLE, rule_name, is_valid, message)
        )

        id = cur.fetchone()[0]
        return id


def insert_record_differences(
    conn: any,
    validation_result_id: int,
    transform_issues: list[dict],
):
    with conn.cursor() as cur:
        for issue in transform_issues:
            cur.execute(
                """
                INSERT INTO public.record_differences 
                (validation_result_id, source_record_id, source_field, source_value, destination_field, destination_value, expected_value)
                VALUES (%s, %s, %s, %s, %s, %s, %s);
                """,
                (
                    validation_result_id,
                    issue['member_id'],
                    issue['source_field'],
                    str(issue['source_value']),
                    issue['destination_field'],
                    str(issue['destination_value']),
                    str(issue['source_value'])
                )
            )

    
def validate_member_profile_migration(is_full_dump: bool = True):
    """
    Compare data between source and destination databases to ensure data integrity.
    Stores validation results in validation_summary, validation_results, and record_differences tables.
    """
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    postgres = PostgresHandler(conn_id=TEMP_CONN_ID)

    # 0. Get sample size
    sample_size = get_sample_size(mssql, is_full_dump)
    logger.info(f"Sample size for data correctness validation: {sample_size}")

    # 1. Get data
    # 1.1 Get source data
    source_df = get_source_data(mssql, sample_size, is_full_dump)

    # 1.2 Get destination data
    sample_member_ids = source_df['member_id'].tolist()
    dest_df = get_dest_data(postgres, sample_member_ids)

    # 2. Initialize validation results
    total_rules = 3  # Count check, missing records checks, and transformation check
    failed_rules = 0
    validation_messages = []

    # 2.1 Rule 1: Check record counts
    source_count, source_member_ids = get_source_count(mssql)
    dest_count, dest_member_ids = get_dest_count(postgres, mssql)
    count_match = source_count == dest_count

    if not count_match:
        failed_rules += 1
        validation_messages.append(f"Record count mismatch: Source={source_count}, Destination={dest_count}")

    # 2.2 Rule 2: Check for missing records
    missing_in_dest = source_member_ids - dest_member_ids
    missing_in_source = dest_member_ids - source_member_ids

    # debugging log: missing records in source
    if missing_in_source:
        logger.info("- - - - - - For debugging: missing records in source - - - - - -")
        missing_source_records = sorted(list(missing_in_source))
        example_records = missing_source_records[:10]
        total_missing = len(missing_source_records)
        logger.info(f"Total records missing in source: {total_missing} records. First 10 examples: {example_records}")
        logger.info("- - - - - - End of debugging: missing records in source - - - - - -")
    # end debugging log

    
    if missing_in_dest:
        failed_rules += 1
        missing_dest_ids = sorted(list(missing_in_dest))
        validation_messages.append(f"Total records missing in destination: {len(missing_dest_ids)} records. First 10 examples: {missing_dest_ids[:10]}")

    # 2.3 Rule 3: Check data correctness
    transform_issues = check_data_correctness(source_df, dest_df)
    transform_valid = len(transform_issues) == 0
    if not transform_valid:
        failed_rules += 1
        validation_messages.append(f"Found {len(transform_issues)} transformation issues")

    # 3. Create final summary
    is_valid = failed_rules == 0
    summary_message = "Validation successful" if is_valid else "; ".join(validation_messages)

    # Insert results into validation tables
    # 3.1 Insert validation summary
    with postgres.hook.get_conn() as conn:
        run_id = insert_validation_summary(conn, TABLE, is_valid, total_rules, failed_rules, summary_message)

        # 3.2 Insert validation results
        insert_validation_results(conn, run_id, 'record_count', count_match, f"Source: {source_count}, Destination: {dest_count}")
        insert_validation_results(conn, run_id, 'missing_in_destination', len(missing_in_dest) == 0, f"Records found in source but missing in destination ({len(missing_in_dest)} records): {missing_in_dest}")
        transform_result_id = insert_validation_results(conn, run_id, 'data_correctness', transform_valid, f"Found {len(transform_issues)} transformation issues")

        # 3.3 Insert transformation issues into record_differences
        if transform_issues:
            insert_record_differences(conn, transform_result_id, transform_issues)

        conn.commit()

    logger.info(f"Validation completed - Run ID: {run_id}")
    logger.info(f"Total Rules: {total_rules}, Failed Rules: {failed_rules}")
    logger.info(f"Summary: {summary_message}")

    return {
        'run_id': run_id,
        'is_valid': is_valid,
        'failed_rules': failed_rules,
        'summary_message': summary_message
    }
