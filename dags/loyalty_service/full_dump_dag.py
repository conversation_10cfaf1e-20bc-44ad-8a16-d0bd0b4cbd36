from airflow import DAG
from datetime import datetime, timedelta
from airflow.operators.python import PythonOperator
from common_helpers.utils import create_migration_result_table
from common_helpers.logging import get_logger
from loyalty_service.migrate_member import migrate_member_data
from loyalty_service.migrate_memberprofile import migrate_member_profile_data
from loyalty_service.migrate_staffprofile import migrate_staff_profile_data
from loyalty_service.migrate_staffcompany import migrate_staff_company_data
from loyalty_service.migrate_mlcobrandhistory import migrate_cobrand_history_data
from loyalty_service.migrate_mltierhistory import mapping_tier_history_data
from loyalty_service.migrate_salestransaction import (
    migrate_sale_transaction_data,
    create_temp_sales_table,
    drop_temp_tables,
)
from loyalty_service.migrate_refundsalestransaction import (
    migrate_refund_sale_transaction_data,
    create_temp_refund_table,
    drop_temp_refund_tables,
)
from loyalty_service.update_member_spending import (
    update_member_spending,
    create_temp_spending_table,
    drop_temp_member_spending_tables,
)
from loyalty_service.migrate_ug_ur_rc import migrate_rc_ug_ur_data

logger = get_logger()

# full dump dag
with DAG(
    "loyalty_service_full_dump_migration",
    description="Full dump migration for Loyalty Service",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "full_dump"],
) as dag:

    create_migration_result_table_task = PythonOperator(
        task_id="migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    # Create temporary tables
    create_temp_sales_table_task = PythonOperator(
        task_id="create_temp_sales_table_task",
        python_callable=create_temp_sales_table,
        op_kwargs={"is_full_dump": True},
    )

    create_temp_refund_table_task = PythonOperator(
        task_id="create_temp_refund_table_task",
        python_callable=create_temp_refund_table,
        op_kwargs={"is_full_dump": True},
    )

    create_temp_spending_table_task = PythonOperator(
        task_id="create_temp_spending_table_task",
        python_callable=create_temp_spending_table,
    )

    # Migration tasks
    migrate_member_task = PythonOperator(
        task_id="migrate_member_data",
        python_callable=migrate_member_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    migrate_member_profile_task = PythonOperator(
        task_id="migrate_member_profile_data",
        python_callable=migrate_member_profile_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    migrate_staff_profile_task = PythonOperator(
        task_id="migrate_staff_profile_data",
        python_callable=migrate_staff_profile_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    migrate_staff_company_task = PythonOperator(
        task_id="migrate_staff_company_data",
        python_callable=migrate_staff_company_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    migrate_cobrand_history_task = PythonOperator(
        task_id="migrate_cobrand_history_data",
        python_callable=migrate_cobrand_history_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    migrate_tier_history_task = PythonOperator(
        task_id="migrate_tier_history_data",
        python_callable=mapping_tier_history_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    migrate_sales_transaction_task = PythonOperator(
        task_id="migrate_sales_transaction_data",
        python_callable=migrate_sale_transaction_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    migrate_refund_sales_transaction_task = PythonOperator(
        task_id="migrate_refund_sales_transaction_data",
        python_callable=migrate_refund_sale_transaction_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    update_member_spending_task = PythonOperator(
        task_id="update_member_spending_task",
        python_callable=update_member_spending,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    migrate_rc_ug_ur_task = PythonOperator(
        task_id="migrate_rc_ug_ur_data",
        python_callable=migrate_rc_ug_ur_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    # Cleanup tasks
    drop_temp_sales_tables_task = PythonOperator(
        task_id="drop_temp_sales_tables_task",
        python_callable=drop_temp_tables,
        op_kwargs={"is_full_dump": True},
    )

    drop_temp_refund_tables_task = PythonOperator(
        task_id="drop_temp_refund_tables_task",
        python_callable=drop_temp_refund_tables,
        op_kwargs={"is_full_dump": True},
    )

    drop_temp_spending_tables_task = PythonOperator(
        task_id="drop_temp_spending_tables_task",
        python_callable=drop_temp_member_spending_tables,
    )

    # Define task dependencies
    create_migration_result_table_task >> [migrate_staff_company_task, migrate_rc_ug_ur_task]
    [migrate_staff_company_task, migrate_rc_ug_ur_task] >> migrate_member_task 
    [migrate_staff_company_task, migrate_rc_ug_ur_task] >> migrate_member_profile_task 

    # Member related tasks
    migrate_member_task >> [
        migrate_staff_profile_task,
        migrate_cobrand_history_task,
        migrate_tier_history_task,
        migrate_sales_transaction_task,
    ]

    # Sales transaction related tasks
    [create_temp_sales_table_task, create_temp_refund_table_task] >> migrate_sales_transaction_task >> migrate_refund_sales_transaction_task

    # Member spending update
    migrate_refund_sales_transaction_task >> create_temp_spending_table_task >> update_member_spending_task

    # Cleanup tasks
    migrate_sales_transaction_task >> drop_temp_sales_tables_task
    migrate_refund_sales_transaction_task >> drop_temp_refund_tables_task
    update_member_spending_task >> drop_temp_spending_tables_task
