from airflow import DAG
from datetime import datetime, timedelta
from airflow.operators.python import PythonOperator
from common_helpers.utils import (
    incremental_date_condition,
    upsert_data_without_encrypt,
    get_last_successful_batch,
    set_last_successful_batch,
    reset_last_successful_batch,
    create_migration_result_table,
    destination_count,
    ls_dag_name,
    save_migration_result,
    log_successfully_migrated_data,
    log_start_process_batch,
    log_success_process_batch,
    calc_offset,
    calc_last_batch_size,
    calc_total_batches,
    is_last_batch,
    get_incremental_date,
    get_df,
    full_dump_date_condition,
    generate_id_via_keysearch,
)
import pandas as pd
import numpy as np
from common_helpers.database_services import MSSQLHandler
from constants import (
    NEWMEMBER_CONN_ID,
    SALESTRANS_BATCH_SIZE as BATCH_SIZE,
    LS_INCREMENTAL_DATE,
    LS_FULLDUMP_DATE,
)
from common_helpers.logging import get_logger

logger = get_logger()
TABLE = "SalesTransaction"


def create_temp_sales_table(is_full_dump: bool = True):
    """
    Prepare indexed temporary tables for SalesTransaction migration.

    Args:
        is_full_dump (bool): The migration type.

    Returns:
        None
    """
    tables = _get_table_names(is_full_dump)
    create_smc_sales_header_temp_table = (
        f"""
            IF OBJECT_ID('temp_smc_sales_header_full_dump', 'U') IS NOT NULL
            BEGIN
                DROP TABLE temp_smc_sales_header_full_dump;
            END

            SELECT member_id
                INTO #temp_active_member_ids_for_sales_tnx_full_dump
                FROM df_member
            WHERE del_flag = '';

            SELECT
                    member_id,
                    key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS key_search,
                    BranchNo,
                    Site,
                    DataDate
            INTO #temp_filtered_smc_sales_header_for_sales_tnx_full_dump
            FROM SMCSalesHeader
            WHERE SaleStatus != 'R' AND {full_dump_date_condition('DataDate', LS_FULLDUMP_DATE)};

            SELECT
                TRIM(ssh.member_id) AS memberId,
                ssh.key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS externalId,
                ssh.BranchNo AS partnerCode,
                ssh.BranchNo AS brandCode,
                ssh.Site AS branchCode,
                ssh.DataDate
            INTO temp_smc_sales_header_full_dump
            FROM #temp_filtered_smc_sales_header_for_sales_tnx_full_dump ssh
            JOIN #temp_active_member_ids_for_sales_tnx_full_dump dm ON dm.member_id = ssh.member_id;

            DROP TABLE IF EXISTS #temp_filtered_smc_sales_header_for_sales_tnx_full_dump;
            DROP TABLE IF EXISTS #temp_active_member_ids_for_sales_tnx_full_dump;

            CREATE INDEX temp_smc_sales_header_full_dump_externalId ON temp_smc_sales_header_full_dump (externalId);
        """
        if is_full_dump
        else f"""
                IF OBJECT_ID('temp_smc_sales_header_incremental', 'U') IS NOT NULL
                BEGIN
                    DROP TABLE temp_smc_sales_header_incremental;
                END

                SELECT member_id
                INTO #temp_active_member_ids_for_sales_tnx_incremental
                FROM df_member
                WHERE del_flag = '';

                SELECT
                    member_id,
                    key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS key_search,
                    BranchNo,
                    Site,
                    DataDate
                INTO #temp_filtered_smc_sales_header_for_sales_tnx_incremental
                FROM SMCSalesHeader
                WHERE SaleStatus != 'R' AND {incremental_date_condition("DataDate", LS_INCREMENTAL_DATE)};

                SELECT
                    TRIM(ssh.member_id) AS memberId,
                    ssh.key_search AS externalId,
                    ssh.BranchNo AS partnerCode,
                    ssh.BranchNo AS brandCode,
                    ssh.Site AS branchCode,
                    ssh.DataDate 
                INTO temp_smc_sales_header_incremental
                FROM #temp_filtered_smc_sales_header_for_sales_tnx_incremental ssh
                JOIN #temp_active_member_ids_for_sales_tnx_incremental dm ON dm.member_id = ssh.member_id;

                DROP TABLE IF EXISTS #temp_filtered_smc_sales_header_for_sales_tnx_incremental;
                DROP TABLE IF EXISTS #temp_active_member_ids_for_sales_tnx_incremental;

                CREATE INDEX temp_smc_sales_header_incremental_externalId ON temp_smc_sales_header_incremental (externalId);
            """
    )
    create_smc_sales_trans_temp_table = (
        """
            IF OBJECT_ID('temp_smc_sales_trans_full_dump', 'U') IS NOT NULL
            BEGIN
                DROP TABLE temp_smc_sales_trans_full_dump;
            END;

            WITH smc_base AS (
                SELECT 
                    key_search,
                    Net,
                    Discount,
                    lineCancel,
                    UpdateCoupon,
                    MAX(CAST(UpdateCoupon AS INT)) OVER (PARTITION BY key_search, MatCode) AS max_coupon,
                    CancelStatus
                FROM SMCSalesTrans
            ),
            filtered_smc_sales_trans AS (
                SELECT 
                    key_search,
                    Net,
                    Discount,
                    lineCancel,
                    CASE 
                        WHEN UpdateCoupon = 1 AND max_coupon = 1 THEN 1
                        WHEN max_coupon = 0 THEN 1
                        ELSE 0
                    END AS row_num,
                    CancelStatus
                FROM smc_base
            )
            SELECT
                key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS key_search,
                SUM(Net) AS netTotalAmount,
                SUM(Discount) AS totalDiscount
            INTO temp_smc_sales_trans_full_dump
            FROM filtered_smc_sales_trans
            WHERE row_num = 1 AND lineCancel = 0 AND CancelStatus = 0
            GROUP BY key_search;
            CREATE INDEX temp_smc_sales_trans_full_dump_key_search ON temp_smc_sales_trans_full_dump (key_search);
        """
        if is_full_dump
        else """
            IF OBJECT_ID('temp_smc_sales_trans_incremental', 'U') IS NOT NULL
            BEGIN
                DROP TABLE temp_smc_sales_trans_incremental;
            END;

            WITH smc_base AS (
                SELECT 
                    key_search,
                    Net,
                    Discount,
                    lineCancel,
                    UpdateCoupon,
                    MAX(CAST(UpdateCoupon AS INT)) OVER (PARTITION BY key_search, MatCode) AS max_coupon,
                    CancelStatus
                FROM SMCSalesTrans
            ),
            filtered_smc_sales_trans AS (
                SELECT 
                    key_search,
                    Net,
                    Discount,
                    lineCancel,
                    CASE 
                        WHEN UpdateCoupon = 1 AND max_coupon = 1 THEN 1
                        WHEN max_coupon = 0 THEN 1
                        ELSE 0
                    END AS row_num,
                    CancelStatus
                FROM smc_base
            )
            SELECT
                key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS key_search,
                SUM(Net) AS netTotalAmount,
                SUM(Discount) AS totalDiscount
            INTO temp_smc_sales_trans_incremental
            FROM filtered_smc_sales_trans
            WHERE row_num = 1 AND lineCancel = 0 AND CancelStatus = 0
            GROUP BY key_search;
            CREATE INDEX temp_smc_sales_trans_incremental_key_search ON temp_smc_sales_trans_incremental (key_search);
        """
    )
    create_temp_accum_table = f"""
            -- Drop temporary tables if they exist
            IF OBJECT_ID (N'TmpT0_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT0_192_168_1_103;
            END
            IF OBJECT_ID (N'TmpT1_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT1_192_168_1_103;
            END
            IF OBJECT_ID (N'TmpT2_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT2_192_168_1_103;
            END
            IF OBJECT_ID (N'TmpT3_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT3_192_168_1_103;
            END
            IF OBJECT_ID (N'TmpT4_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT4_192_168_1_103;
            END
            IF OBJECT_ID (N'TmpT5_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT5_192_168_1_103;
            END
            IF OBJECT_ID (N'temp_t5_sales_tnx_accum') IS NOT NULL 
            BEGIN
                DROP TABLE temp_t5_sales_tnx_accum;
            END

            -- TmpT0
            SELECT DISTINCT 
                CAST(t1.key_search AS CHAR(30)) COLLATE thai_ci_as AS KeySearch 
            INTO TmpT0_192_168_1_103
            FROM SMCSalesHeader t1 WITH (NOLOCK)
            INNER JOIN SMCSalesTrans t2 WITH (NOLOCK)
                ON t1.key_search = t2.key_search 
                AND lineCancel = 0 
                AND CancelStatus = 0
            INNER JOIN SMCSalesPayment smcp WITH (NOLOCK)
                ON t1.key_search = smcp.key_search
            INNER JOIN mast_NonAccCarat WITH (NOLOCK)
                ON smcp.MethodCode = mast_NonAccCarat.code 
                AND MAST_NonAccCarat.type = 'P' 
                AND MAST_NonAccCarat.IsCancel = 0
            INNER JOIN df_member WITH (NOLOCK)
                ON CAST(t1.member_id AS CHAR(8)) = df_member.member_id 
            WHERE t1.saleStatus <> 'R';

            CREATE NONCLUSTERED INDEX ix_tmpT0 ON TmpT0_192_168_1_103(keysearch);

            -- TmpT1
            SELECT 
                CAST(L1.KeySearch AS CHAR(30)) COLLATE thai_ci_as AS keysearch,
                SUM(-1 * l2.amount) AS net  
            INTO TmpT1_192_168_1_103
            FROM LoyaltyValue.dbo.LVHeader L1 WITH (NOLOCK)
            INNER JOIN LoyaltyValue.dbo.LVTrans L2 WITH (NOLOCK)
                ON L1.LVHeaderKey = L2.LVHeaderKey
            INNER JOIN LoyaltyValue.dbo.LVdata L3 WITH (NOLOCK)
                ON L2.LVMainKey = L3.LVMainKey
            INNER JOIN df_member WITH (NOLOCK)
                ON l2.LVNumber COLLATE thai_ci_as = df_member.member_id COLLATE thai_ci_as
            INNER JOIN TmpT0_192_168_1_103 L4
                ON CAST(l1.keysearch AS CHAR(30)) COLLATE thai_ci_as = l4.KeySearch
            WHERE L3.valuecode IN ('ep001','ep007','ep008','ep009','EP010','KPC01','KPO02')
            AND l2.movementcode = 'USE'
            GROUP BY CAST(L1.KeySearch AS CHAR(30));

            CREATE NONCLUSTERED INDEX ix_tmpT1 ON TmpT1_192_168_1_103(keysearch);

            -- TmpT2
            SELECT 
                t1.BranchNo,
                CAST(t1.key_search AS CHAR(30)) COLLATE thai_ci_as AS key_search,
                t1.salesBranch,
                CAST(t1.member_id AS CHAR(8)) AS member_id,
                SUM(t2.qty) AS qty 
            INTO TmpT2_192_168_1_103
            FROM SMCSalesHeader t1 WITH (NOLOCK)
            INNER JOIN SMCSalesTrans t2 WITH (NOLOCK)
                ON t1.key_search = t2.key_search 
                AND lineCancel = 0 
                AND CancelStatus = 0
            INNER JOIN df_member WITH (NOLOCK)
                ON CAST(t1.member_id AS CHAR(8)) = df_member.member_id 
            WHERE t1.saleStatus <> 'R'
            GROUP BY t1.BranchNo, t1.key_search, t1.salesBranch, t1.member_id;

            CREATE NONCLUSTERED INDEX ix_tmpT2 ON TmpT2_192_168_1_103(key_search);

            -- TmpT3
            SELECT *
            INTO TmpT3_192_168_1_103
            FROM TmpT2_192_168_1_103 T2
            INNER JOIN TmpT1_192_168_1_103 T1
                ON T2.key_search = T1.KeySearch;

            CREATE NONCLUSTERED INDEX ix_tmpT3 ON TmpT3_192_168_1_103(key_search);

            -- TmpT4
            SELECT 
                x.member_id,
                x.key_search,
                SUM(t3.net) AS total
            INTO TmpT4_192_168_1_103
            FROM (
                SELECT 
                    t1.key_search,
                    CAST(t1.member_id AS CHAR(8)) AS member_id
                FROM SMCSalesHeader t1 WITH (NOLOCK)
                INNER JOIN SMCSalesTrans t2 WITH (NOLOCK)
                    ON t1.key_search = t2.key_search
                    AND t2.lineCancel = 0 
                    AND t2.CancelStatus = 0
                INNER JOIN df_member WITH (NOLOCK)
                    ON CAST(t1.member_id AS CHAR(8)) = df_member.member_id
                WHERE t1.saleStatus <> 'R'
                GROUP BY t1.BranchNo, t1.key_search, t1.salesBranch, t1.member_id
            ) x
            INNER JOIN SMCSalesPayment t3 WITH (NOLOCK)
                ON x.key_search = t3.key_search
                AND t3.MethodCode NOT IN (
                    SELECT Code 
                    FROM mast_NonAccCarat 
                    WHERE type = 'P' AND iscancel = 0
                )
            GROUP BY x.key_search, x.member_id;

            CREATE NONCLUSTERED INDEX ix_tmpT4 ON TmpT4_192_168_1_103(key_search);

            -- TmpT5
            SELECT 
                z.member_id,
                z.key_search,
                z.totalEarnableAmount,
                z.totalAccumSpendableAmount
            INTO TmpT5_192_168_1_103
            FROM (
                SELECT 
                    member_id, 
                    key_search,
                    net AS totalEarnableAmount,
                    net AS totalAccumSpendableAmount
                FROM TmpT3_192_168_1_103

                UNION

                SELECT 
                    member_id, 
                    key_search,
                    total AS totalEarnableAmount,
                    total AS totalAccumSpendableAmount
                FROM TmpT4_192_168_1_103
            ) z;

            CREATE NONCLUSTERED INDEX ix_tmpT5 ON TmpT5_192_168_1_103(key_search);
            
            -- Final result
            SELECT 
                key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS key_search,
                SUM(totalAccumSpendableAmount) AS totalAccumSpendableAmount,
                SUM(totalEarnableAmount) AS totalEarnableAmount
            INTO temp_t5_sales_tnx_accum
            FROM TmpT5_192_168_1_103
            GROUP BY key_search;

            -- Drop temporary tables after use
            IF OBJECT_ID (N'TmpT0_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT0_192_168_1_103;
            END
            IF OBJECT_ID (N'TmpT1_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT1_192_168_1_103;
            END
            IF OBJECT_ID (N'TmpT2_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT2_192_168_1_103;
            END
            IF OBJECT_ID (N'TmpT3_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT3_192_168_1_103;
            END
            IF OBJECT_ID (N'TmpT4_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT4_192_168_1_103;
            END
            IF OBJECT_ID (N'TmpT5_192_168_1_103') IS NOT NULL 
            BEGIN
                DROP TABLE TmpT5_192_168_1_103;
            END
        """

    create_smc_sales_payment_temp_table = (
        """
            IF OBJECT_ID('temp_smc_sales_payment_full_dump', 'U') IS NOT NULL
            BEGIN
                DROP TABLE temp_smc_sales_payment_full_dump;
            END;

            SELECT 
                key_search,
                totalEarnableAmount,
                totalAccumSpendableAmount
            into temp_smc_sales_payment_full_dump
            FROM temp_t5_sales_tnx_accum;

        CREATE INDEX temp_smc_sales_payment_full_dump_key_search ON temp_smc_sales_payment_full_dump (key_search);

        DROP TABLE temp_t5_sales_tnx_accum;
        """
        if is_full_dump
        else """
            IF OBJECT_ID('temp_smc_sales_payment_incremental', 'U') IS NOT NULL
            BEGIN
                DROP TABLE temp_smc_sales_payment_incremental;
            END;

            SELECT 
                key_search,
                totalEarnableAmount,
                totalAccumSpendableAmount
            into temp_smc_sales_payment_incremental
            FROM temp_t5_sales_tnx_accum;

        CREATE INDEX temp_smc_sales_payment_incremental_key_search ON temp_smc_sales_payment_incremental (key_search);
        
        DROP TABLE temp_t5_sales_tnx_accum;
        """
    )
    create_lv_header_temp_table = (
        f"""
            IF OBJECT_ID('LoyaltyValue.dbo.temp_lv_header_full_dump', 'U') IS NOT NULL
            BEGIN
                DROP TABLE LoyaltyValue.dbo.temp_lv_header_full_dump;
            END;
            
            WITH MaxRowsLVHeader AS (
                SELECT 
                    l.KeySearch,
                    l.LVHeaderKey,
                    l.AddDT,
                    l.FinishDT,
                    l.DocDate,
                    l.CancelHeaderKey,
                    ROW_NUMBER() OVER (
                        PARTITION BY l.KeySearch 
                        ORDER BY 
                            -- Prioritize rows with CancelHeaderKey IS NULL
                            CASE WHEN l.CancelHeaderKey IS NULL THEN 1 ELSE 0 END DESC,
                            -- Then get the max LVHeaderKey within that preference
                            l.LVHeaderKey DESC
                    ) AS rn
                FROM LoyaltyValue.dbo.LVHeader l 
            )
            SELECT
                lvh.KeySearch COLLATE SQL_Latin1_General_CP1_CI_AS AS KeySearch,
                lvh.LVHeaderKey,
                lvh.AddDT AS createdAt,
                lvh.FinishDT AS updatedAt,
                lvh.DocDate AS DocDate,
                lvh.CancelHeaderKey
            INTO LoyaltyValue.dbo.temp_lv_header_full_dump
            FROM MaxRowsLVHeader lvh
            WHERE rn = 1 and {full_dump_date_condition('lvh.DocDate', LS_FULLDUMP_DATE)};
            CREATE INDEX temp_lv_header_full_dump_key_search ON LoyaltyValue.dbo.temp_lv_header_full_dump (KeySearch);
            CREATE INDEX temp_lv_header_full_dump_lv_header_key ON LoyaltyValue.dbo.temp_lv_header_full_dump (LVHeaderKey);
        """
        if is_full_dump
        else f"""
            IF OBJECT_ID('LoyaltyValue.dbo.temp_lv_header_incremental', 'U') IS NOT NULL
            BEGIN
                DROP TABLE LoyaltyValue.dbo.temp_lv_header_incremental;
            END;

            WITH MaxRowsLVHeader AS (
                SELECT 
                    l.KeySearch,
                    l.LVHeaderKey,
                    l.AddDT,
                    l.FinishDT,
                    l.DocDate,
                    l.CancelHeaderKey,
                    ROW_NUMBER() OVER (
                        PARTITION BY l.KeySearch 
                        ORDER BY 
                            -- Prioritize rows with CancelHeaderKey IS NULL
                            CASE WHEN l.CancelHeaderKey IS NULL THEN 1 ELSE 0 END DESC,
                            -- Then get the max LVHeaderKey within that preference
                            l.LVHeaderKey DESC
                    ) AS rn
                FROM LoyaltyValue.dbo.LVHeader l 
                WHERE l.DocDate >= DATEADD(DAY, -1, CAST(CAST(GETDATE() AS DATE) AS DATETIME))
                AND l.DocDate < CAST(CAST(GETDATE() AS DATE) AS DATETIME)
            )
            SELECT
                lvh.KeySearch COLLATE SQL_Latin1_General_CP1_CI_AS AS KeySearch,
                lvh.LVHeaderKey,
                lvh.AddDT AS createdAt,
                lvh.FinishDT AS updatedAt,
                lvh.DocDate AS DocDate,
                lvh.CancelHeaderKey
            INTO LoyaltyValue.dbo.temp_lv_header_incremental
            FROM MaxRowsLVHeader lvh
            WHERE rn = 1 AND {incremental_date_condition("lvh.DocDate", LS_INCREMENTAL_DATE)};
            CREATE INDEX temp_lv_header_incremental_key_search ON LoyaltyValue.dbo.temp_lv_header_incremental (KeySearch);
            CREATE INDEX temp_lv_header_incremental_lv_header_key ON LoyaltyValue.dbo.temp_lv_header_incremental (LVHeaderKey);
        """
    )

    create_temp_sales_trans_with_lv_table = f"""
        IF OBJECT_ID('{tables["sales_trans_with_lv"]}', 'U') IS NOT NULL
        BEGIN
            DROP TABLE {tables["sales_trans_with_lv"]};
        END

        SELECT 
            TRIM(tssh.memberId) AS memberId,
            tssh.externalId, 
            tssh.partnerCode, 
            tssh.brandCode,
            COALESCE(tssh.branchCode, '') AS branchCode,
            tlh.LVHeaderKey as id,
            DATEADD (HOUR, -7, tlh.createdAt) AS createdAt,
            CASE
                WHEN tlh.updatedAt IS NULL THEN DATEADD (HOUR, -7, tlh.createdAt)
                ELSE DATEADD (HOUR, -7, tlh.updatedAt)
            END AS updatedAt,
            DATEADD (HOUR, -7, tlh.createdAt) AS completedAt,
            tsst.netTotalAmount,
            tsst.totalDiscount,
            tssp.totalAccumSpendableAmount,
            tssp.totalEarnableAmount
        into {tables["sales_trans_with_lv"]}
        from {tables["header"]} tssh
        join {tables["lv_header"]} tlh on tssh.externalId = tlh.KeySearch
        join {tables["trans"]} tsst  on tssh.externalId = tsst.key_search
        left join {tables["payment"]} tssp  on tssh.externalId = tssp.key_search
        where tlh.CancelHeaderKey is null

        CREATE INDEX {tables["sales_trans_with_lv"]}_id ON {tables["sales_trans_with_lv"]} (id);
        """

    create_temp_sales_trans_without_lv_table = f"""
        IF OBJECT_ID('{tables["sales_trans_without_lv"]}', 'U') IS NOT NULL
        BEGIN
            DROP TABLE {tables["sales_trans_without_lv"]};
        END

        select 
            TRIM(tssh.memberId) AS memberId,
            tssh.externalId, 
            tssh.partnerCode, 
            tssh.brandCode,
            COALESCE(tssh.branchCode, '') AS branchCode,
            tssh.DataDate AS createdAt,
            tssh.DataDate AS updatedAt,
            tssh.DataDate AS completedAt,
            tsst.netTotalAmount,
            tsst.totalDiscount,
            tssp.totalAccumSpendableAmount,
            tssp.totalEarnableAmount
        INTO {tables["sales_trans_without_lv"]}
        FROM {tables["header"]} tssh
        JOIN {tables["trans"]} tsst ON tssh.externalId = tsst.key_search
        LEFT JOIN {tables["payment"]} tssp ON tssh.externalId = tssp.key_search
        LEFT JOIN {tables["lv_header"]} tlh ON tssh.externalId = tlh.KeySearch
        WHERE tlh.LVHeaderKey IS NULL

        CREATE INDEX {tables["sales_trans_without_lv"]}_external_id ON {tables["sales_trans_without_lv"]} (externalId);
        """

    newmember_handler = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    newmember_connection = newmember_handler.hook.get_conn()

    try:
        logger.info(f"started preparing SMCSalesHeader temp table for migration...")
        newmember_handler.execute_query_string(
            connection=newmember_connection,
            query_string=create_smc_sales_header_temp_table,
        )
        logger.info(f"finished preparing SMCSalesHeader temp table for migration.")

        logger.info(f"started preparing SMCSalesTrans temp table for migration...")
        newmember_handler.execute_query_string(
            connection=newmember_connection,
            query_string=create_smc_sales_trans_temp_table,
        )
        logger.info(f"finished preparing SMCSalesTrans temp table for migration.")

        logger.info(
            f"started preparing temp_t5_sales_tnx_accum temp table for migration..."
        )
        newmember_handler.execute_query_string(
            connection=newmember_connection,
            query_string=create_temp_accum_table,
        )
        logger.info(
            f"finished preparing temp_t5_sales_tnx_accum temp table for migration."
        )

        logger.info(f"started preparing SMCSalesPayment temp table for migration...")
        newmember_handler.execute_query_string(
            connection=newmember_connection,
            query_string=create_smc_sales_payment_temp_table,
        )
        logger.info(f"finished preparing SMCSalesPayment temp table for migration.")

        logger.info(f"started preparing LVHeader temp table for migration...")
        newmember_handler.execute_query_string(
            connection=newmember_connection,
            query_string=create_lv_header_temp_table,
        )
        logger.info(f"finished preparing LVHeader temp table for migration.")

        logger.info(
            f"started preparing sales_trans_with_lv temp table for migration..."
        )
        newmember_handler.execute_query_string(
            connection=newmember_connection,
            query_string=create_temp_sales_trans_with_lv_table,
        )
        logger.info(f"finished preparing sales_trans_with_lv temp table for migration.")

        logger.info(
            f"started preparing sales_trans_without_lv temp table for migration..."
        )
        newmember_handler.execute_query_string(
            connection=newmember_connection,
            query_string=create_temp_sales_trans_without_lv_table,
        )
        logger.info(
            f"finished preparing sales_trans_without_lv temp table for migration."
        )

    finally:
        newmember_connection.close()


def drop_table_query(table: str) -> str:
    return f"DROP TABLE {table};"


def drop_temp_tables(is_full_dump: bool) -> None:
    newmember_handler = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    newmember_connection = newmember_handler.hook.get_conn()
    tables = _get_table_names(is_full_dump)

    try:
        logger.info(f"started dropping SMCSalesHeader temp table for migration...")
        newmember_handler.execute_query_string(
            connection=newmember_connection,
            query_string=drop_table_query(tables["header"]),
        )
        logger.info(f"finished dropping SMCSalesHeader temp table for migration.")

        logger.info(f"started dropping SMCSalesTrans temp table for migration...")
        newmember_handler.execute_query_string(
            connection=newmember_connection,
            query_string=drop_table_query(tables["trans"]),
        )
        logger.info(f"finished dropping SMCSalesTrans temp table for migration.")

        logger.info(f"started dropping SMCSalesPayment temp table for migration...")
        newmember_handler.execute_query_string(
            connection=newmember_connection,
            query_string=drop_table_query(tables["payment"]),
        )
        logger.info(f"finished dropping SMCSalesPayment temp table for migration.")

        logger.info(f"started dropping LVHeader temp table for migration...")
        newmember_handler.execute_query_string(
            connection=newmember_connection,
            query_string=drop_table_query(tables["lv_header"]),
        )
        logger.info(f"finished dropping LVHeader temp table for migration.")

        logger.info(f"started dropping sales_trans_with_lv temp table for migration...")
        newmember_handler.execute_query_string(
            connection=newmember_connection,
            query_string=drop_table_query(tables["sales_trans_with_lv"]),
        )
        logger.info(f"finished dropping sales_trans_with_lv temp table for migration.")

        logger.info(
            f"started dropping sales_trans_without_lv temp table for migration..."
        )
        newmember_handler.execute_query_string(
            connection=newmember_connection,
            query_string=drop_table_query(tables["sales_trans_without_lv"]),
        )
        logger.info(
            f"finished dropping sales_trans_without_lv temp table for migration."
        )

    finally:
        newmember_connection.close()


def _get_table_names(full_dump: bool) -> dict:
    suffix = "full_dump" if full_dump else "incremental"
    return {
        "header": f"temp_smc_sales_header_{suffix}",
        "lv_header": f"LoyaltyValue.dbo.temp_lv_header_{suffix}",
        "trans": f"temp_smc_sales_trans_{suffix}",
        "payment": f"temp_smc_sales_payment_{suffix}",
        # temp table for sales tnx
        "sales_trans_with_lv": f"temp_smc_sales_trans_with_lv_{suffix}",
        "sales_trans_without_lv": f"temp_smc_sales_trans_without_lv_{suffix}",
    }


def mapping_data_with_matched_lvheader(
    offset: int,
    full_dump: bool,
    batch_size: int,
) -> pd.DataFrame:
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    tables = _get_table_names(full_dump)

    query = f"""
    SELECT 
        *
    FROM {tables["sales_trans_with_lv"]}
    ORDER BY id
    OFFSET {offset} ROWS FETCH NEXT {batch_size} ROWS ONLY;
    """
    df = get_df(query, mssql)

    # if there is no payment, totalAccumSpendableAmount = 0, totalEarnableAmount = 0
    df["totalAccumSpendableAmount"].replace(np.nan, 0, inplace=True)
    df["totalEarnableAmount"].replace(np.nan, 0, inplace=True)

    return df


def mapping_data_with_unmatched_lvheader(
    offset: int,
    full_dump: bool,
    batch_size: int,
) -> pd.DataFrame:
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    tables = _get_table_names(full_dump)

    query = f"""
    select 
        *
    FROM {tables["sales_trans_without_lv"]}
    ORDER BY externalId
    OFFSET {offset} ROWS FETCH NEXT {batch_size} ROWS ONLY;
    """
    df = get_df(query, mssql)

    # if there is no payment, totalAccumSpendableAmount = 0, totalEarnableAmount = 0
    df["totalAccumSpendableAmount"].replace(np.nan, 0, inplace=True)
    df["totalEarnableAmount"].replace(np.nan, 0, inplace=True)

    # generate id from key_search
    df["id"] = df["externalId"].apply(generate_id_via_keysearch).astype(str)

    return df


def get_total_rows_with_matched_lvheader(full_dump: bool) -> int:
    tables = _get_table_names(full_dump)
    query = f"""
        SELECT COUNT(*) 
        FROM {tables["sales_trans_with_lv"]}
    """
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    return mssql.extract_data(query)[0][0]


def get_total_rows_with_unmatched_lvheader(full_dump: bool) -> int:
    """
    Get total rows of SalesTransaction with unmatched rows in LoyaltyValue.
    """
    tables = _get_table_names(full_dump)
    query = f"""
        SELECT COUNT(*) 
        FROM {tables["sales_trans_without_lv"]}
    """
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    return mssql.extract_data(query)[0][0]


def migrate_sale_transaction_data():
    start_time = datetime.now()  # keep record for start time
    full_dump = True

    try:
        # Case 1: LVHeaderKey is not NULL
        logger.info("Starting migrate SalesTransaction with matched LVHeader...")

        total_rows_1 = get_total_rows_with_matched_lvheader(full_dump)
        total_batches = calc_total_batches(total_rows_1, BATCH_SIZE)
        last_batch_size = calc_last_batch_size(total_rows_1, BATCH_SIZE)
        last_successful_batch = get_last_successful_batch(TABLE)

        for batch_num in range(last_successful_batch + 1, total_batches):
            log_start_process_batch(TABLE, batch_num, total_batches)

            offset = calc_offset(batch_num, BATCH_SIZE)
            batch_size = (
                last_batch_size
                if is_last_batch(batch_num, total_batches)
                else BATCH_SIZE
            )

            df: pd.DataFrame = mapping_data_with_matched_lvheader(
                offset, full_dump, batch_size
            )

            upsert_data_without_encrypt(df=df, table=TABLE, conflict_target=["id"])

            set_last_successful_batch(TABLE, batch_num)
            log_success_process_batch(
                TABLE, batch_num, total_batches, batch_size, len(df)
            )

        # Case 2: LVHeaderKey is NULL
        logger.info("Starting migrate SalesTransaction with unmatched LVHeader...")

        total_rows_2 = get_total_rows_with_unmatched_lvheader(full_dump)
        total_batches = calc_total_batches(total_rows_2, BATCH_SIZE)
        last_batch_size = calc_last_batch_size(total_rows_2, BATCH_SIZE)
        last_successful_batch = get_last_successful_batch(f"{TABLE}2")

        for batch_num in range(last_successful_batch + 1, total_batches):
            log_start_process_batch(
                f"{TABLE} (For unmatched LVHeader)", batch_num, total_batches
            )

            offset = calc_offset(batch_num, BATCH_SIZE)
            batch_size = (
                last_batch_size
                if is_last_batch(batch_num, total_batches)
                else BATCH_SIZE
            )

            df: pd.DataFrame = mapping_data_with_unmatched_lvheader(
                offset, full_dump, batch_size
            )

            upsert_data_without_encrypt(df=df, table=TABLE, conflict_target=["id"])

            set_last_successful_batch(f"{TABLE}2", batch_num)
            log_success_process_batch(
                f"{TABLE} (For unmatched LVHeader)",
                batch_num,
                total_batches,
                batch_size,
                len(df),
            )

        logger.info(f"destination count: {destination_count(TABLE)}")

        reset_last_successful_batch(
            TABLE
        )  # reset last successful batch for matched LVHeaderKey
        reset_last_successful_batch(
            f"{TABLE}2"
        )  # reset last successful batch for matched LVHeaderKey

    except Exception as err:
        logger.error(f"An error has occured: {err}")
        raise err

    else:
        dest_count = destination_count(TABLE)
        log_successfully_migrated_data(TABLE, total_records=dest_count)

    finally:
        dest_count = destination_count(TABLE)
        save_migration_result(
            full_dump=full_dump,
            source_table="SMCSalesHeader",
            table=TABLE,
            source_count=total_rows_1 + total_rows_2,
            dest_count=dest_count,
            created_at=start_time,
        )


def incremental_migrate_sale_transaction_data():
    start_time = datetime.now()  # keep record for start time
    full_dump = False

    try:
        # Case 1: LVHeaderKey is not NULL
        logger.info("Starting migrate SalesTransaction with matched LVHeader...")

        total_rows_1 = get_total_rows_with_matched_lvheader(full_dump)
        total_batches = calc_total_batches(total_rows_1, BATCH_SIZE)
        last_batch_size = calc_last_batch_size(total_rows_1, BATCH_SIZE)

        dest_count = 0

        for batch_num in range(0, total_batches):
            log_start_process_batch(TABLE, batch_num, total_batches)

            offset = calc_offset(batch_num, BATCH_SIZE)
            batch_size = (
                last_batch_size
                if is_last_batch(batch_num, total_batches)
                else BATCH_SIZE
            )

            df: pd.DataFrame = mapping_data_with_matched_lvheader(
                offset, full_dump, batch_size
            )

            upsert_data_without_encrypt(df=df, table=TABLE, conflict_target=["id"])

            total_records = len(df)
            dest_count += total_records

            log_success_process_batch(
                TABLE, batch_num, total_batches, batch_size, total_records
            )

        # Case 2: LVHeaderKey is NULL
        logger.info("Starting migrate SalesTransaction with unmatched LVHeader...")

        total_rows_2 = get_total_rows_with_unmatched_lvheader(full_dump)
        total_batches = calc_total_batches(total_rows_2, BATCH_SIZE)
        last_batch_size = calc_last_batch_size(total_rows_2, BATCH_SIZE)

        for batch_num in range(0, total_batches):
            log_start_process_batch(
                f"{TABLE} (For unmatched LVHeader)", batch_num, total_batches
            )

            offset = calc_offset(batch_num, BATCH_SIZE)
            batch_size = (
                last_batch_size
                if is_last_batch(batch_num, total_batches)
                else BATCH_SIZE
            )

            df: pd.DataFrame = mapping_data_with_unmatched_lvheader(
                offset, full_dump, batch_size
            )

            upsert_data_without_encrypt(df=df, table=TABLE, conflict_target=["id"])

            total_records = len(df)
            dest_count += total_records

            log_success_process_batch(
                f"{TABLE} (For unmatched LVHeader)",
                batch_num,
                total_batches,
                batch_size,
                total_records,
            )

    except Exception as err:
        logger.error(f"An error has occured: {err}")
        raise err

    else:
        log_successfully_migrated_data(TABLE, total_records=dest_count)

    finally:
        save_migration_result(
            full_dump=full_dump,
            source_table="SMCSalesHeader",
            table=TABLE,
            source_count=total_rows_1 + total_rows_2,
            dest_count=dest_count,
            created_at=start_time,
            incremental_date=get_incremental_date(LS_INCREMENTAL_DATE),
        )


# full dump dag
with DAG(
    ls_dag_name(TABLE, full_dump=True),
    description="Migrate data of SalesTransaction Table",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "full_dump", "sales_transaction"],
) as dag:

    create_migration_result_table_task = PythonOperator(
        task_id=f"migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    create_temp_table_task = PythonOperator(
        task_id=f"create_temp_table_task",
        python_callable=create_temp_sales_table,
        op_kwargs={"is_full_dump": True},
    )

    mapping_data_task = PythonOperator(
        task_id=f"mapping_salestransaction_data",
        python_callable=migrate_sale_transaction_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    drop_temp_tables_task = PythonOperator(
        task_id=f"drop_temp_table_task",
        python_callable=drop_temp_tables,
        op_kwargs={"is_full_dump": True},
    )

    create_temp_table_task >> mapping_data_task
    create_migration_result_table_task >> mapping_data_task
    mapping_data_task >> drop_temp_tables_task

# incremental dag
with DAG(
    ls_dag_name(TABLE, full_dump=False),
    description="Incremental migrate data of SalesTransaction Table",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "incremental", "sales_transaction"],
) as dag:

    create_migration_result_table_task = PythonOperator(
        task_id=f"migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    create_temp_sales_table_task = PythonOperator(
        task_id=f"create_temp_sales_table_task",
        python_callable=create_temp_sales_table,
        op_kwargs={"is_full_dump": False},
    )

    migrate_sales_transaction_task = PythonOperator(
        task_id=f"migrate_sales_transaction_data",
        python_callable=incremental_migrate_sale_transaction_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    drop_temp_tables_task = PythonOperator(
        task_id=f"drop_temp_table_task",
        python_callable=drop_temp_tables,
        op_kwargs={"is_full_dump": False},
    )

    (
        create_migration_result_table_task
        >> create_temp_sales_table_task
        >> migrate_sales_transaction_task
        >> drop_temp_tables_task
    )
