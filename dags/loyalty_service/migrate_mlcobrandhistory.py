import numpy as np
from airflow import DAG
from datetime import datetime, timedelta, timezone
from airflow.operators.python import PythonOperator
from common_helpers.utils import (
    rename_columns,
    generate_ulid,
    create_mapping_df,
    incremental_date_condition,
    upsert_data_without_encrypt,
    get_last_successful_batch,
    set_last_successful_batch,
    reset_last_successful_batch,
    cast_nvarchar,
    create_migration_result_table,
    save_migration_result,
    destination_count,
    ls_dag_name,
    log_start_process_batch,
    log_success_process_batch,
    log_successfully_migrated_data,
    calc_offset,
    calc_last_batch_size,
    calc_total_batches,
    is_last_batch,
    get_incremental_date,
    get_df,
    full_dump_date_condition,
)
import pandas as pd
from common_helpers.database_services import MSSQLHandler
from constants import (
    NEWMEMBER_CONN_ID,
    BATCH_SIZE,
    LS_INCREMENTAL_DATE,
    LS_FULLDUMP_DATE,
)
from common_helpers.logging import get_logger

logger = get_logger()


mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)

daily_condition = f'({incremental_date_condition("dc.add_datetime", LS_INCREMENTAL_DATE)} OR {incremental_date_condition("dc.update_datetime", LS_INCREMENTAL_DATE)})'
full_dump_condition = f"{full_dump_date_condition('dm.add_datetime', LS_FULLDUMP_DATE)} AND {full_dump_date_condition('dc.update_datetime', LS_FULLDUMP_DATE)}"

SERVICE = "Loyalty Service"
TABLE = "MemberLegacyCobrandHistory"


def mapping_data(
    offset: int,
    full_dump: bool,
    batch_size: int,
    field_df: pd.DataFrame,
) -> pd.DataFrame:

    # 1. source table: df_cardhist
    table = "df_cardhist"
    source_fields = field_df[field_df["SMC Table"] == table]["SMC Field"].to_list()
    source_fields = [f"dc.{f}" for f in source_fields]

    migrate_condition = f"CardTypeCode IN ('SCB', 'KBANK')"
    if full_dump:
        migrate_condition = f"{migrate_condition} and {full_dump_condition}"
    else:
        migrate_condition = f"{migrate_condition} and {daily_condition}"

    query = f"""
        SELECT reason_id, card_status, runno as source_runno, "CardTypeCode" as source_cardtypecode, {", ".join(source_fields)} 
        FROM {table} dc
        LEFT JOIN df_member dm ON dc.member_id = dm.member_id 
        WHERE dm.del_flag = ' ' AND {migrate_condition} 
        ORDER BY dc.add_datetime, dc.member_id, dc.runno, dc."CardTypeCode"
        OFFSET {offset} ROWS FETCH NEXT {batch_size} ROWS ONLY;
    """
    df = get_df(query, mssql)

    # Apply .str.strip() to all string columns
    df = df.map(lambda x: x.strip() if isinstance(x, str) else x)

    # df["start_date"] = df["start_date"].apply(convert_bangkok_to_utc)
    # df["end_date"] = df["end_date"].apply(convert_bangkok_to_utc)

    # 2. source tavle: mst_card_type
    query = f"SELECT card_type_code, description FROM mst_card_type"
    ctype_df = get_df(query, mssql)

    # clean data by remiving leading and trailing spaces
    ctype_df = ctype_df.map(lambda x: x.strip() if isinstance(x, str) else x)

    df = df.merge(
        ctype_df, left_on="card_type_id", right_on="card_type_code", how="left"
    )
    df["description"].replace(np.nan, "", inplace=True)

    # 3.source table: mst_reason
    query = f"SELECT reason_id, {cast_nvarchar('reason_desc')} FROM mst_reason"
    reason_df = get_df(query, mssql)

    # clean data by remiving leading and trailing spaces
    reason_df = reason_df.map(lambda x: x.strip() if isinstance(x, str) else x)
    df = df.merge(reason_df, on="reason_id", how="left")

    # 4. source table: MAST_CardStatus
    query = "SELECT StatusName, CardStatus FROM MAST_CardStatus"
    status_df = get_df(query, mssql)
    df = df.merge(status_df, left_on="card_status", right_on="CardStatus", how="left")

    df["id"] = [generate_ulid() for _ in range(len(df))]

    # drop unused field for saving in destination db
    df.drop(
        columns=["card_type_code", "reason_id", "CardStatus", "card_status"],
        inplace=True,
    )

    df = rename_columns(df, field_df)

    # Replace NaN values with None
    df = df.replace({np.nan: None})

    # add current time to `updatedAt` field in utc timezone 
    now_utc = datetime.now(timezone.utc)
    df["updatedAt"] = now_utc

    return df


def get_totat_rows(full_dump: bool):
    if full_dump:
        query = f"""
        select count(*) from df_cardhist dc 
        JOIN df_member dm ON dc.member_id = dm.member_id 
        where dm.del_flag = ' ' and dc.CardTypeCode IN ('SCB', 'KBANK') 
        and {full_dump_condition}
        """
        return mssql.extract_data(query)[0][0]

    query = f"""
    select count(*) from df_cardhist dc 
    JOIN df_member dm ON dc.member_id = dm.member_id 
    where dm.del_flag = ' ' AND dc.CardTypeCode IN ('SCB', 'KBANK')
    AND {daily_condition}
    """
    return mssql.extract_data(query)[0][0]


def migrate_cobrand_history_data() -> None:
    full_dump = True
    start_time = datetime.now()  # keep record for start time

    try:
        total_rows = get_totat_rows(full_dump)
        total_batches = calc_total_batches(total_rows, BATCH_SIZE)
        last_batch_size = calc_last_batch_size(total_rows, BATCH_SIZE)
        last_successful_batch = get_last_successful_batch(TABLE)

        field_df = create_mapping_df(service=SERVICE, table=TABLE)[
            ["Field", "SMC Table", "SMC Field"]
        ]

        for batch_num in range(last_successful_batch + 1, total_batches):
            log_start_process_batch(
                table=TABLE,
                batch_num=batch_num,
                total_batches=total_batches,
            )

            offset = calc_offset(batch_num, BATCH_SIZE)
            batch_size = (
                last_batch_size
                if is_last_batch(batch_num, total_batches)
                else BATCH_SIZE
            )

            df = mapping_data(offset, full_dump, batch_size, field_df)

            upsert_data_without_encrypt(
                df=df,
                table=TABLE,
                conflict_target=["memberId", "source_runno", "source_cardtypecode"],
            )

            # Update the last successful batch after processing the current batch
            set_last_successful_batch(TABLE, batch_num)
            log_success_process_batch(
                table=TABLE,
                batch_num=batch_num,
                total_batches=total_batches,
                batch_size=batch_size,
                total_records=len(df),
            )

        # reset last successful batch
        reset_last_successful_batch(TABLE)

    except Exception as err:
        logger.error(f"An error has occured: {err}")
        raise err

    else:
        dest_count = destination_count(TABLE)
        log_successfully_migrated_data(TABLE, dest_count)

    finally:
        dest_count = destination_count(TABLE)
        save_migration_result(
            full_dump=full_dump,
            source_table="df_cardhist",
            table=TABLE,
            source_count=total_rows,
            dest_count=dest_count,
            created_at=start_time,
        )


def incremental_migrate_cobrand_history_data() -> None:
    start_time = datetime.now()  # keep record for start time
    full_dump = False

    try:
        total_rows = get_totat_rows(full_dump)
        total_batches = calc_total_batches(total_rows, BATCH_SIZE)
        last_batch_size = calc_last_batch_size(total_rows, BATCH_SIZE)

        field_df = create_mapping_df(service=SERVICE, table=TABLE)[
            ["Field", "SMC Table", "SMC Field"]
        ]

        dest_count = 0

        for batch_num in range(0, total_batches):
            log_start_process_batch(TABLE, batch_num, total_batches)

            offset = batch_num * BATCH_SIZE
            batch_size = (
                last_batch_size
                if is_last_batch(batch_num, total_batches)
                else BATCH_SIZE
            )

            df = mapping_data(offset, full_dump, batch_size, field_df)

            upsert_data_without_encrypt(
                df=df,
                table=TABLE,
                conflict_target=["memberId", "source_runno", "source_cardtypecode"],
            )
            total_rows = len(df)
            dest_count += total_rows

            log_success_process_batch(
                table=TABLE,
                batch_num=batch_num,
                total_batches=total_batches,
                batch_size=batch_size,
                total_records=total_rows,
            )

    except Exception as err:
        logger.error(f"An error has occured: {err}")
        raise err

    else:
        log_successfully_migrated_data(TABLE, dest_count)

    finally:
        save_migration_result(
            full_dump=full_dump,
            source_table="df_cardhist",
            table=TABLE,
            source_count=total_rows,
            dest_count=dest_count,
            created_at=start_time,
            incremental_date=get_incremental_date(LS_INCREMENTAL_DATE),
        )


# full dump dag
with DAG(
    ls_dag_name(table=TABLE, full_dump=True),
    description="Migrate data of Member Legacy Cobrand History Table",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "full_dump", "member_legacy_cobrand_history"],
) as dag:

    create_migration_result_table_task = PythonOperator(
        task_id=f"migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    mapping_data_task = PythonOperator(
        task_id=f"mapping_data",
        python_callable=migrate_cobrand_history_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    create_migration_result_table_task >> mapping_data_task
