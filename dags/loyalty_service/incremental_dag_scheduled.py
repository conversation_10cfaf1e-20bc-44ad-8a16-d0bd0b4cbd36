from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.sensors.time_sensor import TimeSensor
from datetime import datetime, timedelta, time
from common_helpers.database_services import PostgresHandler
from constants import TEMP_CONN_ID
from common_helpers.utils import create_migration_result_table
from loyalty_service.migrate_member import incremental_migrate_member_data
from loyalty_service.migrate_memberprofile import (
    incremental_migrate_member_profile_data,
)
from loyalty_service.migrate_staffprofile import (
    incremental_migrate_staff_profile_data,
)
from loyalty_service.migrate_mlcobrandhistory import (
    incremental_migrate_cobrand_history_data,
)
from loyalty_service.migrate_mltierhistory import incremental_migrate_tier_history_data
from loyalty_service.migrate_salestransaction import (
    incremental_migrate_sale_transaction_data,
    create_temp_sales_table,
    drop_temp_tables,
)
from loyalty_service.update_member_spending import (
    update_member_spending,
    create_temp_spending_table,
    drop_temp_member_spending_tables,
)
from loyalty_service.mark_deletedat_date import mark_deleted_at_in_member
from loyalty_service.migrate_refundsalestransaction import (
    incremental_migrate_refund_sale_transaction_data,
    create_temp_refund_table,
    drop_temp_refund_tables,
)

temp_postgres = PostgresHandler(conn_id=TEMP_CONN_ID)

with DAG(
    "loyalty_service_incremental_migration_scheduled",
    description="Loyalty Service Incremental Migration",
    schedule_interval="0 18 * * *",
    start_date=datetime(2025, 6, 22, 18, 0),
    catchup=False,
    tags=["loyalty_service", "incremental"],
) as dag:

    create_migration_result_table_task = PythonOperator(
        task_id=f"migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    create_temp_sales_table_task = PythonOperator(
        task_id=f"create_temp_sales_table_task",
        python_callable=create_temp_sales_table,
        op_kwargs={"is_full_dump": False},
    )

    create_temp_refund_table_task = PythonOperator(
        task_id=f"create_temp_refund_table_task",
        python_callable=create_temp_refund_table,
        op_kwargs={"is_full_dump": False},
    )

    create_temp_spending_table_task = PythonOperator(
        task_id=f"create_temp_spending_table_task",
        python_callable=create_temp_spending_table,
    )

    migrate_member_task = PythonOperator(
        task_id=f"migrate_member_data",
        python_callable=incremental_migrate_member_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    migrate_member_protile_task = PythonOperator(
        task_id=f"migrate_member_profile_data",
        python_callable=incremental_migrate_member_profile_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    migrate_staff_profile_task = PythonOperator(
        task_id="migrate_staff_profile_data",
        python_callable=incremental_migrate_staff_profile_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    migrate_cobrand_history_task = PythonOperator(
        task_id=f"migrate_cobrand_history_data",
        python_callable=incremental_migrate_cobrand_history_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    migrate_tier_history_task = PythonOperator(
        task_id=f"migrate_tier_history_data",
        python_callable=incremental_migrate_tier_history_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    migrate_sales_transaction_task = PythonOperator(
        task_id=f"migrate_sales_transaction_data",
        python_callable=incremental_migrate_sale_transaction_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    migrate_refund_sales_transaction_task = PythonOperator(
        task_id=f"migrate_refund_sales_transaction_data",
        python_callable=incremental_migrate_refund_sale_transaction_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    update_member_spending_task = PythonOperator(
        task_id="update_member_spending_task",
        python_callable=update_member_spending,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    mark_deleted_at_in_member_task = PythonOperator(
        task_id="mark_deleted_at_in_member_task",
        python_callable=mark_deleted_at_in_member,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    drop_temp_sales_trans_tables_task = PythonOperator(
        task_id=f"drop_temp_table_for_sales_trans_task",
        python_callable=drop_temp_tables,
        op_kwargs={"is_full_dump": False},
    )

    drop_temp_refund_sales_trans_tables_task = PythonOperator(
        task_id=f"drop_temp_table_for_refund_sales_trans_task",
        python_callable=drop_temp_refund_tables,
        op_kwargs={"is_full_dump": False},
    )

    drop_temp_member_spending_tables_task = PythonOperator(
        task_id=f"drop_temp_table_for_update_member_spending_task",
        python_callable=drop_temp_member_spending_tables,
    )

    wait_until_300am_bangkok_time = TimeSensor(
        task_id="wait_until_3am_bangkok_time",
        target_time=time(20, 0),  # 20:00 UTC = 3:00 AM UTC+7
    )

    wait_until_240am_bangkok_time = TimeSensor(
        task_id="wait_until_240am_bangkok_time",
        target_time=time(19, 40),  # 19:40 UTC = 2:40 AM UTC+7
    )

    remigrate_member_task = PythonOperator(
        task_id=f"rerun_migrate_member_data",
        python_callable=incremental_migrate_member_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    remigrate_memberprofile_task = PythonOperator(
        task_id=f"rerun_migrate_member_profile_data",
        python_callable=incremental_migrate_member_profile_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    create_migration_result_table_task >> migrate_member_task
    create_migration_result_table_task >> migrate_member_protile_task

    migrate_member_task >> migrate_staff_profile_task
    migrate_member_task >> migrate_cobrand_history_task
    migrate_member_task >> migrate_tier_history_task
    
    (
        migrate_member_task
        >> migrate_sales_transaction_task
        >> migrate_refund_sales_transaction_task
        >> create_temp_spending_table_task
        >> update_member_spending_task
        >> drop_temp_member_spending_tables_task
    )

    migrate_member_task >> mark_deleted_at_in_member_task
    migrate_sales_transaction_task >> drop_temp_sales_trans_tables_task
    migrate_refund_sales_transaction_task >> drop_temp_refund_sales_trans_tables_task

    (
        wait_until_240am_bangkok_time
        >> [remigrate_member_task, remigrate_memberprofile_task]
    )
    remigrate_member_task >> migrate_sales_transaction_task
    (
        wait_until_300am_bangkok_time
        >> create_temp_sales_table_task
        >> migrate_sales_transaction_task
    )
    (
        wait_until_300am_bangkok_time
        >> create_temp_refund_table_task
        >> migrate_refund_sales_transaction_task
    )

