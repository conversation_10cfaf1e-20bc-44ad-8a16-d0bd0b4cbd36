# script to mark deletedAt in Member table (Temp DB) where del_flag = 'X'
from airflow import DAG
from airflow.operators.python import <PERSON><PERSON><PERSON><PERSON>
from datetime import datetime, timedelta
from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MSSQLHandler
from common_helpers.utils import (
    is_last_batch,
    calc_last_batch_size,
    calc_total_batches,
    calc_offset,
    log_start_process_batch,
    log_success_process_batch,
    insert_migration_result,
    create_migration_result_table,
    get_df,
)
from constants import TEMP_CONN_ID, NEWMEMBER_CONN_ID, BATCH_SIZE
from common_helpers.logging import get_logger

logger = get_logger()


def get_total_rows() -> int:
    temp_postgres = PostgresHandler(conn_id=TEMP_CONN_ID)
    query = 'select count(id) from loyalty_service."Member"'
    return temp_postgres.extract_data(query)[0][0]


def get_member_ids_in_temp_db(offset: int, batch_size: int):
    """
    Get member ids in the temp database
    """
    temp_postgres = PostgresHandler(conn_id=TEMP_CONN_ID)
    query = f"""
        SELECT id FROM loyalty_service."Member" ORDER BY id 
        OFFSET {offset} ROWS FETCH NEXT {batch_size} ROWS ONLY;
    """
    df = get_df(query, temp_postgres)
    ids = df["id"].to_list()
    return ids


def find_inactive_member(member_ids: list[str]) -> list[str]:
    """
    Return inactive member's id
    """
    ids_in_temp = ", ".join([f"'{id}'" for id in member_ids])
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)

    query = f"""
    SELECT dm.member_id from df_member dm
    where dm.del_flag = 'X' and dm.member_id IN ({ids_in_temp}) 
    """
    df = get_df(query, mssql)
    df["member_id"] = df["member_id"].apply(lambda id: id.strip())
    inactive_member_ids = df["member_id"].to_list()

    logger.info(f"Inactive member's ID: {inactive_member_ids}")
    return inactive_member_ids


def mark_deleted_at(member_ids: list[str]) -> list[str]:
    """
    Add current datetime to deletedAt field to the given list of member ids
    Return: list of member id that marked deletedAt
    """
    ids = ", ".join([f"'{id}'" for id in member_ids])

    query = f"""
    SELECT id FROM loyalty_service."Member"
    WHERE id IN ({ids}) AND "deletedAt" IS NULL;
    """
    temp_postgres = PostgresHandler(conn_id=TEMP_CONN_ID)
    df = get_df(query, temp_postgres)
    marked_inactive_members = df["id"].to_list()

    query = f"""
    UPDATE loyalty_service."Member"
    SET 
        "deletedAt" = CURRENT_TIMESTAMP,
        "updatedAt" = CURRENT_TIMESTAMP
    WHERE id IN ({ids}) AND "deletedAt" is NULL;
    """
    temp_postgres.execute_query(query)

    logger.info(
        f"Successfully add current datetime to `deletedAt` where member's ids: {ids}"
    )

    return marked_inactive_members


def mark_deleted_at_in_member(**kwargs):
    """
    Update accumulateSpending data for all data in Member Table
    """
    start_time = datetime.now()  # keep record for start time

    total_rows = get_total_rows()
    total_batches = calc_total_batches(total_rows, BATCH_SIZE)
    last_batch_size = calc_last_batch_size(total_rows, BATCH_SIZE)

    inactive_members = []
    for batch_num in range(0, total_batches):

        log_start_process_batch(
            table="Member",
            batch_num=batch_num,
            total_batches=total_batches,
        )

        offset = calc_offset(batch_num, BATCH_SIZE)
        batch_size = (
            last_batch_size if is_last_batch(batch_num, total_batches) else BATCH_SIZE
        )

        # get member's id in the temp database
        member_ids = get_member_ids_in_temp_db(offset, batch_size)

        # get the inactive member
        inactive_member_ids = find_inactive_member(member_ids)

        # delete inactive member in temp db
        # n = len(inactive_member_ids)

        # declare for making sure that log_success_process_batch will access this variable
        marked_member_ids: list = []

        if len(inactive_member_ids) > 0:
            marked_member_ids = mark_deleted_at(inactive_member_ids)

            # keep record of amount and members
            inactive_members += marked_member_ids

        log_success_process_batch(
            table="Member",
            batch_num=batch_num,
            total_batches=total_batches,
            batch_size=batch_size,
            total_records=len(marked_member_ids),
        )

    # log result of `deletedAt` marking
    inactive_member_amount = len(inactive_members)
    logger.info(
        f"Successfully updated `deletedAt` column ({inactive_member_amount} inactive members)"
    )
    logger.info(f"Inactive members: {', '.join(inactive_members)}")

    # Push data to XCom
    ti = kwargs["ti"]
    data = {
        "source_count": total_rows,
        "dest_count": inactive_member_amount,
        "start_time": start_time,
    }
    ti.xcom_push(key="log_result_data", value=data)


def add_result_log(**kwargs) -> None:
    # Pull data from XCom
    ti = kwargs["ti"]
    data = ti.xcom_pull(
        task_ids="mark_deleted_at_task",
        key="log_result_data",
    )

    logger.info("data: data")

    source_count = data["source_count"]
    dest_count = data["dest_count"]
    start_time = data["start_time"]

    # put the log on migration result
    temp_postgres = PostgresHandler(conn_id=TEMP_CONN_ID)

    # calculate `validation_result`
    if source_count == 0 and dest_count == 0:
        validation_result = 100
    else:
        validation_result = dest_count / source_count * 100

    insert_migration_result(
        postgresql_handler=temp_postgres,
        dag_name="loyalty_service_mark_deleted_at_in_member_table",
        migration_type="FULL_DUMP",
        source_table="loyalty_service.Member",
        source_table_count=source_count,
        destination_table="Member",
        destination_table_count=dest_count,
        validation_type="COMPLETENESS",
        validation_result=validation_result,
        created_at=start_time,
    )


with DAG(
    "loyalty_service_mark_deleted_at_in_member_table",
    description="Mark deletedAt to inactive member in temp DB",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "mark_deletedat"],
) as dag:
    create_migration_result_table_task = PythonOperator(
        task_id=f"migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    mark_deleted_at_task = PythonOperator(
        task_id="mark_deleted_at_task",
        python_callable=mark_deleted_at_in_member,
        retries=3,
        retry_delay=timedelta(minutes=5),
        provide_context=True,
    )

    log_result_task = PythonOperator(
        task_id="log_result_task",
        python_callable=add_result_log,
        retries=3,
        retry_delay=timedelta(minutes=5),
        provide_context=True,
    )

    create_migration_result_table_task >> mark_deleted_at_task >> log_result_task
