# Loyalty Service
# debug duplicated keysearch in sales transaction


from airflow import DAG
from datetime import datetime
from collections import Counter
from airflow.operators.python import PythonOperator
from common_helpers.database_services import PostgresHandler
from common_helpers.utils import get_df
from constants import TEMP_CONN_ID


def debug_duplicated_keysearch_in_ls():
    query = """
        WITH MaxRowsLVHeader AS (
            SELECT 
                st.id,
                st."externalId",
                ROW_NUMBER() OVER (PARTITION BY st."externalId" ORDER BY st.id DESC) AS rn
            FROM loyalty_service."SalesTransaction" st 
        )
        select
            r."salesTransactionId",
            r."externalId"
        FROM MaxRowsLVHeader m
        JOIN loyalty_service."RefundSalesTransaction" r ON r."salesTransactionId" = m.id
        where rn > 1 
    """

    temp_postgres = PostgresHandler(TEMP_CONN_ID)
    df = get_df(query, temp_postgres)
    print("---------------------- Loyalty Service -------------------------------")
    print(
        """
        All SalesTransactionIDs that will be removed in SalesTransaction 
        and it will affect RefundSalesTransaction Table
        """
    )
    salestransaction_ids = df["salesTransactionId"].to_list()

    print(len(salestransaction_ids))
    # Count occurrences of each string
    counter = Counter(salestransaction_ids)
    # Find duplicates
    duplicates = [item for item, count in counter.items() if count > 1]
    print("Duplicates SalesTransactionID in RefundSalesTransaction:", duplicates)

    print(df["salesTransactionId"].to_list())
    print("Total SalesTransactionID: ", len(salestransaction_ids))
    print("Total `Unique` SalesTransactionID: ", len(df["salesTransactionId"].unique()))


def debug_duplicated_keysearch_in_ps():
    query = """
        WITH MaxRowsLVHeader AS (
            SELECT 
                st.id,
                st."externalId",
                ROW_NUMBER() OVER (PARTITION BY st."externalId" ORDER BY st.id DESC) AS rn
            FROM partner_service."SalesTransaction" st 
        )
        select
            r."salesTransactionId",
            r."externalId"
        FROM MaxRowsLVHeader m
        JOIN partner_service."RefundSalesTransaction" r ON CAST(r."salesTransactionId" AS TEXT) = m.id
        where rn > 1 
    """

    temp_postgres = PostgresHandler(TEMP_CONN_ID)
    df = get_df(query, temp_postgres)

    print("---------------------- Partner Service -------------------------------")
    print(
        """
        All SalesTransactionIDs that will be removed in SalesTransaction 
        and it will affect RefundSalesTransaction Table
        """
    )
    salestransaction_ids = df["salesTransactionId"].to_list()

    print(len(salestransaction_ids))
    # Count occurrences of each string
    counter = Counter(salestransaction_ids)
    # Find duplicates
    duplicates = [item for item, count in counter.items() if count > 1]
    print("Duplicates SalesTransactionID in RefundSalesTransaction:", duplicates)

    print(df["salesTransactionId"].to_list())
    print("Total SalesTransactionID: ", len(salestransaction_ids))
    print("Total `Unique` SalesTransactionID: ", len(df["salesTransactionId"].unique()))


def debug_duplicated_kerysearch():
    print("Debugging duplicated keysearch in loyalty service")
    debug_duplicated_keysearch_in_ls()

    print("Debugging duplicated keysearch in partner service")
    debug_duplicated_keysearch_in_ps()


# with DAG(
#     "loyalty_service_debug_duplicated_key_search",
#     description="Used for debug duplicated keysearch in sales transaction",
#     schedule_interval=None,
#     start_date=datetime(2023, 1, 1),
#     catchup=False,
#     tags=["loyalty_service", "debug_duplicated_key_search"],
# ) as dag:

#     debug_keysearch_task = PythonOperator(
#         task_id=f"debug_duplicated_keysearch",
#         python_callable=debug_duplicated_kerysearch,
#     )

#     debug_keysearch_task
