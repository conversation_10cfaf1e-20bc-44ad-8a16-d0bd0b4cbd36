import pandas as pd
from airflow import DAG
from common_helpers.utils import get_logger, get_df
from constants import ENCRYPT_KEY, TEMP_CONN_ID, EMAIL_MAPPING_PATH, BATCH_SIZE
from common_helpers.utils_member import email_cleansing_df
from common_helpers.utils import hash_and_encode
from datetime import datetime, timezone
from common_helpers.database_services import PostgresHandler
from airflow.operators.python import PythonOperator
from common_helpers.utils import calc_total_batches, log_start_process_batch, calc_offset, calc_last_batch_size, is_last_batch
from typing import Any


def get_domain(email: str | None) -> str | None:

    if email:
        if '@' in email:
            domain = email.split("@")[-1]
            return domain
        else:
            return None
    
    return None


def get_new_email(email: str, old_domain: str, new_domain: str) -> str:
    email = email.replace(old_domain, new_domain)
    return email


def update_query(gwl_no: str, new_email: str | None) -> str:
    return f"""
    update loyalty_service."Member"
        set email = {f"pgp_sym_encrypt('{new_email}', '{ENCRYPT_KEY}')" if new_email else "NULL"},
            hashEmail = '{hash_and_encode(new_email)}',
            updatedAt = '2025-06-17 07:00:00'
        where gwlNo = '{gwl_no}'
    """

def update_email(df, conn) -> None:
    logger = get_logger()
    logger.info(df.columns.to_list())

    for _, row in df.iterrows():
        query = update_query(row["gwlNo"], row["new_email"])

        with conn.cursor() as cursor:
            cursor.execute(query)

def clean_email_script(
    temp_postgres: PostgresHandler, 
    conn: Any, 
    cleansing_df: pd.DataFrame, 
    offset: int, 
    batch_size: int
) -> int:
    logger = get_logger()
    
    query = f"""
    select 
        "gwlNo",
        pgp_sym_decrypt("email"::bytea, '{ENCRYPT_KEY}')::text as "email"
    from loyalty_service."Member"
    where pgp_sym_decrypt("email"::bytea, '{ENCRYPT_KEY}')::text is not null and "deletedAt" is null
    order by "gwlNo"
    limit {batch_size} offset {offset};
    """

    df = get_df(query, temp_postgres)
    df["domain"] = df["email"].apply(get_domain)
    df["domain_lower_case"] = df["domain"].apply(lambda x: x.lower() if x else None)

    logger.info(f"there are emails these domain is not lower case: {len(df)} records")

    # filter out email that domain is already lower case
    df = df[df["domain"] != df["domain_lower_case"]]
    typo_domains = set(cleansing_df["Typo Domain"].to_list())

    # filter only domain that need to be cleaned and it's not cleaned yet
    df = df[df["domain_lower_case"].isin(typo_domains)]

    need_cleaned_count = len(df)
    logger.info(f"Found {need_cleaned_count} emails that need to be cleaned")

    df = df.merge(cleansing_df, left_on="domain_lower_case", right_on="Suggested Domain", how="left")
    assert need_cleaned_count == len(df)

    df["new_email"] = df.apply(lambda row: get_new_email(row["email"], row["domain"], row["domain_lower_case"]), axis=1)
    
    # update email
    update_email(df, conn) 

    return need_cleaned_count
    

def get_total_rows(temp_postgres: PostgresHandler):
    query = """
    select 
        count(email)
    from loyalty_service."Member"
    where email is not null and "deletedAt" is null    
    """
    total_rows = temp_postgres.extract_data(query)[0][0]
    return total_rows

def clean_email_via_batch():
    logger = get_logger()
    cleansing_df = email_cleansing_df(EMAIL_MAPPING_PATH)[["Typo Domain", "Suggested Domain"]]
    temp_postgres = PostgresHandler(conn_id=TEMP_CONN_ID)
    conn = temp_postgres.hook.get_conn()

    total_rows = get_total_rows(temp_postgres)
    total_batches = calc_total_batches(total_rows, BATCH_SIZE)
    last_batch_size = calc_last_batch_size(total_rows, BATCH_SIZE)

    total_cleaned_count = 0

    for batch_num in range(0, total_batches):
        log_start_process_batch(
            table="clean_email",
            batch_num=batch_num,
            total_batches=total_batches,
        )

        offset = calc_offset(batch_num, BATCH_SIZE)
        batch_size = (
            last_batch_size
            if is_last_batch(batch_num, total_batches)
            else BATCH_SIZE
        )
        need_cleaned_count = clean_email_script(temp_postgres, conn, cleansing_df, offset, batch_size)
        total_cleaned_count += need_cleaned_count
        logger.info(f"Need to clean {need_cleaned_count} emails")

    conn.commit()  
    logger.info(f"Total cleaned emails: {total_cleaned_count}")

# update email script dag
with DAG(
    "loyalty_service_clean_email_dag",
    description="Clean Email's Script",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "clean_email"],
) as dag:

    create_migration_result_table_task = PythonOperator(
        task_id=f"migration_result_table_task",
        python_callable=clean_email_via_batch,
    )