from datetime import datetime, timedelta
from re import T
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.trigger_rule import TriggerRule
from common_helpers.utils import create_validation_tables
from common_helpers.utils import get_validation_ls_dag_name
from loyalty_service.validators import (
    validate_sales_transaction_migration,
    validate_staff_profile_migration,
    validate_member_profile_migration,
    validate_member_migration,
    validate_member_spending,
    validate_mltierhistory_migration,
    validate_mlcobrandhistory_migration,
    adhoc_check_all_mlcobrandhistory,
)

# Member validation DAG
with DAG(
    get_validation_ls_dag_name("member", True),
    description="Validate Member full dump data migration",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "validation", "member"],
) as dag:

    create_validation_tables_task = PythonOperator(
        task_id="create_validation_tables_task",
        python_callable=create_validation_tables,
    )

    validate_migration_task = PythonOperator(
        task_id="validate_member_migration",
        python_callable=validate_member_migration,
        trigger_rule=TriggerRule.ALL_DONE,
    )

    validate_member_spending_task = PythonOperator(
        task_id="validate_member_spending_task",
        python_callable=validate_member_spending,
        trigger_rule=TriggerRule.ALL_DONE,
    )

    create_validation_tables_task >> validate_migration_task >> validate_member_spending_task

with DAG(
    get_validation_ls_dag_name("member", False),
    description="Validate Member incremental data migration",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "validation", "member"],
) as dag:

    create_validation_tables_task = PythonOperator(
        task_id="create_validation_tables_task",
        python_callable=create_validation_tables,
    )

    validate_migration_task = PythonOperator(
        task_id="validate_member_migration",
        python_callable=validate_member_migration,
        trigger_rule=TriggerRule.ALL_DONE,
        op_kwargs={"is_full_dump": False},
    )

    validate_member_spending_task = PythonOperator(
        task_id="validate_member_spending_task",
        python_callable=validate_member_spending,
        trigger_rule=TriggerRule.ALL_DONE,
    )

    create_validation_tables_task >> validate_migration_task >> validate_member_spending_task

# MemberProfile validation DAG
with DAG(
    get_validation_ls_dag_name("member_profile", True),
    description="Validate MemberProfile data migration",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "validation", "member_profile"],
) as dag:

    create_validation_tables_task = PythonOperator(
        task_id="create_validation_tables_task",
        python_callable=create_validation_tables,
    )

    validate_migration_task = PythonOperator(
        task_id="validate_member_profile_migration",
        python_callable=validate_member_profile_migration,
        trigger_rule=TriggerRule.ALL_DONE,
        op_kwargs={"is_full_dump": True},
    )

    create_validation_tables_task >> validate_migration_task


with DAG(
    get_validation_ls_dag_name("member_profile", False),
    description="Validate MemberProfile incremental data migration",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "validation", "member_profile"],
) as dag:

    create_validation_tables_task = PythonOperator(
        task_id="create_validation_tables_task",
        python_callable=create_validation_tables,
    )

    validate_migration_task = PythonOperator(
        task_id="validate_member_profile_migration",
        python_callable=validate_member_profile_migration,
        trigger_rule=TriggerRule.ALL_DONE,
        op_kwargs={"is_full_dump": False},
    )

    create_validation_tables_task >> validate_migration_task


# MemberLegacyTierHistory validation DAG
with DAG(
    get_validation_ls_dag_name("member_legacy_tier_history", True),
    description="Validate MemberLegacyTierHistory full dump data migration",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "validation", "member_legacy_tier_history"],
) as dag:

    create_validation_tables_task = PythonOperator(
        task_id="create_validation_tables_task",
        python_callable=create_validation_tables,
    )

    validate_migration_task = PythonOperator(
        task_id="validate_mltierhistory_migration",
        python_callable=validate_mltierhistory_migration,
        op_kwargs={"is_full_dump": True},
    )

    create_validation_tables_task >> validate_migration_task

with DAG(
    get_validation_ls_dag_name("member_legacy_tier_history", False),
    description="Validate MemberLegacyTierHistory incremental data migration",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "validation", "member_legacy_tier_history"],
) as dag:

    create_validation_tables_task = PythonOperator(
        task_id="create_validation_tables_task",
        python_callable=create_validation_tables,
    )

    validate_migration_task = PythonOperator(
        task_id="validate_mltierhistory_migration",
        python_callable=validate_mltierhistory_migration,
        op_kwargs={"is_full_dump": False},
    )

    create_validation_tables_task >> validate_migration_task


# MemberLegacyCobrandHistory validation DAG
with DAG(
    get_validation_ls_dag_name("member_legacy_cobrand_history", True),
    description="Validate MemberLegacyCobrandHistory full dump data migration",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "validation", "member_legacy_cobrand_history"],
) as dag:

    create_validation_tables_task = PythonOperator(
        task_id="create_validation_tables_task",
        python_callable=create_validation_tables,
    )

    validate_migration_task = PythonOperator(
        task_id="validate_member_legacy_cobrand_history_migration",
        python_callable=validate_mlcobrandhistory_migration,
        op_kwargs={"is_full_dump": True},
    )

    create_validation_tables_task >> validate_migration_task

with DAG(
    get_validation_ls_dag_name("member_legacy_cobrand_history", False),
    description="Validate MemberLegacyCobrandHistory incremental data migration",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "validation", "member_legacy_cobrand_history"],
) as dag:

    create_validation_tables_task = PythonOperator(
        task_id="create_validation_tables_task",
        python_callable=create_validation_tables,
    )

    validate_migration_task = PythonOperator(
        task_id="validate_member_legacy_cobrand_history_migration",
        python_callable=validate_mlcobrandhistory_migration,
        op_kwargs={"is_full_dump": False},
    )

    create_validation_tables_task >> validate_migration_task

# SalesTransaction validation DAG
with DAG(
    get_validation_ls_dag_name("sales_transaction", True),
    description="Validate SalesTransaction data migration",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "validation", "sales_transaction"],
) as dag:

    create_validation_tables_task = PythonOperator(
        task_id="create_validation_tables_task",
        python_callable=create_validation_tables,
    )

    validate_migration_task = PythonOperator(
        task_id="validate_sales_transaction_migration",
        python_callable=validate_sales_transaction_migration,
        trigger_rule=TriggerRule.ALL_DONE,
    )

    create_validation_tables_task >> validate_migration_task

# Adhoc DAG
with DAG(
    "check_data_correctness_in_mlcobrandhistory",
    description="Adhoc validation",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "validation", "adhoc"],
) as dag:

    check_all_mlcobrandhistory_task = PythonOperator(
        task_id="adhoc_check_all_mlcobrandhistory",
        python_callable=adhoc_check_all_mlcobrandhistory,
        trigger_rule=TriggerRule.ALL_DONE,
    )

    check_all_mlcobrandhistory_task


# validate all migration data in loyalty service
with DAG(
    get_validation_ls_dag_name("all", True),
    description="Validate all migration data",
    schedule_interval=None,
    start_date=datetime(2025, 6, 22, 21, 30),
    catchup=False,
    tags=["loyalty_service", "validation"],
) as dag:
    create_validation_tables_task = PythonOperator(
        task_id="create_validation_tables_task",
        python_callable=create_validation_tables,
    )

    validate_staff_profile_task = PythonOperator(
        task_id="validate_staff_profile_task",
        python_callable=validate_staff_profile_migration,
        retries=2,
        retry_delay=timedelta(minutes=2),
        trigger_rule=TriggerRule.ALL_DONE,
        op_kwargs={"is_full_dump": True},
    )

    validate_mlcobrandhistory_task = PythonOperator(
        task_id="validate_mlcobrandhistory_task",
        python_callable=validate_mlcobrandhistory_migration,
        retries=2,
        retry_delay=timedelta(minutes=2),
        trigger_rule=TriggerRule.ALL_DONE,
        op_kwargs={"is_full_dump": True},
    )

    validate_mltierhistory_task = PythonOperator(
        task_id="validate_mltierhistory_task",
        python_callable=validate_mltierhistory_migration,
        retries=2,
        retry_delay=timedelta(minutes=2),
        trigger_rule=TriggerRule.ALL_DONE,
        op_kwargs={"is_full_dump": True},
    )

    validate_member_profile_task = PythonOperator(
        task_id="validate_member_profile_task",
        python_callable=validate_member_profile_migration,
        retries=2,
        retry_delay=timedelta(minutes=2),
        trigger_rule=TriggerRule.ALL_DONE,
        op_kwargs={"is_full_dump": True},
    )

    validate_member_migration_task = PythonOperator(
        task_id="validate_member_migration_task",
        python_callable=validate_member_migration,
        retries=2,
        retry_delay=timedelta(minutes=2),
        trigger_rule=TriggerRule.ALL_DONE,
        op_kwargs={"is_full_dump": True},
    )

    validate_member_spending_task = PythonOperator(
        task_id="validate_member_spending_task",
        python_callable=validate_member_spending,
        retries=2,
        retry_delay=timedelta(minutes=2),
        trigger_rule=TriggerRule.ALL_DONE,
    )

    (
        create_validation_tables_task
        >> validate_staff_profile_task
        >> validate_member_migration_task
        >> validate_member_spending_task
        >> validate_mltierhistory_task
        >> validate_mlcobrandhistory_task
        >> validate_member_profile_task
    )


with DAG(
    get_validation_ls_dag_name("all", False),
    description="Validate all incremental migration data",
    schedule_interval="30 21 * * *",
    start_date=datetime(2025, 6, 23, 21, 30),
    catchup=False,
    tags=["loyalty_service", "validation"],
) as dag:
    create_validation_tables_task = PythonOperator(
        task_id="create_validation_tables_task",
        python_callable=create_validation_tables,
    )

    validate_staff_profile_task = PythonOperator(
        task_id="validate_staff_profile_task",
        python_callable=validate_staff_profile_migration,
        retries=2,
        retry_delay=timedelta(minutes=2),
        trigger_rule=TriggerRule.ALL_DONE,
        op_kwargs={"is_full_dump": False},
    )

    validate_mlcobrandhistory_task = PythonOperator(
        task_id="validate_mlcobrandhistory_task",
        python_callable=validate_mlcobrandhistory_migration,
        retries=2,
        retry_delay=timedelta(minutes=2),
        trigger_rule=TriggerRule.ALL_DONE,
        op_kwargs={"is_full_dump": False},
    )

    validate_mltierhistory_task = PythonOperator(
        task_id="validate_mltierhistory_task",
        python_callable=validate_mltierhistory_migration,
        retries=2,
        retry_delay=timedelta(minutes=2),
        trigger_rule=TriggerRule.ALL_DONE,
        op_kwargs={"is_full_dump": False},
    )

    validate_member_profile_task = PythonOperator(
        task_id="validate_member_profile_task",
        python_callable=validate_member_profile_migration,
        retries=2,
        retry_delay=timedelta(minutes=2),
        trigger_rule=TriggerRule.ALL_DONE,
        op_kwargs={"is_full_dump": False},
    )

    validate_member_migration_task = PythonOperator(
        task_id="validate_member_migration_task",
        python_callable=validate_member_migration,
        retries=2,
        retry_delay=timedelta(minutes=2),
        trigger_rule=TriggerRule.ALL_DONE,
        op_kwargs={"is_full_dump": False},
    )

    validate_member_spending_task = PythonOperator(
        task_id="validate_member_spending_task",
        python_callable=validate_member_spending,
        retries=2,
        retry_delay=timedelta(minutes=2),
        trigger_rule=TriggerRule.ALL_DONE,
    )

    (
        create_validation_tables_task
        >> validate_staff_profile_task
        >> validate_member_migration_task
        >> validate_member_spending_task
        >> validate_mltierhistory_task
        >> validate_mlcobrandhistory_task
        >> validate_member_profile_task
    )
    