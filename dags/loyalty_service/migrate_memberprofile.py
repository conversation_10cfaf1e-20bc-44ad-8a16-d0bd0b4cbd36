import traceback
import pandas as pd
import numpy as np
from airflow import DAG
from datetime import datetime, timedelta, timezone
from airflow.operators.python import PythonOperator
from constants import (
    BATCH_SIZE,
    NEWMEMBER_CONN_ID,
    LS_INCREMENTAL_DATE,
)
from common_helpers.utils import (
    row_count,
    rename_columns,
    generate_ulid,
    incremental_date_condition_for_member,
    get_last_successful_batch,
    set_last_successful_batch,
    reset_last_successful_batch,
    get_field_df,
    cast_nvarchar,
    create_migration_result_table,
    save_migration_result,
    destination_count,
    ls_dag_name,
    escape_single_quotes,
    upsert_encrypt_data,
    log_start_process_batch,
    log_success_process_batch,
    log_successfully_migrated_data,
    calc_total_batches,
    calc_offset,
    calc_last_batch_size,
    is_last_batch,
    get_incremental_date,
    get_df,
    clean_id_card,
)
from common_helpers.utils_memberprofile import (
    get_title_mapping,
    transform_tname,
    transform_ename,
    get_country_mapping,
    transform_address,
    business_mapping_dict,
    get_address_mapping_dict,
    get_address_df,
    safe_int_convert,
)
from common_helpers.database_services import MSSQLHandler
from common_helpers.logging import get_logger

SERVICE = "Loyalty Service"
TABLE = "MemberProfile"


logger = get_logger()

haddress_line = ["haddr_number", "haddr_bldg", "haddr_moo", "haddr_soi", "haddr_road"]

caddress_line = [
    "caddr_number",
    "caddr_bldg",
    "caddr_floor",
    "caddr_dept",
    "caddr_soi",
    "caddr_road",
]

maddress_line = ["maddr_number", "maddr_bldg", "maddr_moo", "maddr_soi", "maddr_road"]

haddress = [
    "haddr_subdistrict",
    "haddr_district",
    "haddr_city",
    "haddr_zip_code",
]


caddress = [
    "caddr_subdistrict",
    "caddr_district",
    "caddr_city",
    "caddr_zip_code",
]

maddress = [
    "maddr_subdistrict",
    "maddr_district",
    "maddr_city",
    "maddr_zip_code",
]


def get_daily_condition() -> str:
    return f"({incremental_date_condition_for_member('add_datetime', LS_INCREMENTAL_DATE)} OR {incremental_date_condition_for_member('update_datetime', LS_INCREMENTAL_DATE)})"


def mapping_data(
    offset: int,
    full_dump: bool,
    field_df: pd.DataFrame,
    title_mapping: dict[int],
    country_mapping: dict[str],
    address_mapping: tuple[dict],
    business_mapping: dict[int],
    batch_size: int = BATCH_SIZE,
) -> pd.DataFrame:

    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)

    # 1. df_member
    table = "df_member"
    thai_fields = ["tname"]
    source_fields = field_df[
        (field_df["SMC Table"] == table) & (~field_df["SMC Field"].isin(thai_fields))
    ]["SMC Field"].unique()

    # migrate conition
    migrate_condition = "del_flag = ''"
    if full_dump:
        migrate_condition = f"{migrate_condition}"
    else:
        migrate_condition = f"{migrate_condition} and {get_daily_condition()}"

    query = f"""
        SELECT country_code, staff_source, {cast_nvarchar("tname")}, {", ".join(source_fields)} FROM {table} 
        WHERE {migrate_condition} ORDER BY member_id
        OFFSET {offset} ROWS FETCH NEXT {batch_size} ROWS ONLY;
    """
    df = get_df(query, mssql)

    # Apply .str.strip() to all string columns
    df = df.map(lambda x: x.strip() if isinstance(x, str) else x)

    # No transform field
    df["id_card"] = df["id_card"].apply(clean_id_card)
    df["date_of_birth"] = df["date_of_birth"].apply(lambda d: d.date())
    df["passport_no"].replace("", None, inplace=True)

    # Transform field
    df["title_id"] = df["title_id"].apply(safe_int_convert).map(title_mapping) # unmatched values becomes Nan
    df["bussiness_id"] = df["bussiness_id"].astype(int)
    df["bussiness_id"] = df["bussiness_id"].map(business_mapping).replace(np.nan, None)
    
    df["country_code"] = df["country_code"].apply(lambda c: c.upper() if isinstance(c, str) else c)
    df["nationalityCode"] = df["country_code"].replace(country_mapping).replace("", "OTH").fillna("OTH")

    df[["firstNameTh", "middleNameTh", "lastNameTh"]] = df.apply(
        lambda row: pd.Series(transform_tname(row["tname"], row["nationalityCode"])),
        axis=1,
    )
    df[["firstName", "middleName", "lastName"]] = df.apply(
        lambda row: pd.Series(transform_ename(row["ename"], row["nationalityCode"])),
        axis=1,
    )

    member_ids = df["member_id"].to_list()
    address_df = get_address_df(member_ids)

    df[["addressLine", "subDistrict", "district", "province", "postalCode"]] = (
        df.apply(
            lambda row: pd.Series(
                transform_address(
                    row["member_id"],
                    row["staff_source"],
                    address_df,
                    address_mapping,
                )
            ),
            axis=1,
        )
    )

    df["id"] = [generate_ulid() for _ in range(len(df))]

    # drop unused column for data migration
    df.drop(["tname", "ename", "country_code", "staff_source"], axis=1, inplace=True)

    df.replace(np.nan, None, inplace=True)
    df = rename_columns(df, field_df)

    # replace ' with ''(excaped character when insert with encryption)
    df["firstName"] = df["firstName"].apply(escape_single_quotes)
    df["firstNameTh"] = df["firstNameTh"].apply(escape_single_quotes)
    df["middleName"] = df["middleName"].apply(escape_single_quotes)
    df["middleNameTh"] = df["middleNameTh"].apply(escape_single_quotes)
    df["lastName"] = df["lastName"].apply(escape_single_quotes)
    df["lastNameTh"] = df["lastNameTh"].apply(escape_single_quotes)
    df["passportNo"] = df["passportNo"].apply(escape_single_quotes)
    df["addressLine"] = df["addressLine"].apply(escape_single_quotes)

    # add current time to `updatedAt` field in utc timezone 
    now_utc = datetime.now(timezone.utc)
    df["updatedAt"] = now_utc

    return df


def save_to_destination_db(df: pd.DataFrame) -> None:
    """
    This function will perform an upsert of all data from the DataFrame
    into the destination table, including encrypting specific columns.
    """
    encrypt_columns = [
        "firstName",
        "firstNameTh",
        "middleName",
        "middleNameTh",
        "lastName",
        "lastNameTh",
        "cid",
        "passportNo",
        "dateOfBirth",
        "addressLine",
        "gender",
    ]

    hash_columns = [
        "addressLine",
        "cid",
        "firstName",
        "firstNameTh",
        "gender",
        "lastName",
        "lastNameTh",
        "middleName",
        "middleNameTh",
        "passportNo",
        "dateOfBirth",
    ]

    upsert_encrypt_data(
        df=df,
        table=TABLE,
        encrypt_columns=encrypt_columns,
        conflict_target=["memberId"],
        hash_columns=hash_columns,
    )


def get_total_rows(full_dump: bool) -> int:
    if full_dump:
        return row_count(
            "member_id",
            "df_member",
            f"del_flag = ''",
        )

    return row_count(
        "member_id",
        "df_member",
        f"del_flag = '' and {get_daily_condition()}",
    )


def migrate_member_profile_data():
    start_time = datetime.now()  # keep record for start time
    full_dump = True

    # create title mapping df
    title_mapping = get_title_mapping()
    country_mapping = get_country_mapping()
    address_mapping = get_address_mapping_dict()
    business_mapping = business_mapping_dict()

    field_df = get_field_df(SERVICE, TABLE)

    try:
        total_rows = get_total_rows(full_dump)
        total_batches = calc_total_batches(total_rows, BATCH_SIZE)
        last_batch_size = calc_last_batch_size(total_rows, BATCH_SIZE)

        last_successful_batch = get_last_successful_batch(TABLE)

        for batch_num in range(last_successful_batch + 1, total_batches):
            log_start_process_batch(TABLE, batch_num, total_batches)

            offset = calc_offset(batch_num, BATCH_SIZE)
            batch_size = (
                last_batch_size
                if is_last_batch(batch_num, total_batches)
                else BATCH_SIZE
            )

            # mapping data
            df = mapping_data(
                offset,
                full_dump,
                field_df,
                title_mapping,
                country_mapping,
                address_mapping,
                business_mapping,
                batch_size,
            )

            save_to_destination_db(df=df)

            set_last_successful_batch(TABLE, batch_num)
            log_success_process_batch(
                table=TABLE,
                batch_num=batch_num,
                total_batches=total_batches,
                batch_size=batch_size,
                total_records=len(df),
            )

        reset_last_successful_batch(TABLE)

    except Exception as err:
        logger.error(f"An error has occured: {err}")
        traceback.print_exc()
        raise err

    else:
        dest_count = destination_count(TABLE)
        log_successfully_migrated_data(table=TABLE, total_records=dest_count)

    finally:
        # save migration result
        dest_count = destination_count(TABLE)
        save_migration_result(
            full_dump=full_dump,
            source_table="df_member",
            table=TABLE,
            source_count=total_rows,
            dest_count=dest_count,
            created_at=start_time,
        )


def incremental_migrate_member_profile_data():
    start_time = datetime.now()  # keep record for start time
    full_dump = False

    field_df = get_field_df(SERVICE, TABLE)

    # create title mapping df
    title_mapping = get_title_mapping()
    country_mapping = get_country_mapping()
    address_mapping = get_address_mapping_dict()
    business_mapping = business_mapping_dict()

    try:
        total_rows = get_total_rows(full_dump)
        total_batches = calc_total_batches(total_rows, BATCH_SIZE)
        last_batch_size = calc_last_batch_size(total_rows, BATCH_SIZE)

        dest_count = 0

        for batch_num in range(0, total_batches):
            offset = calc_offset(batch_num, BATCH_SIZE)
            batch_size = (
                last_batch_size
                if is_last_batch(batch_num, total_batches)
                else BATCH_SIZE
            )

            # mapping data
            df = mapping_data(
                offset,
                full_dump,
                field_df,
                title_mapping,
                country_mapping,
                address_mapping,
                business_mapping,
                batch_size,
            )

            # save to destination db
            save_to_destination_db(df=df)

            total_records = len(df)
            dest_count += total_records

            log_success_process_batch(
                table=TABLE,
                batch_num=batch_num,
                total_batches=total_batches,
                batch_size=batch_size,
                total_records=total_records,
            )

    except Exception as err:
        logger.error(f"An error has occured: {err}")
        raise err

    finally:
        # save migration result
        save_migration_result(
            full_dump=full_dump,
            source_table="df_member",
            table=TABLE,
            source_count=total_rows,
            dest_count=dest_count,
            created_at=start_time,
            incremental_date=get_incremental_date(LS_INCREMENTAL_DATE),
        )


# full dump dag
with DAG(
    ls_dag_name(table=TABLE, full_dump=True),
    description="Migrate data from source to MemberProfile Table",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "full_dump", "member_profile"],
) as dag:

    create_migration_result_table_task = PythonOperator(
        task_id=f"migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    mapping_data_task = PythonOperator(
        task_id=f"mapping_memberprofile_data",
        python_callable=migrate_member_profile_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    create_migration_result_table_task >> mapping_data_task


# incremental dag
with DAG(
    ls_dag_name(table=TABLE, full_dump=False),
    description="Migrate data from source to MemberProfile Table",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "incremental", "member_profile"],
) as dag:

    create_migration_result_table_task = PythonOperator(
        task_id=f"migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    mapping_data_task = PythonOperator(
        task_id=f"mapping_memberprofile_data",
        python_callable=incremental_migrate_member_profile_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    create_migration_result_table_task >> mapping_data_task

