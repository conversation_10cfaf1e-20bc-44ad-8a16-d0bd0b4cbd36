from airflow import DAG
from datetime import datetime, timedelta
from airflow.operators.python import PythonOperator
from common_helpers.database_services import MSSQLHandler
from common_helpers.utils import get_df, convert_bangkok_to_utc
from constants import NEWMEMBER_CONN_ID


def debug_datetime():
    query = "select top 10 dm.member_id, dm.add_datetime from df_member dm order by dm.member_id"

    mssql = MSSQLHandler(NEWMEMBER_CONN_ID)
    df = get_df(query, mssql)
    print(df)

    df["add_datetime_new"] = df["add_datetime"].apply(convert_bangkok_to_utc)
    print(df)

    query = "select top 10 dc.member_id, dc.start_date, dc.end_date from df_cardhist dc order by dc.member_id"

    mssql = MSSQLHandler(NEWMEMBER_CONN_ID)
    df = get_df(query, mssql)
    print(df)

    df["start_date_new"] = df["start_date"].apply(convert_bangkok_to_utc)
    df["end_date_new"] = df["end_date"].apply(convert_bangkok_to_utc)
    print(df)


# with DAG(
#     "loyalty_service_debug_datetime",
#     description="Used for debug datetime",
#     schedule_interval=None,
#     start_date=datetime(2023, 1, 1),
#     catchup=False,
#     tags=["loyalty_service", "debug_datetime"],
# ) as dag:

#     debug_datetime_task = PythonOperator(
#         task_id=f"debug_datetime",
#         python_callable=debug_datetime,
#         retries=3,
#         retry_delay=timedelta(minutes=5),
#     )

#     debug_datetime_task
