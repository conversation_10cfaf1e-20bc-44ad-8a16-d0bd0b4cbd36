import pandas as pd
from airflow import DAG
from datetime import datetime, timedelta
from common_helpers.utils import (
    upsert_data_without_encrypt,
    create_migration_result_table,
    save_migration_result,
    destination_count,
    ls_dag_name,
    log_successfully_migrated_data,
)
from airflow.operators.python import PythonOperator
from common_helpers.logging import get_logger

TABLE = "StaffCompany"

# Master data for staff companies
STAFF_COMPANY_DATA = {
    "code": [
        "KPC",
        "KPD",
        "KPDC",
        "KPF1",
        "KPHM",
        "KPM",
        "KPMN",
        "KPS",
        "KPT",
        "V_AND_A",
        "X8",
    ],
    "name": [
        "KPC",
        "KPD",
        "KPDC",
        "KPF1",
        "KPH<PERSON>",
        "KPM",
        "KPMN",
        "KPS",
        "KPT",
        "V&A",
        "X8",
    ],
}


def migrate_staff_company_data() -> None:
    logger = get_logger()
    start_time = datetime.now()  # keep record for start time

    try:
        df = pd.DataFrame(STAFF_COMPANY_DATA)
        upsert_data_without_encrypt(df=df, table=TABLE, conflict_target=["code"])

    except Exception as err:
        logger.error(f"An error has occured: {err}")
        raise err

    else:
        log_successfully_migrated_data(TABLE, total_records=len(df))

    finally:
        save_migration_result(
            full_dump=True,
            source_table="Staff Master Data sheet",
            table=TABLE,
            source_count=len(df),
            dest_count=destination_count(TABLE),
            created_at=start_time,
        )

# full dump dag
with DAG(
    ls_dag_name(table=TABLE, full_dump=True),
    description="Migrate data from source to Staff Company Table",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "full_dump", "staff_company"],
) as dag:

    create_migration_result_table_task = PythonOperator(
        task_id=f"migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    mapping_data_task = PythonOperator(
        task_id=f"mapping_staff_company_data",
        python_callable=migrate_staff_company_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    create_migration_result_table_task >> mapping_data_task

