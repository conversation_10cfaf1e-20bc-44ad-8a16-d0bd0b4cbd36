from airflow import DAG
import numpy as np
from constants import SUBPROGRAM_PATH, TEMP_CONN_ID
from common_helpers.utils import (
    upsert_data_without_encrypt,
    create_migration_result_table,
    save_migration_result,
    destination_count,
    ls_dag_name,
    get_df,
)
from datetime import datetime, timedelta
from airflow.operators.python import PythonOperator
import pandas as pd
from common_helpers.logging import get_logger
from common_helpers.database_services import PostgresHandler

logger = get_logger()

SERVICE = "Loyalty Service"
RC_TABLE = "RegisterChannel"
UG_TABLE = "UpgradeGroup"
UR_TABLE = "UpgradeReason"
RL_TABLE = "RegisterLocation"

# 4 Tables: UpgradeGroup, UpgradeReason, RegisterChannel, RegisterLocation
TABLE = f"{RC_TABLE}_{RL_TABLE}_{UG_TABLE}_{UR_TABLE}"


def migrate_register_channel(data: list[str]) -> None:
    df = pd.DataFrame(data={"code": data})
    df["name"] = df["code"]
    upsert_data_without_encrypt(df=df, table=RC_TABLE, conflict_target=["code"])


def migrate_upgrade_group(data: list[str]):
    df = pd.DataFrame(data={"code": data})
    df["name"] = df["code"]
    upsert_data_without_encrypt(df=df, table=UG_TABLE, conflict_target=["code"])


def migrate_upgrade_reason(data: list[str]):
    df = pd.DataFrame(data={"code": data})
    df["name"] = df["code"]
    upsert_data_without_encrypt(df=df, table=UR_TABLE, conflict_target=["code"])


def migrate_register_location(data: list[str]):
    df = pd.DataFrame(data={"code": data})
    df["name"] = df["code"]
    upsert_data_without_encrypt(df=df, table=RL_TABLE, conflict_target=["code"])


def migrate_rc_ug_ur_data():
    start_time = datetime.now()  # keep record for start time

    try:
        df: pd.DataFrame = pd.read_csv(
            SUBPROGRAM_PATH,
            usecols=[
                "Registration Channel (GWL)",
                "Upgrade Group (GWL)",
                "Upgrade Reason (GWL)",
                "Registration Location (GWL)",
            ],
        )

        df.rename(
            columns={
                "Registration Channel (GWL)": "register_channel",
                "Upgrade Group (GWL)": "upgrade_group",
                "Upgrade Reason (GWL)": "upgrade_reason",
                "Registration Location (GWL)": "register_location",
            },
            inplace=True,
        )

        # Apply .str.strip() to all string columns
        df = df.map(lambda x: x.strip() if isinstance(x, str) else x)
        df["register_location"].replace(np.nan, "NA", inplace=True)

        register_channel = df["register_channel"].dropna().unique().tolist()
        upgrade_group = df["upgrade_group"].dropna().unique().tolist()
        upgrade_reason = df["upgrade_reason"].dropna().unique().tolist()
        register_location = df["register_location"].dropna().unique().tolist()

        logger.info(f"Start Migration: {RC_TABLE}, {UG_TABLE}, {UR_TABLE}, {RL_TABLE}")

        # migrate data
        migrate_register_channel(register_channel)
        migrate_upgrade_group(upgrade_group)
        migrate_upgrade_reason(upgrade_reason)
        migrate_register_location(register_location)

    except Exception as err:
        logger.error(f"An error has occured: {err}")
        raise err

    finally:
        # upsert logs into smc2temp_migration_result
        source_count = (
            len(register_channel)
            + len(upgrade_group)
            + len(upgrade_reason)
            + len(register_location)
        )
        tables = [RC_TABLE, UR_TABLE, UG_TABLE, RL_TABLE]

        dest_count = 0
        for table in tables:
            dest_count += destination_count(table)

        save_migration_result(
            full_dump=True,
            source_table="GWL attr level mapping sheet",
            source_count=source_count,
            table=TABLE,
            dest_count=dest_count,
            created_at=start_time,
        )


def validate_migration():
    """
    Validates the migration by comparing source and destination data.
    Checks if all records were migrated and if the data matches.
    """
    try:
        # Read source data
        source_df = pd.read_csv(
            SUBPROGRAM_PATH,
            usecols=[
                "Registration Channel (GWL)",
                "Upgrade Group (GWL)",
                "Upgrade Reason (GWL)",
                "Registration Location (GWL)",
            ],
            na_values=[],  # Don't treat any values as NA/NaN
            keep_default_na=False,  # Don't use default NA values
        )

        # Rename columns to match destination
        source_df.rename(
            columns={
                "Registration Channel (GWL)": "register_channel",
                "Upgrade Group (GWL)": "upgrade_group",
                "Upgrade Reason (GWL)": "upgrade_reason",
                "Registration Location (GWL)": "register_location",
            },
            inplace=True,
        )

        # Clean source data
        source_df = source_df.map(lambda x: x.strip() if isinstance(x, str) else x)

        # Get unique values from source
        source_rc = set(source_df["register_channel"].dropna().unique())
        source_ug = set(source_df["upgrade_group"].dropna().unique())
        source_ur = set(source_df["upgrade_reason"].dropna().unique())
        source_rl = set(source_df["register_location"].dropna().unique())

        # Get destination data using PostgresHandler
        temp_handler = PostgresHandler(conn_id=TEMP_CONN_ID)
        
        # Query destination tables with schema name
        dest_rc = set(pd.DataFrame(temp_handler.extract_data(f'SELECT code FROM "loyalty_service"."{RC_TABLE}"'), columns=['code'])["code"])
        dest_ug = set(pd.DataFrame(temp_handler.extract_data(f'SELECT code FROM "loyalty_service"."{UG_TABLE}"'), columns=['code'])["code"])
        dest_ur = set(pd.DataFrame(temp_handler.extract_data(f'SELECT code FROM "loyalty_service"."{UR_TABLE}"'), columns=['code'])["code"])
        dest_rl = set(pd.DataFrame(temp_handler.extract_data(f'SELECT code FROM "loyalty_service"."{RL_TABLE}"'), columns=['code'])["code"])

        # Validate each table
        validation_results = {
            RC_TABLE: {
                "source_count": len(source_rc),
                "dest_count": len(dest_rc),
                "missing_in_dest": source_rc - dest_rc,
                "extra_in_dest": dest_rc - source_rc,
                "is_valid": source_rc == dest_rc
            },
            UG_TABLE: {
                "source_count": len(source_ug),
                "dest_count": len(dest_ug),
                "missing_in_dest": source_ug - dest_ug,
                "extra_in_dest": dest_ug - source_ug,
                "is_valid": source_ug == dest_ug
            },
            UR_TABLE: {
                "source_count": len(source_ur),
                "dest_count": len(dest_ur),
                "missing_in_dest": source_ur - dest_ur,
                "extra_in_dest": dest_ur - source_ur,
                "is_valid": source_ur == dest_ur
            },
            RL_TABLE: {
                "source_count": len(source_rl),
                "dest_count": len(dest_rl),
                "missing_in_dest": source_rl - dest_rl,
                "extra_in_dest": dest_rl - source_rl,
                "is_valid": source_rl == dest_rl
            }
        }

        # Log validation results
        logger.info("=== Validation Results ===")
        for table, results in validation_results.items():
            logger.info("")
            logger.info(f"Results for {table}:")
            logger.info(f"Source count: {results['source_count']}")
            logger.info(f"Destination count: {results['dest_count']}")
            
            if results['missing_in_dest']:
                logger.warning(f"Missing in destination: {sorted(list(results['missing_in_dest']))}")
            if results['extra_in_dest']:
                logger.warning(f"Extra in destination: {sorted(list(results['extra_in_dest']))}")
            
            validation_status = "PASSED" if results['is_valid'] else "FAILED"
            logger.info(f"Validation status: {validation_status}")

        # Log overall summary
        failed_tables = [table for table, results in validation_results.items() if not results['is_valid']]
        if failed_tables:
            logger.warning(f"Validation failed for tables: {', '.join(failed_tables)}")
        else:
            logger.info("All validations passed successfully!")

        return True

    except Exception as err:
        logger.error(f"An error occurred during validation: {err}")
        return False


with DAG(
    ls_dag_name(table=TABLE, full_dump=True),
    description=f"Migrate data of {RC_TABLE}, {RL_TABLE}, {UG_TABLE} and {UR_TABLE}",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=[
        "loyalty_service",
        "full_dump",
        "upgrade_group",
        "upgrade_reason",
        "register_channel",
        "register_location",
    ],
) as dag:

    create_migration_result_table_task = PythonOperator(
        task_id=f"migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    migrate_rc_ug_ur_rl_task = PythonOperator(
        task_id=f"migrate_rc_ug_ur_rl",
        python_callable=migrate_rc_ug_ur_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    validate_migration_task = PythonOperator(
        task_id="validate_migration",
        python_callable=validate_migration,
        retries=1,
        retry_delay=timedelta(minutes=5),
    )

    create_migration_result_table_task >> migrate_rc_ug_ur_rl_task >> validate_migration_task
