from airflow import DAG
from airflow.operators.python import PythonOperator
from migration_utils.migration_ops import fetch_transform_insert_interdb


def temp_sql_run_to_table():
    """
    This function is a placeholder for the actual SQL run logic.
    It should be replaced with the actual implementation.
    """
    # Example SQL run logic
    # Replace this with your actual SQL execution code
    fetch_transform_insert_interdb('partner_service."SalesTransactionItem"')
    # fetch_transform_insert_interdb('partner_service."SalesTransactionWalletActivity"')






    print("Running SQL to do some operation to table")



with DAG(
    dag_id="deposit_table_run_dag",
    start_date=None,
    schedule_interval=None,
    catchup=False,
    tags=["temp","sql_run"],
) as dag:
    

    temp_sql_run = PythonOperator(
        task_id="run_specific_sql_to_table",
        python_callable=temp_sql_run_to_table,
    )

    temp_sql_run