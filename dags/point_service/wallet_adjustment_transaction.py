import json
import math
import os
import pytz
import threading
from concurrent.futures import as_completed, ThreadPoolExecutor
from datetime import datetime, timedelta

from psycopg2.extensions import connection as postgres_connection

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.mapper import CSVMapper
from common_helpers.utils import get_query_offsets, insert_migration_result
from common_helpers.logging import get_logger

logger = get_logger()


class WalletAdjustmentTransaction:
    def __init__(
        self,
        batch_size: int,
        executor_max_workers: int,
        mssql_handler: MSSQLHandler,
        postgresql_handler: PostgresHandler,
        incremental_query_date: str = None,
    ) -> None:
        self.batch_size = batch_size
        self.executor_max_workers = executor_max_workers
        self.incremental_query_date = incremental_query_date
        self.wallet_mapping_file_mapper = CSVMapper(
            file_path=os.path.join(
                "dags",
                "data",
                "wallet_mapping.csv",
            ),
            key="Value Code",
            sub_keys=["WalletCode"],
        )
        self.destination_insert_query = """
            INSERT INTO "point_service"."WalletAdjustmentTransaction" (
                "id",
                "memberId",
                "walletCode",
                "reasonCode",
                "amount",
                "type",
                "remark",
                "createdBy",
                "createdAt"
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT ("id") DO NOTHING;
        """

        self.mssql_handler = mssql_handler
        self.postgresql_handler = postgresql_handler

    def get_incremental_query_condition(self) -> str:
        """
        Generates a query condition for incremental migration, with specific date supported.

        Args:
            None

        Returns:
            str: A query condition string.
        """
        if self.incremental_query_date is None:
            return "lvh.DocDate >= DATEADD(DAY, -1, CAST(CAST(GETDATE() AS DATE) AS DATETIME)) AND lvh.DocDate < CAST(CAST(GETDATE() AS DATE) AS DATETIME)"

        return f"lvh.DocDate >= CAST(CAST({self.incremental_query_date} AS DATE) AS DATETIME) AND lvh.DocDate < DATEADD(DAY, 1, CAST(CAST({self.incremental_query_date} AS DATE) AS DATETIME))"

    def get_count_query_string(self, is_full_dump: bool = True) -> str:
        """
        Generates a query string for counting total records for both full dump and
        incremental migration.

        Args:
            is_full_dump (bool): Migration type.

        Returns:
            str: A query string.
        """
        return f"""
            SELECT
                COUNT(*)
            FROM (
                SELECT
                    lvh.LVHeaderKey
                FROM
                    LVHeader lvh
                    LEFT JOIN LVHeaderExtend lvhe WITH (NOLOCK) ON lvhe.LVHeaderKey = lvh.LVHeaderKey
                    JOIN LVTrans lvt ON lvt.LVHeaderKey = lvh.LVHeaderKey
                    JOIN LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
                WHERE
                    lvt.MovementCode IN ('ADJ', 'ADJOUT')
                    AND lvd.ValueCode IN (
                        'AP001',
                        'EP001',
                        'EP002',
                        'EP003',
                        'EP004',
                        'EP005',
                        'EP006',
                        'EP007',
                        'EP008',
                        'EP009',
                        'EP010',
                        'KPC01',
                        'KPO02',
                        'CR001',
                        'PT001'
                    ){" AND lvh.DocDate < CAST(CAST(GETDATE() AS DATE) AS DATETIME)" if is_full_dump else f" AND {self.get_incremental_query_condition()}"}
                GROUP BY
                    lvh.LVHeaderKey,
                    lvh.AddDT,
                    lvt.MovementCode,
                    lvd.ValueCode,
                    lvd.LVNumber
            ) AS total_rows;
        """

    def get_select_query_string(self, is_full_dump: bool) -> str:
        """
        Generates a query string for selecting records from source for both full dump and
        incremental migration.

        Args:
            is_full_dump (bool): Migration type.

        Returns:
            str: A query string.
        """
        return f"""
            SELECT
                CAST(lvh.LVHeaderKey AS VARCHAR(20)) + '_' + CAST(lvt.MovementCode AS VARCHAR(10)) + '_' + CAST(lvd.ValueCode AS VARCHAR(10)) AS id,
                lvd.LVNumber AS memberId,
                lvd.ValueCode AS valueCode,
                SUM(lvt.Amount) AS amount,
                CASE
                    WHEN lvt.MovementCode = 'ADJ' THEN 'ADD'
                    WHEN lvt.MovementCode = 'ADJOUT' THEN 'DEDUCT'
                    ELSE ''
                END AS type,
                CASE
                    WHEN lvt.MovementCode = 'CVIN' THEN 'Convert In'
                    WHEN lvt.MovementCode = 'CVOUT' THEN 'Convert Out'
                    ELSE MAX(CAST(isnull (lvhe.Remark, '') AS NVARCHAR(max)))
                END AS remark,
                lvh.AddUser as addUser,
                DATEADD (HOUR, -7, lvh.AddDT) AS createdAt
            FROM
                LVHeader lvh
                LEFT JOIN LVHeaderExtend lvhe WITH (NOLOCK) ON lvhe.LVHeaderKey = lvh.LVHeaderKey
                JOIN LVTrans lvt ON lvt.LVHeaderKey = lvh.LVHeaderKey
                JOIN LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
            WHERE
                lvt.MovementCode IN ('ADJ', 'ADJOUT')
                AND lvd.ValueCode IN (
                    'AP001',
                    'EP001',
                    'EP002',
                    'EP003',
                    'EP004',
                    'EP005',
                    'EP006',
                    'EP007',
                    'EP008',
                    'EP009',
                    'EP010',
                    'KPC01',
                    'KPO02',
                    'CR001',
                    'PT001'
                ){" AND lvh.DocDate < CAST(CAST(GETDATE() AS DATE) AS DATETIME)" if is_full_dump else f" AND {self.get_incremental_query_condition()}"}
            GROUP BY
                lvh.LVHeaderKey,
                lvh.AddUser,
                lvh.AddDT,
                lvt.MovementCode,
                lvd.ValueCode,
                lvd.LVNumber
            ORDER BY
                id
            OFFSET
                %s ROWS
            FETCH NEXT
                %s ROWS ONLY;
        """

    def transform_record(
        self,
        record: tuple,
    ) -> tuple:
        """
        Transform a record queried from source table to destination table schema format.

        Args:
            record (tuple): A record queried from source table.

        Returns:
            tuple: A record in destination table schema format.
        """
        (id, memberId, valueCode, amount, type, remark, addUser, createdAt) = record

        walletCode = self.wallet_mapping_file_mapper.get_value(valueCode)["WalletCode"]
        reasonCode = "MIGRATED_FROM_SMC"
        createdBy = {
            "id": 1,
            "name": "migration",
            "email": "<EMAIL>",
            "AddUser": addUser,
        }

        return (
            id,
            memberId,
            walletCode,
            reasonCode,
            amount,
            type,
            remark,
            json.dumps(createdBy),
            createdAt,
        )

    def insert_batch_to_destination(
        self,
        connection: postgres_connection,
        batch: list[tuple],
    ) -> None:
        """
        Insert a batch to destination table.

        Args:
            connection (postgres_connection): A PostgreSQL connection.
            batch (list[tuple]): A batch to process.

        Returns:
            None
        """
        self.postgresql_handler.execute_with_rollback(
            connection, self.destination_insert_query, batch
        )

    def process_batch(
        self,
        connection: postgres_connection,
        batch: list[tuple],
        batch_no: int,
        total_batches: int,
        total_records: int,
        is_full_dump: bool,
    ) -> None:
        """
        Transform queried result from source table and insert them to a new table.

        Args:
            connection (postgres_connection): A PostgreSQL connection.
            batch (list[tuple]): A batch to process.
            batch_no (int): The current batch's number, used only for logging.
            total_batches (int): The total number of batches to process, used only for logging.
            total_records (int): The total number of records being processed, used for batch tracking.
            is_full_dump (bool): Whether this is a full dump migration vs incremental.

        Returns:
            None
        """
        logger.info(
            f"started transforming and inserting batch {batch_no}/{total_batches} (size {len(batch)})..."
        )
        transformed_batch = [
            self.transform_record(
                record=record,
            )
            for record in batch
        ]
        self.insert_batch_to_destination(
            connection=connection,
            batch=transformed_batch,
        )
        logger.info(
            f"successfully transformed and inserted batch {batch_no}/{total_batches} (size {len(batch)})."
        )

        if is_full_dump:
            self.postgresql_handler.update_batch_tracker(
                connection=connection,
                service_name="point_service",
                table_name="WalletAdjustmentTransaction",
                total_records=total_records,
                batch_no=batch_no,
            )

    def migrate(
        self,
        count_query_string: str,
        select_query_string: str,
        is_full_dump: bool = True,
    ) -> None:
        """
        The main function for WalletAdjustmentTransaction migration flow.

        Args:
            count_query_string (str): The query to count the number of records to migrate.
            select_query_string (str): The query to select records to migrate.
            is_full_dump (bool): Whether this is a full dump migration vs incremental.

        Returns:
            None
        """
        created_at = datetime.now()
        incremental_date = (
            None
            if is_full_dump
            else (
                self.incremental_query_date
                if self.incremental_query_date is not None
                else (datetime.now(pytz.timezone('Asia/Bangkok')).date() - timedelta(days=1)).strftime("%Y-%m-%d")
            )
        )

        mssql_connection = self.mssql_handler.hook.get_conn()
        postgresql_connection = self.postgresql_handler.hook.get_conn()

        tracker = (
            self.postgresql_handler.get_latest_batch_info(
                connection=postgresql_connection,
                service_name="point_service",
                table_name="WalletAdjustmentTransaction",
            )
            if is_full_dump
            else None
        )

        total_records = (
            self.mssql_handler.get_table_total_records(count_query_string)
            if tracker is None
            else tracker[0]
        )
        total_batches = math.ceil(total_records / self.batch_size)
        offsets = get_query_offsets(
            total_records=total_records,
            batch_size=self.batch_size,
            starting_offset=(
                0 if tracker is None else (tracker[1] - 1) * self.batch_size
            ),
        )
        completed_batches = tracker[2] if tracker is not None else []

        is_migration_succeeded = False

        try:
            futures = []

            batch_generator = self.mssql_handler.generate_batches(
                connection=mssql_connection,
                query_string=select_query_string,
                total_records=total_records,
                batch_size=self.batch_size,
                offsets=offsets,
                completed_batches=completed_batches,
            )

            with ThreadPoolExecutor(max_workers=self.executor_max_workers) as executor:
                semaphore = threading.Semaphore(self.executor_max_workers)

                while True:
                    semaphore.acquire()

                    try:
                        batch, batch_no = next(batch_generator)
                    except StopIteration:
                        break

                    future = executor.submit(
                        self.process_batch,
                        postgresql_connection,
                        batch,
                        batch_no,
                        total_batches,
                        total_records,
                        is_full_dump,
                    )
                    futures.append(future)
                    future.add_done_callback(lambda _: semaphore.release())

                for future in as_completed(futures):
                    future.result()

            logger.info(
                f"succesfully processed {total_records} records into WalletAdjustmentTransaction"
            )

            logger.info(f"started cleaning up batch tracker...")
            self.postgresql_handler.cleanup_batch_tracker(
                connection=postgresql_connection,
                service_name="point_service",
                table_name="WalletAdjustmentTransaction",
            )
            logger.info(f"finished cleaning up batch tracker.")

            is_migration_succeeded = True

        except Exception as error:
            logger.error(f"an error has occured: {error}")

            if postgresql_connection:
                logger.info("a postgresql connection is found, rolling back...")
                postgresql_connection.rollback()
                logger.info("successfully rolled back.")

            raise error
        finally:
            logger.info("started inserting migration result log...")
            if is_migration_succeeded:
                insert_migration_result(
                    postgresql_handler=self.postgresql_handler,
                    dag_name=f"point_service_{'full_dump' if is_full_dump else 'incremental'}_migration_wallet_adjustment_transaction",
                    migration_type="FULL_DUMP" if is_full_dump else "INCREMENTAL",
                    source_table="LVHeader",
                    source_table_count=total_records,
                    destination_table="WalletAdjustmentTransaction",
                    destination_table_count=total_records,
                    validation_type="COMPLETENESS",
                    validation_result=100,
                    created_at=created_at,
                    incremental_date=incremental_date,
                )
            else:
                tracker = self.postgresql_handler.get_latest_batch_info(
                    connection=postgresql_connection,
                    service_name="point_service",
                    table_name="WalletAdjustmentTransaction",
                )

                destination_table_count = (
                    0
                    if tracker is None
                    else (
                        ((tracker[1] - 1) * self.batch_size)
                        if (tracker[1] - 1) * self.batch_size <= total_records
                        else total_records
                    )
                )

                total_processed = (
                    0 if tracker is None else len(tracker[2]) * self.batch_size
                )

                if tracker is not None and total_batches in tracker[2]:
                    total_processed -= self.batch_size + total_records % self.batch_size

                insert_migration_result(
                    postgresql_handler=self.postgresql_handler,
                    dag_name=f"point_service_{'full_dump' if is_full_dump else 'incremental'}_migration_wallet_adjustment_transaction",
                    migration_type="FULL_DUMP" if is_full_dump else "INCREMENTAL",
                    source_table="LVHeader",
                    source_table_count=total_records,
                    destination_table="WalletAdjustmentTransaction",
                    destination_table_count=destination_table_count,
                    validation_type="COMPLETENESS",
                    validation_result=(
                        0 if tracker is None else total_processed / total_records * 100
                    ),
                    created_at=created_at,
                    incremental_date=incremental_date,
                )
            logger.info("finished inserting migration result log.")

            if mssql_connection:
                mssql_connection.close()
            if postgresql_connection:
                postgresql_connection.close()

    def migrate_full_dump(self) -> None:
        """
        The main function for WalletAdjustmentTransaction full dump migration flow.

        Args:
            None

        Returns:
            None
        """
        full_dump_count_query_string = self.get_count_query_string(is_full_dump=True)
        full_dump_select_query_string = self.get_select_query_string(is_full_dump=True)

        self.migrate(
            count_query_string=full_dump_count_query_string,
            select_query_string=full_dump_select_query_string,
            is_full_dump=True,
        )

    def migrate_incremental(self) -> None:
        """
        The main function for WalletAdjustmentTransaction incremental migration flow.

        Args:
            None

        Returns:
            None
        """
        incremental_count_query_string = self.get_count_query_string(is_full_dump=False)
        incremental_select_query_string = self.get_select_query_string(
            is_full_dump=False
        )

        self.migrate(
            count_query_string=incremental_count_query_string,
            select_query_string=incremental_select_query_string,
            is_full_dump=False,
        )
