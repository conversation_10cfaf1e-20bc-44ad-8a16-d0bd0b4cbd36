import os
from datetime import datetime, timezone

import pandas as pd
from airflow.exceptions import AirflowException

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.logging import get_logger
from common_helpers.utils import insert_validation_logs
from constants import (
    RECORD_COUNTS_VALIDATION,
    RECORD_COUNTS_VALIDATION_ERROR,
    SAMPLE_DATA_VALIDATION,
    SAMPLE_DATA_VALIDATION_ERROR,
)

logger = get_logger()


class WalletBalanceValidation:
    def __init__(
        self,
        mssql_handler: MSSQLHandler,
        postgresql_handler: PostgresHandler,
    ) -> None:
        self.mssql_handler = mssql_handler
        self.postgresql_handler = postgresql_handler
        self.errors = []
        current_utc = datetime.now(timezone.utc)
        formatted_date = current_utc.strftime("%Y%m%d")
        source_table = f"snapshot_lv_data_{formatted_date}"

        wallet_code = self.get_wallet_code_mapping_case_statement()

        self.source_count_query_string = f"""
            SELECT
                COUNT(*)
            FROM
                {source_table}
            WHERE
                ValueCode IN (
                    'AP001',
                    'EP001',
                    'EP002',
                    'EP003',
                    'EP004',
                    'EP005',
                    'EP006',
                    'EP007',
                    'EP008',
                    'EP009',
                    'EP010',
                    'KPC01',
                    'KPO02',
                    'CR001',
                    'PT001'
                );
        """
        self.source_select_query_string = f"""
            SELECT
                LVMainKey AS id,
                LVNumber AS member_id,
                {wallet_code} AS wallet_code,
                CASE 
                    WHEN [ExpireDate] = CAST([ExpireDate] AS DATE)
                    THEN DATEADD(
                        MILLISECOND,
                        61199999, 
                        CAST([ExpireDate] AS DATETIME2(3))
                    )
                    ELSE DATEADD (HOUR, -7, [ExpireDate])
                END AS expired_at,
                LVValue AS amount
            FROM
                {source_table}
            WHERE
                ValueCode IN (
                    'AP001',
                    'EP001',
                    'EP002',
                    'EP003',
                    'EP004',
                    'EP005',
                    'EP006',
                    'EP007',
                    'EP008',
                    'EP009',
                    'EP010',
                    'KPC01',
                    'KPO02',
                    'CR001',
                    'PT001'
                )
        """

    def get_wallet_code_mapping_case_statement(self):
        df = pd.read_csv(
            os.path.join(
                "dags",
                "data",
                "wallet_mapping.csv",
            )
        )

        result_dict = df.groupby("WalletCode")["Value Code"].apply(list).to_dict()

        case_statement = "CASE"

        for key, values in result_dict.items():
            values_str = ", ".join(f"'{value}'" for value in values)
            case_statement += f" WHEN ValueCode IN ({values_str}) THEN '{key}'"

        case_statement += " END"

        return case_statement

    def validate_record_counts(self):
        destination_count_query_string = """
            SELECT COUNT(*) FROM point_service."WalletBalance";
        """

        source_count = self.mssql_handler.get_table_total_records(
            self.source_count_query_string,
        )

        postgresql_connection = self.postgresql_handler.hook.get_conn()

        with postgresql_connection.cursor() as cursor:
            cursor.execute(destination_count_query_string)
            destination_count = cursor.fetchone()[0]

        if source_count != destination_count:
            logger.warning(
                f"count mismatch: source has {source_count} records, but destination has {destination_count} records"
            )
            self.errors.append(RECORD_COUNTS_VALIDATION_ERROR)
        else:
            logger.info(f"count validation passed: {source_count} records match")

    def validate_sample_data(self):
        source_sample_query_string = f"""
            SELECT TOP 100000 * FROM ({self.source_select_query_string}) AS sample_data ORDER BY NEWID()
        """

        source_records = self.mssql_handler.extract_data(source_sample_query_string)

        source_record_ids = [record[0] for record in source_records]

        destination_sample_query_string = f"""
            SELECT
                "id",
                "memberId",
                "walletCode",
                "expiredAt",
                "amount"
            FROM point_service."WalletBalance" WHERE id IN ({", ".join(["'" + str(id) + "'" for id in source_record_ids])})
        """

        postgresql_connection = self.postgresql_handler.hook.get_conn()

        with postgresql_connection.cursor() as cursor:
            cursor.execute(destination_sample_query_string)
            destination_records = cursor.fetchall()

        source_dict = {
            str(record[0]): {
                "member_id": record[1],
                "wallet_code": record[2],
                "expired_at": record[3],
                "amount": record[4],
            }
            for record in source_records
        }

        destination_dict = {
            record[0]: {
                "member_id": record[1],
                "wallet_code": record[2],
                "expired_at": record[3],
                "amount": record[4],
            }
            for record in destination_records
        }

        source_ids = set(source_dict.keys())
        destination_ids = set(destination_dict.keys())
        missing_ids = source_ids - destination_ids
        extra_ids = destination_ids - source_ids

        data_mismatches = []
        common_ids = source_ids.intersection(destination_ids)

        for record_id in common_ids:
            source_data = source_dict[record_id]
            dest_data = destination_dict[record_id]

            mismatches = []

            fields_to_compare = ["member_id", "wallet_code", "expired_at", "amount"]

            for field in fields_to_compare:
                if source_data[field] != dest_data[field]:
                    mismatches.append(
                        f"{field}: source={source_data[field]}, dest={dest_data[field]}"
                    )

            if mismatches:
                data_mismatches.append((record_id, mismatches))

        if missing_ids:
            logger.warning(
                f"found {len(missing_ids)} records missing in destination table"
            )
            logger.warning(f"missing record IDs: {', '.join(sorted(missing_ids))}")
        else:
            logger.info("no missing records found in destination table")

        if extra_ids:
            logger.warning(f"found {len(extra_ids)} extra records in destination table")
            logger.warning(f"extra record IDs: {', '.join(sorted(extra_ids))}")
        else:
            logger.info("no extra records found in destination table")

        if data_mismatches:
            logger.warning(f"found {len(data_mismatches)} records with data mismatches")
            for record_id, mismatches in data_mismatches:
                logger.warning(f"data mismatches for record {record_id}:")
                for mismatch in mismatches:
                    logger.warning(f"  - {mismatch}")
        else:
            logger.info("no data mismatches found in matching records")

        if not missing_ids and not extra_ids and not data_mismatches:
            logger.info(f"sample data validation passed: all sample records match")
        else:
            self.errors.append(SAMPLE_DATA_VALIDATION_ERROR)

    def validate(self, temp_db_handler: PostgresHandler, service: str) -> None:
        logger.info("started record counts validation...")
        self.validate_record_counts()
        logger.info("finished record counts validation.")

        logger.info("started sample data validation...")
        self.validate_sample_data()
        logger.info("finished sample data validation.")

        insert_validation_logs(
            errors=self.errors,
            db_handler=temp_db_handler,
            service=service,
            source_table="LVData",
            destination_table="WalletBalance",
            validation_types=[
                {
                    "name": RECORD_COUNTS_VALIDATION,
                    "error": RECORD_COUNTS_VALIDATION_ERROR,
                },
                {
                    "name": SAMPLE_DATA_VALIDATION,
                    "error": SAMPLE_DATA_VALIDATION_ERROR,
                },
            ],
        )

        if len(self.errors):
            raise AirflowException("validation(s) failed.")
