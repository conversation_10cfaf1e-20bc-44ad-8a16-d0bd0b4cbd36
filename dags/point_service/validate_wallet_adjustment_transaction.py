from airflow.exceptions import AirflowException

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresHand<PERSON>
from common_helpers.logging import get_logger
from common_helpers.smc_validation_helpers import get_wallet_code
from common_helpers.utils import insert_validation_logs
from constants import (
    RECORD_COUNTS_VALIDATION,
    RECORD_COUNTS_VALIDATION_ERROR,
    SAMPLE_DATA_VALIDATION,
    SAMPLE_DATA_VALIDATION_ERROR,
)

logger = get_logger()


class WalletAdjustmentTransactionValidation:
    def __init__(
        self,
        mssql_handler: MSSQLHandler,
        postgresql_handler: PostgresHandler,
    ) -> None:
        self.mssql_handler = mssql_handler
        self.postgresql_handler = postgresql_handler
        self.errors = []

    def get_incremental_query_condition(self) -> str:
        """
        Generates a query condition for incremental migration, with specific date supported.

        Args:
            None

        Returns:
            str: A query condition string.
        """
        return "lvh.DocDate >= DATEADD(DAY, -1, CAST(CAST(GETDATE() AS DATE) AS DATETIME)) AND lvh.DocDate < CAST(CAST(GETDATE() AS DATE) AS DATETIME)"

    def get_source_count_query_string(self) -> str:
        return """
            SELECT
                COUNT(*)
            FROM (
                SELECT
                    lvh.LVHeaderKey
                FROM
                    LVHeader lvh
                    LEFT JOIN LVHeaderExtend lvhe WITH (NOLOCK) ON lvhe.LVHeaderKey = lvh.LVHeaderKey
                    JOIN LVTrans lvt ON lvt.LVHeaderKey = lvh.LVHeaderKey
                    JOIN LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
                WHERE
                    lvt.MovementCode IN ('ADJ', 'ADJOUT')
                    AND lvd.ValueCode IN (
                        'AP001',
                        'EP001',
                        'EP002',
                        'EP003',
                        'EP004',
                        'EP005',
                        'EP006',
                        'EP007',
                        'EP008',
                        'EP009',
                        'EP010',
                        'KPC01',
                        'KPO02',
                        'CR001',
                        'PT001'
                    ) AND lvh.DocDate < CAST('2025-06-20' AS DATETIME)
                GROUP BY
                    lvh.LVHeaderKey,
                    lvh.AddDT,
                    lvt.MovementCode,
                    lvd.ValueCode,
                    lvd.LVNumber
            ) AS total_rows;
        """

    def get_source_select_query_string(self, is_full_dump: bool) -> str:
        return f"""
            SELECT
                CAST(lvh.LVHeaderKey AS VARCHAR(20)) + '_' + CAST(lvt.MovementCode AS VARCHAR(10)) + '_' + CAST(lvd.ValueCode AS VARCHAR(10)) AS id,
                lvd.LVNumber AS memberId,
                lvd.ValueCode AS valueCode,
                SUM(lvt.Amount) AS amount,
                CASE
                    WHEN lvt.MovementCode = 'ADJ' THEN 'ADD'
                    WHEN lvt.MovementCode = 'ADJOUT' THEN 'DEDUCT'
                    ELSE ''
                END AS type,
                CASE
                    WHEN lvt.MovementCode = 'CVIN' THEN 'Convert In'
                    WHEN lvt.MovementCode = 'CVOUT' THEN 'Convert Out'
                    ELSE MAX(CAST(isnull (lvhe.Remark, '') AS NVARCHAR(max)))
                END AS remark,
                DATEADD (HOUR, -7, lvh.AddDT) AS createdAt
            FROM
                LVHeader lvh
                LEFT JOIN LVHeaderExtend lvhe WITH (NOLOCK) ON lvhe.LVHeaderKey = lvh.LVHeaderKey
                JOIN LVTrans lvt ON lvt.LVHeaderKey = lvh.LVHeaderKey
                JOIN LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
            WHERE
                lvt.MovementCode IN ('ADJ', 'ADJOUT')
                AND lvd.ValueCode IN (
                    'AP001',
                    'EP001',
                    'EP002',
                    'EP003',
                    'EP004',
                    'EP005',
                    'EP006',
                    'EP007',
                    'EP008',
                    'EP009',
                    'EP010',
                    'KPC01',
                    'KPO02',
                    'CR001',
                    'PT001'
                ){" AND lvh.DocDate < CAST(CAST(GETDATE() AS DATE) AS DATETIME)" if is_full_dump else f" AND {self.get_incremental_query_condition()}"}
            GROUP BY
                lvh.LVHeaderKey,
                lvh.AddUser,
                lvh.AddDT,
                lvt.MovementCode,
                lvd.ValueCode,
                lvd.LVNumber
        """

    def transform_record(
        self,
        record: tuple,
    ) -> tuple:
        (id, memberId, valueCode, amount, type, remark, createdAt) = record

        walletCode = get_wallet_code(valueCode)
        reasonCode = "MIGRATED_FROM_SMC"

        return (
            id,
            memberId,
            walletCode,
            reasonCode,
            amount,
            type,
            remark,
            createdAt,
        )

    def validate_record_counts(self):
        destination_count_query_string = """
            SELECT COUNT(*) FROM point_service."WalletAdjustmentTransaction";
        """

        source_count = self.mssql_handler.get_table_total_records(
            self.get_source_count_query_string(),
        )

        postgresql_connection = self.postgresql_handler.hook.get_conn()

        with postgresql_connection.cursor() as cursor:
            cursor.execute(destination_count_query_string)
            destination_count = cursor.fetchone()[0]

        if source_count != destination_count:
            logger.warning(
                f"count mismatch: source has {source_count} records, but destination has {destination_count} records"
            )
            self.errors.append(RECORD_COUNTS_VALIDATION_ERROR)
        else:
            logger.info(f"count validation passed: {source_count} records match")

    def validate_sample_data(self, is_full_dump: bool):
        source_sample_query_string = (
            f"""
                SELECT TOP 100000 * FROM ({self.get_source_select_query_string(is_full_dump)}) AS sample_data ORDER BY NEWID()
            """
            if is_full_dump
            else f"""
                SELECT * FROM ({self.get_source_select_query_string(is_full_dump)}) AS sample_data
            """
        )

        source_records = self.mssql_handler.extract_data(source_sample_query_string)

        if not source_records:
            logger.info("no source records found, skipping sample data validation")

            return

        transformed_source_records = [
            self.transform_record(record) for record in source_records
        ]

        source_record_ids = [record[0] for record in transformed_source_records]

        destination_sample_query_string = f"""
            SELECT
                "id",
                "memberId",
                "walletCode",
                "reasonCode",
                "amount",
                "type",
                "remark",
                "createdAt"
            FROM point_service."WalletAdjustmentTransaction" WHERE id IN ({", ".join(["'" + str(id) + "'" for id in source_record_ids])})
        """

        postgresql_connection = self.postgresql_handler.hook.get_conn()

        with postgresql_connection.cursor() as cursor:
            cursor.execute(destination_sample_query_string)
            destination_records = cursor.fetchall()

        source_dict = {
            record[0]: {
                "memberId": record[1],
                "walletCode": record[2],
                "reasonCode": record[3],
                "amount": record[4],
                "type": record[5],
                "remark": record[6],
                "createdAt": record[7],
            }
            for record in transformed_source_records
        }

        destination_dict = {
            record[0]: {
                "memberId": record[1],
                "walletCode": record[2],
                "reasonCode": record[3],
                "amount": record[4],
                "type": record[5],
                "remark": record[6],
                "createdAt": record[7],
            }
            for record in destination_records
        }

        source_ids = set(source_dict.keys())
        destination_ids = set(destination_dict.keys())
        missing_ids = source_ids - destination_ids
        extra_ids = destination_ids - source_ids

        data_mismatches = []
        common_ids = source_ids.intersection(destination_ids)

        for record_id in common_ids:
            source_data = source_dict[record_id]
            dest_data = destination_dict[record_id]

            mismatches = []

            fields_to_compare = [
                "memberId",
                "walletCode",
                "reasonCode",
                "amount",
                "type",
                "remark",
                "createdAt",
            ]

            for field in fields_to_compare:
                if source_data[field] != dest_data[field]:
                    mismatches.append(
                        f"{field}: source={source_data[field]}, dest={dest_data[field]}"
                    )

            if mismatches:
                data_mismatches.append((record_id, mismatches))

        if missing_ids:
            logger.warning(
                f"found {len(missing_ids)} records missing in destination table"
            )
            logger.warning(f"missing record IDs: {', '.join(sorted(missing_ids))}")
        else:
            logger.info("no missing records found in destination table")

        if extra_ids:
            logger.warning(f"found {len(extra_ids)} extra records in destination table")
            logger.warning(f"extra record IDs: {', '.join(sorted(extra_ids))}")
        else:
            logger.info("no extra records found in destination table")

        if data_mismatches:
            logger.warning(f"found {len(data_mismatches)} records with data mismatches")
            for record_id, mismatches in data_mismatches:
                logger.warning(f"data mismatches for record {record_id}:")
                for mismatch in mismatches:
                    logger.warning(f"  - {mismatch}")
        else:
            logger.info("no data mismatches found in matching records")

        if not missing_ids and not extra_ids and not data_mismatches:
            logger.info(f"sample data validation passed: all sample records match")
        else:
            self.errors.append(SAMPLE_DATA_VALIDATION_ERROR)

    def validate(
        self,
        temp_db_handler: PostgresHandler,
        service: str,
        is_full_dump: bool = True,
    ) -> None:
        validation_types = []

        if is_full_dump:
            logger.info("started record counts validation...")
            self.validate_record_counts()
            logger.info("finished record counts validation.")

            validation_types.append(
                {
                    "name": RECORD_COUNTS_VALIDATION,
                    "error": RECORD_COUNTS_VALIDATION_ERROR,
                }
            )

        logger.info("started sample data validation...")
        self.validate_sample_data(is_full_dump)
        logger.info("finished sample data validation.")

        validation_types.append(
            {
                "name": SAMPLE_DATA_VALIDATION,
                "error": SAMPLE_DATA_VALIDATION_ERROR,
            }
        )

        insert_validation_logs(
            errors=self.errors,
            db_handler=temp_db_handler,
            service=service,
            source_table="LVHeader",
            destination_table="WalletAdjustmentTransaction",
            validation_types=validation_types,
        )

        if len(self.errors):
            raise AirflowException("validation(s) failed.")
