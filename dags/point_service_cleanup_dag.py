from datetime import datetime
from common_helpers.database_services import <PERSON>gresHandler
from common_helpers.logging import get_logger

from airflow import DAG
from airflow.operators.python_operator import PythonOperator

logger = get_logger()


class PointServiceCleanup:
    def __init__(self):
        self.postgresql_handler = PostgresHandler(conn_id="temp_db_connection_id")

    def cleanup_wallet_activity(self):
        """
        Cleanup data from table WalletActivity.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up WalletActivity table...")
            cursor.execute('TRUNCATE TABLE point_service."WalletActivity" CASCADE;')
            logger.info(f"finished cleaning up WalletActivity table.")

        self.cleanup(table_cleanup)

    def cleanup_wallet_balance(self):
        """
        Cleanup data from table WalletBalance.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up WalletBalance table...")
            cursor.execute('TRUNCATE TABLE point_service."WalletBalance" CASCADE;')
            logger.info(f"finished cleaning up WalletBalance table.")

        self.cleanup(table_cleanup)

    def cleanup_wallet_transaction(self):
        """
        Cleanup data from table WalletTransaction.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up WalletTransaction table...")
            cursor.execute('TRUNCATE TABLE point_service."WalletTransaction";')
            logger.info(f"finished cleaning up WalletTransaction table.")

        self.cleanup(table_cleanup)

    def cleanup_wallet_adjustment_transaction(self):
        """
        Cleanup data from table WalletAdjustmentTransaction.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up WalletAdjustmentTransaction table...")
            cursor.execute(
                'TRUNCATE TABLE point_service."WalletAdjustmentTransaction";'
            )
            logger.info(f"finished cleaning up WalletAdjustmentTransaction table.")

        self.cleanup(table_cleanup)

    def cleanup(self, func):
        """
        Cleanup data from Point Service table.

        Args:
            func: A function that truncate the table.

        Returns:
            None
        """

        try:
            postgresql_connection = self.postgresql_handler.hook.get_conn()

            with postgresql_connection.cursor() as cursor:
                func(cursor)

            postgresql_connection.commit()
        except Exception as error:
            logger.error(f"an error has occured: {error}")

            if postgresql_connection:
                logger.info("a postgresql connection is found, rolling back...")
                postgresql_connection.rollback()
                logger.info("successfully rolled back.")

            raise error
        finally:
            if postgresql_connection:
                postgresql_connection.close()


with DAG(
    "point_service_wallet_activity_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL WalletActivity migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 2, 28),
    catchup=False,
    tags=["point_service", "wallet_activity", "cleanup"],
) as point_service_wallet_activity_cleanup_dag:
    PythonOperator(
        task_id="cleanup_wallet_activity_task",
        python_callable=PointServiceCleanup().cleanup_wallet_activity,
    )

with DAG(
    "point_service_wallet_balance_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL WalletBalance migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 2, 28),
    catchup=False,
    tags=["point_service", "wallet_balance", "cleanup"],
) as point_service_wallet_balance_cleanup_dag:
    PythonOperator(
        task_id="cleanup_wallet_balance_task",
        python_callable=PointServiceCleanup().cleanup_wallet_balance,
    )

with DAG(
    "point_service_wallet_transaction_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL WalletTransaction migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 2, 28),
    catchup=False,
    tags=["point_service", "wallet_transaction", "cleanup"],
) as point_service_wallet_transaction_cleanup_dag:
    PythonOperator(
        task_id="cleanup_wallet_transaction_task",
        python_callable=PointServiceCleanup().cleanup_wallet_transaction,
    )

with DAG(
    "point_service_wallet_adjustment_transaction_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL WalletAdjustmentTransaction migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 2, 28),
    catchup=False,
    tags=["point_service", "wallet_adjustment_transaction", "cleanup"],
) as point_service_wallet_adjustment_transaction_cleanup_dag:
    PythonOperator(
        task_id="cleanup_wallet_adjustment_transaction_task",
        python_callable=PointServiceCleanup().cleanup_wallet_adjustment_transaction,
    )
