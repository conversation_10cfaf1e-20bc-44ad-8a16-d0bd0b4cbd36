from airflow import DAG
from datetime import datetime
import pytz
from airflow.operators.python import <PERSON><PERSON>perator
import os
from migration_utils.ulid_utils import  generate_ulid_dailysync
from migration_utils.pipeline_ops import process_transformed_tables_dailysync



GEN_ULID_TABLES_custom = [
    { 
        'SOURCE_TABLE': 'staging_point_service."WalletBalanceTemp"',
        'TARGET_TABLE': 'staging_point_service."ulid_WalletBalance"',
        'PK_NAME': 'ulid_WalletBalance_pkey'
    }
]

ALL_MIGRATION_TABLES_custom = [
    
    'point_service."WalletBalance"'
]

def generate_ulid_for_service(**kwargs):
    for table_dict in GEN_ULID_TABLES_custom:
        SOURCE_TABLE = table_dict['SOURCE_TABLE']
        TARGET_TABLE = table_dict['TARGET_TABLE']
        PK_NAME = table_dict['PK_NAME']
        generate_ulid_dailysync(
            SOURCE_TABLE=SOURCE_TABLE,
            TARGET_TABLE=TARGET_TABLE,
            PK_NAME=PK_NAME,
            **kwargs
        )
def run_transform_load_for_service(**kwargs):
    """Run transform and load by order table"""
    process_transformed_tables_dailysync(ALL_MIGRATION_TABLES_custom, **kwargs)

# 13tables_to_migrate = OrderedDict([
# 14    ('loyalty_service."Member"', 'loyalty_service_conn'),
# 15    # ('engagement_service."MemberPrivilege"', 'engagement_service_conn'),
# 16    ('engagement_service."MemberCoupon"', 'engagement_service_conn'),
# 17    ('engagement_service."MemberCouponActivity"', 'engagement_service_conn'),
# 18    ('loyalty_service."SalesTransaction"', 'loyalty_service_conn'),
# 19    ('loyalty_service."MemberProfile"', 'loyalty_service_conn'),
# 20    ('loyalty_service."StaffProfile"', 'loyalty_service_conn'),
# 21    ('loyalty_service."MemberCoBrandCard"', 'loyalty_service_conn'),
# 22    ('loyalty_service."MemberLegacyTierHistory"', 'loyalty_service_conn'),
# 23    ('loyalty_service."MemberLegacyCoBrandHistory"', 'loyalty_service_conn'),
# 24    ('partner_service."SalesTransaction"', 'partner_service_conn'),
# 25    ('point_service."WalletAdjustmentTransaction"', 'point_service_conn'),
# 26    ('point_service."WalletBalance"', 'point_service_conn'),
# 27    ('partner_service."RefundSalesTransaction"', 'partner_service_conn'),
# 28    ('partner_service."SalesTransactionItem"', 'partner_service_conn'),
# 29    ('partner_service."SalesTransactionBurnPayment"', 'partner_service_conn'),
# 30    ('partner_service."SalesTransactionPayment"', 'partner_service_conn'),
# 31    ('point_service."WalletActivity"', 'point_service_conn'),
# 32    ('loyalty_service."RefundSalesTransaction"', 'loyalty_service_conn'),
# 33    ('partner_service."RefundSalesTransactionItem"', 'partner_service_conn'),
# 34    ('partner_service."SalesTransactionWalletActivity"', 'partner_service_conn'),
# 35    ('point_service."WalletTransaction"', 'point_service_conn'),
# 36])
# Get the current time in Bangkok, then convert to UTC

now_bangkok = datetime.now(pytz.timezone('Asia/Bangkok'))
now_utc = now_bangkok.astimezone(pytz.utc)
# Calculate start_timestamps and end_timestamps based on UTC midnight
# default_end_timestamp = str(now_utc.replace(hour=22, minute=0, second=0, microsecond=0))
# default_start_timestamp = str((now_utc.replace(hour=17, minute=0, second=0, microsecond=0)) - timedelta(days=1))
# default_end_timestamp =  '2025-06-09 22:00:00+00:00'
# default_start_timestamp =  '2025-06-08 17:00:00+00:00'
# default_end_timestamp =  '2025-06-10 22:00:00+00:00'
# default_start_timestamp =  '2025-06-09 17:00:00+00:00'
# default_end_timestamp =  '2025-06-13 22:00:00+00:00'
# default_start_timestamp =  '2025-06-10 17:00:00+00:00'
# default_end_timestamp =  '2025-06-15 22:00:00+00:00'
# default_start_timestamp =  '2025-06-14 17:00:00+00:00'
# default_end_timestamp =  '2025-06-23 22:00:00+00:00'
# default_start_timestamp =  '2025-06-20 03:00:00+00:00'
default_end_timestamp =  '2025-06-24 22:00:00+00:00'
default_start_timestamp =  '2025-06-23 17:00:00+00:00'

file_name = os.path.basename(__file__).split('.')[0]

with DAG(
    dag_id=file_name,
    start_date=None,
    schedule_interval=None,
    catchup=False,
    tags=["gwl_uat_cross","sql_runUAT"],
    params= {
        'start_timestamps': default_start_timestamp,
        'end_timestamps': default_end_timestamp,   
    }
) as dag:
    

    # Step 4: Transform and load for each service in sequence
    transform_task = PythonOperator(
        task_id=f"4_transform_load_tables",
        python_callable=run_transform_load_for_service
    )

    # Step 3: Gen ULID
    generate_ulid_task = PythonOperator(
        task_id="3_generate_ulid",
        python_callable=generate_ulid_for_service,
    )


    generate_ulid_task >> transform_task