from datetime import datetime
from common_helpers.database_services import <PERSON>gresHandler
from common_helpers.logging import get_logger

from airflow import DAG
from airflow.operators.python_operator import PythonOperator

logger = get_logger()


class LoyaltyServiceCleanup:
    def __init__(self):
        self.postgresql_handler = PostgresHandler(conn_id="temp_db_connection_id")

    def cleanup_member(self):
        """
        Cleanup data from table Member.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up Member table...")
            cursor.execute('TRUNCATE TABLE loyalty_service."Member" CASCADE;')
            logger.info(f"finished cleaning up Member table.")

        self.cleanup(table_cleanup)

    def cleanup_member_profile(self):
        """
        Cleanup data from table MemberProfile.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up MemberProfile table...")
            cursor.execute('TRUNCATE TABLE loyalty_service."MemberProfile" CASCADE;')
            logger.info(f"finished cleaning up MemberProfile table.")

        self.cleanup(table_cleanup)

    def cleanup_member_legacy_cobrand_history(self):
        """
        Cleanup data from table MemberLegacyCobrandHistory.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up MemberLegacyCobrandHistory table...")
            cursor.execute(
                'TRUNCATE TABLE loyalty_service."MemberLegacyCobrandHistory" CASCADE;'
            )
            logger.info(f"finished cleaning up MemberLegacyCobrandHistory table.")

        self.cleanup(table_cleanup)

    def cleanup_member_legacy_tier_history(self):
        """
        Cleanup data from table MemberLegacyTierHistory.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up MemberLegacyTierHistory table...")
            cursor.execute(
                'TRUNCATE TABLE loyalty_service."MemberLegacyTierHistory" CASCADE;'
            )
            logger.info(f"finished cleaning up MemberLegacyTierHistory table.")

        self.cleanup(table_cleanup)

    def cleanup_sales_transaction(self):
        """
        Cleanup data from table SalesTransaction.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up SalesTransaction table...")
            cursor.execute('TRUNCATE TABLE loyalty_service."SalesTransaction" CASCADE;')
            logger.info(f"finished cleaning up SalesTransaction table.")

        self.cleanup(table_cleanup)

    def cleanup_staff_profile(self):
        """
        Cleanup data from table StaffProfile.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up StaffProfile table...")
            cursor.execute('TRUNCATE TABLE loyalty_service."StaffProfile" CASCADE;')
            logger.info(f"finished cleaning up StaffProfile table.")

        self.cleanup(table_cleanup)

    def cleanup_refund_sales_transaction(self):
        """
        Cleanup data from table RefundSalesTransaction.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up RefundSalesTransaction table...")
            cursor.execute('TRUNCATE TABLE loyalty_service."RefundSalesTransaction" CASCADE;')
            logger.info(f"finished cleaning up RefundSalesTransaction table.")

        self.cleanup(table_cleanup)

    def cleanup_staff_company(self):
        """
        Cleanup data from table StaffCompany.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up StaffCompany table...")
            cursor.execute('TRUNCATE TABLE loyalty_service."StaffCompany" CASCADE;')
            logger.info(f"finished cleaning up StaffCompany table.")

        self.cleanup(table_cleanup)

    def cleanup_register_channel(self):
        """
        Cleanup data from table RegisterChannel.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up RegisterChannel table...")
            cursor.execute('TRUNCATE TABLE loyalty_service."RegisterChannel" CASCADE;')
            logger.info(f"finished cleaning up RegisterChannel table.")

        self.cleanup(table_cleanup)

    def cleanup_upgrade_group(self):
        """
        Cleanup data from table UpgradeGroup.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up UpgradeGroup table...")
            cursor.execute('TRUNCATE TABLE loyalty_service."UpgradeGroup" CASCADE;')
            logger.info(f"finished cleaning up UpgradeGroup table.")

        self.cleanup(table_cleanup)

    def cleanup_upgrade_reason(self):
        """
        Cleanup data from table UpgradeReason.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up UpgradeReason table...")
            cursor.execute('TRUNCATE TABLE loyalty_service."UpgradeReason" CASCADE;')
            logger.info(f"finished cleaning up UpgradeReason table.")

        self.cleanup(table_cleanup)

    def cleanup(self, func):
        """
        Cleanup data from Loyalty Service table.

        Args:
            func: A function that truncate the table.

        Returns:
            None
        """

        try:
            postgresql_connection = self.postgresql_handler.hook.get_conn()

            with postgresql_connection.cursor() as cursor:
                func(cursor)

            postgresql_connection.commit()
        except Exception as error:
            logger.error(f"an error has occured: {error}")

            if postgresql_connection:
                logger.info("a postgresql connection is found, rolling back...")
                postgresql_connection.rollback()
                logger.info("successfully rolled back.")

            raise error
        finally:
            if postgresql_connection:
                postgresql_connection.close()


with DAG(
    "loyalty_service_member_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL Member migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 3, 7),
    catchup=False,
    tags=["loyalty_service", "member", "cleanup"],
) as loyalty_service_member_cleanup_dag:
    PythonOperator(
        task_id="cleanup_member_task",
        python_callable=LoyaltyServiceCleanup().cleanup_member,
    )

with DAG(
    "loyalty_service_member_profile_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL MemberProfile migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 3, 7),
    catchup=False,
    tags=["loyalty_service", "member_profile", "cleanup"],
) as loyalty_service_member_profile_cleanup_dag:
    PythonOperator(
        task_id="cleanup_member_profile_task",
        python_callable=LoyaltyServiceCleanup().cleanup_member_profile,
    )

with DAG(
    "loyalty_service_member_legacy_cobrand_history_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL MemberLegacyCobrandHistory migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 3, 7),
    catchup=False,
    tags=["loyalty_service", "member_legacy_cobrand_history", "cleanup"],
) as loyalty_service_member_legacy_cobrand_history_cleanup_dag:
    PythonOperator(
        task_id="cleanup_member_legacy_cobrand_history_task",
        python_callable=LoyaltyServiceCleanup().cleanup_member_legacy_cobrand_history,
    )

with DAG(
    "loyalty_service_member_legacy_tier_history_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL MemberLegacyTierHistory migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 3, 7),
    catchup=False,
    tags=["loyalty_service", "member_legacy_tier_history", "cleanup"],
) as loyalty_service_member_legacy_tier_history_cleanup_dag:
    PythonOperator(
        task_id="cleanup_member_legacy_tier_history_task",
        python_callable=LoyaltyServiceCleanup().cleanup_member_legacy_tier_history,
    )

with DAG(
    "loyalty_service_sales_transaction_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL SalesTransaction migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 3, 7),
    catchup=False,
    tags=["loyalty_service", "sales_transaction", "cleanup"],
) as loyalty_service_sales_transaction_cleanup_dag:
    PythonOperator(
        task_id="cleanup_sales_transaction_task",
        python_callable=LoyaltyServiceCleanup().cleanup_sales_transaction,
    )

with DAG(
    "loyalty_service_staff_profile_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL StaffProfile migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 3, 7),
    catchup=False,
    tags=["loyalty_service", "staff_profile", "cleanup"],
) as loyalty_service_staff_profile_cleanup_dag:
    PythonOperator(
        task_id="cleanup_staff_profile_task",
        python_callable=LoyaltyServiceCleanup().cleanup_staff_profile,
    )

with DAG(
    "loyalty_service_staff_company_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL StaffCompany migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 3, 7),
    catchup=False,
    tags=["loyalty_service", "staff_company", "cleanup"],
) as loyalty_service_staff_company_cleanup_dag:
    PythonOperator(
        task_id="cleanup_staff_company_task",
        python_callable=LoyaltyServiceCleanup().cleanup_staff_company,
    )

with DAG(
    "loyalty_service_register_channel_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL RegisterChannel migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 3, 7),
    catchup=False,
    tags=["loyalty_service", "register_channel", "cleanup"],
) as loyalty_service_register_channel_cleanup_dag:
    PythonOperator(
        task_id="cleanup_register_channel_task",
        python_callable=LoyaltyServiceCleanup().cleanup_register_channel,
    )

with DAG(
    "loyalty_service_upgrade_group_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL UpgradeGroup migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 3, 7),
    catchup=False,
    tags=["loyalty_service", "upgrade_group", "cleanup"],
) as loyalty_service_upgrade_group_cleanup_dag:
    PythonOperator(
        task_id="cleanup_upgrade_group_task",
        python_callable=LoyaltyServiceCleanup().cleanup_upgrade_group,
    )

with DAG(
    "loyalty_service_upgrade_reason_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL UpgradeReason migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 3, 7),
    catchup=False,
    tags=["loyalty_service", "upgrade_reason", "cleanup"],
) as loyalty_service_upgrade_reason_cleanup_dag:
    PythonOperator(
        task_id="cleanup_upgrade_reason_task",
        python_callable=LoyaltyServiceCleanup().cleanup_upgrade_reason,
    )

with DAG(
    "loyalty_service_refund_sales_transaction_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL RefundSalesTransaction migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 3, 7),
    catchup=False,
    tags=["loyalty_service", "refund_sales_transaction", "cleanup"],
) as loyalty_service_refund_sales_transaction_cleanup_dag:
    PythonOperator(
        task_id="cleanup_refund_sales_transaction_task",
        python_callable=LoyaltyServiceCleanup().cleanup_refund_sales_transaction,
    )
