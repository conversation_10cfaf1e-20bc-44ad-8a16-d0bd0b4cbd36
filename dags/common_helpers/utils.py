import os
import math
from datetime import datetime, date, timedelta

import pandas as pd
import pytz
import requests
import ulid
import re

from sqlalchemy import create_engine
from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.logging import get_logger
from constants import (
    NEWMEMBER_CONN_ID,
    MAPPING_CSV_PATH,
    RECORD_COUNTS_VALIDATION,
    RECORD_COUNTS_VALIDATION_ERROR,
    SAMPLE_DATA_VALIDATION,
    SAMPLE_DATA_VALIDATION_ERROR,
    TEMP_CONN_ID,
    CLIENT_ID,
    CLIENT_SECRET,
    ENCRYPT_KEY,
    HEADERS,
)
from datetime import date

logger = get_logger()


def get_query_offsets(total_records: int, batch_size: int) -> range:
    """
    Generates a range object that yields batch start indices.

    Args:
        total_records (int): Total number of records.
        batch_size (int): The size of each batch to query.

    Returns:
        range: A range object with batch start indices.
    """
    return range(0, total_records, batch_size)


def read_csv(
    file_path: str,
    header: int | None = 0,
    usecols: list[int | str] | None = None,
) -> pd.DataFrame:
    """
    Read a CSV file and return a DataFrame.
    """
    return pd.read_csv(
        file_path, header=header, usecols=usecols, encoding="windows-1252"
    )


def create_mapping_df(service: str, table: str) -> pd.DataFrame:
    """
    Create mapping dataframe
    """
    df = read_csv(MAPPING_CSV_PATH)

    # remove rows where 'Field' column is NaN
    df.dropna(subset=["Field"], inplace=True)

    # change column name
    df.rename(columns={"Sensitive \r\n(e.g. PDPA)\r\n(Y/N)": "Sensitive"}, inplace=True)

    df = df[
        (df["Service"] == service)
        & (df["Table"] == table)
        & (df["In Scope (Y/N)"] == "Y")
    ]

    return df


def remove_file_based_db(path: str) -> None:
    """
    Remove database file
    """
    if os.path.exists(path):
        os.remove(path)
        logger.success(f"Successfully removed database file: {path}")


def rename_columns(df: pd.DataFrame, field_df: pd.DataFrame) -> pd.DataFrame:
    """
    field_df: contain mapping soure and destination fields
    """
    # rename_dict: {'old_name1': 'new_name1', 'old_name2': 'new_name2'}
    # {'emboss_id': 'embossNo', 'member_id': 'gwlNo', ..., 'shopping_card': 'shoppingCardId'}
    rename_dict = dict(zip(field_df["SMC Field"], field_df["Field"]))
    return df.rename(columns=rename_dict)


def row_count(count_field: str, table: str, condition: str | None = None) -> int:
    """
    Get number of total rows
    """
    if condition:
        query = f"SELECT COUNT({count_field}) FROM {table} WHERE {condition}"
    else:
        query = f"SELECT COUNT({count_field}) FROM {table}"
    print(query)

    mssql_handler = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)

    return mssql_handler.extract_data(query)[0][0]


def convert_bangkok_to_utc(bangkok_datetime: datetime) -> datetime:
    """
    Convert a datetime in Bangkok timezone (UTC+7) to UTC.
    bangkok_datetime: datetime object in Bangkok timezone
    return: datetime object in UTC
    """
    bangkok_tz = pytz.timezone("Asia/Bangkok")
    utc_tz = pytz.utc

    # Check if the datetime is naive (doesn't have timezone information)
    if bangkok_datetime.tzinfo is None:
        # Localize the datetime to Bangkok timezone
        bangkok_datetime = bangkok_tz.localize(bangkok_datetime)

    # Convert to UTC
    utc_datetime = bangkok_datetime.astimezone(utc_tz)

    return utc_datetime


def get_api_data(
    url: str, headers: dict | None = None, auth_token: str | None = None
) -> dict:
    """
    Fetch data from the provided API URL with optional query parameters and authorization.
    """
    # Add authorization if provided
    if auth_token:
        headers["Authorization"] = f"Bearer {auth_token}"

    try:
        # Send the GET request
        response = requests.get(url, headers=headers)

        # Check for successful response
        if response.ok:
            # Parse and return the JSON data
            return response.json()

        # Raise an HTTPError for any non-200 status code
        response.raise_for_status()
        # Handle failed response
        # print(f"Error: {response.status_code}")
        # print(f"Response: {response.text}")
        # return {}

    except requests.exceptions.RequestException as e:
        # Handle any request exceptions (e.g., network issues)
        print(f"An error occurred: {e}")
        return {}


def get_access_token(url: str) -> str:
    """
    Get the access token using the Client Credentials Flow for OAuth 2.0.
    """
    # Prepare the data for the token request
    data = {
        "grant_type": "client_credentials",
        "client_id": CLIENT_ID,
        "client_secret": CLIENT_SECRET,
    }

    try:
        response = requests.post(url, data=data, headers=HEADERS)

        # Check if the response is successful (status code 200)
        if response.ok:
            # Parse the response JSON and extract the access token
            token_data = response.json()
            access_token = token_data.get("access_token")

            if access_token:
                return access_token

            raise ValueError("Access token not found in response.")
        else:
            # If the request failed, raise an error with status code and response
            response.raise_for_status()

    except requests.exceptions.RequestException as e:
        # Handle any request exceptions (e.g., network issues, invalid responses)
        print(f"An error occurred: {e}")
        raise  # Re-raise the exception for the calling code to handle it


def get_sensitive_fields(service: str, table: str):
    field_df = create_mapping_df(service=service, table=table)[
        ["Field", "SMC Field", "Sensitive"]
    ]
    encrypt_columns = field_df[field_df["Sensitive"] == "Y"]["Field"].to_list()
    return encrypt_columns


def convert_encrypt_col_to_str(
    df: pd.DataFrame, service: str, table: str
) -> pd.DataFrame:
    # convert the data that need to encrypt to text
    sensitive_cols = get_sensitive_fields(service, table)
    for col in sensitive_cols:
        df[col] = df[col].astype(str)
    return df


def insert_query_without_encrypt(df: pd.DataFrame, table: str) -> str:
    columns = ", ".join([f'"{col}"' for col in df.columns])
    values = ["%s"] * len(df.columns)
    insert_query = f"""INSERT INTO "loyalty_service"."{table}" ({columns}) VALUES ({', '.join(values)})"""

    return insert_query


def upsert_query_without_encrypt(
    df: pd.DataFrame, table: str, conflict_target: list
) -> str:
    # Step 1: Generate the INSERT part of the query
    columns = ", ".join([f'"{col}"' for col in df.columns])
    values = ["%s"] * len(df.columns)
    insert_query = f"""INSERT INTO "loyalty_service"."{table}" ({columns}) VALUES ({', '.join(values)})"""

    # Step 2: Generate the ON CONFLICT part of the query
    conflict_target_str = ", ".join([f'"{col}"' for col in conflict_target])

    update_cols = list(df.columns)
    if "id" in update_cols:
        update_cols.remove("id")
    update_clause = ", ".join([f'"{col}" = EXCLUDED."{col}"' for col in update_cols])

    upsert_query = f"""
        {insert_query}
        ON CONFLICT ({conflict_target_str})
        DO UPDATE SET {update_clause};
    """

    return upsert_query


def hash_and_encode(value: str | None) -> str:
    if value:
        return f"""encode(digest(UPPER('{value}'), 'sha256'), 'hex')"""
    return "NULL"


def format_value(value):
    if value is None:
        return "NULL"

    # Handle datetime (format: 'YYYY-MM-DD HH:MM:SS')
    if isinstance(value, datetime):
        value = value.astimezone(pytz.UTC)
        return f"'{value.strftime('%Y-%m-%d %H:%M:%S')}'"

    # Handle date (format: 'YYYY-MM-DD')
    if isinstance(value, date):
        return f"'{value.strftime('%Y-%m-%d')}'"

    # Handle strings and other simple types
    if isinstance(value, str):
        return f"'{value}'"

    # For other types (numbers, booleans, etc.), just return the value
    return value


def insert_query_with_encrypt(
    df: pd.DataFrame,
    table: str,
    encrypt_columns: list,
    hash_columns: list[str] = [],
) -> str:
    """
    want to support when the encrypt column contain null value
    return
    query: INSERT INTO users (name, age, email)
    VALUES
        ('John Doe', 28, pgp_sym_encrypt('<EMAIL>', 'your_secret_key')),
        ('Jane Smith', 34, pgp_sym_encrypt('<EMAIL>', 'your_secret_key')),
        ('Alice Brown', 25, pgp_sym_encrypt('<EMAIL>', 'your_secret_key'));

    """
    unencrypt_columns = list(set(df.columns) - set(encrypt_columns))

    for col in hash_columns:
        hash_column_name = f"{col}Hash"
        df[hash_column_name] = df[col].apply(hash_and_encode)

    for col in encrypt_columns:
        df[col] = df[col].apply(
            lambda value: (
                f"pgp_sym_encrypt('{value}', '{ENCRYPT_KEY}')"
                if value is not None
                else "NULL"
            )
        )

    for col in unencrypt_columns:
        df[col] = df[col].apply(format_value)

    # Create a new column that joins all columns by ", "
    df["value"] = df.apply(lambda row: f"({', '.join(row.astype(str))})", axis=1)

    values = ", ".join(df["value"].to_list())
    # drop value column: in order to used df columns as destination column
    df = df.drop("value", axis=1)
    columns = ", ".join([f'"{col}"' for col in df.columns])

    insert_query = (
        f"""INSERT INTO "loyalty_service"."{table}" ({columns}) VALUES {values}"""
    )

    return insert_query


def insert_encrypt_data(df: pd.DataFrame, table: str, encrypt_columns: list):
    query = insert_query_with_encrypt(df, table, encrypt_columns)

    postgresql_handler = PostgresHandler(conn_id=TEMP_CONN_ID)

    postgresql_handler.execute_query(query)


def insert_data_without_encrypt(df: pd.DataFrame, table: str):
    query = insert_query_without_encrypt(df, table)
    records = df.values.tolist()

    postgresql_handler = PostgresHandler(conn_id=TEMP_CONN_ID)

    postgresql_handler.insert_data(query, records)


def upsert_data_without_encrypt(df: pd.DataFrame, table: str, conflict_target: list):
    query = upsert_query_without_encrypt(df, table, conflict_target)
    records = df.values.tolist()

    postgresql_handler = PostgresHandler(conn_id=TEMP_CONN_ID)

    postgresql_handler.insert_data(query, records)
    logger.info(f"Successfully upsert {table} data: {len(df)} rows")


def upsert_encrypt_data(
    df: pd.DataFrame,
    table: str,
    encrypt_columns: list,
    conflict_target: list,
    hash_columns: list = [],
) -> None:
    columns = list(df.columns)
    hash_cols = [f"{col}Hash" for col in hash_columns]
    excluded_cols = list((set(columns) | set(hash_cols)) - set(conflict_target))

    # remove `id` from excluded_cols
    if "id" in excluded_cols:
        excluded_cols.remove("id")

    insert_query = insert_query_with_encrypt(df, table, encrypt_columns, hash_columns)
    conflict_target_str = ", ".join([f'"{col}"' for col in conflict_target])
    update_clause = ", ".join([f'"{col}" = EXCLUDED."{col}"' for col in excluded_cols])
    upsert_query = f"""
        {insert_query}
        ON CONFLICT ({conflict_target_str})
        DO UPDATE SET {update_clause};
    """

    postgresql_handler = PostgresHandler(conn_id=TEMP_CONN_ID)
    postgresql_handler.execute_query(upsert_query)

    logger.info(f"Successfully upserted data to {table}. (total records: {len(df)})")


def incremental_date_condition(date_field: str, date: str | None = None) -> str:
    """
    date format: YYYY-MM-DD
    """
    if date is None:
        return f"({date_field} >= DATEADD(DAY, -1, CAST(CAST(GETDATE() AS DATE) AS DATETIME)) AND {date_field} < CAST(CAST(GETDATE() AS DATE) AS DATETIME))"
        # return f"({date_field} >= CAST(CAST('2025-06-20' AS DATE) AS DATETIME) AND {date_field} < DATEADD(DAY, 1, CAST(CAST('2025-06-23' AS DATE) AS DATETIME)))"

    return f"({date_field} >= CAST(CAST('{date}' AS DATE) AS DATETIME) AND {date_field} < DATEADD(DAY, 1, CAST(CAST('{date}' AS DATE) AS DATETIME)))"


def incremental_date_condition_for_member(
    date_field: str, date: str | None = None
) -> str:
    """
    Builds a SQL condition to filter records by date.

    Args:
        date_field (str): Name of the date/datetime column.
        date (str, optional): A date ('YYYY-MM-DD'). If None, filters for the previous day.

    Returns:
        str: SQL WHERE condition for the specified date range.
    """
    if date is None:
        return f"({date_field} >= DATEADD(DAY, -1, CAST(CAST(GETDATE() AS DATE) AS DATETIME)))"
        # return f"({date_field} >= CAST(CAST('2025-06-20' AS DATE) AS DATETIME))"

    return f"({date_field} >= CAST(CAST('{date}' AS DATE) AS DATETIME))"


def full_dump_date_condition(date_field: str, date: str | None = None) -> str:
    """
    Generates a condition string for filtering data based on a date field.

    If no date is provided, the condition filters data before today's date.
    If a date is provided, it filters data before the end of the specified date.

    Args:
        date_field (str): The column name to filter on.
        date (str | None): The date to filter by (optional).

    Returns:
        str: A condition string for use in SQL queries.
    """
    if date is None:
        return f"{date_field} < CAST(CAST(GETDATE() AS DATE) AS DATETIME)"

    return f"{date_field} < DATEADD(DAY, 1, CAST('{date}' AS DATETIME))"


def generate_ulid() -> str:
    return str(ulid.new())


def get_last_successful_batch(table: str) -> int:
    """
    Get current batch from csv file
    """
    postgresql_handler = PostgresHandler(conn_id=TEMP_CONN_ID)
    query = "SELECT * FROM loyalty_service.batch"
    df = get_df(query, postgresql_handler)

    filtered_df = df[df["table"] == table]

    if not filtered_df.empty:
        last_successful_batch = filtered_df["lastSuccessfulBatch"].iloc[0]
    else:
        last_successful_batch = -1

    return last_successful_batch


def set_last_successful_batch(table: str, new_batch: int) -> None:
    query = f"""UPDATE loyalty_service.batch SET "lastSuccessfulBatch" = {new_batch} WHERE "table" = '{table}'"""
    postgresql_handler = PostgresHandler(conn_id=TEMP_CONN_ID)
    postgresql_handler.execute_query(query)
    # logger.info(f"Successfully updated lastSuccessfulBatch to {new_batch} in {table}")


def reset_last_successful_batch(table: str) -> None:
    return set_last_successful_batch(table, -1)


def get_query_offsets(
    total_records: int, batch_size: int, starting_offset: int = 0
) -> range:
    """
    Generates a range object that yields batch start indices.

    Args:
        total_records (int): Total number of records.
        batch_size (int): The size of each batch to query.

    Returns:
        range: A range object with batch start indices.
    """
    return range(starting_offset, total_records, batch_size)


def get_field_df(service: str, table: str):
    field_df = create_mapping_df(service=service, table=table)[
        ["Field", "SMC Table", "SMC Field"]
    ]

    # keep only the rows that has SMC Field (Source Field)
    field_df = field_df[~field_df["SMC Field"].isna()]
    # remove soure field thata contain , or /
    field_df = field_df[~field_df["SMC Field"].str.contains(r"[,/]", regex=True)]
    return field_df


def cast_nvarchar(field: str) -> str:
    return f"CAST({field} AS NVARCHAR(max)) AS {field}"


def destination_count(table: str):
    """
    Count number of rows in the Database
    """
    query = f'SELECT count(*) FROM "loyalty_service"."{table}"'

    postgresql_handler = PostgresHandler(conn_id=TEMP_CONN_ID)

    return postgresql_handler.extract_data(query)[0][0]


def ls_dag_name(
    table: str,
    full_dump: bool,
) -> str:

    def convert_to_snake_case(input_string: str) -> str:
        # Use a regular expression to find capital letters and add an underscore before them (if not already at the beginning)
        result = re.sub(r"([a-z0-9])([A-Z])", r"\1_\2", input_string)
        # Convert to lowercase
        return result.lower()

    dump_type = "full_dump" if full_dump else "incremental"

    dag_name = f"loyalty_service_{dump_type}_migration_{convert_to_snake_case(table)}"

    return dag_name


def create_migration_result_table():
    """
    Create a table for storing migration validation log.

    Args:
        None

    Returns:
        None
    """
    create_table_query_string = """
        DO $$
        BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM pg_type WHERE typname = 'migration_result_migration_type'
            ) THEN
                CREATE TYPE public.migration_result_migration_type AS ENUM ('FULL_DUMP', 'INCREMENTAL');
            END IF;
            IF NOT EXISTS (
                SELECT 1 FROM pg_type WHERE typname = 'migration_result_validation_type'
            ) THEN
                CREATE TYPE public.migration_result_validation_type AS ENUM ('COMPLETENESS');
            END IF;
        END $$;
        CREATE TABLE IF NOT EXISTS public.smc2temp_migration_result (
            id SERIAL PRIMARY KEY,
            dag_name VARCHAR(100) NOT NULL,
            migration_type public.migration_result_migration_type NOT NULL,
            source_table VARCHAR(100) NOT NULL,
            source_table_count INT NOT NULL,
            destination_table VARCHAR(100) NOT NULL,
            destination_table_count INT NOT NULL,
            validation_type public.migration_result_validation_type NOT NULL,
            validation_result DECIMAL(5, 2) NOT NULL,
            incremental_date DATE,
            created_at TIMESTAMPTZ,
            finished_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
            runtime INTERVAL GENERATED ALWAYS AS (finished_at - created_at) STORED
        );
    """
    try:
        postgresql_handler = PostgresHandler(conn_id=TEMP_CONN_ID)
        postgresql_connection = postgresql_handler.hook.get_conn()

        logger.info(f"started preparing migration_result_table for migration...")
        postgresql_handler.execute_with_rollback(
            connection=postgresql_connection,
            query_string=create_table_query_string,
        )
        logger.info(f"finished preparing migration_result_table for migration.")
    finally:
        postgresql_connection.close()


def insert_migration_result(
    postgresql_handler: PostgresHandler,
    dag_name: str,
    migration_type: str,
    source_table: str,
    source_table_count: int,
    destination_table: str,
    destination_table_count: int,
    validation_type: str,
    validation_result: float,
    created_at: datetime,
    incremental_date: date = None,
):
    """
    Insert migration result to smc2temp_migration_result.

    Args:
        postgresql_handler (PostgresHandler): A PostgreSQL handler.
        dag_name (str): The name of the DAG.
        migration_type (str): The migration type.
        source_table (str): The source table name.
        source_table_count (int): Total records from source.
        destination_table (str): The destination table name.
        destination_table_count (int): Total records migrated.
        validation_type (str): The validation type.
        validation_result (float): The migration progress in percentage.

    Returns:
        None
    """
    insert_query_string = """
        INSERT INTO public.smc2temp_migration_result (
            "dag_name",
            "migration_type",
            "source_table",
            "source_table_count",
            "destination_table",
            "destination_table_count",
            "validation_type",
            "validation_result",
            "created_at",
            "incremental_date"
        )
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s);
    """
    postgresql_connection = postgresql_handler.hook.get_conn()

    postgresql_handler.execute_with_rollback(
        connection=postgresql_connection,
        query_string=insert_query_string,
        record=(
            dag_name,
            migration_type,
            source_table,
            source_table_count,
            destination_table,
            destination_table_count,
            validation_type,
            validation_result,
            created_at,
            incremental_date,
        ),
    )


def escape_single_quotes(text: str | None) -> str | None:
    """
    Replaces single apostrophes (') in the input string with two apostrophes ('').

    This function is useful when preparing a string for use in SQL queries, where
    single apostrophes need to be escaped by doubling them (i.e., replacing ' with '')
    to avoid syntax errors.
    """
    if text is None:
        return None

    return text.replace("'", "''")


def get_incremental_date(date: str | None) -> str:
    """
    Returns the provided date if given, otherwise returns yesterday's date.

    This function checks if a date string is provided. If so, it returns that date.
    If the date is not provided (i.e., None), it calculates and returns the date for
    the previous day (yesterday) in the format "YYYY-MM-DD".

    Args:
        date (str | None): A date string in "YYYY-MM-DD" format. If None is provided,
                           the function returns yesterday's date.

    Returns:
        str: The provided date string or yesterday's date in "YYYY-MM-DD" format.

    Example:
        >>> get_incremental_date("2025-03-14")
        '2025-03-14'

        >>> get_incremental_date(None)
        '2025-03-13'  # If today's date is "2025-03-14"
    """
    if date:
        return date

    tz = pytz.timezone("Asia/Bangkok")
    dt = datetime.now(tz) - timedelta(days=1)
    formatted_date = dt.strftime("%Y-%m-%d")
    return formatted_date


def save_migration_result(
    full_dump: bool,
    source_table: str,
    table: str,
    source_count: int,
    dest_count: int,
    created_at: datetime,
    incremental_date: str | None = None,
) -> None:
    """
    Insert migration result to smc2temp_migration_result table
    """
    migration_type = "FULL_DUMP" if full_dump else "INCREMENTAL"
    dag_name = ls_dag_name(table, full_dump)

    # calculate `validation_result`
    if source_count == 0 and dest_count == 0:
        validation_result = 100
    else:
        validation_result = dest_count / source_count * 100

    if incremental_date:
        incremental_date = convert_to_date(incremental_date)

    postgresql_handler = PostgresHandler(conn_id=TEMP_CONN_ID)

    insert_migration_result(
        postgresql_handler=postgresql_handler,
        dag_name=dag_name,
        migration_type=migration_type,
        source_table=source_table,
        source_table_count=source_count,
        destination_table=table,
        destination_table_count=dest_count,
        validation_type="COMPLETENESS",
        validation_result=validation_result,
        created_at=created_at,
        incremental_date=incremental_date,
    )
    logger.info(
        f"Successfully saved migration result for {table}. (validation_result: {validation_result:.2f}%)"
    )


def log_start_process_batch(table: str, batch_num: int, total_batches: int) -> None:
    # batch start from 0 until total_batches - 1
    logger.info(
        f"Started processing batch {batch_num+1}/{total_batches} for {table}..."
    )


def log_success_process_batch(
    table: str,
    batch_num: int,
    total_batches: int,
    batch_size: int,
    total_records: int,
) -> None:
    # start from batch 0 until total_batches - 1
    logger.success(
        f"Successfully processed batch {batch_num+1}/{total_batches} for {table}. "
        f"Batch size: {batch_size}, Total records: {total_records}"
    )


def log_successfully_migrated_data(table: str, total_records: int) -> None:
    logger.success(f"Successfully migrated {total_records} records to {table}.")


def calc_total_batches(total_records: int, batch_size: int) -> int:
    return math.ceil(total_records / batch_size)


def calc_last_batch_size(total_records: int, batch_size: int) -> int:
    return total_records % batch_size


def is_last_batch(batch_num: int, total_batches: int) -> bool:
    return batch_num == total_batches - 1


def calc_offset(batch_num: int, batch_size: int) -> int:
    return batch_num * batch_size


def convert_to_date(date_string: str) -> datetime:
    """
    Convert a date string in 'YYYY-MM-DD' format to a datetime object.

    Args:
        date_string (str): The date string to convert.

    Returns:
        datetime: The corresponding datetime object.

    Raises:
        ValueError: If the input string does not match the 'YYYY-MM-DD' format.
    """
    try:
        # Convert the date string to a datetime object
        return datetime.strptime(date_string, "%Y-%m-%d")
    except ValueError:
        print(f"Invalid date format: {date_string}. Expected format is YYYY-MM-DD.")
        raise


def get_df(query: str, db_handler: PostgresHandler | MSSQLHandler) -> pd.DataFrame:
    engine = create_engine(db_handler.hook.get_uri())
    return pd.read_sql(query, engine)


def clean_id_card(id_card: str | None) -> str | None:
    """
    Cleans an ID card value by applying the following rules:

    - Returns None if the input is None, empty, or whitespace-only.
    - Returns None if the ID starts with 'KPG' (considered invalid).
    - Otherwise, returns the original ID unchanged.

    Args:
        id_card (str | None): The input ID card value to validate and clean.

    Returns:
        str | None: A cleaned ID card string or None if invalid
    """
    if not id_card or pd.isna(id_card):  # catches NaN
        return None

    stripped_id = id_card.strip()
    if not stripped_id or stripped_id.startswith("KPG"):
        return None

    return stripped_id


def _clean_id(id: str) -> str:
    """
    Removes unwanted characters from the input string:
    - Symbols: /, |, _, -
    - Uppercase letters: A–Z

    Args:
        value (str): The input string.

    Returns:
        str: The cleaned string with specified characters removed.
    """
    id = id.replace("-R", "1")

    if not isinstance(id, str):
        raise TypeError("Input must be a string")

    # Regular expression pattern for characters to remove
    pattern = r"[\/|_\-A-Z]"
    return re.sub(pattern, "", id)


def generate_id_via_keysearch(key_search: str) -> int:
    """
    Generates a unique numeric ID for unmatched LVHeader from key_search
    based on the structure of the input string.
    Used for SalesTransaction and RefundSalesTransaction tables.
    """
    key_search = key_search.strip()
    pipe_count = key_search.count("|")

    # Case 1: 5-part format
    if pipe_count == 4:
        key_search = key_search[:3] + key_search[5:]
        parts = key_search.split("|")
        if len(parts) != 5:
            raise ValueError("Unexpected split result from 5-part key_search")

        left = parts[0] + parts[1] + parts[2]
        right = parts[3][-3:] + parts[4]
        raw_id = left + right
        cleaned_id = _clean_id(raw_id)

    # Case 2: starts with "50|3600001191416|"
    elif key_search.startswith("50|3600001191416|"):
        raw_id = key_search.replace("360000", "")
        cleaned_id = _clean_id(raw_id)

    # Case 3: 4-part format with letter 'P' to replace
    elif pipe_count == 3:
        raw_id = key_search.replace("P", "1")
        cleaned_id = _clean_id(raw_id)

    # Case 4: etc.
    else:
        cleaned_id = _clean_id(key_search)

    # Final validation: must be digits only before converting
    if not cleaned_id.isdigit():
        raise ValueError(f"Cleaned ID is not numeric: {cleaned_id}")

    return int(cleaned_id) * -1


def create_validation_summary_table():
    """
    Create a table for storing validation summary results.

    Args:
        None

    Returns:
        None
    """
    create_table_query_string = """
        DO $$
        BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM pg_type WHERE typname = 'service_type'
            ) THEN
                CREATE TYPE public.service_type AS ENUM (
                    'LoyaltyService',
                    'PartnerService',
                    'PointService',
                    'EngagementService'
                );
            END IF;
        END $$;
        
        CREATE TABLE IF NOT EXISTS public.validation_summary (
            run_id SERIAL PRIMARY KEY,
            service public.service_type NOT NULL,
            table_name VARCHAR(100) NOT NULL,
            is_valid BOOLEAN NOT NULL,
            total_rules INTEGER NOT NULL,
            failed_rules INTEGER NOT NULL,
            summary_message TEXT,
            created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
        );
    """
    try:
        postgresql_handler = PostgresHandler(conn_id=TEMP_CONN_ID)
        postgresql_connection = postgresql_handler.hook.get_conn()

        logger.info("Started creating validation_summary table...")
        postgresql_handler.execute_with_rollback(
            connection=postgresql_connection,
            query_string=create_table_query_string,
        )
        logger.info("Finished creating validation_summary table.")
    finally:
        postgresql_connection.close()


def create_validation_results_table():
    """
    Create a table for storing detailed validation results for each rule.
    This table has a foreign key relationship with validation_summary table.

    Args:
        None

    Returns:
        None
    """
    create_table_query_string = """
        CREATE TABLE IF NOT EXISTS public.validation_results (
            id SERIAL PRIMARY KEY,
            run_id INTEGER NOT NULL REFERENCES public.validation_summary(run_id),
            service VARCHAR(100) NOT NULL,
            source_table VARCHAR(100) NOT NULL,
            destination_table VARCHAR(100) NOT NULL,
            rule_name VARCHAR(100) NOT NULL,
            is_valid BOOLEAN NOT NULL,
            message TEXT,
            created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
        );
    """
    try:
        postgresql_handler = PostgresHandler(conn_id=TEMP_CONN_ID)
        postgresql_connection = postgresql_handler.hook.get_conn()

        logger.info("Started creating validation_results table...")
        postgresql_handler.execute_with_rollback(
            connection=postgresql_connection,
            query_string=create_table_query_string,
        )
        logger.info("Finished creating validation_results table.")
    finally:
        postgresql_connection.close()


def create_record_differences_table():
    """
    Create a table for storing field-level differences found during validation.
    This table has a foreign key relationship with validation_results table.
    The source_id column stores the identifier of the record being validated.

    Args:
        None

    Returns:
        None
    """
    create_table_query_string = """
        CREATE TABLE IF NOT EXISTS public.record_differences (
            id SERIAL PRIMARY KEY,
            validation_result_id INTEGER NOT NULL REFERENCES public.validation_results(id),
            source_record_id VARCHAR(100) NOT NULL,
            source_field VARCHAR(100) NOT NULL,
            source_value TEXT,
            destination_field VARCHAR(100) NOT NULL,
            destination_value TEXT,
            expected_value TEXT
        );
    """
    try:
        postgresql_handler = PostgresHandler(conn_id=TEMP_CONN_ID)
        postgresql_connection = postgresql_handler.hook.get_conn()

        logger.info("Started creating record_differences table...")
        postgresql_handler.execute_with_rollback(
            connection=postgresql_connection,
            query_string=create_table_query_string,
        )
        logger.info("Finished creating record_differences table.")
    finally:
        postgresql_connection.close()


def create_validation_tables():
    """
    Creates all validation-related tables in the correct order to maintain foreign key relationships.
    This function creates the following tables:
    1. validation_summary - Main table for validation summary
    2. validation_results - Detailed validation results with FK to validation_summary
    3. record_differences - Field-level differences with FK to validation_results

    Args:
        None

    Returns:
        None
    """
    try:
        logger.info("Starting creation of all validation tables...")

        # Create tables in order due to foreign key dependencies
        create_validation_summary_table()
        create_validation_results_table()
        create_record_differences_table()

        logger.info("Successfully created all validation tables.")
    except Exception as e:
        logger.error(f"Failed to create validation tables: {str(e)}")
        raise e


def get_validation_ls_dag_name(table: str, is_full_dump: bool = True) -> str:
    """
    Get the name of the validation DAG for a given table in loyalty service.
    If table is None, return the name of the validation DAG for all tables in loyalty service.
    """

    def convert_to_snake_case(input_string: str) -> str:
        # Use a regular expression to find capital letters and add an underscore before them (if not already at the beginning)
        result = re.sub(r"([a-z0-9])([A-Z])", r"\1_\2", input_string)
        # Convert to lowercase
        return result.lower()
    
    table = convert_to_snake_case(table)
    dump_type = 'full_dump' if is_full_dump else 'incremental'

    return f"validate_loyalty_service_{table}_{dump_type}"


def insert_validation_summary(
    postgresql_handler: PostgresHandler,
    service: str,
    destination_table: str,
    is_valid: bool,
    total_rules: int,
    failed_rules: int,
    summary_message: str,
) -> int:
    postgresql_connection = postgresql_handler.hook.get_conn()

    insert_query_string = """
        INSERT INTO public.validation_summary 
        (service, table_name, is_valid, total_rules, failed_rules, summary_message)
        VALUES (%s, %s, %s, %s, %s, %s)
        RETURNING run_id;
    """

    with postgresql_connection.cursor() as cursor:
        cursor.execute(
            insert_query_string,
            (
                service,
                destination_table,
                is_valid,
                total_rules,
                failed_rules,
                summary_message,
            ),
        )

        id = cursor.fetchone()[0]

    postgresql_connection.commit()
    postgresql_connection.close()

    return id


def insert_validation_result(
    postgresql_handler: PostgresHandler, results: list[tuple]
) -> int:
    postgresql_connection = postgresql_handler.hook.get_conn()

    insert_query_string = """
        INSERT INTO public.validation_results 
        (run_id, service, source_table, destination_table, rule_name, is_valid, message)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        RETURNING id;
    """

    with postgresql_connection.cursor() as cursor:
        cursor.executemany(
            insert_query_string,
            results,
        )

    postgresql_connection.commit()
    postgresql_connection.close()


def insert_validation_logs(
    errors: list[str],
    db_handler: PostgresHandler,
    service: str,
    source_table: str,
    destination_table: str,
    validation_types: list[dict] = None,
) -> None:
    total_rules = len(validation_types)

    # if validation types are not passed, default it as bellow
    if not validation_types:
        validation_types = (
            [
                {
                    "name": RECORD_COUNTS_VALIDATION,
                    "error": RECORD_COUNTS_VALIDATION_ERROR,
                },
                {
                    "name": SAMPLE_DATA_VALIDATION,
                    "error": SAMPLE_DATA_VALIDATION_ERROR,
                },
            ],
        )

    if not errors:
        logger.info("started logging validation results...")

        run_id = insert_validation_summary(
            postgresql_handler=db_handler,
            service=service,
            destination_table=destination_table,
            is_valid=True,
            total_rules=total_rules,
            failed_rules=len(errors),
            summary_message="",
        )

        results = []
        for validation_type in validation_types:
            results.append(
                (
                    run_id,
                    service,
                    source_table,
                    destination_table,
                    validation_type["name"],
                    True,
                    "",
                )
            )

        insert_validation_result(
            postgresql_handler=db_handler,
            results=results,
        )

        logger.info("finished logging validation results...")
    else:
        logger.info("validation(s) failed.")
        logger.info("started logging validation results...")

        run_id = insert_validation_summary(
            postgresql_handler=db_handler,
            service=service,
            destination_table=destination_table,
            is_valid=False,
            total_rules=total_rules,
            failed_rules=len(errors),
            summary_message="",
        )

        results = []
        for validation_type in validation_types:
            results.append(
                (
                    run_id,
                    service,
                    source_table,
                    destination_table,
                    validation_type["name"],
                    False if validation_type["error"] in errors else True,
                    "",
                )
            )

        insert_validation_result(
            postgresql_handler=db_handler,
            results=results,
        )

        logger.info("finished logging validation results...")


def cutoff_date_condition_mssql(date_field: str, date: str | None = None) -> str:
    """
    Cutoff date for validation's script (MSSQL)
    """
    if date:
        return f"{date_field} < DATEADD(DAY, 1, CAST('{date}' AS DATETIME))"

    # current date - 1 day
    return f"CAST({date_field} AS DATE) < CAST(GETDATE() AS DATE)"


def cutoff_date_condition_postgres(date_field: str, date: str | None = None) -> str:
    """
    Cutoff date for validation's script (PostgreSQL)
    """
    if date:
        # we set need to - INTERVAL '7 hours' (UTC timezone in temp db)
        return (
            f"{date_field} < '{date}'::timestamp + INTERVAL '1 day' - INTERVAL '7 hours'"
        )

    # current date
    return f"{date_field} < CURRENT_DATE::timestamp - INTERVAL '7 hours'"
