import io
import smtplib
from email.mime.application import MIMEApplication
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

import pandas as pd

from common_helpers.logging import get_logger
from constants import MAIL_FROM, MAIL_PASSWORD, MAIL_PORT, MAIL_RECEIVER, MAIL_SERVER

logger = get_logger()


def send_mail_notification(subject: str, text: str, filename: str, df: pd.DataFrame) -> None:
    html_table = df.to_html(index=False, border=0, justify="center")
    html_body = f"""
    <html>
        <head>
            <style>
                body {{
                    font-family: 'Open Sans', sans-serif;
                    line-height: 1.5;
                    margin: 0;
                }}
                
                table {{
                    border-collapse: collapse;
                    width: 100%;
                    margin-top: 20px;
                }}
                
                thead {{
                    border-bottom: 2px solid #ddd;
                    background-color: #f8f9fa;
                }}
                
                th, td {{
                    border: 1px solid #ddd;
                    padding: 12px 8px;
                    text-align: left;
                }}
                
                th {{
                    font-weight: bold;
                    color: #333;
                }}
                
                tr:nth-child(even) {{
                    background-color: #f9f9f9;
                }}
                
                tr:hover {{
                    background-color: #f0f0f0;
                }}
            </style>
        </head>
        
        <body>
            <p>{text}</p>
            {html_table}
        </body>
    </html>
    """

    csv_buffer = io.StringIO()
    df.to_csv(csv_buffer, index=False)
    csv_buffer.seek(0)

    csv_attachment = MIMEApplication(csv_buffer.read(), _subtype="csv")
    csv_attachment.add_header(
        "Content-Disposition",
        "attachment",
        filename=filename
    )

    try:
        with smtplib.SMTP_SSL(MAIL_SERVER, MAIL_PORT) as server:
            server.login(MAIL_FROM, MAIL_PASSWORD)

            message = MIMEMultipart()
            message["From"] = MAIL_FROM
            message["To"] = ", ".join(MAIL_RECEIVER)
            message["Subject"] = subject

            message.attach(MIMEText(html_body, "html"))
            message.attach(csv_attachment)

            server.sendmail(message["From"], MAIL_RECEIVER, message.as_string())
            logger.info(f"Email sent successfully to: {message['To']}")

    except smtplib.SMTPConnectError as e:
        logger.error(f"Failed to connect to SMTP server: {e}")
    except smtplib.SMTPAuthenticationError as e:
        logger.error(f"SMTP authentication failed: {e}")
    except smtplib.SMTPException as e:
        logger.error(f"SMTP error occurred: {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")
