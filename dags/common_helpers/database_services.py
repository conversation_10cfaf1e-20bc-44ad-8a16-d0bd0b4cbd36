import json
import math
import time

from psycopg2.extensions import connection as postgres_connection

from common_helpers.logging import get_logger

from airflow.providers.microsoft.mssql.hooks.mssql import Ms<PERSON>qlHook
from airflow.providers.postgres.hooks.postgres import PostgresHook

logger = get_logger()


class PostgresHandler:
    def __init__(self, conn_id):
        self.conn_id = conn_id
        self.hook = PostgresHook(postgres_conn_id=conn_id)
        self.hook.run("CREATE EXTENSION IF NOT EXISTS pgcrypto;")

    def extract_data(self, query):
        """
        Extract data from a PostgreSQL database.
        query: SQL query to extract data
        return: List of records
        """
        return self.hook.get_records(query)

    def insert_data(self, query, records):
        """
        Insert data into a PostgreSQL table.
        query: SQL query for insertion (e.g., INSERT INTO table VALUES (%s, %s))
        records: List of tuples containing the data to insert
        """
        with self.hook.get_conn() as conn:
            with conn.cursor() as cursor:
                cursor.executemany(query, records)
                conn.commit()

    def execute_with_rollback(
        self,
        connection,
        query_string: str,
        record: tuple | list[tuple] = None,
    ):
        """
        Executes a SQL query to PostgreSQL database with rollback supported.

        Args:
            connection (psycopg2.extensions.Connection): A PostgreSQL connection.
            query_string (str): An insert query string.
            record (tuple): A record or a list of records to extract values to use in the query string.

        Returns:
            None
        """
        with connection.cursor() as cursor:
            if record is not None:
                if isinstance(record, list):
                    cursor.executemany(query_string, record)
                else:
                    cursor.execute(query_string, record)
            else:
                cursor.execute(query_string)

        connection.commit()

    def create_table(self, create_query):
        """
        Create a table in PostgreSQL.
        create_query: SQL query to create the table
        """
        self.hook.run(create_query)

    def drop_table(self, table_name):
        """
        Drop a table in PostgreSQL.
        table_name: Name of the table to drop
        """
        drop_query = f'DROP TABLE IF EXISTS "{table_name}"'
        self.hook.run(drop_query)

    def execute_query(self, query):
        connection = self.hook.get_conn()
        cur = connection.cursor()
        cur.execute(query)
        connection.commit()
        connection.close()

    def cleanup_batch_tracker(
        self,
        connection: postgres_connection,
        service_name: str,
        table_name: str,
    ):
        """
        Delete batch tracker record after migration completion.

        Args:
            connection (postgres_connection): A PostgreSQL connection.
            service_name (str): The service name.
            table_name (str): The table name.

        Returns:
            None
        """
        self.execute_with_rollback(
            connection=connection,
            query_string=f"DELETE FROM {service_name}.batch_tracker WHERE table_name = '{table_name}';",
        )

    def update_batch_tracker(
        self,
        connection: postgres_connection,
        service_name: str,
        table_name: str,
        total_records: int,
        batch_no: int,
    ):
        """
        Update batch tracker table with the latest successful batch.

        Args:
            connection (postgres_connection): A PostgreSQL connection.
            service_name (str): The service name.
            table_name (str): The table name.
            total_records (int): Total records from source database.
            batch_no (int): The current batch number.

        Returns:
            None
        """
        update_query_string = f"""
            INSERT INTO {service_name}.batch_tracker (
                "table_name",
                "total_records",
                "completed_batches",
                "updated_at"
            )
            VALUES (%s, %s, %s::jsonb, NOW () AT TIME ZONE 'UTC')
            ON CONFLICT ("table_name")
            DO UPDATE SET
                completed_batches = {service_name}.batch_tracker.completed_batches || EXCLUDED.completed_batches,
                updated_at = NOW () AT TIME ZONE 'UTC';
        """
        self.execute_with_rollback(
            connection=connection,
            query_string=update_query_string,
            record=(table_name, total_records, json.dumps([batch_no])),
        )

    def get_latest_batch_info(
        self, connection: postgres_connection, service_name: str, table_name: str
    ) -> tuple[int, int]:
        """
        Retrieves the latest successful batch info.

        Args:
            connection (postgres_connection): A PostgreSQL connection.
            table_name (str): The name of the table you're migrating.

        Returns:
            int: The total records to migrate.
            int: The batch to continue on.

            None if the record does not exist.
        """
        try:
            cursor = connection.cursor()

            logger.info("getting leftoff migration info...")
            cursor.execute(
                f"SELECT total_records, completed_batches FROM {service_name}.batch_tracker WHERE table_name = %s;",
                (table_name,),
            )
            result = cursor.fetchone()

            if result:
                (total_records, completed_batches) = result

                next_batch = 1

                while next_batch in completed_batches:
                    next_batch += 1

                logger.info(
                    f"retrieved leftoff migration info with {total_records} total records, latest success batch is {next_batch - 1}"
                )

                return total_records, next_batch, completed_batches
            else:
                logger.info(
                    "leftoff migration info not found, starting from the start."
                )

                return None

        except Exception as error:
            logger.info(f"An error occurred while fetching the latest batch: {error}")

            return None
        finally:
            if cursor:
                cursor.close()


class MSSQLHandler:
    def __init__(self, conn_id: str):
        """
        Initialize the MSSQLHandler with the connection ID.

        mssql_conn_id: The Airflow connection ID for the MSSQL database.
        """
        self.conn_id = conn_id
        self.hook = MsSqlHook(mssql_conn_id=conn_id)

    def execute_query_string(
        self,
        connection,
        query_string: str,
        params=None,
    ):
        """
        Execute a query string.

        Args:
            connection (pyodbc.Connection): A MS SQL connection.
            query_string (str): A select query string.
            params: Addtional parameters.

        Returns:
            list[tuple]: The query result.
        """
        with connection.cursor() as cursor:
            if params:
                cursor.execute(query_string, params)
            else:
                cursor.execute(query_string)

        connection.commit()

    def extract_data(self, query: str):
        """
        Extract data from a MSSQL database using the provided query.
        query: SQL query to extract data
        return: List of records
        """
        # Run the query and fetch results
        return self.hook.get_records(query)

    def generate_batches(
        self,
        connection,
        query_string: str,
        total_records: int,
        batch_size: int,
        offsets: range,
        completed_batches: list[int] = [],
    ):
        """
        Extract data from a MS SQL table with a generator, rollback supported.

        Args:
            connection (pyodbc.Connection): A MS SQL connection.
            query_string (str): A select query string.
            total_records (int): Total records to migrate.
            batch_size (int): The number of records per batch.
            offsets (range): A range object with batch start indices.

        Returns:
            list[tuple]: The query result.
            int: The current batch number.
        """
        total_batches = math.ceil(total_records / batch_size)

        with connection.cursor() as cursor:
            for offset in offsets:
                batch_no = int(offset / batch_size) + 1

                if batch_no in completed_batches:
                    logger.info(f"batch {batch_no} succesfully migrated, skipping...")

                    continue

                remaining_records = total_records - offset
                fetch_size = (
                    batch_size if remaining_records >= batch_size else remaining_records
                )
                logger.info(
                    f"started extracting batch {batch_no}/{total_batches} (size {fetch_size})."
                )
                cursor.execute(query_string, (offset, fetch_size))
                batch = cursor.fetchall()
                logger.info(
                    f"succesfully extracted batch {batch_no}/{total_batches} (size {fetch_size})."
                )

                yield batch, batch_no

    def get_table_total_records(
        self,
        count_query_string: str,
    ) -> int:
        """
        Count the records of a table.

        Args:
            count_query_string (str): A query string to count records.

        Returns:
            int: The number of recordds in the table.
        """
        records = self.extract_data(count_query_string)

        return records[0][0] if records else 0


class QueryCache:
    def __init__(self, ttl_seconds):
        self.ttl = ttl_seconds
        self.cache = {}
        self.timestamps = {}

    def get(self, query):
        current_time = time.time()

        if query in self.cache and (current_time - self.timestamps[query]) < self.ttl:
            return self.cache[query]

        return None

    def set(self, query, result):
        self.cache[query] = result
        self.timestamps[query] = time.time()

    def clear(self):
        self.cache.clear()
        self.timestamps.clear()
