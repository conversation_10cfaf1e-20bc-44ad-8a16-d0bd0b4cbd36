WALLET_CODE_MAPPING = {
    "AP001": "CASH_WALLET",
    "EP001": "EPURSE_PROMO_RANGNAM",
    "EP002": "EPURSE_BD_CASHBACK",
    "EP003": "EPURSE_BD_CASHBACK",
    "EP004": "EPURSE_BD_CASHBACK",
    "EP005": "EPURSE_PROMO_WALLET",
    "EP006": "EPURSE_PROMO_RANGNAM",
    "EP007": "CASH_WALLET",
    "EP008": "CASH_WALLET",
    "EP009": "CASH_WALLET",
    "EP010": "CASH_WALLET",
    "KPC01": "CASH_WALLET",
    "KPO02": "CASH_WALLET",
    "CR001": "CARAT_WALLET",
    "PT001": "CARAT_WALLET",
}

MOVEMENT_CODE_TO_TYPE_CODE_MAPPING = {
    "ADJ": "MANUAL_ADJUSTMENT_ADD",
    "ADJOUT": "MANUAL_ADJUSTMENT_DEDUCT",
    "BD": "CASHBACK_BY_PURCHASE",
    "BE": "TOP_UP",
    "CB": "CASHBACK_BY_PURCHASE",
    "CBO": "CASHBACK_BY_PURCHASE",
    "CHG": "OTHER",
    "CVIN": "OTHER",
    "CVOUT": "OTHER",
    "CVPE": "OTHER",
    "EADJ": "MANUAL_ADJUSTMENT_ADD",
    "EADJOUT": "MANUAL_ADJUSTMENT_DEDUCT",
    "EXP": "EXPIRED",
    "ISS": "OTHER",
    "JS-ADJIN": "MANUAL_ADJUSTMENT_ADD",
    "JS-ADJOUT": "MANUAL_ADJUSTMENT_DEDUCT",
    "MIGRATE": "OTHER",
    "POS": "OTHER",
    "PTPOS": "EARNED_BY_PURCHASE",
    "RD": "REDEMPTION",
    "RDG": "REDEMPTION",
    "STRET": "OTHER",
    "UGC": "OTHER",
    "UGCI": "OTHER",
    "USE": "BURNED_BY_PURCHASE",
}

MOVEMENT_CODE_TO_REF_TYPE_MAPPING = {
    "ADJ": "ADJUSTMENT_TRANSACTION",
    "ADJOUT": "ADJUSTMENT_TRANSACTION",
    "BD": "SALES_TRANSACTION",
    "BE": "TOP_UP_TRANSACTION",
    "CB": "SALES_TRANSACTION",
    "CBO": "SALES_TRANSACTION",
    "CHG": "",
    "CVIN": "",
    "CVOUT": "",
    "CVPE": "",
    "EADJ": "REFUND_ADD",
    "EADJOUT": "REFUND_DEDUCT",
    "EXP": "CASHBACK_BY_PURCHASE",
    "ISS": "CASHBACK_BY_REWARD",
    "JS-ADJIN": "VOID_EARNED_BY_PURCHASE",
    "JS-ADJOUT": "VOID_BURNED_BY_PURCHASED",
    "MIGRATE": "VOID_TOP_UP",
    "POS": "",
    "PTPOS": "SALES_TRANSACTION",
    "RD": "EXPIRE_TRANSACTION",
    "RDG": "ADJUSTMENT_TRANSACTION",
    "STRET": "TOP_UP_TRANSACTION",
    "UGC": "VOID_TOP_UP_TRANSACTION",
    "UGCI": "VOID_SALES_TRANSACTION",
    "USE": "SALES_TRANSACTION",
}

NATURE_TYPE_MAPPING = {
    "ADJ": 1,
    "ADJOUT": -1,
    "BD": 1,
    "BE": 1,
    "CB": 1,
    "CBO": 1,
    "CHG": 1,
    "CVIN": 1,
    "CVOUT": -1,
    "CVPE": 1,
    "EADJ": 1,
    "EADJOUT": -1,
    "EXP": -1,
    "ISS": 1,
    "JS-ADJIN": 1,
    "JS-ADJOUT": -1,
    "MIGRATE": 1,
    "POS": 1,
    "PTPOS": 1,
    "RD": -1,
    "RDG": -1,
    "STRET": -1,
    "UGC": -1,
    "UGCI": 1,
    "USE": -1,
}


def get_wallet_code(value_code: str) -> str:
    return WALLET_CODE_MAPPING.get(value_code, "")


def get_wallet_activity_type_code(movement_code: str) -> str:
    return MOVEMENT_CODE_TO_TYPE_CODE_MAPPING.get(movement_code, "")


def get_wallet_activity_ref_type(movement_code: str) -> str:
    return MOVEMENT_CODE_TO_REF_TYPE_MAPPING.get(movement_code, "")


def get_nature_type(movement_code: str) -> str:
    nature_type = NATURE_TYPE_MAPPING.get(movement_code, "")

    if nature_type == "":
        return ""

    return "INCREASE" if nature_type == 1 else "DECREASE"
