import pandas as pd
import re
from constants import (
    TEMP_CONN_ID,
    ACCESS_TOKEN_URL,
    COUNTRY_CODE_URL,
    HEADERS,
    ENCRYPT_KEY,
    NEWMEMBER_CONN_ID,
)
from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MSSQLHandler
from common_helpers.utils import (
    get_access_token,
    get_api_data,
    read_csv,
    escape_single_quotes,
    get_df,
)
from common_helpers.logging import get_logger
from collections import Counter


logger = get_logger()


def get_subprogram_mapping(mapping_path: str) -> tuple[dict]:
    """
    Create subprogram mapping table into database file
    """
    dtype = {"Sub-program ID (SMC)": str}
    df = pd.read_csv(mapping_path, header=0, encoding="windows-1252", dtype=dtype)

    # rename column's name
    rename_dict = {
        "Sub-program ID (SMC)": "id",
        "Upgrade Group (GWL)": "upgrade_group",
        "Upgrade Reason (GWL)": "upgrade_reason",
        "Registration Channel (GWL)": "register_channel",
        "Registration Location (GWL)": "register_location",
    }

    df.rename(columns=rename_dict, inplace=True)

    # Apply .str.strip() to all string columns
    df = df.map(lambda x: x.strip() if isinstance(x, str) else x)

    # remove rows where 'id' column is NaN
    df = df[~df["id"].isna()]

    # convert `id` dtype: int to string (in source we store string)
    df["id"] = df["id"].astype(str)

    # get mapping dict
    ug_mapping = dict(zip(df["id"], df["upgrade_group"]))
    ur_mapping = dict(zip(df["id"], df["upgrade_reason"]))
    rc_mapping = dict(zip(df["id"], df["register_channel"]))
    rl_mapping = dict(zip(df["id"], df["register_location"]))

    return ug_mapping, ur_mapping, rc_mapping, rl_mapping


def email_cleansing_df(path: str) -> pd.DataFrame:
    df = read_csv(path)
    df = df[df["Propose to Clean"] == "Y"]
    return df


def get_email_domain(email: str) -> str | None:
    """
    Extracts the domain part from a given email address.

    This function splits the provided email address at the "@" symbol and returns the
    domain part if the email is valid (i.e., it contains exactly one "@" symbol and a domain part).
    If the email is invalid or does not contain exactly one "@" symbol, it returns None.
    """
    if "@" in email:
        parts = email.split("@")

        if len(parts) == 2:
            _, domain = parts
            return domain

    return None


def clean_email(email: str | None, email_df: pd.DataFrame) -> str | None:
    """
    Cleanse the email according to the 'Email Cleansing' sheet and convert it to lowercase.

    This function checks if the email domain contains any known typos from the
    'Typo Domain' column of the provided DataFrame and replaces it with the
    suggested domain. The resulting email is then converted to lowercase and
    returned with any single quotes escaped. If the email is not valid or no
    typo domain is found, the original email in lowercase is returned.
    """
    if email:
        email = email.lower()
        domain = get_email_domain(email)
        typo_domains = email_df["Typo Domain"].values

        if domain and domain in typo_domains:
            # Find the suggested domain for the typo
            suggested_domain = email_df.loc[
                email_df["Typo Domain"] == domain, "Suggested Domain"
            ].values[0]

            new_email = email.replace(domain, suggested_domain)

            # escape characters of ' by using two single quotes
            return escape_single_quotes(new_email.lower())

        return escape_single_quotes(email.lower())

    return None


def get_phonecode_df() -> pd.DataFrame:
    """
    Country dataframe from Master's Data (Destination: GWL) that contains `alpha3Code` and `phoneCode`.

    This function retrieves country data from an external API, extracts the `alpha3Code` (3-letter country code)
    and `phoneCode` (country phone code) for each country, and returns this data as a pandas DataFrame.
    The `phoneCode` is formatted with a leading '+' (e.g., '+1' for the United States).

    Returns:
        pd.DataFrame: A DataFrame containing two columns:
                      - `alpha3Code`: The 3-letter country code.
                      - `phoneCode`: The corresponding phone code with a leading '+' symbol.

    Example:
        >>> get_phonecode_df()
        alpha3Code   phoneCode
        0   USA        +1
        1   CAN        +1
        2   GBR        +44
        ...
    """
    # Get the access token needed for authentication to the API
    token = get_access_token(ACCESS_TOKEN_URL)

    # Fetch the country data from the external API using the token
    countries = get_api_data(COUNTRY_CODE_URL, HEADERS, token)["data"]

    # Initialize empty lists to store country alpha3Codes and phoneCodes
    alpha_code = []
    phone_code = []

    # Loop through each country in the response to extract `alpha3Code` and `phoneCode`
    for c in countries:
        alpha_code.append(c["alpha3Code"])  # Append alpha3Code to the list
        phone_code.append(c["phoneCode"])  # Append phoneCode to the list

    df = pd.DataFrame(data={"alpha3Code": alpha_code, "phoneCode": phone_code})

    # Apply a transformation to format the phone code by adding a '+' symbol at the beginning
    df["phoneCode"] = df["phoneCode"].apply(lambda p: f"+{p}")

    return df


def get_phone_number(
    nationality_code: str,
    mobile1: str,
    mobile2: str,
    china_mobile: str,
) -> str:
    """
    Returns the most appropriate phone number based on the nationality code.

    Arguments:
    - nationality_code (str): The nationality code (e.g., 'CHN' for China).
    - mobile1 (str): The first mobile number.
    - mobile2 (str): The second mobile number.
    - china_mobile_phone (str): The special mobile number for China.

    Returns:
    - str | None: The appropriate phone number or None if no valid number is found.
    """

    match nationality_code:

        # If nationality code is 'CHN', prioritize china_mobile_phone first
        case "CHN":
            if china_mobile:
                return china_mobile
            if mobile1:
                return mobile1
            if mobile2:
                return mobile2
            return None

        # For other nationalities, prioritize mobile1, then mobile2, then china_mobile_phone
        case _:
            if mobile1:
                return mobile1
            if mobile2:
                return mobile2
            if china_mobile:
                return china_mobile
            return None


def get_duplicated_contact_in_db() -> tuple[list, list]:
    """
    Fetch duplicated emails and phone numbers from the database.

    This function queries the database to retrieve the list of emails and phone numbers
    associated with members. It then checks for duplicates (considering all occurrences)
    and returns two lists: one with duplicated emails and another with duplicated phone numbers.
    """
    query = f"""
        SELECT email, phone from "loyalty_service"."Member"
    """
    temp_postgres = PostgresHandler(conn_id=TEMP_CONN_ID)
    df = get_df(query, temp_postgres)

    duplicated_emails = (
        df[df["email"].duplicated(keep=False)]["email"].dropna().unique().tolist()
    )
    duplicated_phones = (
        df[df["phone"].duplicated(keep=False)]["phone"].dropna().unique().tolist()
    )

    # Log the results for monitoring
    logger.info(f"Duplicated emails found: {len(duplicated_emails)}")
    logger.info(f"Duplicated emails: {duplicated_emails}")
    logger.info(f"Duplicated phone numbers found: {len(duplicated_phones)}")
    logger.info(f"Duplicated phone numbers: {duplicated_phones}")

    return duplicated_emails, duplicated_phones


def get_duplicated_contact(df: pd.DataFrame) -> tuple[list, list]:
    """
    Get duplicated contact information (email, phone) from the database and the given dataframe.
    Finds duplicates across both the database and input DataFrame.

    Arguments:
    - df (pd.DataFrame): The input DataFrame containing email and phone columns.

    Returns:
    - tuple[list[str], list[str]]: A tuple containing two lists:
        - The first list contains duplicated emails.
        - The second list contains duplicated phone numbers.

    """
    # Query data from the database
    query = f"""
        SELECT 
            pgp_sym_decrypt(email::bytea, '{ENCRYPT_KEY}') AS email, 
            pgp_sym_decrypt(phone::bytea, '{ENCRYPT_KEY}') AS phone
        FROM "loyalty_service"."Member"
    """
    temp_postgres = PostgresHandler(conn_id=TEMP_CONN_ID)
    db_df = get_df(query, temp_postgres)

    # Concatenate database and input DataFrame emails and phones
    all_emails = pd.concat([db_df["email"].dropna(), df["email"].dropna()]).unique()
    all_phones = pd.concat([db_df["phone"].dropna(), df["phone"].dropna()]).unique()

    email_counts = Counter(all_emails)
    phone_counts = Counter(all_phones)

    # Find duplicated emails and phones
    duplicated_emails = [email for email, count in email_counts.items() if count > 1]
    duplicated_phones = [phone for phone, count in phone_counts.items() if count > 1]

    # Log the results for monitoring
    logger.info(f"Duplicated emails found: {len(duplicated_emails)}")
    logger.info(f"Duplicated emails: {duplicated_emails}")
    logger.info(f"Duplicated phone numbers found: {len(duplicated_phones)}")
    logger.info(f"Duplicated phone numbers: {duplicated_phones}")

    return duplicated_emails, duplicated_phones


def dummy_email(member_id: str) -> str:
    return f"pleasecontactcc_{member_id}@gwl.com"


def handle_contact(
    member_id: str,
    nationality: str,
    email: str | None,
    phone: str | None,
    duplicated_emails: list[str],
    duplicated_phones: list[str],
) -> tuple[str | None, str | None]:
    """
    Handle phone and email data according to the Contact Info Handling sheet.
    """
    match nationality:
        case "THA":  # Thai

            # Missing Email
            if email is None:
                # Missing Phone or Duplicated Phone
                if phone is None or phone in duplicated_phones:
                    return dummy_email(member_id), None
                # Unique Phone
                return None, phone

            # Duplicated Email
            elif email in duplicated_emails:
                # Missing Phone or Duplicated Phone
                if phone is None or phone in duplicated_phones:
                    return dummy_email(member_id), None
                # Unique Phone
                return None, phone

            # Unique Email
            else:
                # Missing Phone or Duplicated Phone
                if phone is None or phone in duplicated_phones:
                    return email, None
                # Unique Phone
                return email, phone

        # Non-Thai People
        case _:
            # Missing Email
            if email is None:

                # Missing Phone or Duplicated Phone
                if phone is None or phone in duplicated_phones:
                    return dummy_email(member_id), None
                # Unique Phone
                return dummy_email(member_id), phone

            # Duplicated Email
            elif email in duplicated_emails:

                # Missing Phone or Duplicated Phone
                if phone is None or phone in duplicated_phones:
                    return dummy_email(member_id), None
                # Unique Phone
                return dummy_email(member_id), phone

            # Unique Email
            else:
                # Missing Phone or Duplicated Phone
                if phone is None or phone in duplicated_phones:
                    return email, None
                # Unique Phone
                return email, phone


def get_member_ids_with_address(member_ids: list[str]):
    """
    Get list of member ID that has address
    """
    # for phone code: if has adress -> phone code will be `+66`
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)

    address_columns = [
        "haddr_subdistrict",
        "haddr_district",
        "haddr_city",
        "caddr_subdistrict",
        "caddr_district",
        "caddr_city",
        "maddr_subdistrict",
        "maddr_district",
        "maddr_city",
    ]

    ids = ", ".join([f"'{id}'" for id in member_ids])
    select_fields = f"member_id, {', '.join(address_columns)}"
    query = f"select {select_fields} from df_member dm where member_id in ({ids})"

    df = get_df(query, mssql)
    df = df.map(lambda x: x.strip() if isinstance(x, str) else x)
    df.replace("", None, inplace=True)

    # Get member_ids where any of the address columns is not NULL
    member_ids = df[df[address_columns].notnull().any(axis=1)]["member_id"].tolist()

    return member_ids


def valid_thai_phone(phone: str | None) -> bool:
    """
    Checks if a phone number follows valid Thai phone number patterns.
    
    This function validates if a phone number conforms to standard Thai phone number formats:
    - Mobile numbers: Starting with 6, 8, or 9 followed by 8 digits (total 9 digits)
    - Landline numbers: Starting with 2, 3, 4, 5, or 7 followed by 7 digits (total 8 digits)
    
    Args:
        phone (str | None): The phone number to validate. Can be None.
        
    Returns:
        bool: True if the phone number is a valid Thai phone number format, False otherwise.
    """
    # check pattern of thai phone number
    if re.match(r"^[689]\d{8}$", phone) or re.match(r"^[23457]\d{7}$", phone):
        return True
    
    return False


def clean_phone_number(
    phone_number: str | None,
    email: str,
    id_card: str | None,
    nationality_code: str | None,
    phone_code: str | None,
) -> str | None:
    """
    Clean the phone number by keeping only digits and the '+' sign,
    regardless of whether '+' is at the start or not.
    """
    if phone_number:

        # Remove any characters that are not digits or the plus sign
        phone_number = re.sub(r"[^0-9+]", "", phone_number)

        # remove + sign from phone number start with +
        if phone_number and phone_number[0] == "+":
            phone_number = phone_number[1:]

        # Check if '+' exists in the string and is not at the start
        # if that member has id_card or email -> cleaning phone via return None
        if "+" in phone_number and (id_card or email):
            return None

        # if phoneCode is +66 and phone number starts with 0 -> remove 0
        if phone_code == "+66" and phone_number.startswith("0"):
            phone_number = phone_number[1:]

        # remove + from phone number
        phone_number = phone_number.replace("+", "")

        # check if member is thai and phone number is valid thai phone number
        if nationality_code == "THA" and not valid_thai_phone(phone_number):
            return None

    return phone_number
