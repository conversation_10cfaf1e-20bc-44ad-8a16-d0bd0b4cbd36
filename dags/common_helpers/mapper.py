import pandas as pd


class CSVMapper:
    """
    A CSV mapper class for mapping source columns to target columns.

    Args:
        file_path (str): The path to the mapping CSV file.
        key (str): The column to map value with.
        sub_keys (list[str]): The columns you are trying to get values of.

    Returns:
        dict: A dict containing sub keys with mapped values.
    """

    def __init__(self, file_path: str, key: str, sub_keys: list[str]) -> None:
        self.cache = {}
        self.key = key
        self.sub_keys = sub_keys
        self.mapping = self._load_mapping(file_path)

    def _load_mapping(self, file_path: str) -> dict:
        df = pd.read_csv(file_path)
        result = {}

        for _, row in df.iterrows():
            if row.isna().all():
                break

            mapping_dict = {
                key: "" if pd.isna(row[key]) else row[key] for key in self.sub_keys
            }
            result[row[self.key]] = mapping_dict

        return result

    def get_value(self, key: str) -> dict:
        if key not in self.cache:
            self.cache[key] = self.mapping.get(key, {})

        return self.cache[key]
