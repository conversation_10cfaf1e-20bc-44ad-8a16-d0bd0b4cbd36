from loguru import logger
from airflow.utils.log.logging_mixin import LoggingMixin


def loguru_to_airflow_handler(message):
    airflow_logger = LoggingMixin().log
    record = message.record
    level = record["level"].name.lower()
    log_message = (
        f"{record['time'].strftime('%Y-%m-%d %H:%M:%S')} | "
        f"{record['module']}:{record['line']} - {record['message']}"
    )

    if level == "info":
        airflow_logger.info(log_message)
    elif level == "error":
        airflow_logger.error(log_message)
    elif level == "warning":
        airflow_logger.warning(log_message)
    elif level == "debug":
        airflow_logger.debug(log_message)
    else:
        airflow_logger.info(log_message)


def get_logger():
    logger.remove()
    logger.add(loguru_to_airflow_handler, format="{message}")

    return logger
