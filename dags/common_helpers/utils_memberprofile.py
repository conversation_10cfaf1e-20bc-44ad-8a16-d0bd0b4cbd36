import pandas as pd
import numpy as np
from common_helpers.database_services import MSS<PERSON><PERSON><PERSON><PERSON>
from constants import (
    TITTLE_MAPPING_PATH,
    NEWMEMBER_CONN_ID,
    ACCESS_TOKEN_URL,
    COUNTRY_CODE_URL,
    ADDRESS_URL,
    HEADERS,
    ISSUED_ADDR_PATH,
)
from common_helpers.utils import (
    get_access_token,
    get_api_data,
    read_csv,
    cast_nvarchar,
    get_df,
)
from common_helpers.logging import get_logger

logger = get_logger()


haddress_line = ["haddr_number", "haddr_bldg", "haddr_moo", "haddr_soi", "haddr_road"]

caddress_line = [
    "caddr_number",
    "caddr_bldg",
    "caddr_floor",
    "caddr_dept",
    "caddr_soi",
    "caddr_road",
]

maddress_line = ["maddr_number", "maddr_bldg", "maddr_moo", "maddr_soi", "maddr_road"]

haddress = [
    "haddr_subdistrict",
    "haddr_district",
    "haddr_city",
    "haddr_zip_code",
]


caddress = [
    "caddr_subdistrict",
    "caddr_district",
    "caddr_city",
    "caddr_zip_code",
]

maddress = [
    "maddr_subdistrict",
    "maddr_district",
    "maddr_city",
    "maddr_zip_code",
]


def get_title_mapping() -> dict[int, int]:
    df = read_csv(file_path=TITTLE_MAPPING_PATH, header=1)
    df = df[["title_id", "title (GWL)"]]
    return df.set_index("title_id")["title (GWL)"].to_dict()


def safe_int_convert(value: str) -> int | str:
    """
    Attempts to safely convert a value to an integer.
    
    If the conversion fails due to a ValueError or TypeError,
    the original value is returned unchanged.
    """
    try:
        return int(value)
    except Exception:
        return value



def transform_tname(
    tname: str, country_code: str
) -> tuple[str | None, str | None, str | None]:
    """
    Transform Thai name to first name, middle name and last name
    """
    names = tname.strip().split()

    # Thai People
    if country_code == "THA":
        match len(names):
            case 0:
                return None, None, None
            case 1:
                return names[0], None, None
            case _:
                return names[0], None, " ".join(names[1:])

    # Non-Thai People
    match len(names):
        case 0:  # tname is ""
            return None, None, "-" # None
        case 1:
            return names[0], None, "-" # None
        case 2:
            return names[0], None, names[1]
        case 3:
            return names[0], names[1], names[2]
        case _:
            return " ".join(names[:]), None, "-" # None


def transform_ename(
    ename: str, country_code: str
) -> tuple[str | None, str | None, str | None]:
    """
    Transform English name to first name, middle name and last name
    """
    names = ename.strip().split()

    # Thai People
    if country_code == "THA":
        match len(names):
            case 0:
                return "", None, "-"
            case 1:
                return names[0], None, "-"
            case _:
                return names[0], None, " ".join(names[1:])

    # Non-Thai People
    match len(names):
        case 0:
            return "", None, "-"
        case 1:
            return names[0], None, "-"
        case 2:
            return names[0], None, names[1]
        case 3:
            return names[0], names[1], names[2]
        case _:
            return names[0], None, " ".join(names[1:])


def get_source_country_df() -> pd.DataFrame:
    """
    Create Dataframe that contain `source_country` and `source_alpha_code` columns
    """
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)

    query = f"SELECT country_code, country_name FROM mst_country"
    df = get_df(query, mssql)

    # removes any leading and trailing spaces
    df["country_code"] = df["country_code"].apply(lambda c: c.strip())
    df["country_name"] = df["country_name"].apply(lambda c: c.strip())

    # rename column
    df.rename(
        columns={"country_name": "source_country", "country_code": "source_alpha_code"},
        inplace=True,
    )

    return df


def get_dest_country_df() -> pd.DataFrame:
    """
    Country code dataframe that contain `alpha_code` and `country` column
    from Master's Data (Destination: GWL)
    """
    token = get_access_token(ACCESS_TOKEN_URL)
    countries = get_api_data(COUNTRY_CODE_URL, HEADERS, token)["data"]

    alpha3code = []
    country = []

    for c in countries:
        alpha3code.append(c["alpha3Code"])
        country.append(c["locale"]["en"])

    data = {"alpha_code": alpha3code, "country": country}
    df = pd.DataFrame(data=data)
    return df


def get_country_mapping() -> dict[str, str]:
    """
    Create country code mapping dictionary
    """
    df = get_dest_country_df()
    # convert to upper case in order to merge with old_country_df
    df["country"] = df["country"].apply(lambda x: x.upper())

    source_df = get_source_country_df()

    # Mapping Logic
    # Case 1: if alpha code is in API -> still used that alpha code
    mapping_df = pd.merge(
        source_df, df, left_on="source_alpha_code", right_on="alpha_code", how="inner"
    )
    alpha_code = mapping_df["source_alpha_code"].to_list()
    mapping_dict = {code: code for code in alpha_code}

    # Case 2: if alpha code is not in API -> find it from country code
    mapping_df = pd.merge(
        source_df, df, left_on="source_country", right_on="country", how="left"
    )

    # get only the DF that source_alpha_code is not in the first case
    mapping_df = mapping_df[~mapping_df["source_alpha_code"].isin(alpha_code)]

    # # For mannual mapping, get unmapable alpha_code
    # unmapping_df = mapping_df[mapping_df["alpha_code"].isna()]
    # logger.info(
    #     f"Unmapping Source Alpha Code: {unmapping_df['source_alpha_code'].to_list()}"
    # )

    # remove where destination alpha_code is null
    mapping_df = mapping_df[~mapping_df["alpha_code"].isna()]
    mapping_dict = {
        **mapping_dict,
        **dict(zip(mapping_df["source_alpha_code"], mapping_df["alpha_code"])),
    }

    # Case 3: mannual mapping
    mannual_mapping = {
        "BVI": "VGB",
        "BLA": "PLW",
        "BOS": "BIH",
        "BRI": "IOT",
        "CAR": "CAF",
        "CCU": "IND",
        "CI": "ESP",
        "CAP": "CPV",
        "GBN": "CHN",
        "PRC": "CHN",
        "COS": "CRI",
        "CRO": "HRV",
        "CR": "CZE",
        "DR": "DOM",
        "T": "OTH",  # not found: FOR RUNNING GROUP ?
        "FR": "FRA",
        "FPO": "PYF",
        "CG": "COG",
        "HIJ": "JPN",
        "HOL": "NLD",
        "HON": "HND",
        "JAP": "JPN",
        "KYR": "KGZ",
        "LIT": "LTU",
        "MCA": "MAC",
        "MAD": "MKD",
        "MI": "MHL",
        "MOL": "MDA",
        "MZB": "MOZ",
        "NKG": "CHN",
        "ANT": "NLD",
        "NET": "NLD",
        "NM ": "MNP",
        "OTH": "LSO",
        "PHI": "PHL",
        "RKS": "OTH",  # not found: Republic of Kosovo
        "SEY": "REU",  # REUNION CHELLES -> Réunion
        "SAN": "SMR",
        "SCO": "GBR",  # Scotland, GBR: United Kingdom
        "SCG": "SRB",
        "SER": "SRB",
        "SR": "SVK",
        "STL": "LCA",
        "STG": "VCT",
        "SWA": "SWZ",
        "SWL": "SWZ",
        "GAM": "GMB",
        "UAE": "ARE",
        "UN": "OTH",  # not found: UNITED NATIONS
        "USV": "VIR",
        "WAL": "GBR",  # Wales
        "WAW": "POL",
        "YUG": "OTH",  # not found: Yugoslavia
        "CON": "COD",
        "ZR": "COD",
        "ZHZ": "CHN",
    }
    mapping_dict = {**mapping_dict, **mannual_mapping}

    # logger.info(f"Length of Source Country Code: {len(source_df)}")
    # logger.info(f"Total mapping length: {len(mapping_dict)}")

    return mapping_dict


def has_home_address(member_id: str, addr_df: pd.DataFrame) -> bool:
    df = addr_df[addr_df["member_id"] == member_id][haddress]
    return any(df.iloc[0] != "")


def has_company_address(member_id: str, addr_df: pd.DataFrame) -> bool:
    df = addr_df[addr_df["member_id"] == member_id][caddress]
    return any(df.iloc[0] != "")


def has_mail_address(member_id: str, addr_df: pd.DataFrame) -> bool:
    df = addr_df[addr_df["member_id"] == member_id][caddress]
    return any(df.iloc[0] != "")


def get_address_df(member_ids: list[str]):
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)

    addr_line_address = haddress_line + caddress_line + maddress_line
    source_address = haddress + caddress + maddress

    address_line_fields = ", ".join([cast_nvarchar(addr) for addr in addr_line_address])
    source_fields = ", ".join(source_address)
    query = f"""
    SELECT member_id, {address_line_fields}, {source_fields} FROM df_member WHERE member_id IN ({", ".join([f"'{id}'" for id in member_ids])})
    """
    df = get_df(query, mssql)

    # Apply .str.strip() to all string columns
    df = df.map(lambda x: x.strip() if isinstance(x, str) else x)
    return df


def generate_address_line(data: list[str]) -> str:
    # remove all "" from data
    for _ in range(data.count("")):
        data.remove("")

    if data == []:
        return None

    address_line = " ".join(data)
    # error will happen when we encrypt address_line that contain `'`
    # replace ' with '' (escape character)
    return address_line.replace("'", "''")


def home_address_line(member_id: str, addr_df: pd.DataFrame) -> str:
    df = addr_df[addr_df["member_id"] == member_id][haddress_line]

    df["haddr_moo"] = df["haddr_moo"].apply(lambda x: f"moo. {x}" if x else "")
    df["haddr_soi"] = df["haddr_soi"].apply(lambda x: f"soi. {x}" if x else "")
    df["haddr_road"] = df["haddr_road"].apply(lambda x: f"road. {x}" if x else "")

    data: list = df.values.tolist()[0]
    return generate_address_line(data)


def home_address(member_id: str, addr_df: pd.DataFrame, address_mapping: tuple[dict]):
    df = addr_df[addr_df["member_id"] == member_id][haddress]

    (
        province_mapping,
        district_mapping,
        subdistrict_mapping,
        postcode_mapping,
    ) = address_mapping

    df["haddr_subdistrict"] = (
        df["haddr_subdistrict"].map(subdistrict_mapping).replace(np.nan, None)
    )
    df["haddr_district"] = (
        df["haddr_district"].map(district_mapping).replace(np.nan, None)
    )
    df["haddr_city"] = df["haddr_city"].map(province_mapping).replace(np.nan, None)
    df["haddr_zip_code"] = (
        df["haddr_zip_code"].map(postcode_mapping).replace(np.nan, None)
    )
    # df["city"] = df["haddr_city"].map(province_name_mapping).replace(np.nan, None)

    data: list = df.values.tolist()[0]
    return data


def company_address_line(member_id: str, addr_df: pd.DataFrame):
    df = addr_df[addr_df["member_id"] == member_id][caddress_line]

    # Find Address Line
    df["caddr_floor"] = df["caddr_floor"].apply(lambda x: f"floor. {x}" if x else "")
    df["caddr_soi"] = df["caddr_soi"].apply(lambda x: f"soi. {x}" if x else "")
    df["caddr_road"] = df["caddr_road"].apply(lambda x: f"road. {x}" if x else "")

    data: list = df.values.tolist()[0]
    return generate_address_line(data)


def company_address(
    member_id: str, addr_df: pd.DataFrame, address_mapping: tuple[dict]
):
    df = addr_df[addr_df["member_id"] == member_id][caddress]

    (
        province_mapping,
        district_mapping,
        subdistrict_mapping,
        postcode_mapping,
    ) = address_mapping

    df["caddr_subdistrict"] = (
        df["caddr_subdistrict"].map(subdistrict_mapping).replace(np.nan, None)
    )
    df["caddr_district"] = (
        df["caddr_district"].map(district_mapping).replace(np.nan, None)
    )
    df["caddr_city"] = df["caddr_city"].map(province_mapping).replace(np.nan, None)
    df["caddr_zip_code"] = (
        df["caddr_zip_code"].map(postcode_mapping).replace(np.nan, None)
    )
    # df["city"] = df["caddr_city"].map(province_name_mapping).replace(np.nan, None)

    data: list = df.values.tolist()[0]
    return data


def mail_address_line(member_id: str, addr_df: pd.DataFrame):
    df = addr_df[addr_df["member_id"] == member_id][maddress_line]

    # # Apply .str.strip() to all string columns
    # df = df.map(lambda x: x.strip() if isinstance(x, str) else x)

    # Find Address Line
    df["maddr_moo"] = df["maddr_moo"].apply(lambda x: f"moo. {x}" if x else "")
    df["maddr_soi"] = df["maddr_soi"].apply(lambda x: f"soi. {x}" if x else "")
    df["maddr_road"] = df["maddr_road"].apply(lambda x: f"road. {x}" if x else "")

    data: list = df.values.tolist()[0]
    return generate_address_line(data)


def mail_address(member_id: str, addr_df: pd.DataFrame, address_mapping: tuple[dict]):
    df = addr_df[addr_df["member_id"] == member_id][maddress]

    # # Apply .str.strip() to all string columns
    # df = df.map(lambda x: x.strip() if isinstance(x, str) else x)

    (
        province_mapping,
        district_mapping,
        subdistrict_mapping,
        postcode_mapping,
    ) = address_mapping

    df["maddr_subdistrict"] = (
        df["maddr_subdistrict"].map(subdistrict_mapping).replace(np.nan, None)
    )
    df["maddr_district"] = (
        df["maddr_district"].map(district_mapping).replace(np.nan, None)
    )
    df["maddr_city"] = df["maddr_city"].map(province_mapping).replace(np.nan, None)
    df["maddr_zip_code"] = (
        df["maddr_zip_code"].map(postcode_mapping).replace(np.nan, None)
    )
    # df["city"] = df["maddr_city"].map(province_name_mapping).replace(np.nan, None)

    data: list = df.values.tolist()[0]
    return data


def transform_address(
    member_id: str,
    staff_source: str,
    address_df: pd.DataFrame,
    address_mapping: tuple[dict],
) -> tuple:
    has_company_addr = has_company_address(member_id, address_df)

    if staff_source and has_company_addr:  # "company staff" and "has company address"
        address_line = company_address_line(member_id, address_df)
        # subdistrict, district, province, postcode, city
        address: list = company_address(member_id, address_df, address_mapping)
        return address_line, *address

    if has_home_address(member_id, address_df):
        address_line = home_address_line(member_id, address_df)
        address = home_address(member_id, address_df, address_mapping)
        return address_line, *address

    if has_company_addr:
        address_line = company_address_line(member_id, address_df)
        address = company_address(member_id, address_df, address_mapping)
        return address_line, *address

    if has_mail_address(member_id, address_df):
        address_line = mail_address_line(member_id, address_df)
        address = mail_address(member_id, address_df, address_mapping)
        return address_line, *address

    return None, None, None, None, None


def business_mapping_dict() -> dict[int, int]:
    "mapping by business id"
    # occupation_mapping = {1: 12, 2: None, 3: 14, 4: 13, 5: None, 6: 4, 99: 15}
    return {1: 12, 3: 14, 4: 13, 6: 4, 99: 15}


def get_province_df(provinces: list[dict]) -> pd.DataFrame:
    """
    Create DataFrame that contain `provinceId` and `province`
    from Master's Data (Destination: GWL)
    """
    ids = []
    names = []
    en_names = []

    for p in provinces:
        ids.append(p["id"])
        names.append(p["locale"]["th"])
        en_names.append(p["locale"]["en"])

    return pd.DataFrame(
        data={"provinceId": ids, "province": names, "province_en": en_names}
    )


def get_district_df(districts: list[dict]) -> pd.DataFrame:
    """
    Create DataFrame that contain `districtId` and `district`
    from Master's Data (Destination: GWL)
    """
    ids = []
    names = []

    for d in districts:
        ids.append(d["id"])
        names.append(d["locale"]["th"])

    return pd.DataFrame(data={"districtId": ids, "district": names})


def get_subdistrict_df(subdistricts: list[dict]) -> pd.DataFrame:
    """
    Create DataFrame that contain `subDistrictId` and `subdistrict`
    from Master's Data (Destination: GWL)
    """
    ids = []
    names = []

    for s in subdistricts:
        ids.append(s["id"])
        names.append(s["locale"]["th"])

    return pd.DataFrame(data={"subDistrictId": ids, "subdistrict": names})


def get_address_data_df(data: list[dict]) -> pd.DataFrame:
    """
    Create DataFrame that contain `provinceId`, `districtId`, `subDistrictId` and `postalCode`
    from Master's Data (Destination: GWL)
    """
    p_ids = []
    d_ids = []
    s_ids = []
    postcodes = []

    for d in data:
        p_ids.append(d["provinceId"])
        d_ids.append(d["districtId"])
        s_ids.append(d["subDistrictId"])
        postcodes.append(d["postalCode"])

    return pd.DataFrame(
        data={
            "provinceId": p_ids,
            "districtId": d_ids,
            "subDistrictId": s_ids,
            "postalCode": postcodes,
        }
    )


def get_dest_address_dfs():
    token = get_access_token(ACCESS_TOKEN_URL)
    data = get_api_data(ADDRESS_URL, HEADERS, token)

    province_df = get_province_df(data["provinces"])
    district_df = get_district_df(data["districts"])
    subdistrict_df = get_subdistrict_df(data["subDistricts"])
    data_df = get_address_data_df(data["data"])

    return data_df, province_df, district_df, subdistrict_df


def dest_address_df():
    """
    Create DataFrame that contain `provinceId`, `districtId`, `subDistrictId`,
    `postalCode`, `province`, `province_en`, `district` and `subdistrict` from Master's Data (Destination: GWL)
    """
    data_df, province_df, district_df, subdistrict_df = get_dest_address_dfs()

    df = pd.merge(data_df, province_df, on="provinceId", how="left")
    df = pd.merge(df, district_df, on="districtId", how="left")
    df = pd.merge(df, subdistrict_df, on="subDistrictId", how="left")

    return df


def get_source_province_df() -> pd.DataFrame:
    """
    Create Dataframe that contain `city_code` and `province` columns from Source DB
    """
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)

    query = f"SELECT city_code, {cast_nvarchar('city_name')} FROM mst_city"
    df = get_df(query, mssql)

    # rename column
    df.rename(
        columns={"city_name": "province"},
        inplace=True,
    )

    # removes any leading and trailing spaces
    df["province"] = df["province"].apply(lambda p: p.strip())

    return df


def get_source_district_df() -> pd.DataFrame:
    """
    Create Dataframe that contain `district_code`, `district`, `city_code` columns from Source DB
    """
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)

    query = f"SELECT district_code, {cast_nvarchar('district_name')}, city_code FROM mst_district"
    df = get_df(query, mssql)

    # rename column
    df.rename(
        columns={"district_name": "district"},
        inplace=True,
    )

    # removes any leading and trailing spaces
    df["district"] = df["district"].apply(lambda d: d.strip())

    return df


def get_source_subdistrict_df() -> pd.DataFrame:
    """
    Create Dataframe that contain `subdistrict_code`, `subdistrict`, `district_code` and `zip_code` columns from Source DB
    """
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)

    query = f"SELECT subdistrict_code, {cast_nvarchar('subdistrict_name')}, district_code, zip_code FROM mst_subdistrict"
    df = get_df(query, mssql)

    # rename column
    df.rename(
        columns={"subdistrict_name": "subdistrict"},
        inplace=True,
    )

    # removes any leading and trailing spaces
    df["subdistrict"] = df["subdistrict"].apply(lambda s: s.strip())

    return df


def source_address_df() -> pd.DataFrame:
    """
    Create Dataframe that contain `city_code`, `province`, `district_code`, `district`,
    `subdistrict_code` and `subdistrict` columns from Source DB
    """
    province_df = get_source_province_df()
    district_df = get_source_district_df()
    subdistrict_df = get_source_subdistrict_df()

    df = pd.merge(province_df, district_df, on="city_code", how="right")
    df = pd.merge(df, subdistrict_df, on="district_code", how="right")

    # Apply the transformation only for rows where province is "กรุงเทพมหานคร"
    df.loc[df["province"] == "กรุงเทพมหานคร", "district"] = df.loc[
        df["province"] == "กรุงเทพมหานคร", "district"
    ].apply(lambda n: f"เขต{n}")

    return df


def issued_address_df():
    """
    Dataframe of issued address data. The dataframe will contain the following columns:
    `subdistrict_code`, `subDistrictId`, `districtId`, `provinceId`, `province_en` and `postalCode`
    """
    df = read_csv(ISSUED_ADDR_PATH, usecols=["subdistrict_code", "GWL subdistrict id"])
    df.rename(columns={"GWL subdistrict id": "subDistrictId"}, inplace=True)

    # change type from int64 -> str
    # convert subdistric_code from 7 -> 000007, 120 -> 000120
    df["subdistrict_code"] = (
        df["subdistrict_code"].astype(str).apply(lambda n: str(n).zfill(6))
    )

    # add districtId, ProcvinceId
    df["subDistrictId"] = df["subDistrictId"].astype(str)

    # add postCode to df
    data_df = dest_address_df()[
        ["subDistrictId", "districtId", "provinceId", "province_en", "postalCode"]
    ]
    df = df.merge(data_df, on="subDistrictId", how="left")

    return df


def get_address_mapping_dict() -> tuple[dict]:
    """
    Return dictionary for subDistrict and postalCode mapping
    """
    source_df = source_address_df()
    dest_df = dest_address_df()

    df = pd.merge(
        source_df, dest_df, on=["province", "district", "subdistrict"], how="left"
    )

    unmap_df = df[df["subDistrictId"].isna()][
        ["city_code", "district_code", "subdistrict_code", "zip_code"]
    ]

    # remove the data that can't mapping
    df = df[~df["subDistrictId"].isna()]

    # df from issued address sheet (contain data that we can't mapping)
    address_df = issued_address_df()
    address_df = unmap_df.merge(address_df, on="subdistrict_code", how="left")

    # add data from address_df to the main df
    df = pd.concat([df, address_df], ignore_index=True)

    df = df[
        [
            "subdistrict_code",
            "subDistrictId",
            "district_code",
            "districtId",
            "city_code",
            "provinceId",
            "province_en",
            "zip_code",
            "postalCode",
        ]
    ]

    # Apply .str.strip() to all string columns
    df = df.map(lambda x: x.strip() if isinstance(x, str) else x)

    # get mapping dictionary
    province_mapping = dict(zip(df["city_code"], df["provinceId"]))
    # province_name_mapping = dict(zip(df["provinceId"], df["province_en"]))
    district_mapping = dict(zip(df["district_code"], df["districtId"]))
    subdistrict_mapping = dict(zip(df["subdistrict_code"], df["subDistrictId"]))

    # Filter out postcode where old_postcode == new_postcode
    postcode_mapping = dict(zip(df["zip_code"], df["postalCode"]))

    return (
        province_mapping,
        district_mapping,
        subdistrict_mapping,
        postcode_mapping,
        # province_name_mapping,
    )
