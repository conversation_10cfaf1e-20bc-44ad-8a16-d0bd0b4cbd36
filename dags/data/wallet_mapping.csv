Value Code,Value Name,Type,WalletCode,WalletName,Restriction,Commission,,,,
AP001,E-Money Cash Arrival Pre-Order,Cash Wallet,CASH_WALLET,CASH WALLET,N/A,,,,,
EP001,E-Cash for KPI,Cash Wallet,EPURSE_PROMO_RANGNAM,E-PURSE use at Rangnam exclusively,N/A,Y,To create memo on cost allocation in Leadership,,,
EP002,E-Purse from birthday KPI,EP BD,EPURSE_BD_CASHBACK,BIRTHDAY CASHBACK,"Channel, Location, Expiry",,,,,
EP003,E-Purse from birthday KPD,EP BD,EPURSE_BD_CASHBACK,BIRTHDAY CASHBACK,"Channel, Location, Expiry",,,,,
EP004,E-Purse from birthday KPT,EP BD,EPURSE_BD_CASHBACK,BIRTHDAY CASHBACK,"Channel, Location, Expiry",,,,,
EP005,E-Purse Adjust by member,EP Promo,EPURSE_PROMO_WALLET,E-PURSE use at All King Power stores,,,,,,
EP006,E-CASH BACK,EP Promo,EPURSE_PROMO_RANGNAM,E-PURSE use at Rangnam exclusively,"Channel, Location, Expiry",,,,,
EP007,E-Money Cash,Cash Wallet,CASH_WALLET,CASH WALLET,N/A,Y,,,,
EP008,Debit Cash,Cash Wallet,CASH_WALLET,CASH WALLET,N/A,Y,,,,
EP009,Smart Purse,Cash Wallet,CASH_WALLET,CASH WALLET,N/A,Y,,,,
EP010,Smart Purse Online,Cash Wallet,CASH_WALLET,CASH WALLET,N/A,Y,,,,
KPC01,King Power Card Thai,Cash Wallet,CASH_WALLET,CASH WALLET,N/A,Y,,,,
KPO02,King Power Oversea Online,Cash Wallet,CASH_WALLET,CASH WALLET,N/A,,,,,
CR001,Carat,Carat Wallet,CARAT_WALLET,CARAT WALLET,N/A,,,,,
PT001,Point,Carat Wallet,CARAT_WALLET,CARAT WALLET,,,,,,
,,,,,,,,,,
,,,,,,,,,,
,,,,,,,,,,
,,,,,,,,,,
Wallet Type,Wallet Code,,,,,,,,,
Cash Wallet,CASH_WALLET,,,,,,,,,
EP BD,EPURSE_BD_CASHBACK,,,,,,,,,
EP Promo,EPURSE_PROMO_WALLET,,,,,,,,,
Point,CARAT_WALLET,,,,,,,,,
,,,,,,,,,,
,,,,,,,,,,
,,,,,,,,,,
, ,,,,,,,,,Wallet type seeding
,,,,,,,,,,"INSERT INTO ""public"".""WalletType"" (""code"", ""name"") VALUES"
,,,,,,,,,,"('POINT', 'Point'),"
,,,,,,,,,,"('CASH', 'Cash'),"
,,,,,,,,,,"('BIRTHDAY_CASHBACK', 'Birthday Cashback'),"
,,,,,,,,,,"('EPURSE', 'E-Purse');"
,,,,,,,,,,
,,,,,,,,,,Wallet seeding
,,,,,,,,,,"INSERT INTO ""public"".""Wallet"" (""id"", ""runningId"", ""code"", ""name"", ""walletTypeCode"", ""status"", ""description"", ""currency"", ""image"", ""createdAt"", ""updatedAt"", ""updatedBy"") VALUES"
,,,,,,,,,,"('01JE2QN8JHQ0HC5FHRRZBVAQ7Q', 1, 'CARAT_WALLET', 'Carat Wallet', 'POINT', 'ACTIVE', 'Wallet to collect Carat for Kingpower member', 'CARAT', NULL, '2024-12-02 03:47:08.375', '2024-12-02 03:47:08.375', '{}'),"
,,,,,,,,,,"('01JE2QNAEF61ZGPARVX5T5M9KM', 2, 'CASH_WALLET', 'Cash Wallet', 'CASH', 'ACTIVE', 'Wallet to top up member''s money in to wallet via counter service (via GV)', 'THB', NULL, '2024-12-02 03:47:08.882', '2024-12-02 03:47:08.882', '{}'),"
,,,,,,,,,,"('01JE2QNAFN6T702AQ1M7VGDT67', 3, 'EPURSE_BD_CASHBACK', 'Birthday Cashback', 'BIRTHDAY_CASHBACK', 'ACTIVE', 'Wallet to receive cashback amount when members use birthday privilege coupon', 'THB', NULL, '2024-12-02 03:47:08.918', '2024-12-02 03:47:08.918', '{}'),"
,,,,,,,,,,"('01JKG09W2KNN6538ZH42WQ60PX', 4, 'EPURSE_PROMO_WALLET', 'E-Purse', 'EPURSE', 'ACTIVE', 'Wallet to receive promotion cashback', 'THB', NULL, '2024-12-02 03:47:08.918', '2024-12-02 03:47:08.918', '{}');"
,,,,,,,,,,"('01JNNFJCD6PWTVPY522DRCGZ98', 5, 'EPURSE_PROMO_RANGNAM', Rangnam', 'EPURSE', 'ACTIVE', 'Wallet to receive promotion cashback, only allowed to use at Rangnam', 'THB', NULL, '2024-12-02 03:47:08.918', '2024-12-02 03:47:08.918', '{}');"
