No,Target DB,Service,Table,Field,Data Type,Required?,In Scope (Y/N),Description,Required Under Conditions,Format of Data,Data Format Requirements,Example,SMC Database,SMC Table,SMC Field,"Sensitive 
(e.g. PDPA)
(Y/N)","Cleaning required 
(Y/N)","1:N or N:1 Mapping required
(Y/N)",Transfor-mation Required (Y/N),Ready for Migration (Y/N),Req Business Decision? (Y/N),Note,Mapping/Transformation Logic,,
1,GWL,Loyalty Service,MemberProfile,id,String,Y,N,Unique identifier for member profile data,-,ULID,See ULID in Notes tab,01JAPF6R5QQ763ER3QG3A4EX9S,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,member_id,Generate ULID,,
2,GWL,Loyalty Service,MemberProfile,memberId,String,Y,Y,Identifier of a member,-,ULID,See ULID in Notes tab,01JAPF6R5QQ763ER3QG3A4EX9S,Newmember,df_member,member_id,N,N,Y,N,Y,N,,,,
3,GWL,Loyalty Service,MemberProfile,title,Int,N,Y,Salutation of member,-,Enum,TBD,Mr.,Newmember,df_member,title_id,N,N,N,Y,N,N,,Map from Title ID mapping sheet,,
4,GWL,Loyalty Service,MemberProfile,firstName,String,Y,Y,First name of member,-,-,See Name Requirements in Notes tab,Somchai,Newmember,df_member,ename,Y,N,Y,Y,N,Y,"Use space logic, remainder clear case by case. Consider CN KR names","Use first word before space as firstName. If more than 2 spaces, migrate all as firstname, last name leave as empty string",,
5,GWL,Loyalty Service,MemberProfile,firstNameTh,String,N,Y,First name of member in Thai,-,-,See Name Requirements in Notes tab,?????,Newmember,df_member,tname,Y,N,Y,Y,N,Y,"Use space logic, remainder clear case by case. Consider CN KR names","Use first word before space as firstNameTh. If more than 2 spaces, migrate all as firstname, last name leave as empty string",,
6,GWL,Loyalty Service,MemberProfile,middleName,String,N,Y,Middle name of member,-,-,See Name Requirements in Notes tab,"""""",Newmember,df_member,ename,Y,N,Y,Y,N,Y,"Use space logic, remainder clear case by case. Consider CN KR names","Use the words between first & last space as middleName. For Thai, skip middle name",,
7,GWL,Loyalty Service,MemberProfile,middleNameTh,String,N,Y,Middle name of member in Thai,-,-,See Name Requirements in Notes tab,"""""",Newmember,df_member,tname,Y,N,Y,Y,N,Y,"Use space logic, remainder clear case by case. Consider CN KR names","Use the words between first & last space as middleNameTh. For Thai, skip middle name",,
8,GWL,Loyalty Service,MemberProfile,lastName,String,Y,Y,Last name of member,-,-,See Name Requirements in Notes tab,Jaidee,Newmember,df_member,ename,Y,N,Y,Y,N,Y,"Use space logic, remainder clear case by case. Consider CN KR names","Use the word after last space as lastName. For Thai, any word after first word all last name",,
9,GWL,Loyalty Service,MemberProfile,lastNameTh,String,N,Y,Last name of member in Thai,-,-,See Name Requirements in Notes tab,????,Newmember,df_member,tname,Y,N,Y,Y,N,Y,"Use space logic, remainder clear case by case. Consider CN KR names","Use the word after last space as lastNameTh. For Thai, any word after first word all last name",,
10,GWL,Loyalty Service,MemberProfile,cid,String,C,Y,Citizen ID of member,only required when member is of Thai nationality,NNNNNNNNNNNNN,Must be a valid Thai ID per https://codepen.io/jukbot/pen/JvZyeK,1.12346E+12,Newmember,df_member,id_card,Y,Y,N,N,Y,Y,Already cleaned for Thais (checked data),,,
11,GWL,Loyalty Service,MemberProfile,passportNo,String,C,Y,Passport number of member (passport #'s across countries are not unique),only required when member is not of Thai nationality,-,Impose logic that passports within a country must be unique,MD999999,Newmember,df_member,passport_no,Y,Y,Y,Y,Y,Y,"Migrate as is, if required but blank member will be marked pending verification","If duplicate for Thai, migrate as blank. If duplicate as foreigner but contains unique email, migrate as dummy. Otherwise, cannot migrate member",,
12,GWL,Loyalty Service,MemberProfile,passportExpiryDate,Date,C,Y,Passport expiry date of member,only required when member is not of Thai nationality,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,Newmember,df_member,passport_expiry_date,N,Y,N,Y,Y,N,,,,
13,GWL,Loyalty Service,MemberProfile,dateOfBirth,Date,Y,Y,Date of birth of member,-,yyyy-MM-dd'T'hh:mm:ss'Z',See DoB Requirements in Notes Tab,2001-05-14T00:00:00z,Newmember,df_member,date_of_birth,Y,Y,N,N,Y,N,have to check extreme age (e.g. 150+),N/A,,
14,GWL,Loyalty Service,MemberProfile,gender,String,N,Y,Gender of member,-,Enum,TBD,"M, F, O",Newmember,df_member,sex,Y,N,N,N,N,N,,"(Refer to Master Data Member sheet) Map SMC id to GWL id, and link w.  new master gender json ",,
15,GWL,Loyalty Service,MemberProfile,occupation,Int,N,Y,Occupation of member,-,Enum,TBD,1,Newmember,df_member,bussiness_id,N,N/A,N/A,N/A,N,N,,"(Refer to Master Data Member sheet) Map applicable (Government, State Enterprise, Others)",,
16,GWL,Loyalty Service,MemberProfile,nationalityCode,String,Y,Y,Nationality of member,-,Enum,TBD,1,Newmember,"df_member, mst_country","country_code, country_name",N,Y,N,Y,N,N,alpha3 (unique),"(Refer to Master Data Member) Map SMC code to GWL code, and link w.  new master nationality json",,
17,GWL,Loyalty Service,MemberProfile,addressLine,String,N,Y,"Detailed residence address of member (inc. road, street)",-,-,TBD,127 Ratchadamri Rd,Newmember,df_member,"haddr_number, haddr_bldg, haddr_moo, haddr_soi, haddr_road
/ caddr_xxx/ maddr_xxx",Y,Y,Y,Y,N,N,,"Use FlagGenAddress to get what address to prioritize (1=home,2=office,3=others,4=not mail), otherwise prioritize H -> W -> M",,
18,GWL,Loyalty Service,MemberProfile,subDistrict,Int,N,Y,Sub district of member residence address,-,-,TBD,Lumphini,Newmember,df_member,haddr_subdistrict/ caddr_subdistrict/ maddr_subdistrict,N,Y,N,N,N,N,,"Prioritize from home->company->mailing, if at least one of the field exists (Xaddr_subdistrict, Xaddr_district, Xaddr_city, Xaddr_zip_code) in the category pick that one
Use mailing_addr to get what address to prioritize (1=home,2=office,3=others,4=not mail), otherwise prioritize H -> W -> M",,
19,GWL,Loyalty Service,MemberProfile,district,Int,N,Y,District of member residence address,-,-,"If Thailand, must be within the following list of 978 districts https://web.archive.org/web/20180729111706/http://www.en.moe.go.th/enMoe2017/index.php/educational-statistics/educational-statistics-2016",Pathum Wan,Newmember,df_member,haddr_district/ caddr_district/ maddr_district,N,Y,N,N,N,N,,"Prioritize from home->company->mailing, if at least one of the field exists in the category pick that one
Use mailing_addr to get what address to prioritize (1=home,2=office,3=others,4=not mail), otherwise prioritize H -> W -> M",,
20,GWL,Loyalty Service,MemberProfile,province,Int,N,Y,Province of member residence address,-,-,"If Thailand, must be within the following list of 76 provinces https://en.wikipedia.org/wiki/Provinces_of_Thailand",Bangkok,Newmember,df_member,haddr_city/ caddr_city/ maddr_city,N,N,Y,Y,N,N,,"Prioritize from home->company->mailing, if at least one of the field exists in the category pick that one
Use mailing_addr to get what address to prioritize (1=home,2=office,3=others,4=not mail), otherwise prioritize H -> W -> M",,
21,GWL,Loyalty Service,MemberProfile,city,String,N,Y,City of member residence address,-,-,TBD,Bangkok,Newmember,df_member,haddr_city/ caddr_city/ maddr_city,N,N,N,N,N,N,,"Prioritize from home->company->mailing, if at least one of the field exists in the category pick that one
Use mailing_addr to get what address to prioritize (1=home,2=office,3=others,4=not mail), otherwise prioritize H -> W -> M",,
22,GWL,Loyalty Service,MemberProfile,countryCode,String,N,N,Country of member residence address,-,Enum,TBD,Thailand,Newmember,df_member,,N,N,Y,N,N,N,"ref from city  ???? ??? John ???? udon ?? ???? ""THA""","Migrate with address if available (Alpha3Code), need to check against https://kingpowerclick.atlassian.net/wiki/spaces/KPGDX/pages/3796828280/10.+Master+Data#10.3-Country-(list) whether the Alpha3Code exists in the GWL's list",,
23,GWL,Loyalty Service,MemberProfile,postalCode,String,N,Y,Postal of member residence address,-,-,TBD,10330,Newmember,df_member,haddr_zip_code/ caddr_zip_code/ maddr_zip_code,N,N,N,N,N,N,\,"Prioritize from home->company->mailing, if at least one of the field exists in the category pick that one
Use mailing_addr to get what address to prioritize (1=home,2=office,3=others,4=not mail), otherwise prioritize H -> W -> M""",,
24,GWL,Loyalty Service,MemberProfile,createdAt,Datetime,Y,N,The date and time when the member profile was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
25,GWL,Loyalty Service,MemberProfile,updatedAt,Datetime,Y,N,The date and time when the member profile info was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
45,GWL,Loyalty Service,Member,accumulateMaintainSpending,Decimal,Y,N,spending after he get into latest tier,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,Required but default = 0,,
44,GWL,Loyalty Service,Member,accumulateSpending,Decimal,Y,Y,Member current tier accumulate spending (2 yrs timeframe),-,-,TBD,123456,Newmember,SMCSalesTrans,Net,N,N,N,Y,N,Y,2 year = Last 24 months,Sum Net value for last 2 year transactions (migrate SalesTrans into staging environment first) from datetime that SMC goes down for GWL deployment [Note: need to include accumspending logic into what counts (filter by payment method)],,
53,GWL,Loyalty Service,Member,createdAt,Datetime,Y,N,The date and time when the member account was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-18T12:34:56Z,,df_member,add_datetime,N/A,N/A,N/A,N/A,N/A,N/A,,,,
40,GWL,Loyalty Service,Member,deletedAt,Datetime,N,N,The date and time when the member account was deleted,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
31,GWL,Loyalty Service,Member,email,String,C,Y,Email of member,only required when phone is null or member is not Thai nationality,-,TBD,<EMAIL>,Newmember,df_member,email,Y,Y,N,N,N,Y,"Cleansing logic in Email cleansing logic worksheet
If duplicate check for unique phone, otherwise migrate as blank (will be marked pending verification)","Check Email Cleansing sheet, after cleaning check Contact Info Handling (in case email cleansing creates more duplicates)",,
32,GWL,Loyalty Service,Member,emailVerifiedAt,Datetime,N,N,The date and time when the member email is verified,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
28,GWL,Loyalty Service,Member,embossNo,String,N,Y,EmbossNo of member,-,-,TBD,1.8E+15,Newmember,df_member,emboss_id,Y,N,N,N,Y,N,,N/A,,
27,GWL,Loyalty Service,Member,gwlNo,String,Y,Y,Unique running number of member (same as SMC memberID),-,-,TBD,2019447,Newmember,df_member,member_id,N,N,N,N,Y,N,,N/A,,
26,GWL,Loyalty Service,Member,id,String,Y,Y,Unique identifier of a member,-,ULID,TBD,01J57S7Y1JKABX5P8PE0R21THZ,,N/A,N/A,N,N/A,N/A,N/A,N,N/A,member_id,Generate ULID,,
36,GWL,Loyalty Service,Member,isActive,Boolean,Y,Y,Status of whether the member account is still active,-,Enum,TBD,TRUE,Newmember,df_member,del_flag,N,Y,N,Y,N,N,,Only migrate members where del_flag is BLANK,,
46,GWL,Loyalty Service,Member,lifeTimeSpending,Decimal,Y,Y,Overall spending of member,-,-,TBD,123456,Newmember,SMCSalesTrans,Net,N,N,N,Y,N,N,,Sum all (same as accum spending logic or no?),,
30,GWL,Loyalty Service,Member,onepassId,String,N,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
34,GWL,Loyalty Service,Member,phone,String,C,Y,Phone number of member,only required when member is Thai nationality and email is null,-,TBD,*********,Newmember,df_member,mobile1/ mobile2/ china_mobile,Y,Y,N,N,N,Y,"If duplicate check for unique email, otherwise migrate as blank (will be marked pending verification",Check Contact Info Handling sheet; For CHN member use china_mobile,,
33,GWL,Loyalty Service,Member,phoneCode,String,C,Y,Country code of member phone number,only required when email is null,-,TBD,66,,N/A,N/A,N,N/A,N/A,N/A,N,Y,Check with SMC team on phone code,"(Refer to Master Data Member) Add ""+"" before phone code. For Thai default as 66. Put country code based on member nationality if migrating unique",,
35,GWL,Loyalty Service,Member,phoneVerifiedAt,Datetime,N,N,The date and time when the member phone number is verified,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
47,GWL,Loyalty Service,Member,picRemark,String,N,N,Remark on person in charge,-,-,TBD,Contact Mr. A using +66xxx,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
37,GWL,Loyalty Service,Member,reason,String,N,N,Reason why member account is inactive,-,-,TBD,Duplicated Account,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,inactive reason - free text,,,
48,GWL,Loyalty Service,Member,referralCode,String,N,Y,Info or ID of the staff who refer respective customer to be KP member,-,-,TBD,KPC00025,Newmember,df_member,staff_source,N,N/A,N/A,N/A,N,Y,,Use staff_source for staff referral if available,,
39,GWL,Loyalty Service,Member,registeredAt,Datetime,Y,Y,The date and time when the member account was initially registered,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,Newmember,df_member,add_datetime,N,N,N,N,N,N,,register timestamps df_member.add_datetime,,
51,GWL,Loyalty Service,Member,registrationChannelCode,String,Y,Y,"Code for the channel of registry (KP: migrate normalized text, Terra: refine it to be code)",-,Enum,TBD,1,,df_member,subprogram_id,N,,,,N,N,,"Map from sub-program mapping sheet, default = NA",,
52,GWL,Loyalty Service,Member,registrationLocationCode,String,Y,Y,"Code for the location of registry (KP: migrate normalized text, Terra: refine it to be code)",-,Enum,TBD,1,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,default = NA,,
38,GWL,Loyalty Service,Member,remark,String,Y,Y,,,,,,Newmember,df_member,remark,N,,,,Y,N,New field,"default = """"",,added
29,GWL,Loyalty Service,Member,shoppingCardId,String,N,Y,,,,,,Newmember,df_member,shopping_card,N,N,N,N,Y,N,,N/A,,
43,GWL,Loyalty Service,Member,tierEndedAt,Datetime,Y,Y,The date and time when the specific tier of the member will be ended,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-18T12:34:56Z,Newmember,df_cardhist,,N,N,N,Y,N,Y,use NOW() (will be replaced anyway),"[Terra] Dec 31 2027, but for grace period users need to put end of grace period",,
41,GWL,Loyalty Service,Member,tierId,String,Y,Y,Member current tier,-,ULID,TBD,01J57S7Y1JKABX5P8PE0R21THZ,Newmember,df_member,,N,N,Y,N,N,N,Should we create on-top logic for GWL using MemberLegacy table?,"Default as Navy [Terra] Transform with tier mapping logic (LegacyTierHistory, AccumSpend, CoBrandCard) to GWL tier",,
42,GWL,Loyalty Service,Member,tierStartedAt,Datetime,Y,Y,The date and time when the specific tier of the member was started,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-18T12:34:56Z,Newmember,df_cardhist,,N,N,N,Y,N,Y,use NOW() (will be replaced anyway),[Terra] Go-live time of GWL,,
54,GWL,Loyalty Service,Member,updatedAt,Datetime,Y,N,The date and time when the member account was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-18T12:34:56Z,,df_member,update_datetime,N/A,N/A,N/A,N/A,N/A,N/A,,,,
49,GWL,Loyalty Service,Member,upgradeGroupCode,String,N,Y,"Code for the group of tier upgrade of a member (KP: migrate normalized text, Terra: refine it to be code)",-,Enum,TBD,1,Newmember,df_member,subprogram_id,N,Y,N,Y,N,N,,Map from sub-program mapping sheet,,
50,GWL,Loyalty Service,Member,upgradeReasonCode,String,N,Y,"Code for the reason of tier upgrade of a member (KP: migrate normalized text, Terra: refine it to be code)",-,Enum,TBD,1,Newmember,df_member,subprogram_id,N,N,N,Y,N,N,,Map from sub-program mapping sheet,,
55,GWL,Loyalty Service,StaffProfile,id,String,Y,Y,Unique identifier of staff profile data for table row,-,ULID,TBD,01JAPF6R5QQ763ER3QG3A4EX9S,,N/A,N/A,N,N/A,N/A,N/A,N,N/A,,Generate ULID,,
56,GWL,Loyalty Service,StaffProfile,memberId,String,Y,Y,Identifier of GWL membership (1:1 relationship w. Member table),-,ULID,TBD,01JAPF6R5QQ763ER3QG3A4EX9S,Newmember,df_member,member_id,N,N,Y,N,Y,N,,,,
57,GWL,Loyalty Service,StaffProfile,staffLevelCode,String,Y,Y,Code for the level of staff,-,Enum,TBD,1,,,,N,,,,N,N,,Map from sub-program mapping sheet,,
58,GWL,Loyalty Service,StaffProfile,companyCode,String,Y,Y,Code for the company of staff,-,Enum,TBD,1,Newmember,mst_company_staff,company_code,N,N,N,N,Y,N,mapping using subprogram id ?,N/A,,
59,GWL,Loyalty Service,StaffProfile,staffNo,String,Y,Y,Unique King Power staff no,-,-,TBD,KPC00025,Newmember,TableAttribute,ValueString,N,N,N,N,N,N,,"Look for TableAttribute.ValueCode = df_member.member_id and TableAttribute.TableKey=member_id and AttributeName=staff_id
default as """"",,
60,GWL,Loyalty Service,StaffProfile,createdAt,Datetime,Y,N,The date and time when the staff profile was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
61,GWL,Loyalty Service,StaffProfile,updatedAt,Datetime,Y,N,The date and time when the staff profile was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
,,Loyalty Service,StaffProfile,staffPosition,String,Y,Y,,,,,,,df_member,w_position,N,N,N,Y,N,,only for staff member,default=null,Y,
62,GWL,Loyalty Service,StaffLevel,code,String,Y,N,Unique code of the level of staff,-,-,TBD,1,,,,,,,,N,N,Do we have this in SMC?,,,
63,GWL,Loyalty Service,StaffLevel,name,String,Y,N,Name of the level of staff,-,-,TBD,Officier,,,,,,,,N,N,Do we have this in SMC?,,,
64,GWL,Loyalty Service,StaffLevel,createdAt,Datetime,Y,N,The date and time when the staff level was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
65,GWL,Loyalty Service,StaffLevel,updatedAt,Datetime,Y,N,The date and time when the staff level was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
66,GWL,Loyalty Service,StaffCompany,code,String,Y,Y,Unique code of the company of staff,-,-,TBD,1,Newmember,mst_company_staff,company_code,N,N,N,N,N,N,,Refer to Staff Master Data sheet,,
67,GWL,Loyalty Service,StaffCompany,name,String,Y,Y,Name of the company of staff,-,-,TBD,KPC,Newmember,mst_company_staff,company_code,N,N,N,N,N,N,,Refer to Staff Master Data sheet,,
68,GWL,Loyalty Service,StaffCompany,createdAt,Datetime,Y,N,The date and time when the company name was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
69,GWL,Loyalty Service,StaffCompany,updatedAt,Datetime,Y,N,The date and time when the company name was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
70,GWL,Loyalty Service,MemberLog,id,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
71,GWL,Loyalty Service,MemberLog,memberId,String,Y,N,,,,,,,df_member_log,N/A,N/A,N/A,N/A,N/A,N/A,Y,,,,
72,GWL,Loyalty Service,MemberLog,type,String,Y,N,,,,,,,df_member_log,N/A,N/A,N/A,N/A,N/A,N/A,Y,,,,
73,GWL,Loyalty Service,MemberLog,name,String,Y,N,,,,,,,df_member_log,N/A,N/A,N/A,N/A,N/A,N/A,Y,,,,
74,GWL,Loyalty Service,MemberLog,description,String[],Y,N,,,,,,,df_member_log,N/A,N/A,N/A,N/A,N/A,N/A,Y,,"default = """"",,
75,GWL,Loyalty Service,MemberLog,oldData,Json,N,N,,,,,,,df_member_log,N/A,N/A,N/A,N/A,N/A,N/A,Y,,,,
76,GWL,Loyalty Service,MemberLog,newData,Json,Y,N,,,,,,,df_member_log,N/A,N/A,N/A,N/A,N/A,N/A,Y,,,,
77,GWL,Loyalty Service,MemberLog,createdBy,Json,Y,N,,,,,,,df_member_log,N/A,N/A,N/A,N/A,N/A,N/A,Y,,,,
78,GWL,Loyalty Service,MemberLog,createdAt,Datetime,Y,N,,,,,,,df_member_log,N/A,N/A,N/A,N/A,N/A,N/A,Y,,,,
79,GWL,Loyalty Service,SalesTransaction,id,String,Y,Y,Sales transaction GWL id,-,ULID,TBD,01JAPF6R5QQ763ER3QG3A4EX9S,LoyaltyValue,LVHeader,LVHeaderKey,N,N/A,N/A,N/A,Y,N,Join with SMCSalesHeader->key_search,,,
80,GWL,Loyalty Service,SalesTransaction,memberId,String,Y,Y,Identifier of a member,-,ULID,TBD,01JAPF6R5QQ763ER3QG3A4EX9S,Newmember,SMCSalesHeader,member_id,N,N,Y,N,Y,N,,,,
81,GWL,Loyalty Service,SalesTransaction,externalId,String,Y,Y,Unique identifier for the sales transaction,-,-,TBD,1234567890ABCDEF,Newmember,SMCSalesHeader,key_search,N,N,N,N,N,N,put key_search; no transformation,N/A,,
82,GWL,Loyalty Service,SalesTransaction,netTotalAmount,Decimal,Y,Y,Net value of sales transaction,-,-,TBD,30000,Newmember,SMCSalesTrans,Net,N,N,N,Y,Y,N,sum(Net),(migrate SalesTrans into staging environment first) from datetime that SMC goes down for GWL deployment [Note: need to include accumspending logic into what counts (filter by payment method)],,
83,GWL,Loyalty Service,SalesTransaction,totalDiscount,Decimal,Y,Y,total discount of sales transaction,-,-,TBD,1234,Newmember,SMCSalesTrans,Discount,N,N,N,Y,Y,N,sum(Discount),,,
84,GWL,Loyalty Service,SalesTransaction,totalEarnableAmount,Decimal,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
85,GWL,Loyalty Service,SalesTransaction,totalAccumSpendableAmount,Decimal,Y,Y,,,,,,,SMCSalesPayment,Net,N,N/A,N/A,N/A,N,N/A,sum(Net),"Bring in only payment method that can accum payment, [EPAccSale], cash,",,
86,GWL,Loyalty Service,SalesTransaction,partnerCode,String,Y,Y,,,,,,Newmember,SMCSalesHeader,BranchNo,N,N/A,N/A,N/A,Y,N,,Use Branch data to get Partner info,,
87,GWL,Loyalty Service,SalesTransaction,brandCode,String,Y,Y,,,,,,Newmember,SMCSalesHeader,BranchNo,N,N/A,N/A,N/A,Y,N,,Get the brand data reference from Branch,,
88,GWL,Loyalty Service,SalesTransaction,branchCode,String,Y,Y,,,,,,Newmember,SMCSalesHeader,Site,N,N/A,N/A,N/A,Y,N,,,,
89,GWL,Loyalty Service,SalesTransaction,completedAt,Datetime,Y,Y,The date and time when a transaction is completed,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,LoyaltyValue,LVHeader,AddDT,N,N,N,N,Y,N,,N/A,,
90,GWL,Loyalty Service,SalesTransaction,createdAt,Datetime,Y,Y,The date and time when a sales transaction was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,LoyaltyValue,LVHeader,AddDT,N,N,N,N,Y,N,,N/A,,
91,GWL,Loyalty Service,SalesTransaction,updatedAt,Datetime,Y,Y,The date and time when a sales transaction was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,LoyaltyValue,LVHeader,FinishDT,N,N,N,N,Y,N,,N/A,,
92,GWL,Loyalty Service,UpgradeGroup,code,String,Y,Y,Unique code for the tier upgrade reason group,-,-,TBD,1,Newmember,"df_member, mst_business","business_id, business_desc",N,Y,Y,N,N,N,,Map from sub-program mapping sheet,,
93,GWL,Loyalty Service,UpgradeGroup,name,String,Y,N,Name of tier upgrade reason group (program in SMC),-,-,TBD,Air Crew,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
94,GWL,Loyalty Service,UpgradeGroup,createdAt,Datetime,Y,N,The date and time when the member upgrade group was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
95,GWL,Loyalty Service,UpgradeGroup,updatedAt,Datetime,Y,N,The date and time when the member upgrade group was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
96,GWL,Loyalty Service,UpgradeReason,code,String,Y,Y,Unique code for the tier upgrade reason,-,-,TBD,1,Newmember,df_member,subprogram_id,N,N,Y,N,N,N,,Map from sub-program mapping sheet,,
97,GWL,Loyalty Service,upgradeReason,name,String,Y,N,Name of tier upgrade reason (sub program in SMC),-,-,TBD,THAI AIRWAYS - AIR CREW,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
98,GWL,Loyalty Service,upgradeReason,createdAt,Datetime,Y,N,The date and time when the reason of upgrading tier of member was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
99,GWL,Loyalty Service,upgradeReason,updatedAt,Datetime,Y,N,The date and time when the reason of upgrading tier of member was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
100,GWL,Loyalty Service,RegisterChannel,code,String,Y,N,Unique code of registry channel,-,-,TBD,1,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
101,GWL,Loyalty Service,RegisterChannel,name,String,Y,Y,Name of registry channel,-,-,TBD,Admin Portal,,,,N,,,,N,N,,Map from sub-program mapping sheet,,
102,GWL,Loyalty Service,RegisterChannel,createdAt,Datetime,Y,N,The date and time when the new registry channel was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
103,GWL,Loyalty Service,RegisterChannel,updatedAt,Datetime,Y,N,The date and time when the existing registry channel was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
104,GWL,Loyalty Service,RegisterLocation,code,String,Y,Y,Unique code of location of registry,-,-,TBD,1,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
105,GWL,Loyalty Service,RegisterLocation,name,String,Y,N,Name of registry location,-,-,TBD,KPD_SVB_AIRPORT,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
106,GWL,Loyalty Service,RegisterLocation,createdAt,Datetime,Y,N,The date and time when the location of new registry was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
107,GWL,Loyalty Service,RegisterLocation,updatedAt,Datetime,Y,N,The date and time when the location of existing registry was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
108,GWL,Loyalty Service,Tier,id,String,Y,N,Unique identifier of tier,-,ULID,TBD,01JAPF6R5QQ763ER3QG3A4EX9S,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
109,GWL,Loyalty Service,Tier,code,String,Y,N,Code of tier,-,Enum,TBD,SCARLET,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N_NY,,,
110,GWL,Loyalty Service,Tier,name,String,Y,N,Name of tier,-,Enum,TBD,Scarlet,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,NAVY,,,
111,GWL,Loyalty Service,Tier,type,String,Y,N,Type of tier,-,Enum,TBD,Normal,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
112,GWL,Loyalty Service,Tier,earnRate,Decimal,Y,N,Point earn rate of tier,-,-,TBD,1,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
113,GWL,Loyalty Service,Tier,isActive,Boolean,Y,N,Status of tier,-,Enum,TBD,TRUE,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
114,GWL,Loyalty Service,Tier,description,String,N,N,Description of tier,-,-,TBD,"""""",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
115,GWL,Loyalty Service,Tier,minimumSpending,Decimal,N,N,Amount of spending to enter itier,-,-,TBD,50000,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,"VVIP, Crystal not req any spending",,
116,GWL,Loyalty Service,Tier,maintainSpending,Decimal,N,N,Amount of spending to maintain itier,-,-,TBD,20000,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,"VVIP, Crystal not req any spending",,
117,GWL,Loyalty Service,Tier,image,Json,N,N,Tier image/logo,-,-,TBD,-,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
118,GWL,Loyalty Service,Tier,createdAt,Datetime,Y,N,The date and time when the tier was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
119,GWL,Loyalty Service,Tier,updatedAt,Datetime,Y,N,The date and time when the tier info was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
120,GWL,Loyalty Service,MemberTierHistory,id,String,Y,N,Unique key of member tier history table,-,ULID,TBD,01JAPF6R5QQ763ER3QG3A4EX9S,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
121,GWL,Loyalty Service,MemberTierHistory,memberId,String,Y,N,Identifier of a member,-,ULID,TBD,01JAPF6R5QQ763ER3QG3A4EX9S,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
122,GWL,Loyalty Service,MemberTierHistory,fromTierId,String,Y,N,Identifier of old tier of member,-,ULID,TBD,01JAPF6R5QQ763ER3QG3A4EX9S,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
123,GWL,Loyalty Service,MemberTierHistory,toTierId,String,Y,N,Identifier of new tier of member,-,ULID,TBD,01JAPF6R5QQ763ER3QG3A4EX9S,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
124,GWL,Loyalty Service,MemberTierHistory,tierStartedAt,Datetime,Y,N,The date and time when the specific tier of the member was started,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
125,GWL,Loyalty Service,MemberTierHistory,tierEndedAt,Datetime,Y,N,The date and time when the specific tier of the member was ended,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
126,GWL,Loyalty Service,MemberTierHistory,accumulateSpending,Decimal,Y,N,Member current tier accumulate spending (2 yrs timeframe),-,-,TBD,123456,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
127,GWL,Loyalty Service,MemberTierHistory,createdAt,Datetime,Y,N,The date and time when the record of member tier history was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
128,GWL,Loyalty Service,MemberTierHistory,updatedAt,Datetime,Y,N,The date and time when the record of member tier history was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
129,GWL,Loyalty Service,MemberLegacyCobrandHistory,id,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
130,GWL,Loyalty Service,MemberLegacyCobrandHistory,memberId,String,Y,Y,,,,,,Newmember,df_cardhist,member_id,N,,,,Y,N,,,,
131,GWL,Loyalty Service,MemberLegacyCobrandHistory,cardTypeCode,String,Y,Y,,,,,,Newmember,df_cardhist,card_type_id,N,,,,Y,N,"Filter only CardTypeCode = SCB, KBANK",Filter to only co-brand cards,,
132,GWL,Loyalty Service,MemberLegacyCobrandHistory,description,String,Y,Y,,,,,,Newmember,mst_card_type,description,N,,,,Y,N,,,,
133,GWL,Loyalty Service,MemberLegacyCobrandHistory,embossNo,String,N,Y,,,,,,Newmember,df_cardhist,emboss_id,N,,,,Y,N,,,,
134,GWL,Loyalty Service,MemberLegacyCobrandHistory,startedAt,Datetime,Y,Y,,,,,,Newmember,df_cardhist,start_date,N,,,,Y,N,,,,
135,GWL,Loyalty Service,MemberLegacyCobrandHistory,endedAt,Datetime,Y,Y,,,,,,Newmember,df_cardhist,end_date,N,,,,Y,N,,,,
136,GWL,Loyalty Service,MemberLegacyCobrandHistory,cardStatus,String,N,Y,,,,,,,MAST_CardStatus,StatusName,N,,,,N,N,,Get df_cardhist.card_status to find MAST_CardStatus record,,
137,GWL,Loyalty Service,MemberLegacyCobrandHistory,cardReason,String,N,Y,,,,,,,mst_reason,reason_desc,N,,,,N,N,,Get df_cardhist.reason_id to find mst_reason record,,
138,GWL,Loyalty Service,MemberLegacyCobrandHistory,createdAt,Datetime,Y,N,,,,,,,df_cardhist,add_datetime,,,,,Y,N,,,,
139,GWL,Loyalty Service,MemberLegacyCobrandHistory,updatedAt,Datetime,Y,N,,,,,,,df_cardhist,update_datetime,,,,,Y,N,,,,
140,GWL,Loyalty Service,MemberLegacyTierHistory,id,String,Y,N,Unique key of member tier history table,-,ULID,TBD,01JAPF6R5QQ763ER3QG3A4EX9S,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
141,GWL,Loyalty Service,MemberLegacyTierHistory,memberId,String,Y,Y,Identifier of a member,-,ULID,TBD,01JAPF6R5QQ763ER3QG3A4EX9S,Newmember,df_cardhist,member_id,N,N,Y,N,Y,N,,Map SMC_id to GWL_id (reference memberId and gwlNo from Member table),,
142,GWL,Loyalty Service,MemberLegacyTierHistory,cardTypeCode,String,Y,Y,Identifier of old tier of member,-,ULID,TBD,01JAPF6R5QQ763ER3QG3A4EX9S,Newmember,df_cardhist,card_type_id,N,N,N,Y,N,N,only migrate the data where CardTypeCode = 'LV',Filter to only LV cards,,
,GWL,Loyalty Service,MemberLegacyTierHistory,embossNo,String,N,Y,,,,,,,df_cardhist,emboss_id,N,,,,Y,N,,,,
143,GWL,Loyalty Service,MemberLegacyTierHistory,description,String,Y,Y,,,,,,Newmember,mst_card_type,description,N,,,,Y,N,,,,
144,GWL,Loyalty Service,MemberLegacyTierHistory,tierStartedAt,Datetime,Y,Y,The date and time when the specific tier of the member was started,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,Newmember,df_cardhist,start_date,N,N,N,N,Y,N,convert to utc,N/A,,
145,GWL,Loyalty Service,MemberLegacyTierHistory,tierEndedAt,Datetime,Y,Y,The date and time when the specific tier of the member was ended,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,Newmember,df_cardhist,end_date,N,N,N,N,Y,N,convert to utc,N/A,,
,GWL,Loyalty Service,MemberLegacyTierHistory,cardStatus,String,N,Y,,,,,,,MAST_CardStatus,StatusName,N,,,,N,,,Get df_cardhist.card_status to find MAST_CardStatus record,,
,GWL,Loyalty Service,MemberLegacyTierHistory,cardReason,String,N,Y,,,,,,,mst_reason,reason_desc,N,,,,N,,,Get df_cardhist.reason_id to find mst_reason record,,
146,GWL,Loyalty Service,MemberLegacyTierHistory,createdAt,Datetime,Y,N,The date and time when the record of member tier history was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,df_cardhist,add_datetime,N/A,N/A,N/A,N/A,Y,N,,,,
147,GWL,Loyalty Service,MemberLegacyTierHistory,updatedAt,Datetime,Y,N,The date and time when the record of member tier history was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,df_cardhist,update_datetime,N/A,N/A,N/A,N/A,Y,N,,,,
148,GWL,Loyalty Service,TierUpdateRequest,id,String,Y,N,only current req that happen to each tier,-,ULID,TBD,01JAPF6R5QQ763ER3QG3A4EX9S,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
149,GWL,Loyalty Service,TierUpdateRequest,updatedBy,Json,Y,N,The admin account who updated the request,-,-,TBD,<EMAIL>,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
150,GWL,Loyalty Service,TierUpdateRequest,oldData,Json,Y,N,Info on old tier data,-,-,TBD,every fields under tier table,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
151,GWL,Loyalty Service,TierUpdateRequest,newData,Json,Y,N,Info on new tier data,-,-,TBD,every fields under tier table,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
152,GWL,Loyalty Service,TierUpdateRequest,tierId,String,Y,N,Identifier of a specific tier,-,ULID,TBD,01JAPF6R5QQ763ER3QG3A4EX9S,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
153,GWL,Loyalty Service,TierUpdateRequest,detail,String[],Y,N,Detailed info on the tier data update,-,-,TBD,"['Changed tier image', 'Carat earn rate 1.0 to 1.5']",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
154,GWL,Loyalty Service,TierUpdateRequest,privileges,String[],Y,N,List of privilege identifiers,-,-,TBD,"[01JAPF6R5QQ763ER3QG3A4EX9S, ...]",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
155,GWL,Loyalty Service,TierUpdateRequest,effectiveDate,Datetime,Y,N,The date and time when the updated tier info was effective,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-19T19:35:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
156,GWL,Loyalty Service,TierUpdateRequest,createdAt,Datetime,Y,N,The date and time when the request of tier update was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
157,GWL,Loyalty Service,TierUpdateRequest,updatedAt,Datetime,Y,N,The date and time when the record of the request of tier update was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-19T19:35:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
158,GWL,Loyalty Service,PrivilegeTier,id,String,Y,N,Unique key of privilege tier table,-,ULID,TBD,01JAPF6R5QQ763ER3QG3A4EX9S,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
159,GWL,Loyalty Service,PrivilegeTier,tierId,String,Y,N,identifier of tier data,-,ULID,TBD,01JAPF6R5QQ763ER3QG3A4EX9S,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
160,GWL,Loyalty Service,PrivilegeTier,privilegeId,String,Y,N,identifier of privilege data,-,ULID,TBD,01JAPF6R5QQ763ER3QG3A4EX9S,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
161,GWL,Loyalty Service,PrivilegeTier,createdAt,Datetime,Y,N,The date and time when the record of tier linked to defined privilege was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
162,GWL,Loyalty Service,TierLog,id,String,Y,N,Unique key of tier log table,-,ULID,TBD,01JAPF6R5QQ763ER3QG3A4EX9S,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
163,GWL,Loyalty Service,TierLog,updatedBy,Json,Y,N,The admin account who updated the tier log,-,-,TBD,<EMAIL>,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
164,GWL,Loyalty Service,TierLog,oldData,Json,Y,N,Info on old tier data,-,-,TBD,every fields under tier table,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
165,GWL,Loyalty Service,TierLog,newData,Json,Y,N,Info on new tier data,-,-,TBD,every fields under tier table,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
166,GWL,Loyalty Service,TierLog,tierId,String,Y,N,Identifier of a specific tier,-,ULID,TBD,01JAPF6R5QQ763ER3QG3A4EX9S,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
167,GWL,Loyalty Service,TierLog,detail,String[],Y,N,Detailed info on the tier data update,-,-,TBD,"['Changed tier image', 'Carat earn rate 1.0 to 1.5']",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,default = {},,
168,GWL,Loyalty Service,TierLog,effectiveDate,Datetime,Y,N,The date and time when the updated tier info was effective,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
169,GWL,Loyalty Service,TierLog,createdAt,Datetime,Y,N,The date and time when the log of tier info was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
170,GWL,Loyalty Service,TierLog,updatedAt,Datetime,Y,N,The date and time when the log of tier info was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-28T12:00:00z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
171,GWL,Point Service,WalletActivity,id,String,Y,Y,Unique key for point activity,-,ULID,TBD,01J82MN776HHPS9C4WT05XW4N8,,N/A,N/A,N,N/A,N/A,N/A,N/A,N/A,cast(h.LVHeaderKey as varchar(100)) + '_' + cast(t.MovementCode as varchar(100)) + '_' + cast(d.ValueCode as varchar(100)) + '_' + LVNumber,"Generate new ULID
Grouping: LVHeaderKey + LVTrans.movementCode + LVData.ValueCode + LVNumber => 1 Wallet Activity
Order by: Burn activity first, then earn activity",,
172,GWL,Point Service,WalletActivity,memberId,String,Y,Y,Identifier of a member,-,ULID,TBD,01J82MN776HHPS9C4WT05XW4N8,LoyaltyValue,LVTrans,LVNumber,N,N,Y,N,Y,N,"WalletActivity.id = LVHeaderKey + MovementCode + ValueCode, add remark field",,,
173,GWL,Point Service,WalletActivity,walletCode,String,Y,Y,,,,,,LoyaltyValue,LVTrans,LVMainkey.LVData.ValueCode,N,N,Y,N,N,N,Need to check LVData.ValueCode to know which type of wallet it is,convert ref here: https://kingpowergroup.sharepoint.com/:x:/r/sites/KPGDX-GWL-GWL/Shared%20Documents/GWL/06_Modules/Data%20Migration/GWL_Attribute%20Level%20Data%20Mapping_3JAN25.xlsx?d=w87c53150bb36434cb0716dbe1a6ae202&csf=1&web=1&e=Yshk71&nav=MTJfQjIxX3tCNkRCMEU2Mi1FQTFELTQ2NUItOEM4MS00QzQwMUIxQjkyQjB9,,
174,GWL,Point Service,WalletActivity,WalletActivity,String,Y,Y,,,,,"EARN_BY_PURCHASE, PAY_WITH_CARAT, PAY_WITH_EPURSE, TOPUP, MANUAL_ADJUST, etc.",LoyaltyValue,LVTrans,MovementCode,N,N,Y,N,N,N,,Refer to E-Purse mapping sheet,,
175,GWL,Point Service,WalletActivity,refId,String,Y,Y,Sales Transaction Id,-,ULID,TBD,01J82MN776HHPS9C4WT05XW4N8,LoyaltyValue,LVHeader,LVHeaderKey,N,N,N,N,N,N,,"use GWL Partner service SalesTransaction.id, if it's GV topup type, then use LVHeader.KeySearch",,
176,GWL,Point Service,WalletActivity,refType,String,Y,Y,Type of activitiy for point Earn/Burn,-,Enum,TBD,"SALES_TRANSACTION, REDEMPTION, ADJUSTMENT, etc.",LoyaltyValue,LVTrans,MovementCode,N,N,Y,Y,N,N,,"can only be from 2 sources, sales transaction or adjustment transaction depend on LVTrans.MovementCode",,
177,GWL,Point Service,WalletActivity,externalId,String,N,Y,,,,,,LoyaltyValue,LVHeader,KeySearch,N,,,,Y,N,,,,
178,GWL,Point Service,WalletActivity,amount,Decimal,Y,Y,"The amount of earn or burn points (positive value for earn, negative value for burn)",-,-,TBD,200,LoyaltyValue,LVTrans,Amount,N,N,Y,Y,N,N,,LVTrans.MovementCode != ADJ or ADJOUT,,
179,GWL,Point Service,WalletActivity,partnerCode,String,N,Y,,,,,,Newmember,SMCSalesHeader,BranchNo,N,,,,N,N,,Use Branch data to get Partner info,,
180,GWL,Point Service,WalletActivity,brandCode,String,N,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
181,GWL,Point Service,WalletActivity,branchCode,String,N,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
182,GWL,Point Service,WalletActivity,detail,Json,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,default = {},,
183,GWL,Point Service,WalletActivity,createdAt,Datetime,Y,N,The datetime when the point activity was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-09-30T06:50:03.793Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
184,GWL,Point Service,WalletActivity,updatedAt,Datetime,Y,N,The datetime when the point activity was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-09-30T06:50:03.793Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
,GWL,Point Service,WalletActivity,remark,String,Y,Y,,,,,,,LVHeaderExtend,remark,N,,,,,,,,,
185,GWL,Point Service,WalletTransaction,id,String,Y,Y,Unique key for point transaction,-,ULID,TBD,01J82MN776HHPS9C4WT05XW4N8,,N/A,N/A,N,N/A,N/A,N/A,N/A,N/A,"cast(h.LVHeaderKey as varchar(100)) + '_' + cast(t.MovementCode as varchar(100)) + '_' + cast(d.ValueCode as varchar(100)) + '_' + cast(t.LVMainKey as varchar(100)) as transID,",Generate new ULID,,
186,GWL,Point Service,WalletTransaction,memberId,String,Y,Y,Identifier of a member,-,ULID,TBD,01J82MN776HHPS9C4WT05XW4N8,LoyaltyValue,LVTrans,LVNumber,N,N,Y,N,Y,N,,,,
187,GWL,Point Service,WalletTransaction,walletActivityId,String,Y,N,Identifier of a point activity,-,ULID,TBD,01J82MN776HHPS9C4WT05XW4N8,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,Newly generate during migrate,,,
188,GWL,Point Service,WalletTransaction,walletCode,String,Y,Y,,,,,,LoyaltyValue,LVTrans,LVMainkey.LVData.ValueCode,N,N,Y,N,N,N,,Need to check with LVData table which wallet it is,,
189,GWL,Point Service,WalletTransaction,balanceId,String,Y,Y,Code of the point balance type,-,Enum,TBD,"E-Purse,Birthday Purse, Point",LoyaltyValue,LVTrans,LVMainkey,N,N,Y,N,Y,N,,,,
190,GWL,Point Service,WalletTransaction,type,String,Y,Y,refType & type,-,Enum,TBD,"INCREASE/ DECREASE, �",LoyaltyValue,LVTrans,MovementCode,N,N,Y,N,N,N,"Nature => INCREASE, DECREASE, MIGRATE not in MovementCode",Check in to MAST_Movement.Nature,,
191,GWL,Point Service,WalletTransaction,amount,Decimal,Y,Y,Earn/Burn point amount per transaction,-,-,TBD,200,LoyaltyValue,LVTrans,Amount,N,N,N,Y,Y,N,,,,
192,GWL,Point Service,WalletTransaction,expiredAt,Datetime,N,Y,The datetime when the points will be expired,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-09-30T06:50:03.793Z,LoyaltyValue,LVTrans,LVMainkey.LVData.ExpireDate,N,N,Y,N,N,N,,Check in to LVData.ExpireDate of the specified LVMainkey,,
193,GWL,Point Service,WalletTransaction,createdAt,Datetime,Y,Y,The datetime when the point transaction was initially happen,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-09-30T06:50:03.793Z,LoyaltyValue,LVHeader,AddDT,N,N,N,N,Y,N,,N/A,,
194,GWL,Point Service,WalletTransaction,updatedAt,Datetime,Y,Y,The datetime when the point transaction was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-09-30T06:50:03.793Z,LoyaltyValue,LVHeader,AddDT,N,N/A,N/A,N/A,Y,N,,,,
195,GWL,Point Service,WalletAdjustmentTransaction,id,String,Y,Y,,,,,,,N/A,N/A,N,N/A,N/A,N/A,N/A,N/A,cast(h.LVHeaderKey as varchar(100)) + '_' + cast(t.MovementCode as varchar(100)) + '_' + cast(d.ValueCode as varchar(100)),,,
196,GWL,Point Service,WalletAdjustmentTransaction,memberId,String,Y,Y,,,,,,LoyaltyValue,LVTrans,LVNumber,N,N,Y,N,Y,N,,,,
197,GWL,Point Service,WalletAdjustmentTransaction,walletCode,String,Y,Y,,,,,,LoyaltyValue,LVTrans,LVMainkey.LVData.ValueCode,N,N,Y,N,N,N,,Need to check with LVData table which wallet it is,,
198,GWL,Point Service,WalletAdjustmentTransaction,reasonCode,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,"No record in SMC, might need to create a new type MIGRATED_FROM_SMC",,,
199,GWL,Point Service,WalletAdjustmentTransaction,amount,Decimal,Y,Y,,,,,,LoyaltyValue,LVTrans,Amount,N,N,N,Y,N,N,,LVTrans.MovementCode = ADJ or ADJOUT,,
200,GWL,Point Service,WalletAdjustmentTransaction,type,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,"No record in SMC, might need to create a new type MIGRATED_FROM_SMC",,,
201,GWL,Point Service,WalletAdjustmentTransaction,remark,String,Y,Y,,,,,,LoyaltyValue,LVHeaderExtend,Remark,N,N,N,Y,N,N,empty string if null,"Contain details of the adjustment when type = MIGRATED_FROM_SMC; Otherwise use default = """"",,
202,GWL,Point Service,WalletAdjustmentTransaction,createdBy,Json,Y,Y,,,,,,LoyaltyValue,LVHeader,AddUser,N,N,N,Y,N,N,"The value can vary from staff, machine, etc. need to recheck","Change format from String to Json, { id, name, email }",,
203,GWL,Point Service,WalletAdjustmentTransaction,createdAt,Datetime,Y,Y,,,,,,LoyaltyValue,LVHeader,AddDT,N,N,N,N,Y,N,,N/A,,
204,GWL,Point Service,WalletBalance,id,String,Y,N,Unique key for point balance,-,ULID,TBD,01J82MN776HHPS9C4WT05XW4N8,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
205,GWL,Point Service,WalletBalance,WalletBalance,String,Y,Y,Identifier of a member,-,ULID,TBD,01J82MN776HHPS9C4WT05XW4N8,LoyaltyValue,LVData,LVNumber,N,N,Y,N,Y,N,,,,
206,GWL,Point Service,WalletBalance,walletCode,String,Y,Y,"Code of the point balance type (KP: migrate normalized text, Terra: refine it to be code)",-,Enum,TBD,"E-Purse,Birthday Purse, Point",LoyaltyValue,LVData,ValueCode,N,N,Y,N,N,Y,,Use LvData.ValueCode to map from E-purse mapping sheet,,
207,GWL,Point Service,WalletBalance,amount,Decimal,Y,Y,Sum of amount from WalletTransaction grouped by expiryDate,-,-,TBD,134500,LoyaltyValue,LVData,LVValue,N,N,Y,N,Y,N,Will be sum from walletCode mapping,,,
208,GWL,Point Service,WalletBalance,expiredAt,Datetime,N,Y,The datetime when the points will be expired,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-09-30T06:50:03.793Z,LoyaltyValue,LVData,ExpireDate,N,N,N,N,Y,N,,N/A,,
209,GWL,Point Service,WalletBalance,createdAt,Datetime,Y,N,The datetime when the point balance was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-09-30T06:50:03.793Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
210,GWL,Point Service,WalletBalance,updatedAt,Datetime,Y,N,The datetime when the point balance was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-09-30T06:50:03.793Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
211,GWL,Point Service,Wallet,id,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
212,GWL,Point Service,Wallet,runningId,Int,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
213,GWL,Point Service,Wallet,code,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,Need to convert ,,,
214,GWL,Point Service,Wallet,name,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
215,GWL,Point Service,Wallet,walletTypeCode,String,Y,N,,,,,,LoyaltyValue,,,N,N,Y,N,N,N,,"Consolidate to one of the 4 types: POINT, CASH, BIRTHDAY_CASHBACK, EPURSE
To know which wallet code to use check E-purse mapping sheet",,
216,GWL,Point Service,Wallet,status,String,Y,N,,,,,,LoyaltyValue,,,N,N,Y,N,N,N,,Change format from Boolean to String,,
217,GWL,Point Service,Wallet,description,String,N,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
218,GWL,Point Service,Wallet,currency,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
219,GWL,Point Service,Wallet,image,Json,N,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
220,GWL,Point Service,Wallet,createdAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
221,GWL,Point Service,Wallet,updatedAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
222,GWL,Point Service,Wallet,updatedBy,Json,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
223,GWL,Point Service,WalletType,code,String,Y,N,,,,,,LoyaltyValue,MAST_ValueType,ValueTypeCode,N,N,Y,N,Y,N,,,,
224,GWL,Point Service,WalletType,name,String,Y,N,,,,,,LoyaltyValue,MAST_ValueType,ValueTypeName,N,N,Y,N,Y,N,,,,
225,GWL,Point Service,WalletLog,id,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
226,GWL,Point Service,WalletLog,oldData,Json,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
227,GWL,Point Service,WalletLog,newData,Json,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
228,GWL,Point Service,WalletLog,walletCode,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
229,GWL,Point Service,WalletLog,detail,String[],Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
230,GWL,Point Service,WalletLog,status,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
231,GWL,Point Service,WalletLog,effectiveDate,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
232,GWL,Point Service,WalletLog,updatedBy,Json,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
233,GWL,Point Service,WalletLog,createdAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
234,GWL,Point Service,WalletLog,updatedAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
235,GWL,Point Service,PointSettingLog,id,String,Y,N,Unique key for log of point setting,-,ULID,TBD,01J82MN776HHPS9C4WT05XW4N8,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
236,GWL,Point Service,PointSettingLog,updatedBy,Json,Y,N,The information of GWL admin who updated the point setting log,-,-,TBD,"{
    ""id"": ""ABCDEF"",
    ""name"": ""admin no1"",
    ""email"": ""<EMAIL>""
}",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
237,GWL,Point Service,PointSettingLog,oldData,Json,Y,N,Info on old point setting data,-,-,TBD,every fields under PointSetting table,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
238,GWL,Point Service,PointSettingLog,newData,Json,Y,N,Info on new point setting data,-,-,TBD,every fields under PointSetting table,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
239,GWL,Point Service,PointSettingLog,pointSettingid,String,Y,N,Identifier of a point setting,-,ULID,TBD,01J82MN776HHPS9C4WT05XW4N8,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
240,GWL,Point Service,PointSettingLog,detail,String[],Y,N,used for display in admin portal,-,,TBD,"[ ""Edit point expiration period from 2 to 3"", ""Edit amount of spending money from 0.8 to 0.14"", ""Added point image""]",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
241,GWL,Point Service,PointSettingLog,effectiveDate,Datetime,Y,N,The datetime when the updated point setting is effective,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-09-30T06:50:03.793Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
242,GWL,Point Service,PointSettingLog,createdAt,Datetime,Y,N,The datetime when the log of point setting  was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-09-30T06:50:03.793Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
243,GWL,Point Service,PointSettingLog,updatedAt,Datetime,Y,N,The datetime when the log of point setting was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-09-30T06:50:03.793Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
244,GWL,Point Service,PointSetting,id,String,Y,N,Unique key for point setting,-,ULID,TBD,01J82MN776HHPS9C4WT05XW4N8,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
245,GWL,Point Service,PointSetting,nameEn,String,Y,N,Name of point setting in English (CARAT),-,-,TBD,Carat,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
246,GWL,Point Service,PointSetting,nameTh,String,Y,N,Name of point setting in Thai,-,-,TBD,?????,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
247,GWL,Point Service,PointSetting,unitEn,String,Y,N,Unit of point setting in English,-,-,TBD,Point,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
248,GWL,Point Service,PointSetting,unitTh,String,Y,N,Unit of point setting in Thai,-,-,TBD,????,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
249,GWL,Point Service,PointSetting,earn,Json,Y,N,earn setting,-,-,TBD,"{pointPerEarn: 1, thbPerEarn: 25, minimumSpendToEarn: 1000}",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
250,GWL,Point Service,PointSetting,burn,Json,Y,N,burn setting,-,-,TBD,"{pointPerBurn: 1, thbPerBurn: 25, minimumSpendToBurn: 100}",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
251,GWL,Point Service,PointSetting,expiryPeriodYear,Int,Y,N,"Expiry period from point earned date, unit in calender year (expires at year ends)",-,-,TBD,2,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
252,GWL,Point Service,PointSetting,cost,Decimal,Y,N,Conversion rate from THB to 1 point,-,-,TBD,0.8,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
253,GWL,Point Service,PointSetting,image,Json,N,N,Logo (No image at all in SMC),-,-,TBD,-,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
254,GWL,Point Service,PointSetting,createdAt,Datetime,Y,N,The datetime when the point setting was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-09-30T06:50:03.793Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
255,GWL,Point Service,PointSetting,updatedAt,Datetime,Y,N,"The datetime when the point setting was last updated, UPDATE for Expiry date change",-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-09-30T06:50:03.793Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
256,GWL,Point Service,PointSetting,updatedBy,Json,Y,N,The information of GWL admin who updated the point setting log,-,-,TBD,"{
    ""id"": ""ABCDEF"",
    ""name"": ""admin no1"",
    ""email"": ""<EMAIL>""
}",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
257,GWL,Point Service,PointSettingUpdateRequest,id,String,Y,N,Unique key for point setting update request,-,ULID,TBD,01J82MN776HHPS9C4WT05XW4N8,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
258,GWL,Point Service,PointSettingUpdateRequest,oldData,Json,Y,N,Info on old point setting data,-,-,TBD,every fields under PointSetting table,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
259,GWL,Point Service,PointSettingUpdateRequest,newData,Json,Y,N,Info on new point setting data,-,-,TBD,every fields under PointSetting table,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
260,GWL,Point Service,PointSettingUpdateRequest,pointSettingid,String,Y,N,Identifier of a point setting,-,ULID,TBD,01J82MN776HHPS9C4WT05XW4N8,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
261,GWL,Point Service,PointSettingUpdateRequest,detail,String[],Y,N,Details of request info for point setting update,-,-,TBD,"[ ""Edit amount of spending money from 0.8 to 12""]",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
262,GWL,Point Service,PointSettingUpdateRequest,effectiveDate,Datetime,Y,N,The datetime when the updated point setting is effective,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-09-30T06:50:03.793Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
263,GWL,Point Service,PointSettingUpdateRequest,createdAt,Datetime,Y,N,The datetime when the request for point setting update was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-09-30T06:50:03.793Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
264,GWL,Point Service,PointSettingUpdateRequest,updatedAt,Datetime,Y,N,The datetime when the request for point setting update was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-09-30T06:50:03.793Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
265,GWL,Point Service,PointSettingUpdateRequest,updatedBy,Json,Y,N,The information of GWL admin who updated the point setting log,-,-,TBD,"{
    ""id"": ""ABCDEF"",
    ""name"": ""admin no1"",
    ""email"": ""<EMAIL>""
}",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
266,GWL,Partner Service,SalesTransaction,id,bigint,Y,Y,LVHeader,LVHeaderKey,Y,N,Join with SMCSalesHeader->key_search,LoyaltyValue,LVHeader,LVHeaderKey,N,N,Join with SMCSalesHeader->key_search,N/A,Y,N,Join with SMCSalesHeader->key_search,,,
267,GWL,Partner Service,SalesTransaction,memberId,String,Y,Y,Identifier of a member,-,ULID,TBD,01J7MWVRE2311771F8A29S99A9,,SMCSalesHeader,member_id,N/A,N/A,N/A,N/A,Y,N/A,,,,
268,GWL,Partner Service,SalesTransaction,gwlNo,String,Y,Y,,,,,,Newmember,SMCSalesHeader,member_id,N,N,Y,N,Y,N,,,,
269,GWL,Partner Service,SalesTransaction,externalId,String,Y,Y,Absolute key of sales transaction (external),-,-,TBD,1,Newmember,SMCSalesHeader,key_search,N,N,N,N,Y,N,,N/A,,
270,GWL,Partner Service,SalesTransaction,taxInvoice,Json,N,Y,Tax invoice in free text,-,-,TBD,~ receipt#,Newmember,SMCSalesHeader,key_search,N,N,N,N,N,N,,"Not recorded in SMC, it will be combined to be key_search
If it's an airport order, the key_search contains it at the last pipe
but for downtown, not recorded anywhere in SMC",,
271,GWL,Partner Service,SalesTransaction,partnerId,String,Y,Y,,,,,,Newmember,SMCSalesHeader,BranchNo,N,N/A,N/A,N/A,N,N,mock,Use Branch data to get Partner info,,
272,GWL,Partner Service,SalesTransaction,brandId,String,Y,Y,"Identifier of a channel (KP: migrate normalized text, Terra: refine it to be code)",-,ULID,TBD,01J7MWVRE2311771F8A29S99A9,Newmember,SMCSalesHeader,BranchNo,N,N/A,N/A,N/A,Y,N,mock,Get the brand data reference from Branch,,
273,GWL,Partner Service,SalesTransaction,branchId,String,Y,Y,"Identifier of a sub channel (KP: migrate normalized text, Terra: refine it to be code)",-,ULID,TBD,01J7MWVRE2311771F8A29S99A9,Newmember,SMCSalesHeader,Site,N,N,Y,N,Y,N,"mock, The branchNo means the whole location, thus can be used by multiple Partners",,,
274,GWL,Partner Service,SalesTransaction,netTotalAmount,Decimal,Y,Y,actual paid amount (after discount),-,-,TBD," 13,500.00 ",Newmember,SMCSalesTrans,Net,N,N,N,Y,N,N,"sum(Net), group by key search",Need to sum from each item on SMCSalesTrans ,,
275,GWL,Partner Service,SalesTransaction,totalOriginalPrice,Decimal,Y,Y,total item price before discount ,,,,,Newmember,SMCSalesTrans,Amount,N,N,N,Y,N,N,"sum(Amount), group by key search",Need to sum from each item on SMCSalesTrans ,,
276,GWL,Partner Service,SalesTransaction (not in ERD),totalDiscount,Decimal,Y,Y,Discount amount,-,-,TBD, 453.00 ,Newmember,SMCSalesTrans,Discount,N,N,N,Y,N,N,"sum(Discount), group by key search",Need to sum from each item on SMCSalesTrans ,,
277,GWL,Partner Service,SalesTransaction,totalEarnableAmount,Decimal,Y,Y,Enable amount in THB which might be equal to or less than netTotalAmount,-,-,TBD,"10,000",,SMCSalesPayment,Net,N/A,N/A,N/A,N/A,N/A,N/A,"Exclude payment method that exists in MAST_NonAccCarat
Exclude voided transaction (LVHeader.CancelHeaderKey != null)
Sum(Net) from all payments of the order",,,
278,GWL,Partner Service,SalesTransaction,totalAccumSpendableAmount,Decimal,Y,Y,Accumulated spending - used for Tier calculation,-,-,TBD,"10,000",,SMCSalesPayment,Net,N/A,N/A,N/A,N/A,N/A,N/A,"Exclude payment method that exists in MAST_NonAccCarat
Exclude voided transaction (LVHeader.CancelHeaderKey != null)
Sum(Net) from all payments of the order",,,
279,GWL,Partner Service,SalesTransaction (not in ERD),totalPointEarned,Decimal,N,Y,,,,,,LoyaltyValue,LVTrans,Amount,N,N,Y,Y,N,N,"group by movement code
Sum(Amount) ","find for LVTrans that link to LVMainKey (Table LVData) that has ValueCode=CR001
There can be multiple entries, might need to sum them for the whold sales transaction (LVHeader)",,
280,GWL,Partner Service,SalesTransaction,shippingAmount,Decimal,Y,N,Shipping amount (only used to fulfill accounting report),,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,No record,,,
281,GWL,Partner Service,SalesTransaction,status,String,Y,Y,"BURNED, EARNED, VOIDED",,,,,,SMCSalesHeader,SalesStatus,N,N,N,N,Y,N,"If SMCSaleHeader.SaleStatus = R, put R into this field, otherwise leave blank","Not found in SMCSalesHeader, SMCSalesTrans
Orders that appear in SMCSalesTran means => completed (not sure how to check the refunded ones)",,
282,GWL,Partner Service,SalesTransaction,settings,Json,Y,N,sales transaction setting related to flag on earnable points and countable for accumulated spending,-,-,TBD,"{isEarnable: TRUE, isAccumSpendable: TRUE, �}",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
283,GWL,Partner Service,SalesTransaction,rawRequest,Json,N,N,API request that front channel sent to us,-,-,TBD,-,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
284,GWL,Partner Service,SalesTransaction,completedAt,Datetime,N,Y,Timestamp of the purchase ,,,,,LoyaltyValue,LVHeader,FinishDT,N,N,N,N,Y,N,"The AddDT is the datetime that POS submit the request to earn/burn
FYI ??? POS Airport ??????????? transaction ?????? ?? 3 ?????????  SMCSalesHeader.DataDate",N/A,,
285,GWL,Partner Service,SalesTransaction,createdAt,Datetime,Y,Y,The datetime when the sales transaction was initially happen,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,LoyaltyValue,LVHeader,AddDT,N,N,N,N,Y,N,Order closing date no actual datetime that data created,N/A,,
286,GWL,Partner Service,SalesTransaction,updatedAt,Datetime,Y,Y,The datetime when the sales transaction was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,LoyaltyValue,LVHeader,FinishDT,N,N,N,N,Y,N,,N/A,,
287,GWL,Partner Service,SalesTransaction,detail,Json,N,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,,
288,GWL,Partner Service,SalesTransactionPayment,id,string,Y,N,Unique key of normalized table for sales transaction and payment method,-,ULID,TBD,01J7MWVRE2311771F8A29S99A9,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,UUID,,,
289,GWL,Partner Service,SalesTransactionPayment,salesTransactionId,bigint,Y,Y,Identifier of sales transaction,-,ULID,TBD,01J7MWVRE2311771F8A29S99A9,Newmember,LVHeader,LVHeaderKey,N,N,N,N,Y,N,,N/A,,
290,GWL,Partner Service,SalesTransactionPayment,paymentMethodId,String,Y,Y,Identifier of payment method,-,ULID,TBD,01J7MWVRE2311771F8A29S99A9,Newmember,SMCSalesPayment,MethodCode,N,N,Y,N,N,N,Put MethodCode,Map from Payment method mapping sheet,,
291,GWL,Partner Service,SalesTransactionPayment,amount,Decimal,Y,Y,Purchased amount of a sales transaction,-,-,TBD,"13,500",Newmember,SMCSalesPayment,Net,N,N,N,Y,N,N,"1 to 1, no summing required",Need to sum from each item on SMCSalesTrans ,,
292,GWL,Partner Service,SalesTransactionPayment,settings,Json,Y,N,"sales transaction - payment method setting related to flag on earnable points and countable for accumulated spending (KP: migrate normalized text, Terra: refine it to be fit to GWL)",-,-,TBD,"{isEarnable: TRUE, isAccumSpendable: TRUE, �}",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,No record,,,
293,GWL,Partner Service,SalesTransactionPayment,createdAt,Datetime,Y,Y,The datetime when the data on table of Sales transaction bounded with payment method was initially added,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,Newmember,SMCSalesHeader,DataDate,N,N,N,N,Y,N,Order closing date no actual datetime that data created,N/A,,
294,GWL,Partner Service,SalesTransactionPayment,updatedAt,Datetime,Y,N,The datetime when the data on table of Sales transaction bounded with payment method was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
295,GWL,Partner Service,SalesTransactionItem,id,String,Y,N,Unique key of sales transaction item,-,ULID,TBD,01J7MWVRE2311771F8A29S99A9,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,UUID,,,
296,GWL,Partner Service,SalesTransactionItem,salesTransactionId,bigint,Y,Y,Identifier of a sales transaction,-,ULID,TBD,01J7MWVRE2311771F8A29S99A9,Newmember,LVHeader,LVHeaderKey,N,N,N,N,Y,N,,N/A,,
297,GWL,Partner Service,SalesTransactionItem,productId,String,Y,Y,Identifier of a product,-,ULID,TBD,01J7MWVRE2311771F8A29S99A9,Newmember,SMCSalesTrans,MatCode,N,N,Y,N,Y,N,,,,
298,GWL,Partner Service,SalesTransactionItem,quantity,int,Y,Y,Quantity of the purchased product,-,-,TBD,3,Newmember,SMCSalesTrans,Qty,N,N,N,N,Y,N,Decimal,N/A,,
299,GWL,Partner Service,SalesTransactionItem,netAmount,Decimal,Y,Y,actual paid amount of each item (after discount),-,-,TBD,"13,500",Newmember,SMCSalesTrans,Net,N,N,N,N,Y,N,,N/A,,
300,GWL,Partner Service,SalesTransactionItem,originalPrice,Decimal,Y,Y,item price before discount ,,,,,Newmember,SMCSalesTrans,Amount,N,N,N,N,Y,N,1 to 1,N/A,,
301,GWL,Partner Service,SalesTransactionItem (not in ERD),discount,Decimal,Y,Y,Discounted value of each item,-,-,TBD,4000,Newmember,SMCSalesTrans,Discount,N,N,N,N,Y,N,,N/A,,
302,GWL,Partner Service,SalesTransactionItem,earnableAmount,Decimal,Y,N,Earnable amount (in THB) for each purchased item,-,-,TBD,"13,500",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,this value doesnt exist in SMC. The loyalty.dll code calculates to carat right away without storing the earnableAmount,default: 0,,
303,GWL,Partner Service,SalesTransactionItem,normalPointEarned,Decimal,Y,N,Normal rate (1x rate),-,-,TBD,1,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,this value doesnt exist in SMC. The loyalty.dll code calculates to carat right away without storing the earnableAmount,default: 0,,
304,GWL,Partner Service,SalesTransactionItem,extraPointEarned,Decimal,Y,N,"based on specific tier (scarlet 2x), total Earn = Normal + Extra = 1+2 (for Crown)",-,-,TBD,1,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,this value doesnt exist in SMC. The loyalty.dll code calculates to carat right away without storing the earnableAmount,default: 0,,
305,GWL,Partner Service,SalesTransactionItem,burnPaymentAmount,Decimal,Y,N,sum amount from all burn method (carat + epurse) in THB,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,default: 0,,
306,GWL,Partner Service,SalesTransactionItem,settings,Json,Y,N,"Settings - for calc tier upgrade, point earnable/burnable based on specific category, sku, brand",-,-,TBD,"{isEarnable: TRUE, isAccumSpendable: TRUE, �}",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
307,GWL,Partner Service,SalesTransactionItem,paymentDetail,Json,Y,N,"breakdown of how we calc points, tiers;  (KP: migrate normalized text, Terra: refine it to be fit to GWL)",-,-,TBD,-,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,No separate payment record for each item,,,
308,GWL,Partner Service,SalesTransactionItem,createdAt,Datetime,Y,N,The datetime when the new record of sales transaction item was initially added,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
309,GWL,Partner Service,SalesTransactionItem,updatedAt,Datetime,Y,N,The datetime when the existing sales transaction item was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
310,GWL,Partner Service,SalesTransactionItemBurnPayment,id,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
311,GWL,Partner Service,SalesTransactionItemBurnPayment,itemId,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
312,GWL,Partner Service,SalesTransactionItemBurnPayment,burnPaymentId,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,No record,,,
313,GWL,Partner Service,SalesTransactionItemBurnPayment,burnAmount,Decimal,Y,N,amount of carat/epurse used,,,,,LoyaltyValue,N/A,N/A,N,N,Y,Y,N,N,"1 order 3 items -- total of 1000 THB
- item A - 500 THB
- item B - 300 THB
- item C - 200 THB

if the LVTrans.amount says Epurse 100THB
Then each item gets
- item A - 50 THB
- item B - 30 THB
- item C - 20 THB",Weight based on purchase amount of each item,,
314,GWL,Partner Service,SalesTransactionItemBurnPayment,paymentAmount,Decimal,Y,N,amount of money that the burn method represent in THB (based on burn rate),,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,"Currently SMC does it 1-to-1, 1 Carat = 1 Baht",,,
315,GWL,Partner Service,SalesTransactionItemBurnPayment,settings,Json,Y,N,"restriction settings, sku burnable?",,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,No record due to no conversion rate from Carat->Baht,,,
316,GWL,Partner Service,SalesTransactionItemBurnPayment,createdAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
317,GWL,Partner Service,SalesTransactionItemBurnPayment,updatedAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
318,GWL,Partner Service,SalesTransactionBurnPayment,id,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
319,GWL,Partner Service,SalesTransactionBurnPayment,salesTransactionId,bigint,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
320,GWL,Partner Service,SalesTransactionBurnPayment,walletCode,String,Y,Y,,,,,,LoyaltyValue,LVTrans,LVMainkey,N,N,Y,N,N,N,,take the LVMainkey from LVTrans to find in LVData and check LVData.ValueCode to know which wallet is used,,
321,GWL,Partner Service,SalesTransactionBurnPayment,burnAmount,Decimal,Y,Y,amount of burn method (carat/epurse) used for the transaction,,,,,LoyaltyValue,LVTrans,Amount,N,N,Y,N,N,N,,LVTrans.MovementCode=USE,,
322,GWL,Partner Service,SalesTransactionBurnPayment,beforeAmount,Decimal,Y,Y,balance before used/burn,,,,,LoyaltyValue,LVTrans,PreviousBalance,N,N,N,N,Y,N,,N/A,,
323,GWL,Partner Service,SalesTransactionBurnPayment,afterAmount,Decimal,Y,Y,balance after used/burn,,,,,LoyaltyValue,LVTrans,PreviousBalance + Amount,N,N,Y,N,N,N,,Calculate from LVTrans.PreviousBalance + LVTrans.Amount,,
324,GWL,Partner Service,SalesTransactionBurnPayment,paymentAmount,Decimal,Y,N,amount of money that the burn method represent in THB (based on burn rate),,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,"Currently SMC does it 1-to-1, 1 Carat = 1 Baht",,,
325,GWL,Partner Service,SalesTransactionBurnPayment,settings,Json,Y,N,"restriction setting, channel/brand/branch burnable?",,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,No record due to no conversion rate from Carat->Baht,,,
326,GWL,Partner Service,SalesTransactionBurnPayment,createdAt,Datetime,Y,Y,,,,,,LoyaltyValue,LVHeader,AddDT,N,N,N,N,Y,N,,N/A,,
327,GWL,Partner Service,SalesTransactionBurnPayment,updatedAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
328,GWL,Partner Service,WalletPaymentMethod,walletTypeCode,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
329,GWL,Partner Service,WalletPaymentMethod,paymentMethodCode,String,Y,N,payment code submitted to sales transaction when a wallet used,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
330,GWL,Partner Service,PaymentMethod,id,string,Y,N,Unique key of a payment method table,-,ULID,TBD,01J7MWVRE2311771F8A29S99A9,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
331,GWL,Partner Service,PaymentMethod,code,String,Y,N,"Payment Code (KP: migrate normalized text, Terra: refine it to be fit to GWL)",-,Enum,TBD,"E-Purse, KBankUnionpay",Newmember,df_paymeth,method_code,N,N,Y,Y,N,Y,,Map from Payment method mapping sheet,,
332,GWL,Partner Service,PaymentMethod,name,String,Y,N,"Payment Name (KP: migrate normalized text, Terra: refine it to be fit to GWL)",-,-,TBD,"E-Purse, Kbank Unionpay",,df_paymeth,method_desc,N/A,N/A,N/A,N/A,N/A,N/A,no record,,,
333,GWL,Partner Service,PaymentMethod,type,String,Y,N,"Type of payment (whether it's by cash/ credit card, �)",-,Enum,TBD,"Loyalty Wallet, Credit card, Cash, Voucher",Newmember,df_paymeth,method_group,N,N,Y,Y,N,N,,Map from Payment method mapping sheet,,
334,GWL,Partner Service,PaymentMethod,description,String,Y,N,Description of each payment method,-,-,TBD,KBankUnionpay,Newmember,df_paymeth,method_desc,N,N,Y,Y,N,N,,Map from Payment method mapping sheet,,
335,GWL,Partner Service,PaymentMethod,settings,Json,Y,N,Payment method setting related to flag on earnable points and countable for accumulated spending,-,-,TBD,-,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
336,GWL,Partner Service,PaymentMethod,setting: importId,String,N,N,Imported ID generated from upload file history id,-,-,TBD,01J7MWVRCYGV7G2T8G5MKTRPET,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
337,GWL,Partner Service,PaymentMethod,setting: properties,Json,Y,N,"Properties (earnable, countable for accumulated spending) of a payment method setting",-,-,TBD,-,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
338,GWL,Partner Service,PaymentMethod,setting: properties: isEarnable,Boolean,Y,N,Flag for whether payment method is point earnable or not,-,Enum,TBD,FALSE,LoyaltyValue,MAST_NonAccCarat,multi cols - see Note,N,N,Y,N,N,Y,df_paymeth.Method_Code => MAST_NonAccCarat.code,"For non SMC payment method
if df_paymeth.method_code found in MAST_NonAccCarat.Code and MAST_NonAccCarat.Type = P (payment), it's not earnable

For SMC payment (epurse, carat)
check Loyalty database, table MAST_Value, if column IsCorporate = 0, means not earnable",,
339,GWL,Partner Service,PaymentMethod,setting: properties: isAccumSpendable,Boolean,Y,N,Flag for whether payment method is countable for accumunated spending ,-,Enum,TBD,TRUE,LoyaltyValue,MAST_NonAccCarat,multi cols - see Note,N,N,Y,N,N,N,,same logic with isEarnable above,,
340,GWL,Partner Service,PaymentMethod,setting: effectiveAt,Datetime,Y,N,The date and time when the setting is effective at,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-09-13T05:14:29.699Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
341,GWL,Partner Service,PaymentMethod,setting: createdAt,Datetime,Y,N,The datetime when a setting of sales transaction item was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-09-13T05:14:29.699Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
342,GWL,Partner Service,PaymentMethod,createdBy,String,Y,N,Info of GWL admin who created the payment method,-,-,TBD,"{""id"": ""Admin1"", ""email"": ""<EMAIL>"" }",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
343,GWL,Partner Service,PaymentMethod,createdAt,Datetime,Y,N,The datetime when a payment method was initially happen,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
344,GWL,Partner Service,PaymentMethod,updatedBy,String,N,N,Info of GWL admin who updated the payment method info,-,-,TBD,"{""id"": ""Admin1"", ""email"": ""<EMAIL>"" }",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
345,GWL,Partner Service,PaymentMethod,updatedAt,Datetime,Y,N,The datetime when a payment method was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
346,GWL,Partner Service,PaymentMethod,deletedAt,Datetime,N,N,The datetime when a payment method data is deleted,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
347,GWL,Partner Service,Partner,id,String,Y,N,Unique identifier of a partner,-,ULID,TBD,01GHOBCFNKVMFLKFOGJFR,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
348,GWL,Partner Service,Partner,code,String,Y,N,Code of a partner,-,Enum,TBD,AOT,Newmember,df_company,company_code,N,N,Y,N,Y,N,,,,
349,GWL,Partner Service,Partner (not in ERD),name,String,Y,N,,,,,,Newmember,df_company,company_name,N,N,N,N,Y,N,,N/A,,
350,GWL,Partner Service,Partner,taxid,String,Y,N,Tax id of a partner,-,-,TBD,12345,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,Ref: df_branch or SAP,,,
351,GWL,Partner Service,Partner,partnerType,String,Y,N,partner type,-,Enum,TBD,"INTERNAL, REWARD, POINT_EXCHANGE, CO_BRAND; 
- In Doc, it's a list of types. Can be more than one type?",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
352,GWL,Partner Service,Partner,companyType,String,Y,N,Type of company,-,Enum,TBD,"DOMESTIC, FOREIGN",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
353,GWL,Partner Service,Partner,type,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
354,GWL,Partner Service,Partner,sapCode,String,Y,N,SAP Code of a partner,-,-,TBD,TEST Code,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,Reward/Point partners (external) - come from SAP,,,
355,GWL,Partner Service,Partner,pointCost,String,Y,N,Point cost of a partner,-,-,TBD,200.01,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
356,GWL,Partner Service,Partner,categories,Json,Y,N,List of partner category,-,Enum,TBD,"FOOD_AND_BEVERAGE, HEALTH_AND_BEAUTY, TRAVEL, LIFTSTYLES, SHOPPING",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
357,GWL,Partner Service,Partner,address1,Json,C,N,Domestic (Thai) address of the partner,If companyType is DOMESTIC,-,TBD,-,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,"need to look at df_branch (in SMC), company level (maybe in SAP)",,,
358,GWL,Partner Service,Partner,address2,String,C,N,International (non-Thai) address of the partner,If companyType is FOREIGN,Free text,TBD,"1-504 Tianbai Center, Tianjin, China",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,"need to look at df_branch (in SMC), company level (maybe in SAP)",,,
359,GWL,Partner Service,Partner,attachments,Json,Y,N,Attachments of a partner info,-,-,TBD,-,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
360,GWL,Partner Service,Partner,contact,Json,Y,N,Contact person info of the partner company,-,-,TBD,-,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,Ref: SAP,,,
361,GWL,Partner Service,Partner,status,String,Y,N,Partner active status,-,Enum,TBD,"ACTIVE, INACTIVE, SUSPENDED",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,Ref: SAP,,,
362,GWL,Partner Service,Partner,createdAt,Datetime,Y,N,The datetime when the partner info was initially happen,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
363,GWL,Partner Service,Partner,createdBy,Json,Y,N,Info of GWL admin who created the partner info,-,-,TBD,"{ ""id"": 1234, ""email"": ""<EMAIL>""}",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
364,GWL,Partner Service,Partner,updatedAt,Datetime,Y,N,The datetime when the partner info was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
365,GWL,Partner Service,Partner,updatedBy,Json,Y,N,Info of GWL admin who updated the partner info,-,-,TBD,"{""id"": ""Admin1"", ""email"": ""<EMAIL>"" }",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
366,GWL,Partner Service,Branch,id,String,Y,N,Unique identifier of branch,-,ULID,TBD,01J7MWVRE2311771F8A29S99A9,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
367,GWL,Partner Service,Branch,partnerid,String,Y,N,Identifier of a partner,-,ULID,TBD,01J7MWVRE2311771F8A29S99A9,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
368,GWL,Partner Service,Branch,brandId,String,Y,N,Identifier of a brand,-,ULID,TBD,01J7MWVRE2311771F8A29S99A9,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
369,GWL,Partner Service,Branch,code,String,Y,N,"Text Branch - 00000 HQ (KP: migrate normalized text, Terra: refine it to be fit to GWL)",-,Enum,TBD,KPC_PATTAYA,Newmember,df_branch,branch_no,N,N,N,Y,N,N,,"Currently SMC kept with numbers, need to transform to GWL code",,
370,GWL,Partner Service,Branch,name,Json,Y,N,Name of the branch in two or more different languages,-,-,TBD,-,Newmember,df_branch,name,N,Y,Y,Y,N,N,,"Currently SMC kept more detailed level by location, need to transform to GWL branch name",,
371,GWL,Partner Service,Branch,branchType,String,Y,N,Type of a branch (whether offline/ online),-,Enum,TBD,OFFLINE,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,"Can Refer from POS, customer type",,,
372,GWL,Partner Service,Branch,latitute,Float,Y,N,Lattitute of a branch location,-,-,TBD,13.7563,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
373,GWL,Partner Service,Branch,longitude,Float,Y,N,Longitude of a branch location,-,-,TBD,100.5018,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
374,GWL,Partner Service,Branch,location,String,Y,N,Address of a branch location  in free text,-,-,TBD,Test location,Newmember,df_branch,"address1, address2, address3",N,N,N,Y,N,N,Not used - might be in-accurate,,,
375,GWL,Partner Service,Branch,status,String,Y,N,Status of a branch whether it is active,-,Enum,TBD,ACTIVE,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
376,GWL,Partner Service,Branch,createdAt,Datetime,Y,N,The datetime when a branch was initially added,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
377,GWL,Partner Service,Branch,createdBy,Json,Y,N,Info of GWL admin who created the branch info,-,-,TBD,"{""id"": ""Admin1"", ""email"": ""<EMAIL>"" }",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
378,GWL,Partner Service,Branch,updatedAt,Datetime,Y,N,The datetime when the branch info was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
379,GWL,Partner Service,Branch,updatedBy,Json,Y,N,Info of GWL admin who updated the branch info,-,-,TBD,"{""id"": ""Admin1"", ""email"": ""<EMAIL>"" }",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
380,GWL,Partner Service,Branch,deletedAt,Datetime,N,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,,
381,GWL,Partner Service,WalletEligibleBranch,id,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
382,GWL,Partner Service,WalletEligibleBranch,walletCode,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
383,GWL,Partner Service,WalletEligibleBranch,branchCode,String,Y,N,branch code that's allowed to use the wallet,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
384,GWL,Partner Service,WalletEligibleBranch,createdAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
385,GWL,Partner Service,WalletEligibleBranch,updatedAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
386,GWL,Partner Service,PartnerBrand,id,string,Y,N,Unique key of a brand bounded to partner,-,ULID,TBD,01J7MWVRE2311771F8A29S99A9,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
387,GWL,Partner Service,PartnerBrand,partnerId,string,Y,N,Identifier of a partner,-,ULID,TBD,01J7MWVRE2311771F8A29S99A9,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,Ref: SAP,,,
388,GWL,Partner Service,PartnerBrand,brandId,string,Y,N,Identifier of a brand,-,ULID,TBD,01J7MWVRE2311771F8A29S99A9,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,Ref: SAP,,,
389,GWL,Partner Service,PartnerBrand,createdBy,Json,Y,N,Info of GWL admin who created the partner brand info,-,-,TBD,"{""id"": ""Admin1"", ""email"": ""<EMAIL>"" }",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
390,GWL,Partner Service,PartnerBrand,updatedBy,Json,Y,N,Info of GWL admin who updated the partner brandinfo,-,-,TBD,"{""id"": ""Admin1"", ""email"": ""<EMAIL>"" }",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
391,GWL,Partner Service,Brand,id,string,Y,N,Unique key of a brand,-,ULID,TBD,01J7MWVRE2311771F8A29S99A9,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
392,GWL,Partner Service,Brand,code,string,Y,N,Partner code,-,Enum,TBD,KFC_CODE,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,Ref: SAP,,,
393,GWL,Partner Service,Brand,name,Json,Y,N,Brand name,-,-,TBD,-,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,Ref: SAP,,,
394,GWL,Partner Service,Brand,categories,string,N,N,Category of the brand,-,-,TBD,FOOD_AND_BEVERAGE,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,Ref: SAP,,,
395,GWL,Partner Service,Brand,status,string,Y,N,Status of a brand whether it is active,-,-,TBD,ACTIVE,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,Ref: SAP,,,
396,GWL,Partner Service,Brand,logo,Json,C,N,Logo of a brand,"To activate Brand (status = Active), logo is required.",-,TBD,-,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
397,GWL,Partner Service,Brand,description,String,N,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,Ref: SAP,,,
398,GWL,Partner Service,Brand,settings,Json,N,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
399,GWL,Partner Service,Brand,createdAt,Datetime,Y,N,The datetime when a brand info was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
400,GWL,Partner Service,Brand,createdBy,Json,Y,N,Info of GWL admin who created the brand info,-,-,TBD,"{""id"": ""Admin1"", ""email"": ""<EMAIL>"" }",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
401,GWL,Partner Service,Brand,updatedAt,Datetime,Y,N,The datetime when the existing brand info was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
402,GWL,Partner Service,Brand,updatedBy,Json,Y,N,Info of GWL admin who updated the brand info,-,-,TBD,"{""id"": ""Admin1"", ""email"": ""<EMAIL>"" }",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
403,GWL,Partner Service,Brand,deletedAt,Datetime,N,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
404,GWL,Partner Service,Product,id,String,Y,N,Unique identifier of a product,-,ULID,TBD,01J7MWVRE2311771F8A29S99A9,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
405,GWL,Partner Service,Product,brandId,String,Y,N,Key of a brand,-,ULID,TBD,01J7MWVRE2311771F8A29S99A9,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
406,GWL,Partner Service,Product,sku,String,Y,Y,SKU of a product,-,-,TBD,1020311,,MAST_Article,ArticleCode,N,N,N,N,Y,N,"Suppose it's from SAP, not migrate from SMC",,,
407,GWL,Partner Service,Product,name,String,Y,Y,Name of a product,-,-,TBD,CHANEL BLEU DE CHANEL LIMITED-EDITION PARFUM 30 ML,,MAST_Article,ArticleName,N,N,N,N,Y,N,,,,
408,GWL,Partner Service,Product,categoryCode,String,Y,Y,Category code of a product,-,Enum,TBD,201001001,,MAST_Article,MCCode,N,N,N,N,Y,N,,,,
409,GWL,Partner Service,Product,brandCode,String,N,Y,Brand of a product,-,Enum,TBD,Chanel,,MAST_Article,brandCode,N,N,N,N,Y,N,,,,
410,GWL,Partner Service,Product,settings,Json,Y,Y,product setting related to flag on earnable points and countable for accumulated spending,-,-,TBD,-,LoyaltyValue,MAST_NonAccCarat,Type,N,N,Y,N,N,N,,only collect what product cannot be earn;  (but it's in Category (C) OR Payment (P) - used together with Code),,
411,GWL,Partner Service,Product,status,String,Y,N,Status of a product whether it is active,-,Enum,TBD,"Active, Inactive",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,Ref: SAP,,,
412,GWL,Partner Service,Product,createdAt,Datetime,Y,N,The datetime when a product info was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
413,GWL,Partner Service,Product,createdBy,String,Y,N,Info of GWL admin who created the product info,-,-,TBD,"{""id"": ""Admin1"", ""email"": ""<EMAIL>"" }",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
414,GWL,Partner Service,Product,updatedAt,Datetime,Y,N,The datetime when the existing product info was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
415,GWL,Partner Service,Product,updatedBy,String,N,N,Info of GWL admin who updated the product info,-,-,TBD,"{""id"": ""Admin1"", ""email"": ""<EMAIL>"" }",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
416,GWL,Partner Service,Product,deletedAt,Datetime,N,N,The datetime when a product info is deleted,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
417,GWL,Partner Service,ProductCategory,id,String,Y,N,Unique key of a category,-,ULID,TBD,01J7MWVRE2311771F8A29S99A9,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
418,GWL,Partner Service,ProductCategory,brandId,String,Y,N,Identifier of a brand,-,ULID,TBD,01J7MWVRE2311771F8A29S99A9,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
419,GWL,Partner Service,ProductCategory,code,String,Y,Y,Code of a category,-,Enum,TBD,201001002,,MAST_MCCode,MC_Code,N,N,N,N,Y,N,"cat (first 3 digits), section (mid 3 digits), subsection (last 3 digit) - mst_cate is no longer used",,,
420,GWL,Partner Service,ProductCategory,name,String,Y,Y,Name of a category,-,-,TBD,Eau de Perfume,,MAST_MCCode,MC_Name,N,N,N,N,Y,N,,,,
421,GWL,Partner Service,ProductCategory,settings,Json,Y,N,category setting related to flag on earnable points and countable for accumulated spending,-,-,TBD,-,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
422,GWL,Partner Service,ProductCategory,createdAt,Datetime,Y,N,The datetime when a category info was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
423,GWL,Partner Service,ProductCategory,createdBy,String,Y,N,Info of GWL admin who created the category info,-,-,TBD,"{""id"": ""Admin1"", ""email"": ""<EMAIL>"" }",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
424,GWL,Partner Service,ProductCategory,updatedAt,Datetime,Y,N,The datetime when the existing category info was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
425,GWL,Partner Service,ProductCategory,updatedBy,String,N,N,Info of GWL admin who updated the category info,-,-,TBD,"{""id"": ""Admin1"", ""email"": ""<EMAIL>"" }",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
426,GWL,Partner Service,ProductCategory,deletedAt,Datetime,N,N,The datetime when a category data is deleted,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
427,GWL,Partner Service,ProductBrand,id,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
428,GWL,Partner Service,ProductBrand,brandid,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
429,GWL,Partner Service,ProductBrand,code,String,Y,Y,,,,,,,MAST_brand,brand_code,N,N,N,N,Y,N,,,,
430,GWL,Partner Service,ProductBrand,name,String,Y,Y,,,,,,,MAST_brand,brand_name,N,N,N,N,Y,N,,,,
431,GWL,Partner Service,ProductBrand,settings,Json,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
432,GWL,Partner Service,ProductBrand,createdAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
433,GWL,Partner Service,ProductBrand,createdBy,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
434,GWL,Partner Service,ProductBrand,updatedAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
435,GWL,Partner Service,ProductBrand,updatedBy,String,N,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
436,GWL,Partner Service,ProductBrand,deletedAt,Datetime,N,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
437,GWL,Partner Service,CostCenter,id,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
438,GWL,Partner Service,CostCenter,partnerId,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
439,GWL,Partner Service,CostCenter,code,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
440,GWL,Partner Service,CostCenter,name,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
441,GWL,Partner Service,CostCenter (not in ERD),companyCode,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
442,GWL,Partner Service,CostCenter (not in ERD),companyName,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
443,GWL,Partner Service,CostCenter,businessAreaCode,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
444,GWL,Partner Service,CostCenter,businessAreaName,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
445,GWL,Partner Service,CostCenter,createdAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
446,GWL,Partner Service,CostCenter,createdBy,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
447,GWL,Partner Service,CostCenter,updatedAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
448,GWL,Partner Service,CostCenter,updatedBy,String,N,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
449,GWL,Partner Service,CostCenter,deletedAt,Datetime,N,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
450,GWL,Partner Service,Import,id,String,Y,N,Unique identifier of import table,-,ULID,TBD,01J7ZD24VHA1ERCAASAAP314CN,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
451,GWL,Partner Service,Import,module,String,Y,N,Module of an import ,-,Enum,TBD,"PAYMENT_METHOD, PRODUCT, BRAND, BRANCH, MEMBER",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
452,GWL,Partner Service,Import,file,Json,Y,N,Imported file,-,-,TBD,"{""fileName"": ""Sheeet-success-id-lowercase_1726557000488.csv"", ""fileUrl"": ""/admin/import-histories/01J7ZD24VHA1ERCAASAAP314CN/file""}",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
453,GWL,Partner Service,Import,status,String,Y,N,Status of import,-,Enum,TBD,FAILED,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
454,GWL,Partner Service,Import,errorLog,Json,N,N,Log of import when error happens,-,-,TBD,"""/admin/import-histories/01J7ZD24VHA1ERCAASAAP314CN/errors""",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
455,GWL,Partner Service,Import,metadata,Json,Y,N,Metadata of an import,-,-,TBD,-,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
456,GWL,Partner Service,Import,createdBy,String,Y,N,Info of GWL admin who created the import record,-,-,TBD,"{""id"": ""Admin1"", ""email"": ""<EMAIL>"" }",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
457,GWL,Partner Service,Import,createdAt,Datetime,Y,N,The datetime when an import record was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
458,GWL,Partner Service,Import,updatedBy,String,Y,N,Info of GWL admin who updated the inport data info,-,-,TBD,"{""id"": ""Admin1"", ""email"": ""<EMAIL>"" }",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
459,GWL,Partner Service,Import,updatedAt,Datetime,Y,N,The datetime when the existing import record was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
460,GWL,Partner Service,Import,deletedAt,Datetime,N,N,The datetime when an import record is deleted,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-10-14T14:43:52.179Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
461,GWL,Engagement Service,Privilege,id,String,Y,N,Unique identifier of a privilege,-,ULID,TBD,01J6AER94YB98VN7C9V1RGRJWF,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
462,GWL,Engagement Service ,Privilege,nameEn,String,Y,N,Name of the privilege in English,-,-,TBD,Birthday Discount 30% (3 Month),,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
463,GWL,Engagement Service,Privilege,nameTh,String,Y,N,Name of the privilege in Thai,-,-,TBD,?????????????????? 30%,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
464,GWL,Engagement Service,Privilege,nameCn,String,N,N,Name of the privilege in Chinese,-,-,TBD,-,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
465,GWL,Engagement Service,Privilege,status,String,Y,N,"draft, active, inactive, archived",,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
466,GWL,Engagement Service,Privilege,typeCode,String,Y,N,Type of the privilege,-,-,TBD,Service/ Cashback/ Discount,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
467,GWL,Engagement Service,Privilege,subTypeCode,String,Y,N,Sub type of the privilege,-,-,TBD,Birthday Discount %,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
468,GWL,Engagement Service,Privilege,value,Decimal,C,N,Absolute usable value,For the privileges that are not Lounge access,-,TBD,"10 (if discount %, it means 10%)",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
469,GWL,Engagement Service,Privilege,renewalFrequency,String,Y,N,The renewal frequency used together with renewal every to get when does the privilege effective,-,-,TBD,"Monthly, Yearly, Birthmonth, never",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
470,GWL,Engagement Service,Privilege,renewalEvery,String,N,N,The renewal every used together with renewal frequency to get when does the privilege effective,-,-,TBD,"Beginning of cycle, End of cycle",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
471,GWL,Engagement Service,Privilege,quantity,Int,N,N,quota for each member,-,-,TBD,6,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
472,GWL,Engagement Service,Privilege,contractReference,String,N,N,The document/ string for the contract when the privilege getting collaborative w/ Partner; Not common for Privilege,-,-,TBD,-,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
473,GWL,Engagement Service,Privilege,cost,Decimal,N,N,Expense for the resp. privilege (THB); cost for 1 privilege for 1 member,-,-,TBD,"10,000",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
474,GWL,Engagement Service,Privilege,usageCondition,Json,Y,N,The detailed condition to be used specific privilege,-,-,TBD,-,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
475,GWL,Engagement Service,Privilege,usageCondition: Validity,Json,Y,N,The validity of the specific privilege,-,Enum,TBD,"export enum PrivilegeUsageValidity { END_OF_YEAR = 'END_OF_YEAR', SPECIFIC_PERIOD = 'SPECIFIC_PERIOD', }",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
476,GWL,Engagement Service,Privilege,usageCondition: expireValue,Int,Y,N,,,,,5,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
477,GWL,Engagement Service,Privilege,usageCondition: expireUnit,Json,Y,N,,,,,"export enum PrivilegeUsageExpireUnit { DAY = 'DAY', MONTH = 'MONTH', YEAR = 'YEAR', }",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
478,GWL,Engagement Service,Privilege,usageCondition: method,Json,Y,N,,,,,"export enum PrivilegeUsageMethod { MEMBER_ID = 'MEMBER_ID', COUPON = 'COUPON', }",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
479,GWL,Engagement Service,Privilege,usageCondition: quotalLimitation,Json,Y,N,,,,,"export enum PrivilegeUsageQuotaLimitation { LIMIT = 'LIMIT', UNLIMITED = 'UNLIMITED', }",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
480,GWL,Engagement Service,Privilege,usageCondition: quotalValue,Int,Y,N,,,,,1000,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
481,GWL,Engagement Service,Privilege,usageCondition: quotalPeriod,Json,Y,N,,,,,"export enum PrivilegeUsageQuotaPeriod { PER_DAY = 'PER_DAY', PER_MONTH = 'PER_MONTH', PER_YEAR = 'PER_YEAR', }",,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
482,GWL,Engagement Service,Privilege,effectiveDate,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
483,GWL,Engagement Service,Privilege,createdAt,Datetime,Y,N,The datetime when the privilege was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-18T12:34:56Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
484,GWL,Engagement Service,Privilege,updatedAt,Datetime,Y,N,The datetime when the privilege was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-18T12:34:56Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
485,GWL,Engagement Service,Privilege,deletedAt,Datetime,N,N,The datetime when the privliege is deleted,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-18T12:34:56Z,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
486,GWL,Engagement Service,MemberPrivilege,id,String,Y,Y,Unique identifier for a privilege member table,-,ULID,TBD,01J6AER94YB98VN7C9V1RGRJWF,,LVBirthday,LVTransKey,N,N/A,N/A,N/A,Y,N,"UUID + ""_"" + LVTransKey","For migration, this table will only keep record for birthday usage",,
487,GWL,Engagement Service,MemberPrivilege,memberId,String,Y,Y,Identifier of a member,-,ULID,TBD,01J6AER94YB98VN7C9V1RGRJWF,,LVBirthday,LVNumber,N,N,Y,N,Y,N,LVBirthday can be used to track birthday usage,,,
488,GWL,Engagement Service,MemberPrivilege,privilegeId,String,Y,N,Identifier of a privilege,-,ULID,TBD,01J6AER94YB98VN7C9V1RGRJWF,,,,N,N,Y,Y,N,N,,Just create a mocking in Privilege table and relate its id here,,
489,GWL,Engagement Service,MemberPrivilege,status,String,Y,N,,,,,,,,,N,N,Y,Y,N,N,,put ACTIVE,,
490,GWL,Engagement Service,MemberPrivilege,isUnlimited,Boolean,Y,N,,,,,,,,,N,N,Y,Y,N,N,,put FALSE,,
491,GWL,Engagement Service,MemberPrivilege,grantedAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,Put any mock date time just to satisfy the column,,
492,GWL,Engagement Service,MemberPrivilege,expiredAt,Datetime,Y,N,,,,,,,LVData,ExpireDate,N,N,Y,Y,N,N,,Get the LVBirthday.LVTransKey to find LVData,,
493,GWL,Engagement Service,MemberPrivilege,createdAt,Datetime,Y,N,The date and time when the privilege - member record was initially created,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-18T12:34:56Z,,LVHeader,AddDT,N/A,N/A,N/A,N/A,Y,N,,Get the LVBirthday.LVTransKey to find LVHeader,,
494,GWL,Engagement Service,MemberPrivilege,updatedAt,Datetime,Y,N,The date and time when the privilege - member record was last updated,-,yyyy-MM-dd'T'hh:mm:ss'Z',TBD,2024-08-18T12:34:56Z,,LVHeader,FinishDT,N/A,N/A,N/A,N/A,Y,N,,,,
495,GWL,Engagement Service,MemberPrivilegeHistory,id,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
496,GWL,Engagement Service,MemberPrivilegeHistory,memberId,String,Y,N,,,,,,LoyaltyValue,LVBirthday,LVNumber,Y,N,Y,N,N,N,,Map SMC_id to GWL_id (reference memberId and gwlNo from Member table),,
497,GWL,Engagement Service,MemberPrivilegeHistory,privilegeId,String,Y,N,,,,,,LoyaltyValue,,,N,N,Y,Y,N,N,,put MIGRATE_BIRTHDAY,,
498,GWL,Engagement Service,MemberPrivilegeHistory,typeCode,String,Y,N,,,,,,LoyaltyValue,LVBirthday,ValueCode,N,N,Y,Y,N,N,,"One value for Birthday Privilege, Reset lounge, Limo will have list from limo team",,
499,GWL,Engagement Service,MemberPrivilegeHistory,subTypeCode,String,Y,N,,,,,,LoyaltyValue,LVBirthday,ValueCode,N,N,Y,Y,N,N,,"One value for Birthday Privilege, Reset lounge, Limo will have list from limo team",,
500,GWL,Engagement Service,MemberPrivilegeHistory,couponCode,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
501,GWL,Engagement Service,MemberPrivilegeHistory,activity,String,Y,N,,,,,,LoyaltyValue,LVBirthday,LVTransKey,N,N,Y,Y,N,N,LVBirthday can be used to track birthday usage,Get list of members who used brithday privilege,,
502,GWL,Engagement Service,MemberPrivilegeHistory,reason,String,N,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
503,GWL,Engagement Service,MemberPrivilegeHistory,location,String,N,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
504,GWL,Engagement Service,MemberPrivilegeHistory,createdBy,String,N,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
505,GWL,Engagement Service,MemberPrivilegeHistory,usedBy,String,N,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
506,GWL,Engagement Service,MemberPrivilegeHistory,createdAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
507,GWL,Engagement Service,MemberPrivilegeHistory,deletedAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
508,GWL,Engagement Service,BenefitType,id,String,Y,N,Unique identifier of a benefit type,-,ULID,TBD,01J6AER94YB98VN7C9V1RGRJWF,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
509,GWL,Engagement Service,BenefitType,name,String,Y,N,Name of benefit type,-,-,TBD,Service,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
510,GWL,Engagement Service,BenefitType,code,String,Y,N,Code of benefit type,-,-,TBD,SERVICE,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
511,GWL,Engagement Service,BenefitType,subTypes,Json,Y,N,list of benefit sub types,-,-,TBD,-,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
512,GWL,Engagement Service,BenefitType,subTypes: name,String,Y,N,name of benefit sub type,-,-,TBD,Lounge,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
513,GWL,Engagement Service,BenefitType,subTypes: code,String,Y,N,code of benefit sub type,-,-,TBD,LOUNGE,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
514,GWL,Engagement Service,MemberCoupon,id,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
515,GWL,Engagement Service,MemberCoupon,memberId,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
516,GWL,Engagement Service,MemberCoupon,entityType,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
517,GWL,Engagement Service,MemberCoupon,entityId,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
518,GWL,Engagement Service,MemberCoupon,status,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
519,GWL,Engagement Service,MemberCoupon,usedAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
520,GWL,Engagement Service,MemberCoupon,expiredAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
521,GWL,Engagement Service,MemberCoupon,couponRef,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
522,GWL,Engagement Service,MemberCoupon,isUnlimited,Boolean,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
523,GWL,Engagement Service,MemberCoupon,isUsedForGuest,Boolean,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
524,GWL,Engagement Service,MemberCoupon,isActive,Boolean,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
525,GWL,Engagement Service,MemberCoupon,createdAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
526,GWL,Engagement Service,MemberCoupon,updatedAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
527,GWL,Engagement Service,MemberCoupon,deletedAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
528,GWL,Engagement Service,PrivilegeCostAllocation,id,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
529,GWL,Engagement Service,PrivilegeCostAllocation,privilegeId,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
530,GWL,Engagement Service,PrivilegeCostAllocation,entityId,String,Y,N,Id = Priviliege-Partner OR id = CostCenter,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
531,GWL,Engagement Service,PrivilegeCostAllocation,entityType,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
532,GWL,Engagement Service,PrivilegeCostAllocation,value,Int,Y,N,"equal to ""subsidizePercent""",,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
533,GWL,Engagement Service,PrivilegeCostAllocation,createdAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
534,GWL,Engagement Service,PrivilegeCostAllocation,updatedAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
535,GWL,Engagement Service,Reward,id,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
536,GWL,Engagement Service,Reward,nameEn,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
537,GWL,Engagement Service,Reward,nameTh,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
538,GWL,Engagement Service,Reward,nameCn,String,N,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
539,GWL,Engagement Service,Reward,isActive,Boolean,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
540,GWL,Engagement Service,Reward,typeCode,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
541,GWL,Engagement Service,Reward,subTypeCode,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
542,GWL,Engagement Service,Reward,value,Decimal,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
543,GWL,Engagement Service,Reward,quantity,int,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
544,GWL,Engagement Service,Reward,quota,int,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
545,GWL,Engagement Service,Reward,contractReference,String,N,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
546,GWL,Engagement Service,Reward,cost,Decimal,N,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
547,GWL,Engagement Service,Reward,usageCondition,Json,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
548,GWL,Engagement Service,Reward,createdAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
549,GWL,Engagement Service,Reward,updatedAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
550,GWL,Engagement Service,Reward,deletedAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
551,GWL,Engagement Service,RedemptionTransaction,id,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
552,GWL,Engagement Service,RedemptionTransaction,memberId,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
553,GWL,Engagement Service,RedemptionTransaction,rewardId,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
554,GWL,Engagement Service,RedemptionTransaction,status,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
555,GWL,Engagement Service,RedemptionTransaction,createdAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
556,GWL,Engagement Service,RedemptionTransaction,updatedAt,Datetime,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
557,GWL,Engagement Service,RewardCostCenter,id,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
558,GWL,Engagement Service,RewardCostCenter,rewardId,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
559,GWL,Engagement Service,RewardCostCenter,costCenterId,String,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
560,GWL,Engagement Service,RewardCostCenter,subsidizePercent,Decimal,Y,N,,,,,,,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,,,,
