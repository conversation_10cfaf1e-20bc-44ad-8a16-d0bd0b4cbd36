from migration_utils.ulid_utils import generate_ulid
from airflow.operators.python import PythonOperator
from airflow import DAG


# SOURCE_TABLE = 'staging_loyalty_service."Member"'
# TARGET_TABLE = 'staging_loyalty_service."ulid_member"'
# PK_NAME = 'ulid_member_pkey'
# SOURCE_TABLE = 'staging_point_service."WalletActivity"'
# TARGET_TABLE = 'staging_point_service."ulid_WalletActivity"'
# PK_NAME = 'ulid_WalletActivity_pkey'
SOURCE_TABLE = 'staging_point_service."WalletBalanceTemp"'
TARGET_TABLE = 'staging_point_service."ulid_WalletBalance"'
PK_NAME = 'ulid_WalletBalance_pkey'
# SOURCE_TABLE = 'staging_point_service."WalletTransaction"'
# TARGET_TABLE = 'staging_point_service."ulid_WalletTransaction"'
# PK_NAME = 'ulid_WalletTransaction_pkey'
# SOURCE_TABLE = 'staging_point_service."WalletAdjustmentTransaction"'
# TARGET_TABLE = 'staging_point_service."ulid_WalletAdjustmentTransaction"'
# PK_NAME = 'ulid_WalletAdjustmentTransaction_pkey'
# SOURCE_TABLE = 'staging_engagement_service."MemberPrivilege"'
# TARGET_TABLE = 'staging_engagement_service."ulid_MemberPrivilege"'
# PK_NAME = 'ulid_MemberPrivilege_pkey'
# SOURCE_TABLE = 'staging_partner_service."SalesTransactionBurnPayment"'
# TARGET_TABLE = 'staging_partner_service."ulid_SalesTransactionBurnPayment"'
# PK_NAME = 'ulid_SalesTransactionBurnPayment_pkey'
# SOURCE_TABLE = 'public."RefundSalesTransactionItemId"'
# TARGET_TABLE = 'staging_partner_service."ulid_RefundSalesTransactionItem"'
# PK_NAME = 'ulid_RefundSalesTransactionItem_pkey'
# SOURCE_TABLE = 'staging_partner_service."SalesTransactionWalletActivityTemp"'
# TARGET_TABLE = 'staging_partner_service."ulid_SalesTransactionWalletActivity"'
# PK_NAME = 'ulid_SalesTransactionWalletActivity_pkey'
CHUNK_SIZE = 100000
LIMIT_CLAUSE = ''
PAGE_SIZE =10000  # Sub-batch size for execute_values

def generate_ulid_member_table():
    generate_ulid(
        SOURCE_TABLE=SOURCE_TABLE,
        TARGET_TABLE=TARGET_TABLE,
        PK_NAME=PK_NAME,
        CHUNK_SIZE=CHUNK_SIZE,
        LIMIT_CLAUSE=LIMIT_CLAUSE,
        PAGE_SIZE=PAGE_SIZE
    )

with DAG(
    dag_id="generate_ulid_member_table",
    start_date=None,
    schedule_interval=None,
    catchup=False,
    tags=["gen","ulid"],
) as dag:
    

    create_ulid_member_table = PythonOperator(
        task_id="create_ulid_member_table_in_stg",
        python_callable=generate_ulid_member_table,
    )

    create_ulid_member_table