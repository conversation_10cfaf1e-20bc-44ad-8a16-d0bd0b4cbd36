from datetime import datetime

from airflow import <PERSON><PERSON>
from airflow.operators.bash_operator import <PERSON><PERSON><PERSON><PERSON><PERSON>
from airflow.operators.python_operator import Python<PERSON><PERSON>ator
from airflow.sensors.external_task import ExternalTaskSensor

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>gresHandler
from engagement_service.validate_member_privilege import MemberPrivilegeValidation


with DAG(
    "validate_engagement_service_full_dump",
    default_args={
        "owner": "airflow",
    },
    description="A dag for Engagement Service validation.",
    schedule_interval=None,
    start_date=datetime(2025, 6, 1),
    catchup=False,
    tags=["engagement_service", "validation"],
) as validate_engagement_service_full_dump_dag:
    temp_db_handler = PostgresHandler(conn_id="temp_db_connection_id")

    member_privilege_validation = MemberPrivilegeValidation(
        service_name="engagement_service",
        mssql_handler=MSSQLHandler(conn_id="loyalty_value_smc_db_connection_id"),
        postgresql_handler=temp_db_handler,
    )

    validate_member_privilege_task = PythonOperator(
        task_id="validate_member_privilege_task",
        python_callable=member_privilege_validation.validate,
        op_args=[temp_db_handler, "EngagementService"],
    )

    validate_member_privilege_task

with DAG(
    "validate_engagement_service_incremental",
    default_args={
        "owner": "airflow",
    },
    description="A dag for Engagement Service incremental validation.",
    schedule_interval="0 17 * * *",
    start_date=datetime(2025, 6, 24, 17, 0),
    catchup=False,
    tags=["engagement_service", "validation"],
) as validate_engagement_service_incremental_dag:
    source_db_handler = MSSQLHandler(conn_id="loyalty_value_smc_db_connection_id")
    destination_db_handler = PostgresHandler(conn_id="temp_db_connection_id")

    # Wait for all migrations to complete
    wait_for_member_privilege_incremental_migration_task = ExternalTaskSensor(
        task_id="wait_for_member_privilege_incremental_migration_task",
        external_dag_id="engagement_service_incremental_migration_member_privilege",
        external_task_id="migrate_member_privilege_task",
    )

    # Validation tasks
    member_privilege_validation = MemberPrivilegeValidation(
        service_name="engagement_service",
        mssql_handler=source_db_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_member_privilege_task = PythonOperator(
        task_id="validate_member_privilege_task",
        python_callable=member_privilege_validation.validate,
        op_args=[destination_db_handler, "EngagementService"],
    )

    delay_after_migrations = BashOperator(
        task_id="delay_after_migrations",
        bash_command="sleep 300",
    )

    # Wait for all migrations, then delay, then run all validations in parallel
    (
        [
            wait_for_member_privilege_incremental_migration_task,
        ]
        >> delay_after_migrations
        >> [
            validate_member_privilege_task,
        ]
    )

with DAG(
    "validate_engagement_service_incremental_manual",
    default_args={
        "owner": "airflow",
    },
    description="A dag for Engagement Service incremental manual validation.",
    schedule_interval=None,
    start_date=datetime(2025, 6, 22),
    catchup=False,
    tags=["engagement_service", "validation"],
) as validate_engagement_service_incremental_manual_dag:
    source_db_handler = MSSQLHandler(conn_id="loyalty_value_smc_db_connection_id")
    destination_db_handler = PostgresHandler(conn_id="temp_db_connection_id")

    # Validation tasks
    member_privilege_validation = MemberPrivilegeValidation(
        service_name="engagement_service",
        mssql_handler=source_db_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_member_privilege_task = PythonOperator(
        task_id="validate_member_privilege_task",
        python_callable=member_privilege_validation.validate,
        op_args=[destination_db_handler, "EngagementService"],
    )

    [validate_member_privilege_task]
