from datetime import datetime
from common_helpers.database_services import <PERSON>gresHandler
from common_helpers.logging import get_logger

from airflow import DAG
from airflow.operators.python_operator import PythonOperator

logger = get_logger()


class PartnerServiceCleanup:
    def __init__(self):
        self.postgresql_handler = PostgresHandler(conn_id="temp_db_connection_id")

    def cleanup_sales_transaction(self):
        """
        Cleanup data from table SalesTransaction.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up SalesTransaction table...")
            cursor.execute('TRUNCATE TABLE partner_service."SalesTransaction" CASCADE;')
            logger.info(f"finished cleaning up SalesTransaction table.")

        self.cleanup(table_cleanup)

    def cleanup_sales_transaction_item(self):
        """
        Cleanup data from table SalesTransactionItem.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up SalesTransactionItem table...")
            cursor.execute(
                'TRUNCATE TABLE partner_service."SalesTransactionItem" CASCADE;'
            )
            logger.info(f"finished cleaning up SalesTransactionItem table.")

        self.cleanup(table_cleanup)

    def cleanup_sales_transaction_payment(self):
        """
        Cleanup data from table SalesTransactionPayment.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up SalesTransactionPayment table...")
            cursor.execute('TRUNCATE TABLE partner_service."SalesTransactionPayment";')
            logger.info(f"finished cleaning up SalesTransactionPayment table.")

        self.cleanup(table_cleanup)

    def cleanup_sales_transaction_burn_payment(self):
        """
        Cleanup data from table SalesTransactionBurnPayment.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up SalesTransactionBurnPayment table...")
            cursor.execute(
                'TRUNCATE TABLE partner_service."SalesTransactionBurnPayment" CASCADE;'
            )
            logger.info(f"finished cleaning up SalesTransactionBurnPayment table.")

        self.cleanup(table_cleanup)

    def cleanup_product_brand(self):
        """
        Cleanup data from table ProductBrand.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up ProductBrand table...")
            cursor.execute('TRUNCATE TABLE partner_service."ProductBrand";')
            logger.info(f"finished cleaning up ProductBrand table.")

        self.cleanup(table_cleanup)

    def cleanup_product_category(self):
        """
        Cleanup data from table ProductCategory.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up ProductCategory table...")
            cursor.execute('TRUNCATE TABLE partner_service."ProductCategory";')
            logger.info(f"finished cleaning up ProductCategory table.")

        self.cleanup(table_cleanup)

    def cleanup_refund_sales_transaction(self):
        """
        Cleanup data from table RefundSalesTransaction.

        Args:
            None

        Returns:
            None
        """

        def table_cleanup(cursor):
            logger.info(f"started cleaning up RefundSalesTransaction table...")
            cursor.execute(
                'TRUNCATE TABLE partner_service."RefundSalesTransaction" CASCADE;'
            )
            logger.info(f"finished cleaning up RefundSalesTransaction table.")

        self.cleanup(table_cleanup)

    def cleanup(self, func):
        """
        Cleanup data from Partner Service table.

        Args:
            func: A function that truncate the table.

        Returns:
            None
        """

        try:
            postgresql_connection = self.postgresql_handler.hook.get_conn()

            with postgresql_connection.cursor() as cursor:
                func(cursor)

            postgresql_connection.commit()
        except Exception as error:
            logger.error(f"an error has occured: {error}")

            if postgresql_connection:
                logger.info("a postgresql connection is found, rolling back...")
                postgresql_connection.rollback()
                logger.info("successfully rolled back.")

            raise error
        finally:
            if postgresql_connection:
                postgresql_connection.close()


with DAG(
    "partner_service_sales_transaction_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL SalesTransactionPayment migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 2, 28),
    catchup=False,
    tags=["partner_service", "sales_transaction", "cleanup"],
) as partner_service_sales_transaction_cleanup_dag:
    PythonOperator(
        task_id="cleanup_sales_transaction_task",
        python_callable=PartnerServiceCleanup().cleanup_sales_transaction,
    )

with DAG(
    "partner_service_sales_transaction_item_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL SalesTransactionItem migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 2, 28),
    catchup=False,
    tags=["partner_service", "sales_transaction_item", "cleanup"],
) as partner_service_sales_transaction_item_cleanup_dag:
    PythonOperator(
        task_id="cleanup_sales_transaction_item_task",
        python_callable=PartnerServiceCleanup().cleanup_sales_transaction_item,
    )

with DAG(
    "partner_service_sales_transaction_payment_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL SalesTransactionPayment migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 2, 28),
    catchup=False,
    tags=["partner_service", "sales_transaction_payment", "cleanup"],
) as partner_service_sales_transaction_payment_cleanup_dag:
    PythonOperator(
        task_id="cleanup_sales_transaction_payment_task",
        python_callable=PartnerServiceCleanup().cleanup_sales_transaction_payment,
    )

with DAG(
    "partner_service_sales_transaction_burn_payment_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL SalesTransactionBurnPayment migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 2, 28),
    catchup=False,
    tags=["partner_service", "sales_transaction_burn_payment", "cleanup"],
) as partner_service_sales_transaction_burn_payment_cleanup_dag:
    PythonOperator(
        task_id="cleanup_sales_transaction_burn_payment_task",
        python_callable=PartnerServiceCleanup().cleanup_sales_transaction_burn_payment,
    )

with DAG(
    "partner_service_product_brand_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL ProductBrand migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 2, 28),
    catchup=False,
    tags=["partner_service", "product_brand", "cleanup"],
) as partner_service_product_brand_cleanup_dag:
    PythonOperator(
        task_id="cleanup_product_brand_task",
        python_callable=PartnerServiceCleanup().cleanup_product_brand,
    )

with DAG(
    "partner_service_product_category_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL ProductCategory migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 2, 28),
    catchup=False,
    tags=["partner_service", "product_category", "cleanup"],
) as partner_service_product_category_cleanup_dag:
    PythonOperator(
        task_id="cleanup_product_category_task",
        python_callable=PartnerServiceCleanup().cleanup_product_category,
    )

with DAG(
    "partner_service_refund_sales_transaction_cleanup",
    default_args={
        "owner": "airflow",
    },
    description="**WARNING**: This DAG cleans up ALL RefundSalesTransaction migration data.",
    schedule_interval=None,
    start_date=datetime(2025, 5, 4),
    catchup=False,
    tags=["partner_service", "refund_sales_transaction", "cleanup"],
) as partner_service_refund_sales_transaction_cleanup_dag:
    PythonOperator(
        task_id="cleanup_refund_sales_transaction_task",
        python_callable=PartnerServiceCleanup().cleanup_refund_sales_transaction,
    )
