import os
from .db_utils import get_p_key, get_p_key_stg
from configs.db_configs import DBConfig
import logging

logger = logging.getLogger(__name__)

def get_table_filepath_crossdb(table_name) -> str:
    '''Get SQL file path for a single table'''
    foldername = table_name.split('.')[0]
    filename = table_name.split('.')[-1].replace("\"", '') + ".sql"
    return os.path.join("dags/crossdb_sql", foldername, filename)

def get_table_filepath_pre_transform(table_name) -> str:
    '''Get SQL file path for a single table'''
    foldername = table_name.split('.')[0]
    filename = table_name.split('.')[-1].replace("\"", '') + ".sql"
    return os.path.join("dags/pre_transform_sql", foldername, filename)


def get_table_filepath_pre_transform_dailysync(table_name) -> str:
    '''Get SQL file path for a single table'''
    foldername = table_name.split('.')[0]
    filename = table_name.split('.')[-1].replace("\"", '') + ".sql"
    return os.path.join("dags/pre_transform_dailysync_sql", foldername, filename)

def get_table_filepath_remove_dailysync(table_name) -> str:
    '''Get SQL file path for a single table'''
    foldername = table_name.split('.')[0]
    filename = table_name.split('.')[-1].replace("\"", '') + ".sql"
    return os.path.join("dags/member_remove_dailysync_sql", foldername, filename)

def get_table_filepath_remove_seperatedservice_dailysync(table_name) -> str:
    '''Get SQL file path for a single table'''
    foldername = table_name.split('.')[0]
    filename = table_name.split('.')[-1].replace("\"", '') + ".sql"
    return os.path.join("dags/member_remove_dailysync_seperatedservice_sql", foldername, filename)

def get_table_filepath_interdb(table_name) -> str:
    '''Get SQL file path for a single table'''
    foldername = table_name.split('.')[0]
    filename = table_name.split('.')[-1].replace("\"", '') + ".sql"
    return os.path.join("dags/interdb_sql", foldername, filename)

def get_table_filepath_cross_gwl_uat(table_name) -> str:
    '''Get SQL file path for a single table'''
    foldername = table_name.split('.')[0]
    filename = table_name.split('.')[-1].replace("\"", '') + ".sql"
    return os.path.join("dags/gwl_uat_crossdb_separatedservice_sql", foldername, filename)



def read_sql_file_crossdb(sql_filepath, table_name, dblink_connection, chunk_size, offset):
    '''Read and prepare SQL file with chunk parameters'''
    order_by_clause = get_p_key(table_name)

    try:
        with open(sql_filepath, "r") as file:
            sql_script = file.read()
            print('sql_script from read: ', sql_script)
            
            # Split the SQL into parts before and after ON CONFLICT
            parts = sql_script.split("ON CONFLICT")
            if len(parts) != 2:
                raise ValueError("SQL file must contain ON CONFLICT clause")
                
            base_sql = parts[0].strip()
            conflict_sql = "ON CONFLICT" + parts[1].strip()
            
            # Replace the connection placeholder
            base_sql = base_sql.replace('my_connection', dblink_connection)
            
            # Extract the AS t1(...) part to preserve column definitions
            as_part = base_sql[base_sql.find("AS"):].strip()
            select_part = base_sql[:base_sql.find("AS")].strip()
            
            # Add LIMIT and OFFSET to the inner SELECT
            # chunked_sql = f'''
            # {select_part} LIMIT {chunk_size} OFFSET {offset}\') {as_part}
            # '''.replace("\') LIMIT ", "  {} LIMIT ".format(order_by_clause))
            chunked_sql = f'''
                {base_sql}
                {conflict_sql}
            '''
            
            return chunked_sql
            
    except FileNotFoundError:
        raise FileNotFoundError(f"SQL file not found: {sql_filepath}")
    except Exception as e:
        raise Exception(f"Error reading SQL file {sql_filepath}: {str(e)}")
    

def read_sql_file_crossdb_dailysync(sql_filepath, table_name, dblink_connection, chunk_size, offset, **kwargs):
    '''Read and prepare SQL file with chunk parameters'''
    order_by_clause = get_p_key(table_name)

    start_ts    = kwargs['params'].get('start_timestamps', '')   
    end_ts      = kwargs['params'].get('end_timestamps', '')  

    try:
        with open(sql_filepath, "r") as file:
            sql_script = file.read()
            
            # Split the SQL into parts before and after ON CONFLICT
            parts = sql_script.split("ON CONFLICT")
            if len(parts) != 2:
                raise ValueError("SQL file must contain ON CONFLICT clause")
                
            base_sql = parts[0].strip()
            conflict_sql = "ON CONFLICT" + parts[1].strip()
            
            # Check if the "updatedAt" column exists in the source table
            where_clause = ""
            if "updatedAt" in sql_script:
                where_clause = f'''WHERE (t1.\"createdAt\" BETWEEN '{start_ts}' AND '{end_ts}') OR (t1.\"updatedAt\" BETWEEN '{start_ts}' AND '{end_ts}') '''
            else:
                where_clause = f'''WHERE (t1.\"createdAt\" BETWEEN '{start_ts}' AND '{end_ts}') '''

            # Replace the connection placeholder
            base_sql = base_sql.replace('my_connection', dblink_connection)
            
            # Extract the AS t1(...) part to preserve column definitions
            as_part = base_sql[base_sql.find("AS"):].strip()
            select_part = base_sql[:base_sql.find("AS")].strip()
            
            # Add LIMIT and OFFSET to the inner SELECT
            # chunked_sql = f'''
            # {select_part} LIMIT {chunk_size} OFFSET {offset}\') {as_part}
            # '''.replace("\') LIMIT ", "  {} LIMIT ".format(order_by_clause))
            chunked_sql = f'''
                {base_sql}
                {where_clause}
                {conflict_sql}
            '''
            
            return chunked_sql
            
    except FileNotFoundError:
        raise FileNotFoundError(f"SQL file not found: {sql_filepath}")
    except Exception as e:
        raise Exception(f"Error reading SQL file {sql_filepath}: {str(e)}")
    




def read_sql_file_crossdb_dailysync_takefulltable(sql_filepath, table_name, dblink_connection, chunk_size, offset, **kwargs):
    '''Read and prepare SQL file with chunk parameters'''
    order_by_clause = get_p_key(table_name)

    start_ts    = kwargs['params'].get('start_timestamps', '')   
    end_ts      = kwargs['params'].get('end_timestamps', '')  

    try:
        with open(sql_filepath, "r") as file:
            sql_script = file.read()
            
            # Split the SQL into parts before and after ON CONFLICT
            parts = sql_script.split("ON CONFLICT")
            if len(parts) != 2:
                raise ValueError("SQL file must contain ON CONFLICT clause")
                
            base_sql = parts[0].strip()
            conflict_sql = "ON CONFLICT" + parts[1].strip()
            
            
            # Replace the connection placeholder
            base_sql = base_sql.replace('my_connection', dblink_connection)
            
            # Extract the AS t1(...) part to preserve column definitions
            as_part = base_sql[base_sql.find("AS"):].strip()
            select_part = base_sql[:base_sql.find("AS")].strip()
            
            # Add LIMIT and OFFSET to the inner SELECT
            # chunked_sql = f'''
            # {select_part} LIMIT {chunk_size} OFFSET {offset}\') {as_part}
            # '''.replace("\') LIMIT ", "  {} LIMIT ".format(order_by_clause))
            chunked_sql = f'''
                {base_sql}
                {conflict_sql}
            '''
            
            return chunked_sql
            
    except FileNotFoundError:
        raise FileNotFoundError(f"SQL file not found: {sql_filepath}")
    except Exception as e:
        raise Exception(f"Error reading SQL file {sql_filepath}: {str(e)}")
    

def read_sql_file_crossdb_dailysync_for_incremental_count(sql_filepath, table_name, dblink_connection, chunk_size, offset, **kwargs):
    '''Read and prepare SQL file with chunk parameters'''
    order_by_clause = get_p_key(table_name)

    start_ts    = kwargs['params'].get('start_timestamps', '')   
    end_ts      = kwargs['params'].get('end_timestamps', '')  

    try:
        with open(sql_filepath, "r") as file:
            sql_script = file.read()
            
            # Split the SQL into parts before and after ON CONFLICT
            parts = sql_script.split("ON CONFLICT")
            if len(parts) != 2:
                raise ValueError("SQL file must contain ON CONFLICT clause")
                
            base_sql = parts[0].strip()
            count_sql = "SELECT COUNT(*) FROM " + base_sql.split("SELECT * FROM")[1].strip()
            # conflict_sql = "ON CONFLICT" + parts[1].strip()
            
            # Check if the "updatedAt" column exists in the source table
            where_clause = ""
            if "updatedAt" in sql_script:
                where_clause = f'''WHERE (\"createdAt\" BETWEEN '{start_ts}' AND '{end_ts}') OR (\"updatedAt\" BETWEEN '{start_ts}' AND '{end_ts}') '''
            else:
                where_clause = f'''WHERE (\"createdAt\" BETWEEN '{start_ts}' AND '{end_ts}') '''

            # Replace the connection placeholder
            count_sql = count_sql.replace('my_connection', dblink_connection)
            
            # Extract the AS t1(...) part to preserve column definitions
            as_part = base_sql[base_sql.find("AS"):].strip()
            select_part = base_sql[:base_sql.find("AS")].strip()
            
            # Add LIMIT and OFFSET to the inner SELECT
            # chunked_sql = f'''
            # {select_part} LIMIT {chunk_size} OFFSET {offset}\') {as_part}
            # '''.replace("\') LIMIT ", "  {} LIMIT ".format(order_by_clause))
            incremental_count_sql = f'''
                {count_sql} {where_clause}
            '''
            
            return incremental_count_sql
            
    except FileNotFoundError:
        raise FileNotFoundError(f"SQL file not found: {sql_filepath}")
    except Exception as e:
        raise Exception(f"Error reading SQL file {sql_filepath}: {str(e)}")


def read_sql_file_crossdb_dailysync_for_incremental_count_seperatedservice_takefulltable(sql_filepath, table_name, dblink_connection, chunk_size, offset, **kwargs):
    '''Read and prepare SQL file with chunk parameters
       no where clause 
    '''
    order_by_clause = get_p_key(table_name)

    start_ts    = kwargs['params'].get('start_timestamps', '')   
    end_ts      = kwargs['params'].get('end_timestamps', '')  

    try:
        with open(sql_filepath, "r") as file:
            sql_script = file.read()
            
            # Split the SQL into parts before and after ON CONFLICT
            parts = sql_script.split("ON CONFLICT")
            if len(parts) != 2:
                raise ValueError("SQL file must contain ON CONFLICT clause")
                
            base_sql = parts[0].strip()
            count_sql = "SELECT COUNT(*) FROM " + base_sql.split("SELECT * FROM")[1].strip()
            # conflict_sql = "ON CONFLICT" + parts[1].strip()
            
            
            # Replace the connection placeholder
            count_sql = count_sql.replace('my_connection', dblink_connection)
            
            # Extract the AS t1(...) part to preserve column definitions
            as_part = base_sql[base_sql.find("AS"):].strip()
            select_part = base_sql[:base_sql.find("AS")].strip()
            
            # Add LIMIT and OFFSET to the inner SELECT
            # chunked_sql = f'''
            # {select_part} LIMIT {chunk_size} OFFSET {offset}\') {as_part}
            # '''.replace("\') LIMIT ", "  {} LIMIT ".format(order_by_clause))
            incremental_count_sql = f'''
                {count_sql}
            '''
            
            return incremental_count_sql
            
    except FileNotFoundError:
        raise FileNotFoundError(f"SQL file not found: {sql_filepath}")
    except Exception as e:
        raise Exception(f"Error reading SQL file {sql_filepath}: {str(e)}")

def read_sql_file_crossdb_dailysync_for_delete(sql_filepath, table_name, dblink_connection, chunk_size, offset, **kwargs):
    '''Read and prepare SQL file with chunk parameters'''
    order_by_clause = get_p_key(table_name)

    start_ts    = kwargs['params'].get('start_timestamps', '')   
    end_ts      = kwargs['params'].get('end_timestamps', '')  
    logger.info(sql_filepath)
    logger.info(table_name)

    try:
        with open(sql_filepath, "r") as file:
            sql_script = file.read()
            
            # Split the SQL into parts before and after ON CONFLICT
            parts = sql_script.split("ON CONFLICT")
            if len(parts) != 2:
                raise ValueError("SQL file must contain ON CONFLICT clause")
                
            base_sql = parts[0].strip()
            conflict_sql = "ON CONFLICT" + parts[1].strip()
            # where_clause = f'''WHERE \"cratedAt\" BETWEEN '{start_ts}' AND '{end_ts}' ''' # column name to be change depend on table update with specific column
            
            # Replace the connection placeholder
            base_sql = base_sql.replace('my_connection', dblink_connection)
            
            # Replace the 'start_timestamps' and 'end_timestamps' in the SQL script it will replace all occurrences of the specified substring with the new substring. However, you can use the optional count parameter to limit the number of replacements. With count parameter:string.replace(old, new, count)  
            base_sql = base_sql.replace('start_timestamps', str(start_ts))
            base_sql = base_sql.replace('end_timestamps', str(end_ts))

            # Extract the AS t1(...) part to preserve column definitions
            as_part = base_sql[base_sql.find("AS"):].strip()
            select_part = base_sql[:base_sql.find("AS")].strip()
            
            # Add LIMIT and OFFSET to the inner SELECT
            # chunked_sql = f'''
            # {select_part} LIMIT {chunk_size} OFFSET {offset}\') {as_part}
            # '''.replace("\') LIMIT ", "  {} LIMIT ".format(order_by_clause))
            chunked_sql = f'''
                {base_sql}
                {conflict_sql}
            '''
            
            return chunked_sql
            
    except FileNotFoundError:
        raise FileNotFoundError(f"SQL file not found: {sql_filepath}")
    except Exception as e:
        raise Exception(f"Error reading SQL file {sql_filepath}: {str(e)}")
    
def     read_sql_file_crossdb_dailysync_seperatedservice_for_delete(sql_filepath, table_name, dblink_connection, gwl_dblink_connection, chunk_size, offset, **kwargs):
    '''Read and prepare SQL file with chunk parameters'''
    order_by_clause = get_p_key(table_name)

    start_ts    = kwargs['params'].get('start_timestamps', '')   
    end_ts      = kwargs['params'].get('end_timestamps', '')  

    try:
        with open(sql_filepath, "r") as file:
            sql_script = file.read()
            
            # Split the SQL into parts before and after ON CONFLICT
            parts = sql_script.split("ON CONFLICT")
            if len(parts) != 2:
                raise ValueError("SQL file must contain ON CONFLICT clause")
                
            base_sql = parts[0].strip()
            conflict_sql = "ON CONFLICT" + parts[1].strip()
            # where_clause = f'''WHERE \"cratedAt\" BETWEEN '{start_ts}' AND '{end_ts}' ''' # column name to be change depend on table update with specific column
            
            # Replace the connection placeholder
            base_sql = base_sql.replace('my_connection', dblink_connection)
            base_sql = base_sql.replace('my_gwl_connection', gwl_dblink_connection)
            
            # Replace the 'start_timestamps' and 'end_timestamps' in the SQL script it will replace all occurrences of the specified substring with the new substring. However, you can use the optional count parameter to limit the number of replacements. With count parameter:string.replace(old, new, count)  
            base_sql = base_sql.replace('start_timestamps', str(start_ts))
            base_sql = base_sql.replace('end_timestamps', str(end_ts))

            # Extract the AS t1(...) part to preserve column definitions
            as_part = base_sql[base_sql.find("AS"):].strip()
            select_part = base_sql[:base_sql.find("AS")].strip()
            
            # Add LIMIT and OFFSET to the inner SELECT
            # chunked_sql = f'''
            # {select_part} LIMIT {chunk_size} OFFSET {offset}\') {as_part}
            # '''.replace("\') LIMIT ", "  {} LIMIT ".format(order_by_clause))
            chunked_sql = f'''
                {base_sql}
                {conflict_sql}
            '''
            
            return chunked_sql
            
    except FileNotFoundError:
        raise FileNotFoundError(f"SQL file not found: {sql_filepath}")
    except Exception as e:
        raise Exception(f"Error reading SQL file {sql_filepath}: {str(e)}")

def read_sql_file_interdb(sql_filepath, table_name, chunk_size, offset):
    '''Read and prepare SQL file with chunk parameters'''
    order_by_clause = get_p_key_stg(table_name)

    if os.path.isfile(sql_filepath):
        print(f"SQL file  founded: {sql_filepath}")

        try:
            with open(sql_filepath, "r") as file:
                sql_script = file.read()
        
                # Extract the transformation column clause from the original SQL
                start_index = sql_script.find("INSERT")
                end_index = sql_script.find("ON CONFLICT")
                insert_transform = sql_script[start_index:end_index].strip()
                # replace <encrypted_key> with DBConfig.secret_pii_encryption
                insert_transform = insert_transform.replace('<encrypted_key>', DBConfig.secret_pii_encryption)

                on_conflict_clause = ""
                if "ON CONFLICT" in sql_script:
                    on_conflict_start = sql_script.find('ON CONFLICT')
                    on_conflict_end = sql_script.find(';', on_conflict_start)
                    if on_conflict_end != -1:  # Make sure we found the semicolon
                        on_conflict_clause = sql_script[on_conflict_start:on_conflict_end]

                # Add LIMIT and OFFSET to the inner SELECT
                # chunked_sql = f'''
                # {insert_transform}{order_by_clause} LIMIT {chunk_size} OFFSET {offset}
                # {on_conflict_clause}
                # '''
                chunked_sql = f'''
                {insert_transform}
                {on_conflict_clause}
                '''
                
                return chunked_sql
                
        except FileNotFoundError:
            raise FileNotFoundError(f"SQL file not found: {sql_filepath}")
        except Exception as e:
            raise Exception(f"Error reading SQL file {sql_filepath}: {str(e)}")
    else:
        print(f"!!!!!!!! SQL file NOT found !!!!!!!! : {sql_filepath}") 




def read_sql_file_interdb_dailysync(sql_filepath, table_name, chunk_size, offset, **kwargs):
    '''Read and prepare SQL file with chunk parameters'''
    order_by_clause = get_p_key_stg(table_name)

    start_ts    = kwargs['params'].get('start_timestamps', '')   
    end_ts      = kwargs['params'].get('end_timestamps', '')  

    if os.path.isfile(sql_filepath):
        print(f"SQL file  founded: {sql_filepath}")

        try:
            with open(sql_filepath, "r") as file:
                sql_script = file.read()
        
                # Extract the transformation column clause from the original SQL
                start_index = sql_script.find("INSERT")
                end_index = sql_script.find("ON CONFLICT")
                insert_transform = sql_script[start_index:end_index].strip()
                # replace <encrypted_key> with DBConfig.secret_pii_encryption
                insert_transform = insert_transform.replace('<encrypted_key>', DBConfig.secret_pii_encryption)
                insert_transform = insert_transform.replace('start_timestamps', str(start_ts))
                insert_transform = insert_transform.replace('end_timestamps', str(end_ts))

                # Check if the "updatedAt" column exists in the source table
                where_clause = ""
                if "updatedAt" in sql_script:
                    where_clause = f'''WHERE (\"createdAt\" BETWEEN '{start_ts}' AND '{end_ts}') OR (\"updatedAt\" BETWEEN '{start_ts}' AND '{end_ts}') '''
                else:
                    where_clause = f'''WHERE (\"createdAt\" BETWEEN '{start_ts}' AND '{end_ts}') '''
                

                on_conflict_clause = ""
                if "ON CONFLICT" in sql_script:
                    on_conflict_start = sql_script.find('ON CONFLICT')
                    on_conflict_end = sql_script.find(';', on_conflict_start)
                    if on_conflict_end != -1:  # Make sure we found the semicolon
                        on_conflict_clause = sql_script[on_conflict_start:on_conflict_end]

                # Add LIMIT and OFFSET to the inner SELECT
                # chunked_sql = f'''
                # {insert_transform}{order_by_clause} LIMIT {chunk_size} OFFSET {offset}
                # {on_conflict_clause}
                # '''
                chunked_sql = f'''
                {insert_transform}
                {where_clause}
                {on_conflict_clause}
                '''
                
                return chunked_sql
                
        except FileNotFoundError:
            raise FileNotFoundError(f"SQL file not found: {sql_filepath}")
        except Exception as e:
            raise Exception(f"Error reading SQL file {sql_filepath}: {str(e)}")
    else:
        print(f"!!!!!!!! SQL file NOT found !!!!!!!! : {sql_filepath}") 





def read_sql_file_interdb_dailysync_takefulltable(sql_filepath, table_name, chunk_size, offset, **kwargs):
    '''Read and prepare SQL file with chunk parameters'''
    order_by_clause = get_p_key_stg(table_name)

    start_ts    = kwargs['params'].get('start_timestamps', '')   
    end_ts      = kwargs['params'].get('end_timestamps', '')  

    if os.path.isfile(sql_filepath):
        print(f"SQL file  founded: {sql_filepath}")

        try:
            with open(sql_filepath, "r") as file:
                sql_script = file.read()
        
                # Extract the transformation column clause from the original SQL
                start_index = sql_script.find("INSERT")
                end_index = sql_script.find("ON CONFLICT")
                insert_transform = sql_script[start_index:end_index].strip()
                # replace <encrypted_key> with DBConfig.secret_pii_encryption
                insert_transform = insert_transform.replace('<encrypted_key>', DBConfig.secret_pii_encryption)
                insert_transform = insert_transform.replace('start_timestamps', str(start_ts))
                insert_transform = insert_transform.replace('end_timestamps', str(end_ts))

                

                on_conflict_clause = ""
                if "ON CONFLICT" in sql_script:
                    on_conflict_start = sql_script.find('ON CONFLICT')
                    on_conflict_end = sql_script.find(';', on_conflict_start)
                    if on_conflict_end != -1:  # Make sure we found the semicolon
                        on_conflict_clause = sql_script[on_conflict_start:on_conflict_end]

                # Add LIMIT and OFFSET to the inner SELECT
                # chunked_sql = f'''
                # {insert_transform}{order_by_clause} LIMIT {chunk_size} OFFSET {offset}
                # {on_conflict_clause}
                # '''
                chunked_sql = f'''
                {insert_transform}               
                {on_conflict_clause}
                '''
                
                return chunked_sql
                
        except FileNotFoundError:
            raise FileNotFoundError(f"SQL file not found: {sql_filepath}")
        except Exception as e:
            raise Exception(f"Error reading SQL file {sql_filepath}: {str(e)}")
    else:
        print(f"!!!!!!!! SQL file NOT found !!!!!!!! : {sql_filepath}") 