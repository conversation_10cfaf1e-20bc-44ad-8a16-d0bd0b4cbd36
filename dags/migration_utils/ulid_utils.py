import time
import pandas as pd
import ulid
from sqlalchemy import create_engine
import psycopg2
from psycopg2.extras import execute_values
from configs.db_configs import DBConfig

temp_dbname = DBConfig.temp_dbname
temp_user = DBConfig.temp_user
temp_password = DBConfig.temp_password
temp_host = DBConfig.temp_host
temp_port = DBConfig.temp_port

gwl_dbname = DBConfig.gwl_dbname
gwl_user = DBConfig.gwl_user
gwl_password = DBConfig.gwl_password
gwl_host = DBConfig.gwl_host
gwl_port = DBConfig.gwl_port

# SOURCE_TABLE = 'staging_loyalty_service."Member"'
# TARGET_TABLE = 'staging_loyalty_service."member_1"'
# CHUNK_SIZE = 100000
# LIMIT_CLAUSE = '' # example 'LIMIT 100'
# PAGE_SIZE =10000  # Sub-batch size for execute_values

def generate_ulid(SOURCE_TABLE: str, TARGET_TABLE: str, PK_NAME: str, CHUNK_SIZE: int = 100000, LIMIT_CLAUSE: str = '', PAGE_SIZE: int = 10000):
    
    """Transform data from source to target table using DataFrame."""
    try:
        start_time = time.time()
        print("Starting data transformation...")

        # Create SQLAlchemy engine for pandas
        connection_string = f"postgresql://{gwl_user}:{gwl_password}@{gwl_host}:{gwl_port}/{gwl_dbname}"
        engine = create_engine(connection_string)
        print(f'''Successfully connected to {TARGET_TABLE}... via SQLAlchemy''')
        
        # Create PostgreSQL connection
        conn = psycopg2.connect(
            dbname=gwl_dbname,
            user=gwl_user,
            password=gwl_password,
            host=gwl_host,
            port=gwl_port
        )
        print(f"Successfully connected to {TARGET_TABLE}... via psycopg2")

        print(f"Drop and create {TARGET_TABLE}...")

        # Create the table (adjust columns as needed)
        with conn.cursor() as cur:
            # Drop table if exists
            # cur.execute(f'''DROP TABLE IF EXISTS {TARGET_TABLE} CASCADE;''')
            
            # Create table
            cur.execute(f'''
                CREATE TABLE IF NOT EXISTS {TARGET_TABLE} (
                    ulid_id text NOT NULL,
                    id text NOT NULL,
                    "createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
                    CONSTRAINT "{PK_NAME}" PRIMARY KEY (id)
                );
            ''')
            conn.commit()

        # Process data in chunks
        total_rows = 0
        chunk_num = 1
        
        print(f"Reading and processing data from {SOURCE_TABLE} in chunks...")
        with engine.connect() as engine_conn:
            for chunk_df in pd.read_sql(
                sql=f'''SELECT id, "createdAt" FROM {SOURCE_TABLE} {LIMIT_CLAUSE}''',
                con=engine_conn.connection,
                chunksize=CHUNK_SIZE
            ):
                chunk_start_time = time.time()
                
                # Generate ULIDs for this chunk
                chunk_df['ulid_id'] = [str(ulid.new()) for _ in range(len(chunk_df))]
                
                # Convert chunk to values for insertion
                columns = chunk_df.columns.tolist()
                values = [tuple(x) for x in chunk_df.values]
                
                # Insert chunk
                with conn.cursor() as cur:
                    insert_query = f'''
                        INSERT INTO {TARGET_TABLE} ({', '.join(f'"{col}"' for col in columns)})
                        VALUES %s
                        ON CONFLICT ("id") DO NOTHING
                    '''
                    execute_values(
                        cur, 
                        insert_query,
                        values,
                        page_size=PAGE_SIZE  # Sub-batch size for execute_values
                    )
                    conn.commit()
                
                chunk_duration = time.time() - chunk_start_time
                total_rows += len(chunk_df)
                
                print(f"Chunk {chunk_num} processed: {len(chunk_df):,} rows "
                      f"({chunk_duration:.1f} seconds, "
                      f"{len(chunk_df)/chunk_duration:.0f} rows/second)")
                
                chunk_num += 1

        conn.close()
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        print("\nOperation Complete!")
        print(f"├─ Total rows processed: {total_rows:,}")
        print(f"├─ Total time: {total_duration/60:.1f} minutes")
        print(f"└─ Average rate: {total_rows/total_duration:.0f} rows/second")
            
    except Exception as e:
        print(f"Error during transformation: {e}")
        raise
    finally:
        if 'conn' in locals() and conn:
            conn.close()
        if 'engine' in locals():
            engine.dispose()









def generate_ulid_dailysync(SOURCE_TABLE: str, TARGET_TABLE: str, PK_NAME: str, CHUNK_SIZE: int = 100000, LIMIT_CLAUSE: str = '', PAGE_SIZE: int = 10000, **kwargs):
    
    """Transform data from source to target table using DataFrame."""
    start_ts    = kwargs['params'].get('start_timestamps', '')   
    end_ts      = kwargs['params'].get('end_timestamps', '')

    try:
        start_time = time.time()
        print("Starting data transformation...")

        # Create SQLAlchemy engine for pandas
        connection_string = f"postgresql://{gwl_user}:{gwl_password}@{gwl_host}:{gwl_port}/{gwl_dbname}"
        engine = create_engine(connection_string)
        print(f'''Successfully connected to {TARGET_TABLE}... via SQLAlchemy''')
        
        # Create PostgreSQL connection
        conn = psycopg2.connect(
            dbname=gwl_dbname,
            user=gwl_user,
            password=gwl_password,
            host=gwl_host,
            port=gwl_port
        )
        print(f"Successfully connected to {TARGET_TABLE}... via psycopg2")

        print(f"Drop and create {TARGET_TABLE}...")

        # Create the table (adjust columns as needed)
        with conn.cursor() as cur:
            # Drop table if exists
            # cur.execute(f'''DROP TABLE IF EXISTS {TARGET_TABLE} CASCADE;''')
            
            # Create table
            cur.execute(f'''
                CREATE TABLE IF NOT EXISTS {TARGET_TABLE} (
                    ulid_id text NOT NULL,
                    id text NOT NULL,
                    "createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
                    CONSTRAINT "{PK_NAME}" PRIMARY KEY (id)
                );
            ''')
            conn.commit()

        # Process data in chunks
        total_rows = 0
        chunk_num = 1
        
        print(f"Reading and processing data from {SOURCE_TABLE} in chunks...")
        print('\nchecking the memberId before gen ulid if exist skip')
        print(f'only gen ulid for new data that created or updated between {start_ts} and {end_ts}')
        with engine.connect() as engine_conn:
            # get only memberId that not exist in ulid_xxx table and only new data that created or updated between start_ts and end_ts
            
            # # Check if the "updatedAt" column exists in the source table
            # check_updated_at_query = f'''
            #     SELECT 1
            #     FROM information_schema.columns
            #     WHERE table_schema = '{SOURCE_TABLE.split('.')[0]}'
            #     AND table_name = '{SOURCE_TABLE.split('.')[1]}'
            #     AND column_name = 'updatedAt';
            # '''
            # has_updated_at = engine_conn.execute(check_updated_at_query).scalar_one_or_none() is not None

            # Construct the main query dynamically
            where_clause = ''
            if  SOURCE_TABLE == 'public."RefundSalesTransactionItemId"' or \
                SOURCE_TABLE == 'staging_point_service."WalletTransaction"' or \
                SOURCE_TABLE == 'staging_point_service."WalletAdjustmentTransaction"':
                where_clause = f'''
                    WHERE ulid_table.id IS NULL
                    AND (s."createdAt" BETWEEN '{start_ts}' AND '{end_ts}')
                '''
            else:
                where_clause = f'''
                    WHERE ulid_table.id IS NULL
                    AND (s."createdAt" BETWEEN '{start_ts}' AND '{end_ts}' OR s."updatedAt" BETWEEN '{start_ts}' AND '{end_ts}')
                '''
                

            query = f'''
                SELECT s.id
                FROM {SOURCE_TABLE} s
                LEFT JOIN {TARGET_TABLE} ulid_table ON s.id = ulid_table.id
                {where_clause}
            '''
            
            print(query)
            
            for chunk_df in pd.read_sql(
                    sql=query,
                    con=engine_conn.connection,
                    chunksize=CHUNK_SIZE
                ):
                    chunk_start_time = time.time()
                    
                    # Generate ULIDs for this chunk
                    chunk_df['ulid_id'] = [str(ulid.new()) for _ in range(len(chunk_df))]
                    
                    # Convert chunk to values for insertion
                    columns = chunk_df.columns.tolist()
                    values = [tuple(x) for x in chunk_df.values]
                    
                    # Insert chunk
                    with conn.cursor() as cur:
                        insert_query = f'''
                            INSERT INTO {TARGET_TABLE} ({', '.join(f'"{col}"' for col in columns)})
                            VALUES %s
                            ON CONFLICT ("id") DO NOTHING
                        '''
                        execute_values(
                            cur, 
                            insert_query,
                            values,
                            page_size=PAGE_SIZE  # Sub-batch size for execute_values
                        )
                        conn.commit()
                    
                    chunk_duration = time.time() - chunk_start_time
                    total_rows += len(chunk_df)
                    
                    print(f"Chunk {chunk_num} processed: {len(chunk_df):,} rows "
                        f"({chunk_duration:.1f} seconds, "
                        f"{len(chunk_df)/chunk_duration:.0f} rows/second)")
                    
                    chunk_num += 1

        conn.close()
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        print("\nOperation Complete!")
        print(f"├─ generate ulid only for new data that did not exist in {TARGET_TABLE} table")
        print(f"├─ Total rows processed: {total_rows:,}")
        print(f"├─ Total time: {total_duration/60:.1f} minutes")
        print(f"└─ Average rate: {total_rows/total_duration:.0f} rows/second")
            
    except Exception as e:
        print(f"Error during transformation: {e}")
        raise
    finally:
        if 'conn' in locals() and conn:
            conn.close()
        if 'engine' in locals():
            engine.dispose()