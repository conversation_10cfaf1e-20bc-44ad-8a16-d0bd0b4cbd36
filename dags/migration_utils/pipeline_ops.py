from typing import List
from .migration_ops import (fetch_insert_crossdb_with_dblink, fetch_transform_insert_interdb,
                          make_pre_transform_table, truncate_tables, add_sync_timestamp_column,
                          fetch_insert_crossdb_with_dblink_dailysync, fetch_detect_remove_crossdb_dailysync,
                          make_pre_transform_table_dailysync, fetch_transform_insert_interdb_dailysync)
from _table_sequence import PRE_TRANSFORM_TABLES

def filter_tables_by_service(tables: List[str], service_name: str) -> List[str]:
    """Filter tables by service name"""
    return [table for table in tables if table.startswith(f'{service_name}.')]

def get_staging_table_names(tables: List[str]) -> List[str]:
    """Convert table names to staging table names"""
    return [f'''staging_{table.split('.')[-1].replace('"', '')}''' for table in tables]

def process_pre_transform_tables():
    """Process pre-transform tables in the specified order"""
    print("\nProcessing pre-transform tables in specified order")
    
    for sql_file in PRE_TRANSFORM_TABLES:
        try:
            print(f"\nProcessing pre-transform file: {sql_file}")
            # Extract table name from SQL filename
            table_name = sql_file.replace('.sql', '')
            make_pre_transform_table(table_name)
            print(f"Successfully processed {sql_file}")
        except Exception as e:
            print(f"Error processing pre-transform file {sql_file}: {str(e)}")
            raise

def process_pre_transform_tables_dailysync(**kwargs):
    """Process pre-transform tables in the specified order"""
    print("\nProcessing pre-transform tables in specified order")
    
    for sql_file in PRE_TRANSFORM_TABLES:
        try:
            print(f"\nProcessing pre-transform file: {sql_file}")
            # Extract table name from SQL filename
            table_name = sql_file.replace('.sql', '')
            make_pre_transform_table_dailysync(table_name, **kwargs)
            print(f"Successfully processed {sql_file}")
        except Exception as e:
            print(f"Error processing pre-transform file {sql_file}: {str(e)}")
            raise

def process_service_tables(tables: List[str], service_name: str, operation: str, is_incremental: bool = False):
    """Process tables for a specific service"""
    service_tables = filter_tables_by_service(tables, service_name)
    if not service_tables:
        print(f"No tables found for service: {service_name}")
        return

    print(f"\nProcessing {len(service_tables)} tables for {service_name}")
    
    for table in service_tables:
        try:
            if operation == "crossdb":
                fetch_insert_crossdb_with_dblink(table, is_incremental=is_incremental)
            elif operation == "pre_transform":
                make_pre_transform_table(table)
            elif operation == "transform":
                fetch_transform_insert_interdb(table)
            else:
                raise ValueError(f"Unknown operation: {operation}")
        except Exception as e:
            print(f"Error processing table {table}: {str(e)}")
            raise


def process_service_tables_dailysync(tables: List[str], service_name: str, operation: str, is_incremental: bool = False, startts: str = None, endts: str = None, **kwargs):
    """Process tables for a specific service"""
    service_tables = filter_tables_by_service(tables, service_name)
    if not service_tables:
        print(f"No tables found for service: {service_name}")
        return

    print(f"\nProcessing {len(service_tables)} tables for {service_name}")
    
    for table in service_tables:
        try:
            if operation == "crossdb_dailysync":
                fetch_insert_crossdb_with_dblink_dailysync(table, is_incremental=is_incremental, **kwargs)
            elif operation == "pre_transform":
                make_pre_transform_table(table)
            elif operation == "transform":
                fetch_transform_insert_interdb_dailysync(table)
            else:
                raise ValueError(f"Unknown operation: {operation}")
        except Exception as e:
            print(f"Error processing table {table}: {str(e)}")
            raise


def process_remove_member_dailysync(tables: List[str], **kwargs):
    """Process remove member that update from daily sync on tables for a specific service"""
    for table in tables:
        try:
            fetch_detect_remove_crossdb_dailysync(table, **kwargs)
        except Exception as e:
            print(f"Error processing table {table}: {str(e)}")
            raise



def process_transformed_tables(tables: List[str]):
    '''Process tables for ordered tables'''
    for table in tables:
        try:
            fetch_transform_insert_interdb(table)
        except Exception as e:
            print(f"Error processing table {table}: {str(e)}")
            raise


def process_transformed_tables_dailysync(tables: List[str], **kwargs):
    '''Process tables for ordered tables'''
    for table in tables:
        try:
            fetch_transform_insert_interdb_dailysync(table, **kwargs)
        except Exception as e:
            print(f"Error processing table {table}: {str(e)}")
            raise

def run_migration_pipeline(tables: List[str], service_name: str = None):
    """Run the complete migration pipeline for specified tables or service"""
    if service_name:
        # Process only tables for the specified service
        service_tables = filter_tables_by_service(tables, service_name)
        if not service_tables:
            print(f"No tables found for service: {service_name}")
            return
        
        print(f"\nStarting migration pipeline for service: {service_name}")
        print(f"Found {len(service_tables)} tables to process")
        
        # Truncate staging tables first
        print("\nStep 0: Truncating staging tables")
        staging_tables = get_staging_table_names(service_tables)
        truncate_tables(', '.join(staging_tables))
        
        # Run crossdb migration
        print("\nStep 1: Cross-database migration")
        process_service_tables(service_tables, service_name, "crossdb")
        
        # Run pre-transform in specific order
        print("\nStep 2: Pre-transform (in specified order)")
        process_pre_transform_tables()
        
        # Run transform
        print("\nStep 3: Transform and load")
        process_service_tables(service_tables, service_name, "transform")
        
    else:
        # Process all tables
        print(f"\nStarting migration pipeline for all {len(tables)} tables")
        
        # Truncate staging tables first
        print("\nStep 0: Truncating staging tables")
        staging_tables = get_staging_table_names(tables)
        truncate_tables(', '.join(staging_tables))
        
        # Run crossdb migration
        print("\nStep 1: Cross-database migration")
        for table in tables:
            fetch_insert_crossdb_with_dblink(table)
        
        # Run pre-transform in specific order
        print("\nStep 2: Pre-transform (in specified order)")
        process_pre_transform_tables()
        
        # Run transform
        print("\nStep 3: Transform and load")
        for table in tables:
            fetch_transform_insert_interdb(table)
    
    print("\nMigration pipeline completed successfully!")

def run_daily_sync_pipeline(tables: List[str], service_name: str = None):
    """Run the daily sync pipeline for specified tables or service"""
    if service_name:
        # Process only tables for the specified service
        service_tables = filter_tables_by_service(tables, service_name)
        if not service_tables:
            print(f"No tables found for service: {service_name}")
            return
        
        print(f"\nStarting daily sync pipeline for service: {service_name}")
        print(f"Found {len(service_tables)} tables to process")
        
        # Ensure sync_timestamp column exists in staging tables
        print("\nStep 0: Ensuring sync_timestamp column exists")
        staging_tables = get_staging_table_names(service_tables)
        for table in staging_tables:
            add_sync_timestamp_column(table)
        
        # Run crossdb migration with incremental loading
        print("\nStep 1: Cross-database migration (incremental)")
        process_service_tables(service_tables, service_name, "crossdb", is_incremental=True)
        
        # Run pre-transform in specific order
        print("\nStep 2: Pre-transform (in specified order)")
        process_pre_transform_tables()
        
        # Run transform
        print("\nStep 3: Transform and load")
        process_service_tables(service_tables, service_name, "transform")
        
    else:
        # Process all tables
        print(f"\nStarting daily sync pipeline for all {len(tables)} tables")
        
        # Ensure sync_timestamp column exists in staging tables
        print("\nStep 0: Ensuring sync_timestamp column exists")
        staging_tables = get_staging_table_names(tables)
        for table in staging_tables:
            add_sync_timestamp_column(table)
        
        # Run crossdb migration with incremental loading
        print("\nStep 1: Cross-database migration (incremental)")
        for table in tables:
            fetch_insert_crossdb_with_dblink(table, is_incremental=True)
        
        # Run pre-transform in specific order
        print("\nStep 2: Pre-transform (in specified order)")
        process_pre_transform_tables()
        
        # Run transform
        print("\nStep 3: Transform and load")
        for table in tables:
            fetch_transform_insert_interdb(table)
    
    print("\nDaily sync pipeline completed successfully!") 




def test_kwargs(**kwargs):
    print("kwargs params\n")
    print(kwargs['params'])
