from airflow import DAG
from airflow.operators.python import PythonOperator
from migration_utils.migration_ops import  fetch_transform_insert_cross_gwl_uat,fetch_transform_insert_cross_gwl_prod
import os

def temp_sql_run_to_table():
    """
    This function is a placeholder for the actual SQL run logic.
    It should be replaced with the actual implementation.
    """
    # Example SQL run logic
    # Replace this with your actual SQL execution code
    fetch_transform_insert_cross_gwl_prod('partner_service."SalesTransactionItem"', prod_service_conn_name='partner_service_conn')
    print("Data for migrated successfully.")
    # fetch_transform_insert_cross_gwl_prod('partner_service."SalesTransactionWalletActivity"', prod_service_conn_name='partner_service_conn')
    # print("Data for migrated successfully.")
    # fetch_transform_insert_cross_gwl_prod('partner_service."SalesTransactionBurnPayment"', prod_service_conn_name='partner_service_conn')
    # print("Data for migrated successfully.")
    # fetch_transform_insert_cross_gwl_prod('point_service."WalletActivity"', prod_service_conn_name='point_service_conn')
    # print("Data for migrated successfully.")

# 13tables_to_migrate = OrderedDict([
# 14    ('loyalty_service."Member"', 'loyalty_service_conn'),
# 15    # ('engagement_service."MemberPrivilege"', 'engagement_service_conn'),
# 16    ('engagement_service."MemberCoupon"', 'engagement_service_conn'),
# 17    ('engagement_service."MemberCouponActivity"', 'engagement_service_conn'),
# 18    ('loyalty_service."SalesTransaction"', 'loyalty_service_conn'),
# 19    ('loyalty_service."MemberProfile"', 'loyalty_service_conn'),
# 20    ('loyalty_service."StaffProfile"', 'loyalty_service_conn'),
# 21    ('loyalty_service."MemberCoBrandCard"', 'loyalty_service_conn'),
# 22    ('loyalty_service."MemberLegacyTierHistory"', 'loyalty_service_conn'),
# 23    ('loyalty_service."MemberLegacyCoBrandHistory"', 'loyalty_service_conn'),
# 24    ('partner_service."SalesTransaction"', 'partner_service_conn'),
# 25    ('point_service."WalletAdjustmentTransaction"', 'point_service_conn'),
# 26    ('point_service."WalletBalance"', 'point_service_conn'),
# 27    ('partner_service."RefundSalesTransaction"', 'partner_service_conn'),
# 28    ('partner_service."SalesTransactionItem"', 'partner_service_conn'),
# 29    ('partner_service."SalesTransactionBurnPayment"', 'partner_service_conn'),
# 30    ('partner_service."SalesTransactionPayment"', 'partner_service_conn'),
# 31    ('point_service."WalletActivity"', 'point_service_conn'),
# 32    ('loyalty_service."RefundSalesTransaction"', 'loyalty_service_conn'),
# 33    ('partner_service."RefundSalesTransactionItem"', 'partner_service_conn'),
# 34    ('partner_service."SalesTransactionWalletActivity"', 'partner_service_conn'),
# 35    ('point_service."WalletTransaction"', 'point_service_conn'),
# 36])

file_name = os.path.basename(__file__).split('.')[0]

with DAG(
    dag_id=file_name,
    start_date=None,
    schedule_interval=None,
    catchup=False,
    tags=["gwl_uat_cross","sql_runUAT"],
) as dag:
    

    temp_sql_run_prod = PythonOperator(
        task_id="run_specific_sql_to_table_cross_gwl_uat",
        python_callable=temp_sql_run_to_table,
    )

    temp_sql_run_prod