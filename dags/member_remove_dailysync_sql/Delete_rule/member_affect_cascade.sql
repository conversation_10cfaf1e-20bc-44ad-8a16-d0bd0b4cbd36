-- this script is for set delete rule to be cascade on foreign key
-- sql code to drop and recreate foreign key constraints in the database.


-- "memberId" in loyalty_service."Member" affect on:
    
--Likely Tables with Foreign Keys to loyalty_service."Member".id:

-- loyalty_service."Member".id affect on:
    -- loyalty_service tables:
-- loyalty_service."RefundSalesTransaction"
-- loyalty_service."MemberLegacyTierHistory"
-- loyalty_service."SalesTransaction"
-- loyalty_service."MemberLegacyCoBrandHistory"
-- loyalty_service."MemberLog"
-- loyalty_service."MemberSubSegment"
-- loyalty_service."MemberTierHistory"
-- loyalty_service."MemberCoBrandCard"
    -- loyalty_service."MemberCoBrandCardLog"
-- loyalty_service."StaffProfile"
-- loyalty_service."MemberProfile"

--loyalty_service."RefundSalesTransaction" affect on 1 table 2 foreign keys here:
ALTER TABLE loyalty_service."RefundSalesTransaction"
DROP CONSTRAINT IF EXISTS "RefundSalesTransaction_memberId_fkey"; -- Replace with the actual constraint name if different
ALTER TABLE loyalty_service."RefundSalesTransaction"
ADD CONSTRAINT "RefundSalesTransaction_memberId_fkey"
FOREIGN KEY ("memberId")
REFERENCES loyalty_service."Member"(id)
ON DELETE CASCADE ON UPDATE CASCADE;
-- 2s
-- effect from sales transaction is below for more



--loyalty_service."MemberLegacyTierHistory" affect on 1 tables 1 foreign key here:
ALTER TABLE loyalty_service."MemberLegacyTierHistory"
DROP CONSTRAINT IF EXISTS "MemberLegacyTierHistory_memberId_fkey"; -- Replace with the actual constraint name if different
ALTER TABLE loyalty_service."MemberLegacyTierHistory"
ADD CONSTRAINT "MemberLegacyTierHistory_memberId_fkey"
FOREIGN KEY ("memberId")
REFERENCES loyalty_service."Member"(id)
ON DELETE CASCADE ON UPDATE CASCADE;
-- 4s

--loyalty_service."SalesTransaction" affect on 1 tables 1 foreign key here:
ALTER TABLE loyalty_service."SalesTransaction"
DROP CONSTRAINT IF EXISTS "SalesTransaction_memberId_fkey"; -- Replace with the actual constraint name if different
ALTER TABLE loyalty_service."SalesTransaction"
ADD CONSTRAINT "SalesTransaction_memberId_fkey"
FOREIGN KEY ("memberId")
REFERENCES loyalty_service."Member"(id)
ON DELETE CASCADE ON UPDATE CASCADE;
-- 15s

--loyalty_service."MemberLegacyCoBrandHistory" affect on 1 tables 1 foreign key here:
ALTER TABLE loyalty_service."MemberLegacyCoBrandHistory"
DROP CONSTRAINT IF EXISTS "MemberLegacyCobrandHistory_memberId_fkey"; -- Replace with the actual constraint name if different
ALTER TABLE loyalty_service."MemberLegacyCoBrandHistory"
ADD CONSTRAINT "MemberLegacyCobrandHistory_memberId_fkey"
FOREIGN KEY ("memberId")
REFERENCES loyalty_service."Member"(id)
ON DELETE CASCADE ON UPDATE CASCADE;
-- Execute time	1m 22s

--loyalty_service."MemberLog" affect on 1 tables 1 foreign key here:
ALTER TABLE loyalty_service."MemberLog"
DROP CONSTRAINT IF EXISTS "MemberLog_Member_fkey"; -- Replace with the actual constraint name if different
ALTER TABLE loyalty_service."MemberLog"
ADD CONSTRAINT "MemberLog_Member_fkey"
FOREIGN KEY ("memberId")
REFERENCES loyalty_service."Member"(id)
ON DELETE CASCADE ON UPDATE CASCADE;
-- 1s

--loyalty_service."MemberSubSegment" affect on 1 tables 1 foreign key here:
-- another foreign key is master table loyalty_service."SubSegment" do not change it!
ALTER TABLE loyalty_service."MemberSubSegment"
DROP CONSTRAINT IF EXISTS "membersubsegment_member_fk"; -- Replace with the actual constraint name if different
ALTER TABLE loyalty_service."MemberSubSegment"
ADD CONSTRAINT "membersubsegment_member_fk"
FOREIGN KEY ("memberId")
REFERENCES loyalty_service."Member"(id)
ON DELETE CASCADE ON UPDATE CASCADE;
-- 0.5s


-- loyalty_service."MemberTierHistory" affect on 1 tables 1 foreign key here:
ALTER TABLE loyalty_service."MemberTierHistory" DROP CONSTRAINT IF EXISTS "MemberTierHistory_Member_fkey";
ALTER TABLE loyalty_service."MemberTierHistory"
ADD CONSTRAINT "MemberTierHistory_Member_fkey"
FOREIGN KEY ("memberId")
REFERENCES loyalty_service."Member"(id)
ON DELETE CASCADE ON UPDATE CASCADE;
-- ?? can not drop with sql code 2s


-- loyalty_service."MemberCoBrandCard" affect on 2 tables 
    -- 1 foreign key here:
ALTER TABLE loyalty_service."MemberCoBrandCard"
DROP CONSTRAINT IF EXISTS "MemberCoBrandCard_memberId_fkey"; -- Replace with the actual constraint name if different
ALTER TABLE loyalty_service."MemberCoBrandCard"
ADD CONSTRAINT "MemberCoBrandCard_memberId_fkey"
FOREIGN KEY ("memberId")
REFERENCES loyalty_service."Member"(id)
ON DELETE CASCADE ON UPDATE CASCADE;
-- -- ?? can not drop with sql code add new 2s

    -- 1 foreign key here:
    -- loyalty_service."MemberCoBrandCardLog"
ALTER TABLE loyalty_service."MemberCoBrandCardLog" DROP CONSTRAINT IF EXISTS "MemberCoBrandCardLog_memberCoBrandCard_fkey";
ALTER TABLE loyalty_service."MemberCoBrandCardLog" ADD CONSTRAINT "MemberCoBrandCardLog_memberCoBrandCard_fkey" FOREIGN KEY ("memberCoBrandCardId") REFERENCES loyalty_service."MemberCoBrandCard"(id) ON DELETE CASCADE ON UPDATE CASCADE;

-- loyalty_service."StaffProfile" affect on 1 tables 1 foreign key here:
ALTER TABLE loyalty_service."StaffProfile"
DROP CONSTRAINT IF EXISTS "StaffProfile_memberId_fkey"; -- Replace with the actual constraint name if different
ALTER TABLE loyalty_service."StaffProfile"
ADD CONSTRAINT "StaffProfile_memberId_fkey"
FOREIGN KEY ("memberId")
REFERENCES loyalty_service."Member"(id)
ON DELETE CASCADE ON UPDATE CASCADE;
-- 2s


-- loyalty_service."MemberProfile" affect on 1 tables 1 foreign key here:
ALTER TABLE loyalty_service."MemberProfile"
DROP CONSTRAINT IF EXISTS "MemberProfile_memberId_fkey"; -- Replace with the actual constraint name if different
ALTER TABLE loyalty_service."MemberProfile"
ADD CONSTRAINT "MemberProfile_memberId_fkey"
FOREIGN KEY ("memberId")
REFERENCES loyalty_service."Member"(id)
ON DELETE CASCADE ON UPDATE CASCADE;
-- 8s

-- =================================================================================

    -- engagement_service tables:
        -- engagement_service."MemberCouponActivity"
        -- engagement_service."MemberCoupon"
            -- engagement_service."CouponClaimTransaction"
        -- engagement_service."MemberPrivilege"
    
-- engagement_service."MemberCouponActivity" affect on 1 tables 1 foreign key here:
ALTER TABLE engagement_service."MemberCouponActivity"
DROP CONSTRAINT IF EXISTS "MemberCouponActivity_memberId_fkey"; -- Replace with the actual constraint name if different
ALTER TABLE engagement_service."MemberCouponActivity"
ADD CONSTRAINT "MemberCouponActivity_memberId_fkey"
FOREIGN KEY ("memberId")
REFERENCES loyalty_service."Member"(id)
ON DELETE CASCADE ON UPDATE CASCADE;
-- 1s


-- engagement_service."MemberCoupon" affect on 2 tables 
    --1 foreign key here:
ALTER TABLE engagement_service."MemberCoupon"
DROP CONSTRAINT IF EXISTS "MemberCoupon_memberId_fkey"; -- Replace with the actual constraint name if different
ALTER TABLE engagement_service."MemberCoupon"
ADD CONSTRAINT "MemberCoupon_memberId_fkey"
FOREIGN KEY ("memberId")
REFERENCES loyalty_service."Member"(id)
ON DELETE CASCADE ON UPDATE CASCADE;
-- 1s

    -- another on 1 tables 1 foreign key here:
    -- engagement_service."CouponClaimTransaction" affect on 1 tables 1 foreign key here:
ALTER TABLE engagement_service."CouponClaimTransaction"
DROP CONSTRAINT IF EXISTS "CouponClaimTransaction_memberCouponId_fkey"; -- Replace with the actual constraint name if different
ALTER TABLE engagement_service."CouponClaimTransaction"
ADD CONSTRAINT "CouponClaimTransaction_memberCouponId_fkey"
FOREIGN KEY ("memberCouponId")
REFERENCES engagement_service."MemberCoupon"(id)
ON DELETE CASCADE ON UPDATE CASCADE;
-- 0.6s



-- engagement_service."MemberPrivilege"
ALTER TABLE engagement_service."MemberPrivilege"
DROP CONSTRAINT IF EXISTS "MemberPrivilege_memberId_fkey"; -- Replace with the actual constraint name if different
ALTER TABLE engagement_service."MemberPrivilege"
ADD CONSTRAINT "MemberPrivilege_memberId_fkey"
FOREIGN KEY ("memberId")
REFERENCES loyalty_service."Member"(id)
ON DELETE CASCADE ON UPDATE CASCADE;
-- 2s



-- =================================================================================

-- "memberId" in loyalty_service."Member" affect on:
    --  Tables with Foreign Keys to point_service:

-- point_service."WalletBalance"
    -- point_service."WalletTransaction"
-- point_service."WalletActivity"
    -- point_service."WalletTransaction"
    -- point_service."VoidWalletActivity"


-- point_service."WalletBalance"
-- have to delete point_service."WalletBalance".id with sql file base on loyalty_service."Member".id = point_service."WalletBalance"."memberId"

    -- point_service."WalletTransaction"
ALTER TABLE point_service."WalletTransaction" DROP CONSTRAINT IF EXISTS "WalletTransaction_WalletBalance_fkey";
ALTER TABLE point_service."WalletTransaction" ADD CONSTRAINT "WalletTransaction_WalletBalance_fkey" FOREIGN KEY ("balanceId") REFERENCES point_service."WalletBalance"(id) ON DELETE CASCADE ON UPDATE CASCADE;
-- 10s




-- point_service."WalletActivity"
-- have to delete point_service."WalletActivity".id with sql file base on loyalty_service."Member".id = point_service."WalletActivity"."memberId"
    -- point_service."WalletTransaction"
ALTER TABLE point_service."WalletTransaction" DROP CONSTRAINT IF EXISTS "WalletTransaction_WalletActivity_fkey";
ALTER TABLE point_service."WalletTransaction" ADD CONSTRAINT "WalletTransaction_WalletActivity_fkey" FOREIGN KEY ("walletActivityId") REFERENCES point_service."WalletActivity"(id) ON DELETE CASCADE ON UPDATE CASCADE;
-- 40s

    -- point_service."VoidWalletActivity"
ALTER TABLE point_service."VoidWalletActivity" DROP CONSTRAINT IF EXISTS "VoidWalletActivity_WalletActivity_fkey1";
ALTER TABLE point_service."VoidWalletActivity" ADD CONSTRAINT "VoidWalletActivity_WalletActivity_fkey1" FOREIGN KEY ("VoidWalletActivityId") REFERENCES point_service."WalletActivity"(id) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE point_service."VoidWalletActivity" DROP CONSTRAINT IF EXISTS "VoidWalletActivity_WalletActivity_fkey2";
ALTER TABLE point_service."VoidWalletActivity" ADD CONSTRAINT "VoidWalletActivity_WalletActivity_fkey2" FOREIGN KEY ("parentWalletActivityId") REFERENCES point_service."WalletActivity"(id) ON DELETE CASCADE ON UPDATE CASCADE;
-- 2s












-- =================================================================================

-- partner_service."SalesTransaction".id affect on:             -- loyalty_service."Member".id = partner_service."SalesTransaction"."memberId"
    -- partner_service."SalesTransactionItem":
    -- partner_service."SalesTransactionCoupon":
    -- partner_service."SalesTransactionPayment":
    -- partner_service."SalesTransactionWalletActivity":
    -- partner_service."SalesTransactionBurnPayment":
    -- partner_service."RefundSalesTransaction":
        -- partner_service."RefundSalesTransactionItem
        -- partner_service."RefundSalesTransactionWalletActivity"
        -- partner_service."SalesTransactionCouponAdjustment"





--partner_service."RefundSalesTransaction" affect on 3 tables foreign key here:
    -- partner_service."RefundSalesTransactionItem" 2 foreign keys
ALTER TABLE partner_service."RefundSalesTransactionItem"
DROP CONSTRAINT IF EXISTS "RefundSalesTransactionItem_refundSalesTransactionId_fkey"; -- Replace with the actual constraint name if different
ALTER TABLE partner_service."RefundSalesTransactionItem"
ADD CONSTRAINT "RefundSalesTransactionItem_refundSalesTransactionId_fkey"
FOREIGN KEY ("refundSalesTransactionId")
REFERENCES partner_service."RefundSalesTransaction"(id)
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE partner_service."RefundSalesTransactionItem" DROP CONSTRAINT "RefundSalesTransactionItem_salesTransactionItemId_fkey";
ALTER TABLE partner_service."RefundSalesTransactionItem" ADD CONSTRAINT "RefundSalesTransactionItem_salesTransactionItemId_fkey" FOREIGN KEY ("salesTransactionItemId") REFERENCES partner_service."SalesTransactionItem"(id) ON DELETE CASCADE ON UPDATE CASCADE;

    -- partner_service."RefundSalesTransactionWalletActivity" affect on 1 table foreign key here:
ALTER TABLE partner_service."RefundSalesTransactionWalletActivity"
DROP CONSTRAINT IF EXISTS "RefundSalesTransactionWalletActivity_RefundSalesTransaction_fke"; -- Replace with the actual constraint name if different
ALTER TABLE partner_service."RefundSalesTransactionWalletActivity"
ADD CONSTRAINT "RefundSalesTransactionWalletActivity_RefundSalesTransaction_fke"
FOREIGN KEY ("refundSalesTransactionId")
REFERENCES partner_service."RefundSalesTransaction"(id)
ON DELETE CASCADE ON UPDATE CASCADE;

    -- partner_service."SalesTransactionCouponAdjustment" affect on 1 table foreign key here:
ALTER TABLE partner_service."SalesTransactionCouponAdjustment"
DROP CONSTRAINT IF EXISTS "SalesTransactionCouponAdjustment_RefundSalesTransaction_fkey"; -- Replace with the actual constraint name if different
ALTER TABLE partner_service."SalesTransactionCouponAdjustment"
ADD CONSTRAINT "SalesTransactionCouponAdjustment_RefundSalesTransaction_fkey"
FOREIGN KEY ("refundSalesTransactionId")
REFERENCES partner_service."RefundSalesTransaction"(id)
ON DELETE CASCADE ON UPDATE CASCADE;



--partner_service."SalesTransactionItem" affect on 1 table foreign key here:
ALTER TABLE partner_service."SalesTransactionItem"
DROP CONSTRAINT IF EXISTS "salestransactionitem_salestransaction_fk"; -- Replace with the actual constraint name if different
ALTER TABLE partner_service."SalesTransactionItem"
ADD CONSTRAINT "salestransactionitem_salestransaction_fk"
FOREIGN KEY ("salesTransactionId")
REFERENCES partner_service."SalesTransaction"(id)
ON DELETE CASCADE ON UPDATE CASCADE;
-- 1m 56s

-- partner_service."SalesTransactionCoupon" affect on 1 table foreign key here:
ALTER TABLE partner_service."SalesTransactionCoupon"
DROP CONSTRAINT IF EXISTS "salestransactioncoupon_salestransaction_fk"; -- Replace with the actual constraint name if different
ALTER TABLE partner_service."SalesTransactionCoupon"
ADD CONSTRAINT "salestransactioncoupon_salestransaction_fk"
FOREIGN KEY ("salesTransactionId")
REFERENCES partner_service."SalesTransaction"(id)
ON DELETE CASCADE ON UPDATE CASCADE;

-- partner_service."SalesTransactionPayment" affect on 1 table foreign key here:
-- another foreign key is master table partner_service."paymentMethod" do not change it!
ALTER TABLE partner_service."SalesTransactionPayment"
DROP CONSTRAINT IF EXISTS "salestransactionpayment_salestransaction_fk"; -- Replace with the actual constraint name if different
ALTER TABLE partner_service."SalesTransactionPayment"
ADD CONSTRAINT "salestransactionpayment_salestransaction_fk"
FOREIGN KEY ("salesTransactionId")
REFERENCES partner_service."SalesTransaction"(id)
ON DELETE CASCADE ON UPDATE CASCADE;
-- 56s

-- partner_service."SalesTransactionWalletActivity" affect on 1 table foreign key here:
ALTER TABLE partner_service."SalesTransactionWalletActivity"
DROP CONSTRAINT IF EXISTS "salestransactionwalletactivity_salestransaction_fk"; -- Replace with the actual constraint name if different
ALTER TABLE partner_service."SalesTransactionWalletActivity"
ADD CONSTRAINT "salestransactionwalletactivity_salestransaction_fk"
FOREIGN KEY ("salesTransactionId")
REFERENCES partner_service."SalesTransaction"(id)
ON DELETE CASCADE ON UPDATE CASCADE;
-- 35s 

-- partner_service."SalesTransactionBurnPayment" affect on 1 table foreign key here:
ALTER TABLE partner_service."SalesTransactionBurnPayment"
DROP CONSTRAINT IF EXISTS "salestransactionburnpayment_salestransaction_fk"; -- Replace with the actual constraint name if different
ALTER TABLE partner_service."SalesTransactionBurnPayment"
ADD CONSTRAINT "salestransactionburnpayment_salestransaction_fk"
FOREIGN KEY ("salesTransactionId")
REFERENCES partner_service."SalesTransaction"(id)
ON DELETE CASCADE ON UPDATE CASCADE;
-- 16s

-- =================================================================================

