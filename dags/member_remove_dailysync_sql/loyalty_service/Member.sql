WITH fethch_member_delete_list AS 

(SELECT 
    *
FROM dblink(
            'my_connection', 
            'SELECT   
                id,
                "gwlNo",     
                "createdAt",
                "updatedAt",
                "deletedAt"
                
            FROM loyalty_service."Member"'
            )  AS t1(
                    id TEXT,
                    "gwlNo" TEXT,	
                    "createdAt" TIMESTAMP(3),
                    "updatedAt" TIMESTAMP(3),
                    "deletedAt" TIMESTAMP(3)
                    )
WHERE (
        ((t1."createdAt" BETWEEN 'start_timestamps' AND 'end_timestamps') OR (t1."updatedAt" BETWEEN 'start_timestamps' AND 'end_timestamps'))
        AND t1."deletedAt" IS NOT NULL)
)

,member_delete_list AS
(
SELECT 
    stg_m.ulid_id as member_uild,
	fmdl."gwlNo" "memberId",
	fmdl."createdAt",
	fmdl."updatedAt",
	fmdl."deletedAt"
FROM fethch_member_delete_list AS fmdl
LEFT JOIN staging_loyalty_service.ulid_member AS stg_m ON fmdl.id = stg_m.id
LEFT JOIN public.members_blacklist AS mbl ON fmdl.id = mbl."memberId"
WHERE mbl."memberId" IS NULL
)

DELETE FROM loyalty_service."Member"
WHERE id IN (SELECT member_uild FROM member_delete_list)


ON CONFLICT -- for prevent error
