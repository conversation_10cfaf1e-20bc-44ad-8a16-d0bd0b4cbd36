from datetime import datetime

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Postgres<PERSON>andler
from common_helpers.logging import get_logger
from common_helpers.utils import create_migration_result_table
from partner_service.refund_sales_transaction import RefundSalesTransaction
from partner_service.sales_transaction import SalesTransaction
from partner_service.sales_transaction_item import SalesTransactionItem
from partner_service.sales_transaction_burn_payment import (
    SalesTransactionBurnPayment,
)
from partner_service.sales_transaction_payment import SalesTransactionPayment
from partner_service.product_category import ProductCategory
from partner_service.product_brand import ProductBrand

from airflow import DAG
from airflow.models import Variable
from airflow.operators.python_operator import PythonOperator

logger = get_logger()


class PartnerService:
    def __init__(self):
        self.service_name = "partner_service"
        self.passphrase = Variable.get("SECRET_PII_ENCRYPTION")
        self.batch_size = int(
            Variable.get(f"{self.service_name}.batch_size", default_var=10000),
        )
        self.executor_max_workers = int(
            Variable.get(f"{self.service_name}.executor_max_workers", default_var=5),
        )
        self.incremental_query_date = Variable.get(
            f"{self.service_name}.incremental_query_date", default_var=None
        )
        self.loyalty_value_handler = MSSQLHandler(
            conn_id="loyalty_value_smc_db_connection_id"
        )
        self.newmember_handler = MSSQLHandler(conn_id="newmember_smc_db_connection_id")
        self.postgresql_handler = PostgresHandler(conn_id="temp_db_connection_id")

    def prepare_brand_table_for_migration(
        self,
    ) -> None:
        """
        Insert a row into Brand, with id of PARTNER_SERVICE_MIGRATION,
        which is needed for ProductBrand and ProductCategory migrations.

        Args:
            None

        Returns:
            None
        """
        insert_query = """
            INSERT INTO "partner_service"."Brand" (
                "id",
                "code",
                "name",
                "status",
                "createdAt",
                "createdBy",
                "updatedAt",
                "updatedBy"
            )
            VALUES (
                'PARTNER_SERVICE_MIGRATION',
                'PARTNER_SERVICE_MIGRATION',
                '{}',
                'PARTNER_SERVICE_MIGRATION',
                NOW () AT TIME ZONE 'UTC',
                '{}',
                NOW () AT TIME ZONE 'UTC',
                '{}'
            )
            ON CONFLICT ("id") DO NOTHING;
        """

        try:
            postgresql_connection = self.postgresql_handler.hook.get_conn()

            logger.info(f"started preparing Brand table for migration...")
            self.postgresql_handler.execute_with_rollback(
                connection=postgresql_connection, query_string=insert_query
            )
            logger.info(f"finished preparing Brand table for migration.")
        except Exception as error:
            logger.error(f"an error has occured: {error}")

            if postgresql_connection:
                logger.info("a postgresql connection is found, rolling back...")
                postgresql_connection.rollback()
                logger.info("successfully rolled back.")

            raise error
        finally:
            if postgresql_connection:
                postgresql_connection.close()

    def prepare_batch_tracker_table(self):
        """
        Prepare a table to track the migration process.

        Args:
            None

        Returns:
            None
        """
        create_table_query_string = """
            CREATE TABLE IF NOT EXISTS partner_service.batch_tracker (
                table_name VARCHAR(100) PRIMARY KEY,
                total_records INT NOT NULL,
                completed_batches JSONB NOT NULL DEFAULT '[]'::jsonb,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """
        postgresql_connection = self.postgresql_handler.hook.get_conn()

        try:
            logger.info(f"started preparing batch tracker table for migration...")
            self.postgresql_handler.execute_with_rollback(
                connection=postgresql_connection,
                query_string=create_table_query_string,
            )
            logger.info(f"finished preparing batch tracker table for migration.")
        finally:
            postgresql_connection.close()

    def get_incremental_query_condition(self, date_field: str) -> str:
        """
        Generates a query condition for incremental migration, with specific date supported.

        Args:
            None

        Returns:
            str: A query condition string.
        """
        if self.incremental_query_date is None:
            return f"{date_field} >= DATEADD(DAY, -1, CAST(CAST(GETDATE() AS DATE) AS DATETIME)) AND {date_field} < CAST(CAST(GETDATE() AS DATE) AS DATETIME)"

        return f"{date_field} >= CAST('{self.incremental_query_date}' AS DATETIME) AND {date_field} < DATEADD(DAY, 1, CAST('{self.incremental_query_date}' AS DATETIME))"

    def prepare_generate_sales_transaction_id_function(self) -> None:
        generate_sales_transaction_id_function_query = """
            IF NOT EXISTS (
                SELECT
                    1
                FROM
                    sys.objects
                WHERE
                    object_id = OBJECT_ID (N'dbo.fn_generate_sales_transaction_id')
                    AND type IN (N'FN', N'IF', N'TF', N'FS', N'FT')
            ) BEGIN EXEC (
                '
                CREATE FUNCTION dbo.fn_generate_sales_transaction_id (@key NVARCHAR(255))
                RETURNS BIGINT
                AS
                BEGIN
                    DECLARE @result BIGINT
                    DECLARE @temp NVARCHAR(255)
                    DECLARE @pipe_count INT = LEN(@key) - LEN(REPLACE(@key, ''|'', ''''))

                    IF @pipe_count = 4
                    BEGIN
                        SET @temp = 
                            LEFT(
                                SUBSTRING(@key, 1, 3) + SUBSTRING(@key, 6, LEN(@key)),
                                CHARINDEX(''|'', @key, 
                                    CHARINDEX(''|'', @key, 
                                        CHARINDEX(''|'', @key, -2) + 1
                                    ) + 1
                                ) - 2
                            ) +
                            RIGHT(
                                @key,
                                LEN(@key) + 4 - CHARINDEX(''|'', @key, 
                                    CHARINDEX(''|'', @key, 
                                        CHARINDEX(''|'', @key, 
                                            CHARINDEX(''|'', @key, 0) + 1
                                        ) + 1
                                    ) + 1
                                )
                            )
                    END
                    ELSE IF @pipe_count = 3
                    BEGIN
                        SET @temp = REPLACE(@key, ''P'', ''1'')
                    END
                    ELSE IF @key LIKE ''50|3600001191416|%'' 
                    BEGIN
                        SET @temp = REPLACE(@key, ''360000'', '''')
                    END
                    ELSE
                    BEGIN
                        SET @temp = @key
                    END

                    SET @temp = REPLACE(@temp, ''|'', '''')
                    SET @temp = REPLACE(@temp, ''/'', '''')
                    SET @temp = REPLACE(@temp, ''-R'', ''1'')
                    SET @temp = REPLACE(@temp, ''A'', '''')
                    SET @temp = REPLACE(@temp, ''C'', '''')
                    SET @temp = REPLACE(@temp, ''D'', '''')
                    SET @temp = REPLACE(@temp, ''E'', '''')
                    SET @temp = REPLACE(@temp, ''M'', '''')
                    SET @temp = REPLACE(@temp, ''B'', '''')
                    SET @temp = REPLACE(@temp, ''I'', '''')
                    SET @temp = REPLACE(@temp, ''K'', '''')
                    SET @temp = REPLACE(@temp, ''S'', '''')
                    SET @temp = REPLACE(@temp, ''P'', '''')
                    SET @temp = REPLACE(@temp, ''R'', '''')
                    SET @temp = REPLACE(@temp, ''U'', '''')
                    SET @temp = REPLACE(@temp, ''H'', '''')
                    SET @temp = REPLACE(@temp, ''G'', '''')
                    SET @temp = REPLACE(@temp, ''Z'', '''')
                    SET @temp = REPLACE(@temp, ''N'', '''')
                    SET @temp = REPLACE(@temp, ''T'', '''')
                    SET @temp = REPLACE(@temp, ''X'', '''')
                    SET @temp = REPLACE(@temp, ''L'', '''')
                    SET @temp = REPLACE(@temp, ''J'', '''')
                    SET @temp = REPLACE(@temp, ''F'', '''')
                    SET @temp = REPLACE(@temp, ''V'', '''')
                    SET @temp = REPLACE(@temp, ''Q'', '''')
                    SET @temp = REPLACE(@temp, ''W'', '''')
                    SET @temp = REPLACE(@temp, ''O'', '''')
                    SET @temp = REPLACE(@temp, ''_'', '''')

                    SET @result = TRY_CAST(@temp AS BIGINT)

                    RETURN ISNULL(@result, -1) * -1
                END
                '
            ) END
        """

        loyalty_value_connection = self.loyalty_value_handler.hook.get_conn()

        try:
            logger.info(
                f"started preparing a function for salesTransactionId generation..."
            )
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=generate_sales_transaction_id_function_query,
            )
            logger.info(
                f"finished preparing a function for salesTransactionId generation."
            )
        finally:
            loyalty_value_connection.close()

    def prepare_temp_tables_for_sales_transaction(self, is_full_dump: bool = True):
        """
        Prepare indexed temporary tables for SalesTransaction migration.

        Args:
            is_full_dump (bool): The migration type.

        Returns:
            None
        """
        create_smc_sales_header_temp_table = (
            """
                IF OBJECT_ID('Newmember.dbo.temp_smc_sales_header_for_sales_transaction_full_dump', 'U') IS NULL
                BEGIN
                    SELECT
                        member_id,
                        key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS key_search,
                        BranchNo,
                        Site,
                        DataDate,
                        ShoppingCard
                    INTO #temp_filtered_smc_sales_header_for_sales_transaction_full_dump
                    FROM Newmember.dbo.SMCSalesHeader
                    WHERE SaleStatus != 'R' AND DataDate < CAST(CAST(GETDATE() AS DATE) AS DATETIME);

                    SELECT member_id
                    INTO #temp_active_member_ids_for_sales_transaction_payment_full_dump
                    FROM Newmember.dbo.df_member
                    WHERE del_flag = '';

                    SELECT
                        TRIM(ssh.member_id) AS memberId,
                        TRIM(ssh.member_id) AS gwlNo,
                        ssh.key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS externalId,
                        ssh.BranchNo AS partnerId,
                        ssh.BranchNo AS brandId,
                        ssh.Site AS branchId,
                        'EARNED' AS status,
                        ssh.DataDate,
                        ssh.ShoppingCard
                    INTO Newmember.dbo.temp_smc_sales_header_for_sales_transaction_full_dump
                    FROM #temp_filtered_smc_sales_header_for_sales_transaction_full_dump ssh
                    JOIN #temp_active_member_ids_for_sales_transaction_payment_full_dump dm
                    ON dm.member_id = ssh.member_id;

                    DROP TABLE IF EXISTS #temp_filtered_smc_sales_header_for_sales_transaction_full_dump;
                    DROP TABLE IF EXISTS #temp_active_member_ids_for_sales_transaction_payment_full_dump;

                    CREATE INDEX temp_smc_sales_header_for_sales_transaction_full_dump_externalId ON Newmember.dbo.temp_smc_sales_header_for_sales_transaction_full_dump (externalId);
                END;
            """
            if is_full_dump
            else f"""
                SELECT
                    member_id,
                    key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS key_search,
                    BranchNo,
                    Site,
                    DataDate,
                    ShoppingCard
                INTO #temp_filtered_smc_sales_header_for_sales_transaction_incremental
                FROM Newmember.dbo.SMCSalesHeader
                WHERE SaleStatus != 'R' AND {self.get_incremental_query_condition('DataDate')};

                SELECT member_id
                INTO #temp_active_member_ids_for_sales_transaction_payment_incremental
                FROM Newmember.dbo.df_member
                WHERE del_flag = '';

                DROP TABLE IF EXISTS Newmember.dbo.temp_smc_sales_header_for_sales_transaction_incremental;

                SELECT
                    TRIM(ssh.member_id) AS memberId,
                    TRIM(ssh.member_id) AS gwlNo,
                    ssh.key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS externalId,
                    ssh.BranchNo AS partnerId,
                    ssh.BranchNo AS brandId,
                    ssh.Site AS branchId,
                    'EARNED' AS status,
                    ssh.DataDate,
                    ssh.ShoppingCard
                INTO Newmember.dbo.temp_smc_sales_header_for_sales_transaction_incremental
                FROM #temp_filtered_smc_sales_header_for_sales_transaction_incremental ssh
                JOIN #temp_active_member_ids_for_sales_transaction_payment_incremental dm
                ON dm.member_id = ssh.member_id;

                DROP TABLE IF EXISTS #temp_filtered_smc_sales_header_for_sales_transaction_incremental;
                DROP TABLE IF EXISTS #temp_active_member_ids_for_sales_transaction_payment_incremental;

                CREATE INDEX temp_smc_sales_header_for_sales_transaction_incremental_externalId ON Newmember.dbo.temp_smc_sales_header_for_sales_transaction_incremental (externalId);
            """
        )
        create_smc_sales_trans_temp_table = (
            """
                IF OBJECT_ID('Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_full_dump', 'U') IS NULL
                BEGIN
                    SELECT
                        key_search,
                        Net,
                        Amount,
                        Discount,
                        UpdateCoupon,
                        MAX(CAST(UpdateCoupon AS INT)) OVER (PARTITION BY key_search, MatCode) AS max_coupon
                    INTO #temp_smc_base_full_dump
                    FROM Newmember.dbo.SMCSalesTrans
                    WHERE lineCancel = 0 AND CancelStatus = 0;

                    SELECT 
                        key_search,
                        Net,
                        Amount,
                        Discount,
                        CASE 
                            WHEN UpdateCoupon = 1 AND max_coupon = 1 THEN 1
                            WHEN max_coupon = 0 THEN 1
                            ELSE 0
                        END AS row_num
                    INTO #temp_filtered_smc_sales_trans_full_dump
                    FROM #temp_smc_base_full_dump;

                    SELECT
                        key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS key_search,
                        SUM(Net) AS netTotalAmount,
                        SUM(Amount) AS totalOriginalPrice,
                        SUM(Discount) AS totalDiscount
                    INTO Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_full_dump
                    FROM #temp_filtered_smc_sales_trans_full_dump
                    WHERE row_num = 1
                    GROUP BY key_search;

                    DROP TABLE IF EXISTS #temp_smc_base_full_dump;
                    DROP TABLE IF EXISTS #temp_filtered_smc_sales_trans_full_dump;
                    
                    CREATE INDEX temp_smc_sales_trans_for_sales_transaction_full_dump_key_search ON Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_full_dump (key_search);
                END;
            """
            if is_full_dump
            else """
                DROP TABLE IF EXISTS Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_incremental;

                SELECT
                    key_search,
                    Net,
                    Amount,
                    Discount,
                    UpdateCoupon,
                    MAX(CAST(UpdateCoupon AS INT)) OVER (PARTITION BY key_search, MatCode) AS max_coupon
                INTO #temp_smc_base_incremental
                FROM Newmember.dbo.SMCSalesTrans
                WHERE lineCancel = 0 AND CancelStatus = 0;

                SELECT 
                    key_search,
                    Net,
                    Amount,
                    Discount,
                    CASE 
                        WHEN UpdateCoupon = 1 AND max_coupon = 1 THEN 1
                        WHEN max_coupon = 0 THEN 1
                        ELSE 0
                    END AS row_num
                INTO #temp_filtered_smc_sales_trans_incremental
                FROM #temp_smc_base_incremental;

                SELECT
                    key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS key_search,
                    SUM(Net) AS netTotalAmount,
                    SUM(Amount) AS totalOriginalPrice,
                    SUM(Discount) AS totalDiscount
                INTO Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_incremental
                FROM #temp_filtered_smc_sales_trans_incremental
                WHERE row_num = 1
                GROUP BY key_search;

                DROP TABLE IF EXISTS #temp_smc_base_incremental;
                DROP TABLE IF EXISTS #temp_filtered_smc_sales_trans_incremental;

                CREATE INDEX temp_smc_sales_trans_for_sales_transaction_incremental_key_search ON Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_incremental (key_search);
            """
        )
        create_smc_sales_payment_temp_table = (
            """
                IF OBJECT_ID('Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_full_dump', 'U') IS NULL
                BEGIN
                    SELECT DISTINCT
                        CAST(t1.key_search AS CHAR(30)) COLLATE THAI_CI_AS AS KeySearch INTO #TmpT0
                    FROM
                        Newmember.dbo.SMCSalesHeader t1
                    WITH
                        (NOLOCK)
                        INNER JOIN Newmember.dbo.SMCSalesTrans t2
                    WITH
                        (NOLOCK) ON t1.key_search = t2.key_search
                        AND lineCancel = 0
                        AND CancelStatus = 0
                        INNER JOIN Newmember.dbo.SMCSalesPayment smcp
                    WITH
                        (NOLOCK) ON t1.key_search = smcp.key_search
                        INNER JOIN mast_NonAccCarat
                    WITH
                        (NOLOCK) ON smcp.MethodCode = MAST_NonAccCarat.code
                        AND MAST_NonAccCarat.type = 'P'
                        AND MAST_NonAccCarat.IsCancel = 0
                        INNER JOIN Newmember.dbo.df_member dm
                    WITH
                        (NOLOCK) ON CAST(t1.member_id AS CHAR(8)) = dm.member_id
                    WHERE
                        t1.saleStatus <> 'R';

                    CREATE NONCLUSTERED INDEX ix_tmpT0 ON #TmpT0 (KeySearch);

                    SELECT
                        CAST(L1.KeySearch AS CHAR(30)) COLLATE THAI_CI_AS AS KeySearch,
                        SUM(-1 * l2.amount) AS net INTO #TmpT1
                    FROM
                        LoyaltyValue.dbo.LVHeader L1
                    WITH
                        (NOLOCK)
                        INNER JOIN LoyaltyValue.dbo.LVTrans L2
                    WITH
                        (NOLOCK) ON L1.LVHeaderKey = L2.LVHeaderKey
                        INNER JOIN LoyaltyValue.dbo.LVdata L3
                    WITH
                        (NOLOCK) ON L2.LVMainKey = L3.LVMainKey
                        INNER JOIN Newmember.dbo.df_member dm
                    WITH
                        (NOLOCK) ON l2.LVNumber COLLATE THAI_CI_AS = dm.member_id COLLATE THAI_CI_AS
                        INNER JOIN #TmpT0 L4 ON CAST(L1.KeySearch AS CHAR(30)) COLLATE THAI_CI_AS = L4.KeySearch
                    WHERE
                        L3.valuecode IN (
                            'EP001',
                            'EP007',
                            'EP008',
                            'EP009',
                            'EP010',
                            'KPC01',
                            'KPO02'
                        )
                        AND l2.movementcode = 'USE'
                    GROUP BY
                        CAST(L1.KeySearch AS CHAR(30));

                    CREATE NONCLUSTERED INDEX ix_tmpT1 ON #TmpT1 (KeySearch);

                    SELECT
                        t1.BranchNo,
                        CAST(t1.key_search AS CHAR(30)) COLLATE THAI_CI_AS AS key_search,
                        t1.salesBranch,
                        CAST(t1.member_id AS CHAR(8)) AS member_id,
                        SUM(t2.qty) AS qty INTO #TmpT2
                    FROM
                        Newmember.dbo.SMCSalesHeader t1
                    WITH
                        (NOLOCK)
                        INNER JOIN Newmember.dbo.SMCSalesTrans t2
                    WITH
                        (NOLOCK) ON t1.key_search = t2.key_search
                        AND lineCancel = 0
                        AND CancelStatus = 0
                        INNER JOIN Newmember.dbo.df_member dm
                    WITH
                        (NOLOCK) ON CAST(t1.member_id AS CHAR(8)) = dm.member_id
                    WHERE
                        t1.saleStatus <> 'R'
                    GROUP BY
                        t1.BranchNo,
                        t1.key_search,
                        t1.salesBranch,
                        t1.member_id;

                    CREATE NONCLUSTERED INDEX ix_tmpT2 ON #TmpT2 (key_search);

                    SELECT
                        * INTO #TmpT3
                    FROM
                        #TmpT2 T2
                        INNER JOIN #TmpT1 T1 ON T2.key_search = T1.KeySearch;

                    CREATE NONCLUSTERED INDEX ix_tmpT3 ON #TmpT3 (key_search);

                    SELECT
                        x.member_id,
                        x.key_search,
                        SUM(t3.net) AS total INTO #TmpT4
                    FROM
                        (
                            SELECT
                                t1.key_search,
                                CAST(t1.member_id AS CHAR(8)) AS member_id
                            FROM
                                Newmember.dbo.SMCSalesHeader t1
                            WITH
                                (NOLOCK)
                                INNER JOIN Newmember.dbo.SMCSalesTrans t2
                            WITH
                                (NOLOCK) ON t1.key_search = t2.key_search
                                AND t2.lineCancel = 0
                                AND t2.CancelStatus = 0
                                INNER JOIN Newmember.dbo.df_member dm
                            WITH
                                (NOLOCK) ON CAST(t1.member_id AS CHAR(8)) = dm.member_id
                            WHERE
                                t1.saleStatus <> 'R'
                            GROUP BY
                                t1.BranchNo,
                                t1.key_search,
                                t1.salesBranch,
                                t1.member_id
                        ) x
                        INNER JOIN Newmember.dbo.SMCSalesPayment t3
                    WITH
                        (NOLOCK) ON x.key_search = t3.key_search
                        AND t3.MethodCode NOT IN (
                            SELECT
                                Code
                            FROM
                                mast_NonAccCarat
                            WHERE
                                type = 'P'
                                AND iscancel = 0
                        )
                    GROUP BY
                        x.key_search,
                        x.member_id;

                    CREATE NONCLUSTERED INDEX ix_tmpT4 ON #TmpT4 (key_search);

                    SELECT
                        z.key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS key_search,
                        SUM(z.totalEarnableAmount) AS totalEarnableAmount,
                        SUM(z.totalAccumSpendableAmount) AS totalAccumSpendableAmount
                    INTO Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_full_dump
                    FROM
                        (
                            SELECT
                                key_search,
                                net AS totalEarnableAmount,
                                net AS totalAccumSpendableAmount
                            FROM
                                #TmpT3
                            UNION
                            SELECT
                                key_search,
                                total AS totalEarnableAmount,
                                total AS totalAccumSpendableAmount
                            FROM
                                #TmpT4
                        ) z
                    GROUP BY z.key_search;

                    CREATE NONCLUSTERED INDEX ix_tmpT5 ON Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_full_dump (key_search);
                END;
            """
            if is_full_dump
            else """
                DROP TABLE IF EXISTS Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_incremental;

                SELECT DISTINCT
                    CAST(t1.key_search AS CHAR(30)) COLLATE THAI_CI_AS AS KeySearch INTO #TmpT0
                FROM
                    Newmember.dbo.SMCSalesHeader t1
                WITH
                    (NOLOCK)
                    INNER JOIN Newmember.dbo.SMCSalesTrans t2
                WITH
                    (NOLOCK) ON t1.key_search = t2.key_search
                    AND lineCancel = 0
                    AND CancelStatus = 0
                    INNER JOIN Newmember.dbo.SMCSalesPayment smcp
                WITH
                    (NOLOCK) ON t1.key_search = smcp.key_search
                    INNER JOIN mast_NonAccCarat
                WITH
                    (NOLOCK) ON smcp.MethodCode = MAST_NonAccCarat.code
                    AND MAST_NonAccCarat.type = 'P'
                    AND MAST_NonAccCarat.IsCancel = 0
                    INNER JOIN Newmember.dbo.df_member dm
                WITH
                    (NOLOCK) ON CAST(t1.member_id AS CHAR(8)) = dm.member_id
                WHERE
                    t1.saleStatus <> 'R';

                CREATE NONCLUSTERED INDEX ix_tmpT0 ON #TmpT0 (KeySearch);

                SELECT
                    CAST(L1.KeySearch AS CHAR(30)) COLLATE THAI_CI_AS AS KeySearch,
                    SUM(-1 * l2.amount) AS net INTO #TmpT1
                FROM
                    LoyaltyValue.dbo.LVHeader L1
                WITH
                    (NOLOCK)
                    INNER JOIN LoyaltyValue.dbo.LVTrans L2
                WITH
                    (NOLOCK) ON L1.LVHeaderKey = L2.LVHeaderKey
                    INNER JOIN LoyaltyValue.dbo.LVdata L3
                WITH
                    (NOLOCK) ON L2.LVMainKey = L3.LVMainKey
                    INNER JOIN Newmember.dbo.df_member dm
                WITH
                    (NOLOCK) ON l2.LVNumber COLLATE THAI_CI_AS = dm.member_id COLLATE THAI_CI_AS
                    INNER JOIN #TmpT0 L4 ON CAST(L1.KeySearch AS CHAR(30)) COLLATE THAI_CI_AS = L4.KeySearch
                WHERE
                    L3.valuecode IN (
                        'EP001',
                        'EP007',
                        'EP008',
                        'EP009',
                        'EP010',
                        'KPC01',
                        'KPO02'
                    )
                    AND l2.movementcode = 'USE'
                GROUP BY
                    CAST(L1.KeySearch AS CHAR(30));

                CREATE NONCLUSTERED INDEX ix_tmpT1 ON #TmpT1 (KeySearch);

                SELECT
                    t1.BranchNo,
                    CAST(t1.key_search AS CHAR(30)) COLLATE THAI_CI_AS AS key_search,
                    t1.salesBranch,
                    CAST(t1.member_id AS CHAR(8)) AS member_id,
                    SUM(t2.qty) AS qty INTO #TmpT2
                FROM
                    Newmember.dbo.SMCSalesHeader t1
                WITH
                    (NOLOCK)
                    INNER JOIN Newmember.dbo.SMCSalesTrans t2
                WITH
                    (NOLOCK) ON t1.key_search = t2.key_search
                    AND lineCancel = 0
                    AND CancelStatus = 0
                    INNER JOIN Newmember.dbo.df_member dm
                WITH
                    (NOLOCK) ON CAST(t1.member_id AS CHAR(8)) = dm.member_id
                WHERE
                    t1.saleStatus <> 'R'
                GROUP BY
                    t1.BranchNo,
                    t1.key_search,
                    t1.salesBranch,
                    t1.member_id;

                CREATE NONCLUSTERED INDEX ix_tmpT2 ON #TmpT2 (key_search);

                SELECT
                    * INTO #TmpT3
                FROM
                    #TmpT2 T2
                    INNER JOIN #TmpT1 T1 ON T2.key_search = T1.KeySearch;

                CREATE NONCLUSTERED INDEX ix_tmpT3 ON #TmpT3 (key_search);

                SELECT
                    x.member_id,
                    x.key_search,
                    SUM(t3.net) AS total INTO #TmpT4
                FROM
                    (
                        SELECT
                            t1.key_search,
                            CAST(t1.member_id AS CHAR(8)) AS member_id
                        FROM
                            Newmember.dbo.SMCSalesHeader t1
                        WITH
                            (NOLOCK)
                            INNER JOIN Newmember.dbo.SMCSalesTrans t2
                        WITH
                            (NOLOCK) ON t1.key_search = t2.key_search
                            AND t2.lineCancel = 0
                            AND t2.CancelStatus = 0
                            INNER JOIN Newmember.dbo.df_member dm
                        WITH
                            (NOLOCK) ON CAST(t1.member_id AS CHAR(8)) = dm.member_id
                        WHERE
                            t1.saleStatus <> 'R'
                        GROUP BY
                            t1.BranchNo,
                            t1.key_search,
                            t1.salesBranch,
                            t1.member_id
                    ) x
                    INNER JOIN Newmember.dbo.SMCSalesPayment t3
                WITH
                    (NOLOCK) ON x.key_search = t3.key_search
                    AND t3.MethodCode NOT IN (
                        SELECT
                            Code
                        FROM
                            mast_NonAccCarat
                        WHERE
                            type = 'P'
                            AND iscancel = 0
                    )
                GROUP BY
                    x.key_search,
                    x.member_id;

                CREATE NONCLUSTERED INDEX ix_tmpT4 ON #TmpT4 (key_search);

                SELECT
                    z.key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS key_search,
                    SUM(z.totalEarnableAmount) AS totalEarnableAmount,
                    SUM(z.totalAccumSpendableAmount) AS totalAccumSpendableAmount
                INTO Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_incremental
                FROM
                    (
                        SELECT
                            key_search,
                            net AS totalEarnableAmount,
                            net AS totalAccumSpendableAmount
                        FROM
                            #TmpT3
                        UNION
                        SELECT
                            key_search,
                            total AS totalEarnableAmount,
                            total AS totalAccumSpendableAmount
                        FROM
                            #TmpT4
                    ) z
                GROUP BY z.key_search;

                CREATE NONCLUSTERED INDEX ix_tmpT5 ON Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_incremental (key_search);
            """
        )
        create_lv_header_temp_table = (
            """
                IF OBJECT_ID('temp_lv_header_for_sales_transaction_full_dump', 'U') IS NULL
                BEGIN
                    WITH key_search_grouped_lv_header AS (
                        SELECT 
                            KeySearch,
                            LVHeaderKey,
                            AddDT,
                            FinishDT,
                            CancelHeaderKey,
                            DocDate,
                            LVCardNo,
                            ROW_NUMBER() OVER (
                                PARTITION BY KeySearch 
                                ORDER BY 
                                    CASE WHEN CancelHeaderKey IS NULL THEN 0 ELSE 1 END,
                                    LVHeaderKey DESC
                            ) AS row_num
                        FROM LVHeader lvh
                    ), filtered_df_cardhist AS (
                        SELECT 
                            emboss_id,
                            card_type_id,
                            ROW_NUMBER() OVER (
                                PARTITION BY emboss_id
                                ORDER BY update_datetime DESC
                            ) AS row_num
                        FROM Newmember.dbo.df_cardhist
                    )
                    SELECT
                        CancelHeaderKey,
                        lvh.KeySearch COLLATE SQL_Latin1_General_CP1_CI_AS AS KeySearch,
                        lvh.LVHeaderKey,
                        lvh.AddDT AS createdAt,
                        lvh.DocDate AS DocDate,
                        CASE 
                            WHEN lvh.FinishDT IS NOT NULL THEN lvh.FinishDT
                            ELSE lvh.AddDT
                        END AS updatedAt,
                        CASE 
                            WHEN mct.card_type_id IS NOT NULL THEN 
                                (SELECT mct.card_type_id AS 'memberLegacyTier.id', mct.card_type_code AS 'memberLegacyTier.code' FOR JSON PATH, WITHOUT_ARRAY_WRAPPER)
                            ELSE '{}'
                        END AS settings
                    INTO temp_lv_header_for_sales_transaction_full_dump
                    FROM key_search_grouped_lv_header lvh
                    LEFT JOIN filtered_df_cardhist dch ON dch.emboss_id = lvh.LVCardNo COLLATE SQL_Latin1_General_CP1_CI_AS AND dch.row_num = 1
                    LEFT JOIN Newmember.dbo.mst_card_type mct ON mct.card_type_code = dch.card_type_id
                    WHERE lvh.row_num = 1 AND lvh.DocDate < CAST(CAST(GETDATE() AS DATE) AS DATETIME);

                    CREATE INDEX temp_lv_header_for_sales_transaction_full_dump_key_search ON temp_lv_header_for_sales_transaction_full_dump (KeySearch);
                    CREATE INDEX temp_lv_header_for_sales_transaction_full_dump_lv_header_key ON temp_lv_header_for_sales_transaction_full_dump (LVHeaderKey);
                END;
            """
            if is_full_dump
            else f"""
                DROP TABLE IF EXISTS temp_lv_header_for_sales_transaction_incremental;

                WITH key_search_grouped_lv_header AS (
                    SELECT 
                        KeySearch,
                        LVHeaderKey,
                        AddDT,
                        FinishDT,
                        CancelHeaderKey,
                        DocDate,
                        LVCardNo,
                        ROW_NUMBER() OVER (
                            PARTITION BY KeySearch 
                            ORDER BY 
                                CASE WHEN CancelHeaderKey IS NULL THEN 0 ELSE 1 END,
                                LVHeaderKey DESC
                        ) AS row_num
                    FROM LVHeader lvh
                ), filtered_df_cardhist AS (
                    SELECT 
                        emboss_id,
                        card_type_id,
                        ROW_NUMBER() OVER (
                            PARTITION BY emboss_id
                            ORDER BY update_datetime DESC
                        ) AS row_num
                    FROM Newmember.dbo.df_cardhist
                )
                SELECT
                    CancelHeaderKey,
                    lvh.KeySearch COLLATE SQL_Latin1_General_CP1_CI_AS AS KeySearch,
                    lvh.LVHeaderKey,
                    lvh.AddDT AS createdAt,
                    CASE 
                        WHEN lvh.FinishDT IS NOT NULL THEN lvh.FinishDT
                        ELSE lvh.AddDT
                    END AS updatedAt,
                    CASE 
                        WHEN mct.card_type_id IS NOT NULL THEN 
                            (SELECT mct.card_type_id AS 'memberLegacyTier.id', mct.card_type_code AS 'memberLegacyTier.code' FOR JSON PATH, WITHOUT_ARRAY_WRAPPER)
                        ELSE '{{}}'
                    END AS settings
                INTO temp_lv_header_for_sales_transaction_incremental
                FROM key_search_grouped_lv_header lvh
                LEFT JOIN filtered_df_cardhist dch ON dch.emboss_id = lvh.LVCardNo COLLATE SQL_Latin1_General_CP1_CI_AS AND dch.row_num = 1
                LEFT JOIN Newmember.dbo.mst_card_type mct ON mct.card_type_code = dch.card_type_id
                WHERE lvh.row_num = 1 AND {self.get_incremental_query_condition('lvh.DocDate')};

                CREATE INDEX temp_lv_header_for_sales_transaction_incremental_key_search ON temp_lv_header_for_sales_transaction_incremental (KeySearch);
                CREATE INDEX temp_lv_header_for_sales_transaction_incremental_lv_header_key ON temp_lv_header_for_sales_transaction_incremental (LVHeaderKey);
            """
        )
        create_lv_trans_temp_table = (
            """
                IF OBJECT_ID('temp_lv_trans_for_sales_transaction_full_dump', 'U') IS NULL
                BEGIN
                    SELECT
                        lvh.KeySearch COLLATE SQL_Latin1_General_CP1_CI_AS AS KeySearch,
                        SUM(lvt.totalPointEarned) AS totalPointEarned
                    INTO temp_lv_trans_for_sales_transaction_full_dump
                    FROM (
                        SELECT
                            lvt.LVHeaderKey AS LVHeaderKey,
                            SUM(CASE WHEN lvt.MovementCode = 'PTPOS' THEN lvt.Amount ELSE 0 END) AS totalPointEarned
                        FROM
                            LVTrans lvt
                            JOIN LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
                        WHERE
                            lvd.ValueCode IN (
                                'AP001',
                                'EP001',
                                'EP002',
                                'EP003',
                                'EP004',
                                'EP005',
                                'EP006',
                                'EP007',
                                'EP008',
                                'EP009',
                                'EP010',
                                'KPC01',
                                'KPO02',
                                'CR001',
                                'PT001'
                            )
                        GROUP BY
                            lvt.LVHeaderKey
                    ) AS lvt JOIN LVHeader lvh ON lvh.LVHeaderKey = lvt.LVHeaderKey
                    GROUP BY lvh.KeySearch;

                    CREATE INDEX temp_lv_trans_for_sales_transaction_full_dump_key_search ON temp_lv_trans_for_sales_transaction_full_dump (KeySearch);
                END;
            """
            if is_full_dump
            else f"""
                DROP TABLE IF EXISTS temp_lv_trans_for_sales_transaction_incremental;

                SELECT
                    lvh.KeySearch COLLATE SQL_Latin1_General_CP1_CI_AS AS KeySearch,
                    SUM(lvt.totalPointEarned) AS totalPointEarned
                INTO temp_lv_trans_for_sales_transaction_incremental
                FROM (
                    SELECT
                        lvt.LVHeaderKey AS LVHeaderKey,
                        SUM(CASE WHEN lvt.MovementCode = 'PTPOS' THEN lvt.Amount ELSE 0 END) AS totalPointEarned
                    FROM
                        LVTrans lvt
                        JOIN LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
                    WHERE
                        lvd.ValueCode IN (
                            'AP001',
                            'EP001',
                            'EP002',
                            'EP003',
                            'EP004',
                            'EP005',
                            'EP006',
                            'EP007',
                            'EP008',
                            'EP009',
                            'EP010',
                            'KPC01',
                            'KPO02',
                            'CR001',
                            'PT001'
                        )
                    GROUP BY
                        lvt.LVHeaderKey
                ) AS lvt JOIN LVHeader lvh ON lvh.LVHeaderKey = lvt.LVHeaderKey
                GROUP BY lvh.KeySearch;
                
                CREATE INDEX temp_lv_trans_for_sales_transaction_incremental_key_search ON temp_lv_trans_for_sales_transaction_incremental (KeySearch);
            """
        )

        create_with_lv_header_result_table = (
            """
                IF OBJECT_ID('temp_sales_transaction_with_lv_header_for_full_dump_migration', 'U') IS NULL
                BEGIN
                    SELECT
                        lvh.LVHeaderKey AS id,
                        ssh.memberId,
                        ssh.gwlNo,
                        ssh.externalId,
                        ssh.partnerId,
                        ssh.brandId,
                        CASE
                            WHEN ssh.branchId IS NULL THEN ''
                            ELSE ssh.branchId
                        END AS branchId,
                        sst.netTotalAmount,
                        sst.totalOriginalPrice,
                        sst.totalDiscount,
                        COALESCE(ssp.totalEarnableAmount, 0) AS totalEarnableAmount,
                        COALESCE(ssp.totalAccumSpendableAmount, 0) AS totalAccumSpendableAmount,
                        COALESCE(lvt.totalPointEarned, 0) AS totalPointEarned,
                        ssh.status,
                        lvh.settings,
                        DATEADD (HOUR, -7, lvh.createdAt) AS createdAt,
                        DATEADD (HOUR, -7, lvh.updatedAt) AS updatedAt,
                        DATEADD (HOUR, -7, lvh.updatedAt) AS completedAt,
                        ssh.DataDate,
                        ssh.ShoppingCard
                    INTO temp_sales_transaction_with_lv_header_for_full_dump_migration
                    FROM Newmember.dbo.temp_smc_sales_header_for_sales_transaction_full_dump ssh
                    JOIN Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_full_dump sst ON sst.key_search = ssh.externalId
                    LEFT JOIN Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_full_dump ssp ON ssp.key_search = sst.key_search
                    JOIN temp_lv_header_for_sales_transaction_full_dump lvh ON lvh.KeySearch = sst.key_search AND lvh.CancelHeaderKey IS NULL
                    LEFT JOIN temp_lv_trans_for_sales_transaction_full_dump lvt ON lvt.KeySearch = lvh.KeySearch;

                    CREATE INDEX ix_temp_sales_transaction_with_lv_header_for_full_dump_migration_id
                    ON temp_sales_transaction_with_lv_header_for_full_dump_migration (id);
                END;
            """
            if is_full_dump
            else """
                DROP TABLE IF EXISTS temp_sales_transaction_with_lv_header_for_incremental_migration;

                SELECT
                    lvh.LVHeaderKey AS id,
                    ssh.memberId,
                    ssh.gwlNo,
                    ssh.externalId,
                    ssh.partnerId,
                    ssh.brandId,
                    CASE
                        WHEN ssh.branchId IS NULL THEN ''
                        ELSE ssh.branchId
                    END AS branchId,
                    sst.netTotalAmount,
                    sst.totalOriginalPrice,
                    sst.totalDiscount,
                    COALESCE(ssp.totalEarnableAmount, 0) AS totalEarnableAmount,
                    COALESCE(ssp.totalAccumSpendableAmount, 0) AS totalAccumSpendableAmount,
                    COALESCE(lvt.totalPointEarned, 0) AS totalPointEarned,
                    ssh.status,
                    lvh.settings,
                    DATEADD (HOUR, -7, lvh.createdAt) AS createdAt,
                    DATEADD (HOUR, -7, lvh.updatedAt) AS updatedAt,
                    DATEADD (HOUR, -7, lvh.updatedAt) AS completedAt,
                    ssh.DataDate,
                    ssh.ShoppingCard
                INTO temp_sales_transaction_with_lv_header_for_incremental_migration
                FROM Newmember.dbo.temp_smc_sales_header_for_sales_transaction_incremental ssh
                JOIN Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_incremental sst ON sst.key_search = ssh.externalId
                LEFT JOIN Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_incremental ssp ON ssp.key_search = sst.key_search
                JOIN temp_lv_header_for_sales_transaction_incremental lvh ON lvh.KeySearch = sst.key_search AND lvh.CancelHeaderKey IS NULL
                LEFT JOIN temp_lv_trans_for_sales_transaction_incremental lvt ON lvt.KeySearch = lvh.KeySearch;

                CREATE INDEX ix_temp_sales_transaction_with_lv_header_for_incremental_migration_id
                ON temp_sales_transaction_with_lv_header_for_incremental_migration (id);
            """
        )

        create_without_lv_header_result_table = (
            """
            IF OBJECT_ID('temp_sales_transaction_without_lv_header_for_full_dump_migration', 'U') IS NULL
            BEGIN
                SELECT
                    dbo.fn_generate_sales_transaction_id(ssh.externalId) AS id,
                    ssh.memberId,
                    ssh.gwlNo,
                    ssh.externalId,
                    ssh.partnerId,
                    ssh.brandId,
                    CASE
                        WHEN ssh.branchId IS NULL THEN ''
                        ELSE ssh.branchId
                    END AS branchId,
                    sst.netTotalAmount,
                    sst.totalOriginalPrice,
                    sst.totalDiscount,
                    CASE
                        WHEN ssp.totalEarnableAmount IS NOT NULL THEN ssp.totalEarnableAmount
                        ELSE 0
                    END AS totalEarnableAmount,
                    CASE
                        WHEN ssp.totalAccumSpendableAmount IS NOT NULL THEN ssp.totalAccumSpendableAmount
                        ELSE 0
                    END AS totalAccumSpendableAmount,
                    0 AS totalPointEarned,
                    ssh.status,
                    '{}' AS settings,
                    ssh.DataDate AS createdAt,
                    ssh.DataDate AS updatedAt,
                    ssh.DataDate AS completedAt,
                    ssh.DataDate,
                    ssh.ShoppingCard
                INTO temp_sales_transaction_without_lv_header_for_full_dump_migration
                FROM Newmember.dbo.temp_smc_sales_header_for_sales_transaction_full_dump ssh
                JOIN Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_full_dump sst ON sst.key_search = ssh.externalId
                LEFT JOIN Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_full_dump ssp ON ssp.key_search = sst.key_search
                LEFT JOIN temp_lv_header_for_sales_transaction_full_dump lvh ON lvh.KeySearch = sst.key_search
                WHERE lvh.LVHeaderKey IS NULL;

                CREATE INDEX ix_temp_sales_transaction_without_lv_header_for_full_dump_migration_id
                ON temp_sales_transaction_without_lv_header_for_full_dump_migration (id);
            END;
        """
            if is_full_dump
            else """
                DROP TABLE IF EXISTS temp_sales_transaction_without_lv_header_for_incremental_migration;

                SELECT
                    dbo.fn_generate_sales_transaction_id(ssh.externalId) AS id,
                    ssh.memberId,
                    ssh.gwlNo,
                    ssh.externalId,
                    ssh.partnerId,
                    ssh.brandId,
                    CASE
                        WHEN ssh.branchId IS NULL THEN ''
                        ELSE ssh.branchId
                    END AS branchId,
                    sst.netTotalAmount,
                    sst.totalOriginalPrice,
                    sst.totalDiscount,
                    CASE
                        WHEN ssp.totalEarnableAmount IS NOT NULL THEN ssp.totalEarnableAmount
                        ELSE 0
                    END AS totalEarnableAmount,
                    CASE
                        WHEN ssp.totalAccumSpendableAmount IS NOT NULL THEN ssp.totalAccumSpendableAmount
                        ELSE 0
                    END AS totalAccumSpendableAmount,
                    0 AS totalPointEarned,
                    ssh.status,
                    '{}' AS settings,
                    ssh.DataDate AS createdAt,
                    ssh.DataDate AS updatedAt,
                    ssh.DataDate AS completedAt,
                    ssh.DataDate,
                    ssh.ShoppingCard
                INTO temp_sales_transaction_without_lv_header_for_incremental_migration
                FROM Newmember.dbo.temp_smc_sales_header_for_sales_transaction_incremental ssh
                JOIN Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_incremental sst ON sst.key_search = ssh.externalId
                LEFT JOIN Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_incremental ssp ON ssp.key_search = sst.key_search
                LEFT JOIN temp_lv_header_for_sales_transaction_incremental lvh ON lvh.KeySearch = sst.key_search
                WHERE lvh.LVHeaderKey IS NULL;

                CREATE INDEX ix_temp_sales_transaction_without_lv_header_for_incremental_migration_id
                ON temp_sales_transaction_without_lv_header_for_incremental_migration (id);
            """
        )

        loyalty_value_connection = self.loyalty_value_handler.hook.get_conn()

        try:
            logger.info(f"started preparing SMCSalesHeader temp table for migration...")
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_smc_sales_header_temp_table,
            )
            logger.info(f"finished preparing SMCSalesHeader temp table for migration.")

            logger.info(f"started preparing SMCSalesTrans temp table for migration...")
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_smc_sales_trans_temp_table,
            )
            logger.info(f"finished preparing SMCSalesTrans temp table for migration.")

            logger.info(
                f"started preparing SMCSalesPayment temp table for migration..."
            )
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_smc_sales_payment_temp_table,
            )
            logger.info(f"finished preparing SMCSalesPayment temp table for migration.")

            logger.info(f"started preparing LVHeader temp table for migration...")
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_lv_header_temp_table,
            )
            logger.info(f"finished preparing LVHeader temp table for migration.")

            logger.info(f"started preparing LVTrans temp table for migration...")
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_lv_trans_temp_table,
            )
            logger.info(f"finished preparing LVTrans temp table for migration.")

            logger.info(
                f"started preparing with LVHeader result temp table for migration..."
            )
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_with_lv_header_result_table,
            )
            logger.info(
                f"finished preparing with LVHeader result temp table for migration."
            )

            logger.info(
                f"started preparing without LVHeader result temp table for migration..."
            )
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_without_lv_header_result_table,
            )
            logger.info(
                f"finished preparing without LVHeader result temp table for migration."
            )

        finally:
            loyalty_value_connection.close()

    def prepare_temp_tables_for_sales_transaction_item(self, is_full_dump: bool = True):
        """
        Prepare indexed temporary tables for SalesTransactionItem migration.

        Args:
            is_full_dump (bool): The migration type.

        Returns:
            None
        """
        create_lv_header_temp_table = (
            """
                IF OBJECT_ID('temp_lv_header_for_sales_transaction_item_full_dump', 'U') IS NULL
                BEGIN
                    SELECT
                        KeySearch COLLATE Thai_CI_AS AS KeySearch,
                        LVHeaderKey
                    INTO temp_lv_header_for_sales_transaction_item_full_dump
                    FROM LVHeader
                    WHERE DocDate < CAST(CAST(GETDATE() AS DATE) AS DATETIME);

                    CREATE NONCLUSTERED INDEX idx_temp_lv_header_for_sales_transaction_item_full_dump_key_search_lv_header
                    ON temp_lv_header_for_sales_transaction_item_full_dump (KeySearch, LVHeaderKey);
                END;
            """
            if is_full_dump
            else f"""
                DROP TABLE IF EXISTS temp_lv_header_for_sales_transaction_item_incremental;
                
                SELECT
                    KeySearch COLLATE Thai_CI_AS AS KeySearch,
                    LVHeaderKey
                INTO temp_lv_header_for_sales_transaction_item_incremental
                FROM LVHeader
                WHERE {self.get_incremental_query_condition('DocDate')};

                CREATE NONCLUSTERED INDEX idx_temp_lv_header_for_sales_transaction_item_incremental_key_search_lv_header
                ON temp_lv_header_for_sales_transaction_item_incremental (KeySearch, LVHeaderKey);
            """
        )

        create_recent_sales_temp_table = (
            """
                IF OBJECT_ID('temp_sales_transaction_item_recent_sales_full_dump', 'U') IS NULL
                BEGIN
                    SELECT
                        CASE
                            WHEN lvh.LVHeaderKey IS NOT NULL THEN lvh.LVHeaderKey
                            ELSE dbo.fn_generate_sales_transaction_id(sst.key_search)
                        END AS sales_transaction_id,
                        sst.MatCode AS sku,
                        sst.Qty AS quantity,
                        sst.Net AS net_amount,
                        sst.Amount AS original_price,
                        lvh.LVHeaderKey
                    INTO temp_sales_transaction_item_recent_sales_full_dump
                    FROM
                        Newmember.dbo.SMCSalesTrans sst
                        JOIN Newmember.dbo.SMCSalesHeader ssh ON ssh.key_search = sst.key_search
                        LEFT JOIN temp_lv_header_for_sales_transaction_item_full_dump lvh ON lvh.KeySearch = ssh.key_search
                    WHERE sst.lineCancel = 0 AND sst.CancelStatus = 0 AND ssh.DataDate < CAST(CAST(GETDATE () AS DATE) AS DATETIME);

                    CREATE INDEX ix_temp_sales_transaction_item_recent_sales_full_dump_sales_transaction_id
                    ON temp_sales_transaction_item_recent_sales_full_dump (sales_transaction_id);
                END;
            """
            if is_full_dump
            else f"""
                DROP TABLE IF EXISTS temp_sales_transaction_item_recent_sales_incremental;

                SELECT
                    CASE
                        WHEN lvh.LVHeaderKey IS NOT NULL THEN lvh.LVHeaderKey
                        ELSE dbo.fn_generate_sales_transaction_id(sst.key_search)
                    END AS sales_transaction_id,
                    sst.MatCode AS sku,
                    sst.Qty AS quantity,
                    sst.Net AS net_amount,
                    sst.Amount AS original_price,
                    lvh.LVHeaderKey
                INTO temp_sales_transaction_item_recent_sales_incremental
                FROM Newmember.dbo.SMCSalesTrans sst
                JOIN Newmember.dbo.SMCSalesHeader ssh ON ssh.key_search = sst.key_search
                LEFT JOIN temp_lv_header_for_sales_transaction_item_incremental lvh ON lvh.KeySearch = ssh.key_search
                WHERE sst.lineCancel = 0 AND sst.CancelStatus = 0 AND {self.get_incremental_query_condition('ssh.DataDate')};

                CREATE INDEX ix_temp_sales_transaction_item_recent_sales_incremental_sales_transaction_id
                ON temp_sales_transaction_item_recent_sales_incremental (sales_transaction_id);
            """
        )

        create_partitioned_temp_table = (
            """
                IF OBJECT_ID('temp_sales_transaction_item_partitioned_full_dump', 'U') IS NULL
                BEGIN
                    SELECT
                        sales_transaction_id,
                        sku,
                        quantity,
                        net_amount,
                        original_price,
                        LVHeaderKey,
                        ROW_NUMBER() OVER (
                            PARTITION BY sales_transaction_id
                            ORDER BY sales_transaction_id
                        ) AS row_num
                    INTO temp_sales_transaction_item_partitioned_full_dump
                    FROM temp_sales_transaction_item_recent_sales_full_dump;

                    CREATE INDEX ix_temp_sales_transaction_item_partitioned_full_dump_sales_transaction_id
                    ON temp_sales_transaction_item_partitioned_full_dump (sales_transaction_id);
                END;
            """
            if is_full_dump
            else """
                DROP TABLE IF EXISTS temp_sales_transaction_item_partitioned_incremental;

                SELECT
                    sales_transaction_id,
                    sku,
                    quantity,
                    net_amount,
                    original_price,
                    LVHeaderKey,
                    ROW_NUMBER() OVER (
                        PARTITION BY sales_transaction_id
                        ORDER BY sales_transaction_id
                    ) AS row_num
                INTO temp_sales_transaction_item_partitioned_incremental
                FROM temp_sales_transaction_item_recent_sales_incremental;

                CREATE INDEX ix_temp_sales_transaction_item_partitioned_incremental_sales_transaction_id
                ON temp_sales_transaction_item_partitioned_incremental (sales_transaction_id);
            """
        )

        create_point_earned_temp_table = """
            DROP TABLE IF EXISTS temp_sales_transaction_item_point_earned;

            SELECT 
                lvh.LVHeaderKey AS sales_transaction_id,
                SUM(lvt.Amount) AS normal_point_earned
            INTO temp_sales_transaction_item_point_earned
            FROM LVHeader lvh
            JOIN LVTrans lvt
                ON lvt.MovementCode = 'PTPOS'
                AND lvt.LVHeaderKey = lvh.LVHeaderKey
            GROUP BY lvh.LVHeaderKey;

            CREATE INDEX ix_temp_sales_transaction_item_point_earned_sales_transaction_id
            ON temp_sales_transaction_item_point_earned (sales_transaction_id);
        """

        create_result_table = (
            """
                IF OBJECT_ID('temp_sales_transaction_item_for_full_dump_migration', 'U') IS NULL
                BEGIN
                    SELECT
                        ROW_NUMBER() OVER (ORDER BY partitioned.sales_transaction_id, partitioned.sku, partitioned.row_num) AS id,
                        partitioned.sales_transaction_id,
                        partitioned.sku,
                        partitioned.quantity,
                        partitioned.net_amount,
                        partitioned.original_price,
                        CASE
                            WHEN partitioned.LVHeaderKey IS NULL THEN 0
                            WHEN partitioned.row_num = 1 THEN COALESCE(point_earned.normal_point_earned, 0)
                            ELSE 0
                        END AS normal_point_earned
                    INTO temp_sales_transaction_item_for_full_dump_migration
                    FROM temp_sales_transaction_item_partitioned_full_dump partitioned
                    LEFT JOIN temp_sales_transaction_item_point_earned point_earned 
                        ON point_earned.sales_transaction_id = partitioned.sales_transaction_id
                    ORDER BY partitioned.sales_transaction_id;

                    CREATE CLUSTERED INDEX ix_temp_sales_transaction_item_for_full_dump_migration_id
                    ON temp_sales_transaction_item_for_full_dump_migration (id);
                END;
            """
            if is_full_dump
            else """
                DROP TABLE IF EXISTS temp_sales_transaction_item_for_incremental_migration;

                SELECT
                    ROW_NUMBER() OVER (ORDER BY partitioned.sales_transaction_id, partitioned.sku, partitioned.row_num) AS id,
                    partitioned.sales_transaction_id,
                    partitioned.sku,
                    partitioned.quantity,
                    partitioned.net_amount,
                    partitioned.original_price,
                    CASE
                        WHEN partitioned.LVHeaderKey IS NULL THEN 0
                        WHEN partitioned.row_num = 1 THEN COALESCE(point_earned.normal_point_earned, 0)
                        ELSE 0
                    END AS normal_point_earned
                INTO temp_sales_transaction_item_for_incremental_migration
                FROM temp_sales_transaction_item_partitioned_incremental partitioned
                LEFT JOIN temp_sales_transaction_item_point_earned point_earned 
                    ON point_earned.sales_transaction_id = partitioned.sales_transaction_id;

                CREATE CLUSTERED INDEX ix_temp_sales_transaction_item_for_incremental_migration_id
                ON temp_sales_transaction_item_for_incremental_migration (id);
            """
        )

        loyalty_value_connection = self.loyalty_value_handler.hook.get_conn()

        try:
            logger.info(f"started preparing LVHeader temp table for migration...")
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_lv_header_temp_table,
            )
            logger.info(f"finished preparing LVHeader temp table for migration.")

            logger.info(f"started preparing recent sales temp table...")
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_recent_sales_temp_table,
            )
            logger.info(f"finished preparing recent sales temp table.")

            logger.info(f"started preparing partitioned temp table...")
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_partitioned_temp_table,
            )
            logger.info(f"finished preparing partitioned temp table.")

            logger.info(f"started preparing point earned temp table for migration...")
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_point_earned_temp_table,
            )
            logger.info(f"finished preparing point earned temp table for migration.")

            logger.info(f"started preparing result temp table for migration...")
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_result_table,
            )
            logger.info(f"finished preparing result temp table for migration.")
        finally:
            loyalty_value_connection.close()

    def prepare_temp_tables_for_refund_sales_transaction(
        self, is_full_dump: bool = True
    ):
        """
        Prepare temporary tables for RefundSalesTransaction migration.

        Args:
            is_full_dump (bool): The migration type.

        Returns:
            None
        """
        create_refund_sales_transaction_temp_tables_query_string = (
            f"""
                IF OBJECT_ID('temp_refund_sales_transaction_for_migration_full_dump', 'U') IS NULL
                BEGIN
                    SELECT
                        ssh.key_search,
                        ssh.RecvKeySearch COLLATE SQL_Latin1_General_CP1_CI_AS AS RecvKeySearch,
                        ssh.DataDate,
                        ssh2.member_id
                    INTO #all_refund_sales_transactions
                    FROM Newmember.dbo.SMCSalesHeader ssh 
                    JOIN Newmember.dbo.SMCSalesHeader ssh2 ON ssh2.key_search = ssh.RecvKeySearch
                    WHERE ssh.SaleStatus = 'R' AND ssh.DataDate < CAST(CAST(GETDATE() AS DATE) AS DATETIME);

                    SELECT member_id
                    INTO #all_refund_sales_transaction_active_members
                    FROM Newmember.dbo.df_member
                    WHERE del_flag = '';

                    SELECT 
                        KeySearch,
                        LVHeaderKey,
                        CancelHeaderKey,
                        ROW_NUMBER() OVER (PARTITION BY KeySearch ORDER BY LVHeaderKey DESC) AS row_num
                    INTO #latest_lv_headers
                    FROM LVHeader
                    WHERE DocDate < CAST(CAST(GETDATE() AS DATE) AS DATETIME);

                    SELECT
                        ROW_NUMBER() OVER (
                            ORDER BY
                                CASE
                                    WHEN lvh.LVHeaderKey IS NOT NULL THEN lvh.LVHeaderKey
                                    ELSE dbo.fn_generate_sales_transaction_id (ssh.RecvKeySearch)
                                END,
                                ssh.key_search,
                                ssh.DataDate
                        ) AS id,
                        CASE
                            WHEN lvh.LVHeaderKey IS NOT NULL THEN lvh.LVHeaderKey
                            ELSE dbo.fn_generate_sales_transaction_id(ssh.RecvKeySearch)
                        END AS sales_transaction_id,
                        'FULL_REFUND' AS type,
                        ssh.key_search AS external_id,
                        0 AS carat_refund_amount,
                        0 AS carat_revoke_amount,
                        0 AS cashback_refund_amount,
                        0 AS cashback_revoke_amount,
                        0 AS charge_back_amount,
                        ssh.DataDate AS refunded_at,
                        ssh.DataDate AS approved_at,
                        ssh.DataDate AS created_at,
                        ssh.DataDate AS updated_at
                    INTO temp_refund_sales_transaction_for_migration_full_dump
                    FROM #all_refund_sales_transactions ssh
                    JOIN #all_refund_sales_transaction_active_members dm ON dm.member_id = ssh.member_id 
                    LEFT JOIN #latest_lv_headers lvh ON lvh.KeySearch = ssh.RecvKeySearch
                    WHERE lvh.LVHeaderKey IS NULL OR (lvh.CancelHeaderKey IS NULL AND lvh.row_num = 1);

                    DROP TABLE IF EXISTS #all_refund_sales_transactions;
                    DROP TABLE IF EXISTS #all_refund_sales_transaction_active_members;
                    DROP TABLE IF EXISTS #latest_lv_headers;
                END
            """
            if is_full_dump
            else f"""
                DROP TABLE IF EXISTS temp_refund_sales_transaction_for_migration_incremental;

                SELECT
                    ssh.key_search,
                    ssh.RecvKeySearch COLLATE SQL_Latin1_General_CP1_CI_AS AS RecvKeySearch,
                    ssh.DataDate,
                    ssh2.member_id
                INTO #all_refund_sales_transactions
                FROM Newmember.dbo.SMCSalesHeader ssh 
                JOIN Newmember.dbo.SMCSalesHeader ssh2 ON ssh2.key_search = ssh.RecvKeySearch
                WHERE ssh.SaleStatus = 'R' AND {self.get_incremental_query_condition('ssh.DataDate')};

                SELECT member_id
                INTO #all_refund_sales_transaction_active_members
                FROM Newmember.dbo.df_member
                WHERE del_flag = '';

                SELECT 
                    KeySearch,
                    LVHeaderKey,
                    CancelHeaderKey,
                    ROW_NUMBER() OVER (PARTITION BY KeySearch ORDER BY LVHeaderKey DESC) AS row_num
                INTO #latest_lv_headers
                FROM LVHeader
                WHERE {self.get_incremental_query_condition('DocDate')};

                SELECT
                    ROW_NUMBER() OVER (
                        ORDER BY
                            CASE
                                WHEN lvh.LVHeaderKey IS NOT NULL THEN lvh.LVHeaderKey
                                ELSE dbo.fn_generate_sales_transaction_id (ssh.RecvKeySearch)
                            END,
                            ssh.key_search,
                            ssh.DataDate
                    ) AS id,
                    CASE
                        WHEN lvh.LVHeaderKey IS NOT NULL THEN lvh.LVHeaderKey
                        ELSE dbo.fn_generate_sales_transaction_id(ssh.RecvKeySearch)
                    END AS sales_transaction_id,
                    'FULL_REFUND' AS type,
                    ssh.key_search AS external_id,
                    0 AS carat_refund_amount,
                    0 AS carat_revoke_amount,
                    0 AS cashback_refund_amount,
                    0 AS cashback_revoke_amount,
                    0 AS charge_back_amount,
                    ssh.DataDate AS refunded_at,
                    ssh.DataDate AS approved_at,
                    ssh.DataDate AS created_at,
                    ssh.DataDate AS updated_at
                INTO temp_refund_sales_transaction_for_migration_incremental
                FROM #all_refund_sales_transactions ssh
                JOIN #all_refund_sales_transaction_active_members dm ON dm.member_id = ssh.member_id 
                LEFT JOIN #latest_lv_headers lvh ON lvh.KeySearch = ssh.RecvKeySearch
                WHERE lvh.LVHeaderKey IS NULL OR (lvh.CancelHeaderKey IS NULL AND lvh.row_num = 1);

                DROP TABLE IF EXISTS #all_refund_sales_transactions;
                DROP TABLE IF EXISTS #all_refund_sales_transaction_active_members;
                DROP TABLE IF EXISTS #latest_lv_headers;
            """
        )

        loyalty_value_connection = self.loyalty_value_handler.hook.get_conn()

        try:
            logger.info(
                f"started preparing RefundSalesTransaction temp table for migration..."
            )
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_refund_sales_transaction_temp_tables_query_string,
            )
            logger.info(
                f"finished preparing RefundSalesTransaction temp table for migration."
            )
        finally:
            loyalty_value_connection.close()

    def drop_refund_sales_transaction_temp_table(self):
        """
        Drop RefundSalesTransaction temp table for both full dump and incremental migration.

        Args:
            None

        Returns:
            None
        """
        query_string = """
            DROP TABLE IF EXISTS temp_refund_sales_transaction_for_migration;
        """

        loyalty_value_connection = self.loyalty_value_handler.hook.get_conn()

        try:
            logger.info(
                f"started dropping RefundSalesTransaction temp table for migration..."
            )
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=query_string,
            )
            logger.info(
                f"finished dropping RefundSalesTransaction temp table for migration."
            )
        finally:
            loyalty_value_connection.close()

    def cleanup_sales_transaction_temp_tables(self):
        """
        Clean up temporary tables created for SalesTransaction full dump migration.

        Args:
            None

        Returns:
            None
        """
        cleanup_queries = """
            DROP TABLE IF EXISTS Newmember.dbo.temp_smc_sales_header_for_sales_transaction_full_dump;
            DROP TABLE IF EXISTS Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_full_dump;
            DROP TABLE IF EXISTS Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_full_dump;
            DROP TABLE IF EXISTS temp_lv_header_for_sales_transaction_full_dump;
            DROP TABLE IF EXISTS temp_lv_trans_for_sales_transaction_full_dump;
            DROP TABLE IF EXISTS temp_sales_transaction_with_lv_header_for_full_dump_migration;
            DROP TABLE IF EXISTS temp_sales_transaction_without_lv_header_for_full_dump_migration;
        """

        loyalty_value_connection = self.loyalty_value_handler.hook.get_conn()

        try:
            logger.info(f"started cleaning up SalesTransaction temporary tables...")
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=cleanup_queries,
            )
            logger.info(f"finished cleaning up SalesTransaction temporary tables.")
        finally:
            loyalty_value_connection.close()

    def cleanup_sales_transaction_item_temp_tables(self):
        """
        Clean up temporary tables created for SalesTransactionItem full dump migration.

        Args:
            None

        Returns:
            None
        """
        cleanup_queries = """
            DROP TABLE IF EXISTS temp_lv_header_for_sales_transaction_item_full_dump;
            DROP TABLE IF EXISTS temp_sales_transaction_item_recent_sales_full_dump;
            DROP TABLE IF EXISTS temp_sales_transaction_item_partitioned_full_dump;
            DROP TABLE IF EXISTS temp_sales_transaction_item_point_earned;
            DROP TABLE IF EXISTS temp_sales_transaction_item_for_full_dump_migration;
        """

        loyalty_value_connection = self.loyalty_value_handler.hook.get_conn()

        try:
            logger.info(f"started cleaning up SalesTransactionItem temporary tables...")
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=cleanup_queries,
            )
            logger.info(f"finished cleaning up SalesTransactionItem temporary tables.")
        finally:
            loyalty_value_connection.close()

    def cleanup_sales_transaction_payment_temp_tables(self):
        """
        Clean up temporary tables created for SalesTransactionPayment full dump migration.

        Args:
            None

        Returns:
            None
        """
        cleanup_queries = """
            DROP TABLE IF EXISTS temp_sales_transaction_payment_for_full_dump_migration;
        """

        loyalty_value_connection = self.loyalty_value_handler.hook.get_conn()

        try:
            logger.info(
                f"started cleaning up SalesTransactionPayment temporary tables..."
            )
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=cleanup_queries,
            )
            logger.info(
                f"finished cleaning up SalesTransactionPayment temporary tables."
            )
        finally:
            loyalty_value_connection.close()

    def migrate_full_dump_product_brand(self):
        """
        The main function for ProductBrand full dump migration task.

        Args:
            None

        Returns:
            None
        """
        ProductBrand(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.newmember_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_full_dump()

    def migrate_incremental_product_brand(self):
        """
        The main function for ProductBrand incremental migration task.

        Args:
            None

        Returns:
            None
        """
        ProductBrand(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.newmember_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_incremental()

    def migrate_full_dump_product_category(self):
        """
        The main function for ProductCategory full dump migration task.

        Args:
            None

        Returns:
            None
        """
        ProductCategory(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.newmember_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_full_dump()

    def migrate_incremental_product_category(self):
        """
        The main function for ProductCategory incremental migration task.

        Args:
            None

        Returns:
            None
        """
        ProductCategory(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.newmember_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_incremental()

    def migrate_full_dump_sales_transaction(self):
        """
        The main function for SalesTransaction full dump migration task.

        Args:
            None

        Returns:
            None
        """
        SalesTransaction(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_full_dump()

    def migrate_full_dump_sales_transaction_without_lv_header(self):
        """
        The main function for SalesTransaction full dump migration task.

        Args:
            None

        Returns:
            None
        """
        SalesTransaction(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_full_dump_without_lv_header()

    def migrate_incremental_sales_transaction(self):
        """
        The main function for SalesTransaction incremental migration task.

        Args:
            None

        Returns:
            None
        """
        SalesTransaction(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_incremental()

    def migrate_incremental_sales_transaction_without_lv_header(self):
        """
        The main function for SalesTransaction incremental migration task.

        Args:
            None

        Returns:
            None
        """
        SalesTransaction(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_incremental_without_lv_header()

    def migrate_full_dump_sales_transaction_item(self):
        """
        The main function for SalesTransactionItem full dump migration task.

        Args:
            None

        Returns:
            None
        """
        SalesTransactionItem(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_full_dump()

    def migrate_incremental_sales_transaction_item(self):
        """
        The main function for SalesTransactionItem incremental migration task.

        Args:
            None

        Returns:
            None
        """
        SalesTransactionItem(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_incremental()

    def migrate_full_dump_sales_transaction_payment(self):
        """
        The main function for SalesTransactionPayment full dump migration task.

        Args:
            None

        Returns:
            None
        """
        SalesTransactionPayment(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_full_dump()

    def migrate_incremental_sales_transaction_payment(self):
        """
        The main function for SalesTransactionPayment incremental migration task.

        Args:
            None

        Returns:
            None
        """
        SalesTransactionPayment(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_incremental()

    def migrate_full_dump_sales_transaction_burn_payment(self):
        """
        The main function for SalesTransactionBurnPayment full dump migration task.

        Args:
            None

        Returns:
            None
        """
        SalesTransactionBurnPayment(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_full_dump()

    def migrate_incremental_sales_transaction_burn_payment(self):
        """
        The main function for SalesTransactionBurnPayment incremental migration task.

        Args:
            None

        Returns:
            None
        """
        SalesTransactionBurnPayment(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_incremental()

    def migrate_full_dump_refund_sales_transaction(self):
        """
        The main function for RefundSalesTransaction full dump migration task.

        Args:
            None

        Returns:
            None
        """
        RefundSalesTransaction(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_full_dump()

    def migrate_incremental_refund_sales_transaction(self):
        """
        The main function for RefundSalesTransaction incremental migration task.

        Args:
            None

        Returns:
            None
        """
        RefundSalesTransaction(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_incremental()


with DAG(
    "partner_service_full_dump_migration_product_brand",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, ProductBrand full dump migration",
    schedule_interval=None,
    start_date=datetime(2025, 3, 5),
    catchup=False,
    tags=["partner_service", "full_dump", "product_brand"],
) as partner_service_full_dump_migration_product_brand_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_batch_tracker_table_task = PythonOperator(
        task_id="prepare_batch_tracker_table_task",
        python_callable=partner_service.prepare_batch_tracker_table,
    )

    prepare_brand_table_task = PythonOperator(
        task_id="prepare_brand_table_task",
        python_callable=partner_service.prepare_brand_table_for_migration,
    )

    migrate_product_brand_task = PythonOperator(
        task_id="migrate_product_brand_task",
        python_callable=partner_service.migrate_full_dump_product_brand,
    )

    (
        [
            prepare_migration_result_table_task,
            prepare_batch_tracker_table_task,
            prepare_brand_table_task,
        ]
        >> migrate_product_brand_task
    )

with DAG(
    "partner_service_full_dump_migration_product_category",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, ProductCategory full dump migration",
    schedule_interval=None,
    start_date=datetime(2025, 3, 5),
    catchup=False,
    tags=["partner_service", "full_dump", "product_category"],
) as partner_service_full_dump_migration_product_category_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_batch_tracker_table_task = PythonOperator(
        task_id="prepare_batch_tracker_table_task",
        python_callable=partner_service.prepare_batch_tracker_table,
    )

    prepare_brand_table_task = PythonOperator(
        task_id="prepare_brand_table_task",
        python_callable=partner_service.prepare_brand_table_for_migration,
    )

    migrate_product_category_task = PythonOperator(
        task_id="migrate_product_category_task",
        python_callable=partner_service.migrate_full_dump_product_category,
    )

    (
        [
            prepare_migration_result_table_task,
            prepare_batch_tracker_table_task,
            prepare_brand_table_task,
        ]
        >> migrate_product_category_task
    )

with DAG(
    "partner_service_full_dump_migration_sales_transaction",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, SalesTransaction full dump migration",
    schedule_interval=None,
    start_date=datetime(2025, 3, 7),
    catchup=False,
    tags=["partner_service", "full_dump", "sales_transaction"],
) as partner_service_full_dump_migration_sales_transaction_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_batch_tracker_table_task = PythonOperator(
        task_id="prepare_batch_tracker_table_task",
        python_callable=partner_service.prepare_batch_tracker_table,
    )

    prepare_temp_tables_task = PythonOperator(
        task_id="prepare_temp_tables_task",
        python_callable=partner_service.prepare_temp_tables_for_sales_transaction,
        op_args=[True],
    )

    prepare_generate_sales_transaction_id_function_task = PythonOperator(
        task_id="prepare_generate_sales_transaction_id_function_task",
        python_callable=partner_service.prepare_generate_sales_transaction_id_function,
    )

    migrate_sales_transaction_task = PythonOperator(
        task_id="migrate_sales_transaction_task",
        python_callable=partner_service.migrate_full_dump_sales_transaction,
    )

    migrate_sales_transaction_task_without_lv_header_task = PythonOperator(
        task_id="migrate_sales_transaction_task_without_lv_header_task",
        python_callable=partner_service.migrate_full_dump_sales_transaction_without_lv_header,
    )

    # cleanup_temp_tables_task = PythonOperator(
    #     task_id="cleanup_temp_tables_task",
    #     python_callable=partner_service.cleanup_sales_transaction_temp_tables,
    # )

    (
        [
            prepare_migration_result_table_task,
            prepare_batch_tracker_table_task,
            prepare_temp_tables_task,
            prepare_generate_sales_transaction_id_function_task,
        ]
        >> migrate_sales_transaction_task
        >> migrate_sales_transaction_task_without_lv_header_task
        # >> cleanup_temp_tables_task
    )

with DAG(
    "partner_service_full_dump_migration_sales_transaction_item",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, SalesTransactionItem full dump migration",
    schedule_interval=None,
    start_date=datetime(2025, 3, 3),
    catchup=False,
    tags=["partner_service", "full_dump", "sales_transaction_item"],
) as partner_service_full_dump_migration_sales_transaction_item_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_batch_tracker_table_task = PythonOperator(
        task_id="prepare_batch_tracker_table_task",
        python_callable=partner_service.prepare_batch_tracker_table,
    )

    prepare_temp_tables_task = PythonOperator(
        task_id="prepare_temp_tables_task",
        python_callable=partner_service.prepare_temp_tables_for_sales_transaction_item,
        op_args=[True],
    )

    prepare_generate_sales_transaction_id_function_task = PythonOperator(
        task_id="prepare_generate_sales_transaction_id_function_task",
        python_callable=partner_service.prepare_generate_sales_transaction_id_function,
    )

    migrate_sales_transaction_item_task = PythonOperator(
        task_id="migrate_sales_transaction_item_task",
        python_callable=partner_service.migrate_full_dump_sales_transaction_item,
    )

    # cleanup_temp_tables_task = PythonOperator(
    #     task_id="cleanup_temp_tables_task",
    #     python_callable=partner_service.cleanup_sales_transaction_item_temp_tables,
    # )

    (
        [
            prepare_migration_result_table_task,
            prepare_batch_tracker_table_task,
            prepare_temp_tables_task,
            prepare_generate_sales_transaction_id_function_task,
        ]
        >> migrate_sales_transaction_item_task
        # >> cleanup_temp_tables_task
    )

with DAG(
    "partner_service_full_dump_migration_sales_transaction_payment",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, SalesTransactionPayment full dump migration",
    schedule_interval=None,
    start_date=datetime(2025, 3, 5),
    catchup=False,
    tags=["partner_service", "full_dump", "sales_transaction_payment"],
) as partner_service_full_dump_migration_sales_transaction_payment_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_batch_tracker_table_task = PythonOperator(
        task_id="prepare_batch_tracker_table_task",
        python_callable=partner_service.prepare_batch_tracker_table,
    )

    prepare_generate_sales_transaction_id_function_task = PythonOperator(
        task_id="prepare_generate_sales_transaction_id_function_task",
        python_callable=partner_service.prepare_generate_sales_transaction_id_function,
    )

    migrate_sales_transaction_payment_task = PythonOperator(
        task_id="migrate_sales_transaction_payment_task",
        python_callable=partner_service.migrate_full_dump_sales_transaction_payment,
    )

    cleanup_temp_tables_task = PythonOperator(
        task_id="cleanup_temp_tables_task",
        python_callable=partner_service.cleanup_sales_transaction_payment_temp_tables,
    )

    (
        [
            prepare_migration_result_table_task,
            prepare_batch_tracker_table_task,
            prepare_generate_sales_transaction_id_function_task,
        ]
        >> migrate_sales_transaction_payment_task
        >> cleanup_temp_tables_task
    )

with DAG(
    "partner_service_full_dump_migration_sales_transaction_burn_payment",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, SalesTransactionBurnPayment full dump migration",
    schedule_interval=None,
    start_date=datetime(2025, 3, 5),
    catchup=False,
    tags=["partner_service", "full_dump", "sales_transaction_burn_payment"],
) as partner_service_full_dump_migration_sales_transaction_burn_payment_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_batch_tracker_table_task = PythonOperator(
        task_id="prepare_batch_tracker_table_task",
        python_callable=partner_service.prepare_batch_tracker_table,
    )

    migrate_sales_transaction_burn_payment_task = PythonOperator(
        task_id="migrate_sales_transaction_burn_payment_task",
        python_callable=partner_service.migrate_full_dump_sales_transaction_burn_payment,
    )

    [
        prepare_migration_result_table_task,
        prepare_batch_tracker_table_task,
    ] >> migrate_sales_transaction_burn_payment_task

with DAG(
    "partner_service_full_dump_migration_refund_sales_transaction",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, RefundSalesTransaction full dump migration",
    schedule_interval=None,
    start_date=datetime(2025, 3, 5),
    catchup=False,
    tags=["partner_service", "full_dump", "refund_sales_transaction"],
) as partner_service_full_dump_migration_refund_sales_transaction_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_batch_tracker_table_task = PythonOperator(
        task_id="prepare_batch_tracker_table_task",
        python_callable=partner_service.prepare_batch_tracker_table,
    )

    prepare_generate_sales_transaction_id_function_task = PythonOperator(
        task_id="prepare_generate_sales_transaction_id_function_task",
        python_callable=partner_service.prepare_generate_sales_transaction_id_function,
    )

    prepare_temp_tables_task = PythonOperator(
        task_id="prepare_temp_tables_task",
        python_callable=partner_service.prepare_temp_tables_for_refund_sales_transaction,
        op_args=[True],
    )

    migrate_refund_sales_transaction_task = PythonOperator(
        task_id="migrate_refund_sales_transaction_task",
        python_callable=partner_service.migrate_full_dump_refund_sales_transaction,
    )

    drop_refund_sales_transaction_temp_table_task = PythonOperator(
        task_id="drop_refund_sales_transaction_temp_table_task",
        python_callable=partner_service.drop_refund_sales_transaction_temp_table,
    )

    (
        [
            prepare_migration_result_table_task,
            prepare_batch_tracker_table_task,
            prepare_generate_sales_transaction_id_function_task,
            prepare_temp_tables_task,
        ]
        >> migrate_refund_sales_transaction_task
        >> drop_refund_sales_transaction_temp_table_task
    )


with DAG(
    "partner_service_incremental_migration_product_brand",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, ProductBrand incremental migration",
    schedule_interval="0 20 * * *",
    start_date=datetime(2025, 6, 22, 20, 0),
    catchup=False,
    tags=["partner_service", "incremental", "product_brand"],
) as partner_service_incremental_migration_product_brand_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_brand_table_task = PythonOperator(
        task_id="prepare_brand_table_task",
        python_callable=partner_service.prepare_brand_table_for_migration,
    )

    migrate_product_brand_task = PythonOperator(
        task_id="migrate_product_brand_task",
        python_callable=partner_service.migrate_incremental_product_brand,
    )

    (
        [
            prepare_migration_result_table_task,
            prepare_brand_table_task,
        ]
        >> migrate_product_brand_task
    )

with DAG(
    "partner_service_incremental_migration_product_category",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, ProductCategory incremental migration",
    schedule_interval="0 20 * * *",
    start_date=datetime(2025, 6, 22, 20, 0),
    catchup=False,
    tags=["partner_service", "incremental", "product_category"],
) as partner_service_incremental_migration_product_category_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_brand_table_task = PythonOperator(
        task_id="prepare_brand_table_task",
        python_callable=partner_service.prepare_brand_table_for_migration,
    )

    migrate_product_category_task = PythonOperator(
        task_id="migrate_product_category_task",
        python_callable=partner_service.migrate_incremental_product_category,
    )

    (
        [
            prepare_migration_result_table_task,
            prepare_brand_table_task,
        ]
        >> migrate_product_category_task
    )

with DAG(
    "partner_service_incremental_migration_sales_transaction_item",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, SalesTransactionItem incremental migration",
    schedule_interval="0 20 * * *",
    start_date=datetime(2025, 6, 22, 20, 0),
    catchup=False,
    tags=["partner_service", "incremental", "sales_transaction_item"],
) as partner_service_incremental_migration_sales_transaction_item_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_temp_tables_task = PythonOperator(
        task_id="prepare_temp_tables_task",
        python_callable=partner_service.prepare_temp_tables_for_sales_transaction_item,
        op_args=[False],
    )

    prepare_generate_sales_transaction_id_function_task = PythonOperator(
        task_id="prepare_generate_sales_transaction_id_function_task",
        python_callable=partner_service.prepare_generate_sales_transaction_id_function,
    )

    migrate_sales_transaction_item_task = PythonOperator(
        task_id="migrate_sales_transaction_item_task",
        python_callable=partner_service.migrate_incremental_sales_transaction_item,
    )

    (
        [
            prepare_migration_result_table_task,
            prepare_temp_tables_task,
            prepare_generate_sales_transaction_id_function_task,
        ]
        >> migrate_sales_transaction_item_task
    )

with DAG(
    "partner_service_incremental_migration_sales_transaction",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, SalesTransaction incremental migration",
    schedule_interval="0 20 * * *",
    start_date=datetime(2025, 6, 22, 20, 0),
    catchup=False,
    tags=["partner_service", "incremental", "sales_transaction"],
) as partner_service_incremental_migration_sales_transaction_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_temp_tables_task = PythonOperator(
        task_id="prepare_temp_tables_task",
        python_callable=partner_service.prepare_temp_tables_for_sales_transaction,
        op_args=[False],
    )

    prepare_generate_sales_transaction_id_function_task = PythonOperator(
        task_id="prepare_generate_sales_transaction_id_function_task",
        python_callable=partner_service.prepare_generate_sales_transaction_id_function,
    )

    migrate_sales_transaction_task = PythonOperator(
        task_id="migrate_sales_transaction_task",
        python_callable=partner_service.migrate_incremental_sales_transaction,
    )

    migrate_sales_transaction_without_lv_header_task = PythonOperator(
        task_id="migrate_sales_transaction_without_lv_header_task",
        python_callable=partner_service.migrate_incremental_sales_transaction_without_lv_header,
    )

    (
        [
            prepare_temp_tables_task,
            prepare_migration_result_table_task,
            prepare_generate_sales_transaction_id_function_task,
        ]
        >> migrate_sales_transaction_task
        >> migrate_sales_transaction_without_lv_header_task
    )

with DAG(
    "partner_service_incremental_migration_sales_transaction_payment",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, SalesTransactionPayment incremental migration",
    schedule_interval="0 20 * * *",
    start_date=datetime(2025, 6, 22, 20, 0),
    catchup=False,
    tags=["partner_service", "incremental", "sales_transaction_payment"],
) as partner_service_incremental_migration_sales_transaction_payment_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_generate_sales_transaction_id_function_task = PythonOperator(
        task_id="prepare_generate_sales_transaction_id_function_task",
        python_callable=partner_service.prepare_generate_sales_transaction_id_function,
    )

    migrate_sales_transaction_payment_task = PythonOperator(
        task_id="migrate_sales_transaction_payment_task",
        python_callable=partner_service.migrate_incremental_sales_transaction_payment,
    )

    [
        prepare_migration_result_table_task,
        prepare_generate_sales_transaction_id_function_task,
    ] >> migrate_sales_transaction_payment_task

with DAG(
    "partner_service_incremental_migration_sales_transaction_burn_payment",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, SalesTransactionBurnPayment incremental migration",
    schedule_interval="0 20 * * *",
    start_date=datetime(2025, 6, 22, 20, 0),
    catchup=False,
    tags=["partner_service", "incremental", "sales_transaction_burn_payment"],
) as partner_service_incremental_migration_sales_transaction_burn_payment_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    migrate_sales_transaction_burn_payment_task = PythonOperator(
        task_id="migrate_sales_transaction_burn_payment_task",
        python_callable=partner_service.migrate_incremental_sales_transaction_burn_payment,
    )

    prepare_migration_result_table_task >> migrate_sales_transaction_burn_payment_task

with DAG(
    "partner_service_incremental_migration_refund_sales_transaction",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, RefundSalesTransaction incremental migration",
    schedule_interval="0 20 * * *",
    start_date=datetime(2025, 6, 22, 20, 0),
    catchup=False,
    tags=["partner_service", "incremental", "refund_sales_transaction"],
) as partner_service_incremental_migration_refund_sales_transaction_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_generate_sales_transaction_id_function_task = PythonOperator(
        task_id="prepare_generate_sales_transaction_id_function_task",
        python_callable=partner_service.prepare_generate_sales_transaction_id_function,
    )

    prepare_temp_tables_task = PythonOperator(
        task_id="prepare_temp_tables_task",
        python_callable=partner_service.prepare_temp_tables_for_refund_sales_transaction,
        op_args=[False],
    )

    migrate_refund_sales_transaction_task = PythonOperator(
        task_id="migrate_refund_sales_transaction_task",
        python_callable=partner_service.migrate_incremental_refund_sales_transaction,
    )

    drop_refund_sales_transaction_temp_table_task = PythonOperator(
        task_id="drop_refund_sales_transaction_temp_table_task",
        python_callable=partner_service.drop_refund_sales_transaction_temp_table,
    )

    (
        [
            prepare_migration_result_table_task,
            prepare_generate_sales_transaction_id_function_task,
            prepare_temp_tables_task,
        ]
        >> migrate_refund_sales_transaction_task
        >> drop_refund_sales_transaction_temp_table_task
    )
