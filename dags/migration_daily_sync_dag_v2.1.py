from datetime import datetime, <PERSON><PERSON><PERSON>
import pytz
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from migration_utils.pipeline_ops import (
    process_service_tables,
    process_pre_transform_tables,
    add_sync_timestamp_column,
    process_service_tables_dailysync,
    process_remove_member_dailysync,
    test_kwargs,
    process_pre_transform_tables_dailysync,
    process_transformed_tables,
    process_transformed_tables_dailysync
)
from migration_utils.ulid_utils import generate_ulid, generate_ulid_dailysync
from _table_sequence import ALL_MIGRATION_TABLES, NOT_IN_TEMP_TABLES, DELETE_MEMBER_TABLES,GEN_ULID_TABLES



def run_crossdb_for_servic_dailysync(service_name: str, **kwargs):
    """Run cross DB operations for a specific service with incremental sync"""
    migrated_tables = [table for table in ALL_MIGRATION_TABLES if table not in NOT_IN_TEMP_TABLES]
    process_service_tables_dailysync(migrated_tables, service_name, "crossdb_dailysync", is_incremental=True, **kwargs)

def run_transform_load_for_service(**kwargs):
    """Run transform and load by order table"""
    process_transformed_tables_dailysync(ALL_MIGRATION_TABLES, **kwargs)

def run_update_member_remove(**kwargs):
    """Run detect member remove and remove from related tables for a specific service"""
    process_remove_member_dailysync(DELETE_MEMBER_TABLES, **kwargs)



def generate_ulid_for_service(**kwargs):
    for table_dict in GEN_ULID_TABLES:
        SOURCE_TABLE = table_dict['SOURCE_TABLE']
        TARGET_TABLE = table_dict['TARGET_TABLE']
        PK_NAME = table_dict['PK_NAME']
        generate_ulid_dailysync(
            SOURCE_TABLE=SOURCE_TABLE,
            TARGET_TABLE=TARGET_TABLE,
            PK_NAME=PK_NAME,
            **kwargs
        )


# Get the current time in Bangkok, then convert to UTC
now_bangkok = datetime.now(pytz.timezone('Asia/Bangkok'))
now_utc = now_bangkok.astimezone(pytz.utc)
# Calculate start_timestamps and end_timestamps based on UTC midnight
# default_end_timestamp = str(now_utc.replace(hour=22, minute=0, second=0, microsecond=0))
# default_start_timestamp = str((now_utc.replace(hour=17, minute=0, second=0, microsecond=0)) - timedelta(days=1))
# default_end_timestamp =  '2025-06-09 22:00:00+00:00'
# default_start_timestamp =  '2025-06-08 17:00:00+00:00'
# default_end_timestamp =  '2025-06-10 22:00:00+00:00'
# default_start_timestamp =  '2025-06-09 17:00:00+00:00'
# default_end_timestamp =  '2025-06-13 22:00:00+00:00'
# default_start_timestamp =  '2025-06-10 17:00:00+00:00'
# default_end_timestamp =  '2025-06-15 22:00:00+00:00'
# default_start_timestamp =  '2025-06-14 17:00:00+00:00'
# default_end_timestamp =  '2025-06-23 22:00:00+00:00'
# default_start_timestamp =  '2025-06-20 03:00:00+00:00'
# default_end_timestamp =  '2025-06-24 22:00:00+00:00'
# default_start_timestamp =  '2025-06-23 17:00:00+00:00'
default_end_timestamp =  '2025-06-26 22:00:00+00:00'
default_start_timestamp =  '2025-06-24 17:00:00+00:00'

with DAG(
    dag_id="migration_daily_sync_v2.1",
    schedule_interval="0 22 * * *", # # 0 22 * * *base on utc +0000 this is 5am bangkok time (utc+0700)
    # schedule_interval=None, # # 0 22 * * *base on utc +0000 this is 5am bangkok time (utc+0700)
    start_date=datetime(2025, 6, 4, 23, 00),
    catchup=False,
    tags=["crossdb_stg_gwl", "daily_sync"],
    params= {
        'start_timestamps': default_start_timestamp,
        'end_timestamps': default_end_timestamp,   
    }
) as dag:
    
    start_migration_task = EmptyOperator(task_id="start_migration")
    end_migration_task = EmptyOperator(task_id="end_migration")
    
    

    # Step 1: Cross DB operations for each service in sequence (incremental)
    service_names = ["engagement_service", "partner_service", "loyalty_service", "point_service"]
    crossdb_tasks = {}
    for idx, service in enumerate(service_names, 1):
        task = PythonOperator(
            task_id=f"1_{idx}_crossdb_sync_{service}",
            python_callable=run_crossdb_for_servic_dailysync,
            op_args=[service],
        )
        crossdb_tasks[service] = task

    # Step 2: Pre-transform step (follows PRE_TRANSFORM_ORDER)
    pre_transform_task = PythonOperator(
        task_id="2_pre_transform_tables",
        python_callable=process_pre_transform_tables_dailysync,
    )

    # Step 3: Gen ULID
    generate_ulid_task = PythonOperator(
        task_id="3_generate_ulid",
        python_callable=generate_ulid_for_service,
    )

    # Step 4: Transform and load for each service in sequence
    transform_task = PythonOperator(
        task_id=f"4_transform_load_tables",
        python_callable=run_transform_load_for_service
    )

    # Step 5: Transform and load for each service in sequence
    run_update_member_remove_task = PythonOperator(
        task_id="5_update_member_remove_related_tables",
        python_callable=run_update_member_remove
    )


    # Step 6: Trigger Validation DAG
    trigger_validation_dag_task = TriggerDagRunOperator(
        task_id='6_trigger_validation',
        trigger_dag_id='validation_temp_to_gwl',
        wait_for_completion=True,  # Wait for previous dag to complete before continuing
        poke_interval=60  # Check every 60 seconds
    )

    # # Step 7: Trigger prod sep DAG
    # trigger_dailysync_gwl_sep_service_dag_task = TriggerDagRunOperator(
    #     task_id='7_dailysync_gwl_sep_service',
    #     trigger_dag_id='migratoin_daily_sync_gwl_sep_service_dag',
    #     wait_for_completion=True,  # Wait for previous dag to complete before continuing
    #     poke_interval=60  # Check every 60 seconds
    # )

    # Set dependencies
    start_migration_task >> [crossdb_tasks[service] for service in service_names]
    
    # Wait all crossdb_tasks of all service done then run pre_transform_task
    [crossdb_tasks[service] for service in service_names] >> pre_transform_task
    
    # # Connect pre-transform to transform task
    pre_transform_task >> generate_ulid_task
    generate_ulid_task >> transform_task

   
    transform_task >> trigger_validation_dag_task
    
    # run_update_member_remove_task >> trigger_dailysync_gwl_sep_service_dag_task
    trigger_validation_dag_task >> run_update_member_remove_task >> end_migration_task

    # trigger_dailysync_gwl_sep_service_dag_task >> end_migration_task

  

