from datetime import datetime, timedelta, timezone

from psycopg2.extensions import connection as postgres_connection

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.logging import get_logger
from common_helpers.utils import create_migration_result_table
from point_service.wallet_activity import WalletActivity
from point_service.wallet_balance import WalletBalance
from point_service.wallet_adjustment_transaction import WalletAdjustmentTransaction
from point_service.wallet_transaction import WalletTransaction

from airflow import DAG
from airflow.models import Variable
from airflow.operators.python_operator import PythonOperator

logger = get_logger()


class PointService:
    def __init__(self):
        self.service_name = "point_service"
        self.batch_size = int(
            Variable.get(f"{self.service_name}.batch_size", default_var=10000),
        )
        self.executor_max_workers = int(
            Variable.get(f"{self.service_name}.executor_max_workers", default_var=5),
        )
        self.incremental_query_date = Variable.get(
            f"{self.service_name}.incremental_query_date", default_var=None
        )
        self.loyalty_value_handler = MSSQLHandler(
            conn_id="loyalty_value_smc_db_connection_id"
        )
        self.postgresql_handler = PostgresHandler(conn_id="temp_db_connection_id")

    def create_lv_data_snapshot(self) -> None:
        current_utc = datetime.now(timezone.utc)
        formatted_date = current_utc.strftime("%Y%m%d")

        query_string = f"""
            IF OBJECT_ID('snapshot_lv_data_{formatted_date}', 'U') IS NULL
            BEGIN
                SELECT *
                INTO snapshot_lv_data_{formatted_date}
                FROM LVData;
            END
        """

        loyalty_value_connection = self.loyalty_value_handler.hook.get_conn()

        try:
            logger.info(f"started creating daily LVData snapshot...")
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=query_string,
            )
            logger.info(f"finished creating daily LVData snapshot.")
        finally:
            loyalty_value_connection.close()

    def prepare_wallet_table_for_migration(
        self, connection: postgres_connection
    ) -> None:
        """
        Insert rows into point_service.Wallet, with 4 mandatory wallets, CARAT_WALLET, CASH_WALLET,
        EPURSE_BD_CASHBACK and EPURSE_PROMO_WALLET, which is needed for other migration flows.

        Args:
            None

        Returns:
            None
        """
        insert_query = """
            INSERT INTO
                "point_service"."Wallet" (
                    "id",
                    "runningId",
                    "code",
                    "name",
                    "walletTypeCode",
                    "status",
                    "description",
                    "currency",
                    "image",
                    "createdAt",
                    "updatedAt",
                    "updatedBy"
                )
            VALUES
                ('01JE2QN8JHQ0HC5FHRRZBVAQ7Q', 1, 'CARAT_WALLET', 'Carat Wallet', 'POINT', 'ACTIVE', 'Wallet to collect Carat for Kingpower member', 'CARAT', NULL, '2024-12-02 03:47:08.375', '2024-12-02 03:47:08.375', '{}'),
                ('01JE2QNAEF61ZGPARVX5T5M9KM', 2, 'CASH_WALLET', 'Cash Wallet', 'CASH', 'ACTIVE', 'Wallet to top up member''s money in to wallet via counter service (via GV)', 'THB', NULL, '2024-12-02 03:47:08.882', '2024-12-02 03:47:08.882', '{}'),
                ('01JE2QNAFN6T702AQ1M7VGDT67', 3, 'EPURSE_BD_CASHBACK', 'Birthday Cashback', 'BIRTHDAY_CASHBACK', 'ACTIVE', 'Wallet to receive cashback amount when members use birthday privilege coupon', 'THB', NULL, '2024-12-02 03:47:08.918', '2024-12-02 03:47:08.918', '{}'),
                ('01JKG09W2KNN6538ZH42WQ60PX', 4, 'EPURSE_PROMO_WALLET', 'E-Purse', 'EPURSE', 'ACTIVE', 'Wallet to receive promotion cashback', 'THB', NULL, '2024-12-02 03:47:08.918', '2024-12-02 03:47:08.918', '{}'),
                ('01JNNFJCD6PWTVPY522DRCGZ98', 5, 'EPURSE_PROMO_RANGNAM', 'Rangnam', 'EPURSE', 'ACTIVE', 'Wallet to receive promotion cashback, only allowed to use at Rangnam', 'THB', NULL, '2024-12-02 03:47:08.918', '2024-12-02 03:47:08.918', '{}')
            ON CONFLICT ("code") DO NOTHING;
        """

        self.postgresql_handler.execute_with_rollback(
            connection=connection,
            query_string=insert_query,
        )

    def prepare_adjustment_reason_table_for_migration(
        self,
        connection: postgres_connection,
    ) -> None:
        """
        Insert a row into point_service.AdjustmentReason, with code of MIGRATED_FROM_SMC,
        which is needed for point_service.WalletAdjustmentTransaction migration.

        Args:
            None

        Returns:
            None
        """
        insert_query = """
            INSERT INTO "point_service"."AdjustmentReason" (
                "code",
                "name",
                "type",
                "createdAt",
                "updatedAt"
            )
            VALUES ('MIGRATED_FROM_SMC', '', '', NOW () AT TIME ZONE 'UTC', NOW () AT TIME ZONE 'UTC')
            ON CONFLICT ("code") DO NOTHING;
        """

        self.postgresql_handler.execute_with_rollback(
            connection=connection, query_string=insert_query
        )

    def prepare_tables(self):
        """
        Prepare mandatory table(s) for Point Service migration.

        Args:
            None

        Returns:
            None
        """
        postgresql_connection = self.postgresql_handler.hook.get_conn()

        try:
            logger.info(f"started preparing Wallet table for migration...")
            self.prepare_wallet_table_for_migration(postgresql_connection)
            logger.info(f"finished preparing Wallet table for migration.")

            logger.info(f"started preparing AdjustmentReason table for migration...")
            self.prepare_adjustment_reason_table_for_migration(postgresql_connection)
            logger.info(f"finished preparing AdjustmentReason table for migration.")
        except Exception as error:
            logger.error(f"an error has occured: {error}")

            if postgresql_connection:
                logger.info("a postgresql connection is found, rolling back...")
                postgresql_connection.rollback()
                logger.info("successfully rolled back.")

            raise error
        finally:
            if postgresql_connection:
                postgresql_connection.close()

    def prepare_batch_tracker_table(self):
        """
        Prepare a table to track the migration process.

        Args:
            None

        Returns:
            None
        """
        create_table_query_string = """
            CREATE TABLE IF NOT EXISTS point_service.batch_tracker (
                table_name VARCHAR(100) PRIMARY KEY,
                total_records INT NOT NULL,
                completed_batches JSONB NOT NULL DEFAULT '[]'::jsonb,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """
        postgresql_connection = self.postgresql_handler.hook.get_conn()

        try:
            logger.info(f"started preparing batch tracker table for migration...")
            self.postgresql_handler.execute_with_rollback(
                connection=postgresql_connection,
                query_string=create_table_query_string,
            )
            logger.info(f"finished preparing batch tracker table for migration.")
        finally:
            postgresql_connection.close()

    def get_incremental_query_condition(self, date_field: str) -> str:
        if self.incremental_query_date is None:
            return f"{date_field} >= DATEADD(DAY, -1, CAST(CAST(GETDATE() AS DATE) AS DATETIME)) AND {date_field} < CAST(CAST(GETDATE() AS DATE) AS DATETIME)"

        return f"{date_field} >= CAST('{self.incremental_query_date}' AS DATETIME) AND {date_field} < DATEADD(DAY, 1, CAST('{self.incremental_query_date}' AS DATETIME))"

    def prepare_temp_tables_for_wallet_activity(self, is_full_dump: bool = True):
        """
        Prepare indexed temporary tables for WalletActivity migration.

        Args:
            is_full_dump (bool): Is the migration type full dump.

        Returns:
            None
        """
        create_smc_sales_header_temp_table = (
            """
                IF OBJECT_ID('Newmember.dbo.temp_smc_sales_header_for_wallet_activity_full_dump', 'U') IS NULL
                BEGIN
                    SELECT
                        key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS key_search, DataDate, Site
                    INTO Newmember.dbo.temp_smc_sales_header_for_wallet_activity_full_dump
                    FROM Newmember.dbo.SMCSalesHeader;
                    
                    CREATE INDEX temp_smc_sales_header_for_wallet_activity_full_dump_key_search ON Newmember.dbo.temp_smc_sales_header_for_wallet_activity_full_dump (key_search);
                END;
            """
            if is_full_dump
            else f"""
                DROP TABLE IF EXISTS Newmember.dbo.temp_smc_sales_header_for_wallet_activity_incremental;

                SELECT
                    key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS key_search, DataDate, Site
                INTO Newmember.dbo.temp_smc_sales_header_for_wallet_activity_incremental
                FROM Newmember.dbo.SMCSalesHeader
                WHERE {self.get_incremental_query_condition('DataDate')};
                CREATE INDEX temp_smc_sales_header_for_wallet_activity_incremental_key_search ON Newmember.dbo.temp_smc_sales_header_for_wallet_activity_incremental (key_search);
            """
        )

        create_lv_header_temp_table = (
            """
                IF OBJECT_ID('temp_lv_header_for_wallet_activity_full_dump', 'U') IS NULL
                BEGIN
                    SELECT
                        KeySearch COLLATE SQL_Latin1_General_CP1_CI_AS AS KeySearch,
                        LVHeaderKey,
                        BranchNo,
                        AddUser,
                        AddDT,
                        CASE
                            WHEN FinishDT IS NOT NULL THEN FinishDT
                            ELSE AddDT
                        END AS FinishDT,
                        DocDate
                    INTO temp_lv_header_for_wallet_activity_full_dump
                    FROM LVHeader;
                    
                    CREATE INDEX temp_lv_header_for_wallet_activity_full_dump_key_search ON temp_lv_header_for_wallet_activity_full_dump (KeySearch);
                    CREATE INDEX temp_lv_header_for_wallet_activity_full_dump_lv_header_key ON temp_lv_header_for_wallet_activity_full_dump (LVHeaderKey);
                END;
            """
            if is_full_dump
            else f"""
                DROP TABLE IF EXISTS temp_lv_header_for_wallet_activity_incremental;

                SELECT
                    KeySearch COLLATE SQL_Latin1_General_CP1_CI_AS AS KeySearch,
                    LVHeaderKey,
                    BranchNo,
                    AddUser,
                    AddDT,
                    CASE
                        WHEN FinishDT IS NOT NULL THEN FinishDT
                        ELSE AddDT
                    END AS FinishDT,
                    DocDate
                INTO temp_lv_header_for_wallet_activity_incremental
                FROM LVHeader
                WHERE {self.get_incremental_query_condition('DocDate')};
                CREATE INDEX temp_lv_header_for_wallet_activity_incremental_key_search ON temp_lv_header_for_wallet_activity_incremental (KeySearch);
                CREATE INDEX temp_lv_header_for_wallet_activity_incremental_lv_header_key ON temp_lv_header_for_wallet_activity_incremental (LVHeaderKey);
            """
        )

        loyalty_value_connection = self.loyalty_value_handler.hook.get_conn()

        try:
            logger.info(f"started preparing SMCSalesHeader temp table for migration...")
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_smc_sales_header_temp_table,
            )
            logger.info(f"finished preparing SMCSalesHeader temp table for migration.")

            logger.info(f"started preparing LVHeader temp table for migration...")
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_lv_header_temp_table,
            )
            logger.info(f"finished preparing LVHeader temp table for migration.")
        finally:
            loyalty_value_connection.close()

    def cleanup_wallet_activity_temp_tables(self):
        """
        Clean up temporary tables created for WalletActivity full dump migration.

        Args:
            None

        Returns:
            None
        """
        cleanup_queries = """
            DROP TABLE IF EXISTS Newmember.dbo.temp_smc_sales_header_for_wallet_activity_full_dump;
            DROP TABLE IF EXISTS temp_lv_header_for_wallet_activity_full_dump;
        """

        loyalty_value_connection = self.loyalty_value_handler.hook.get_conn()

        try:
            logger.info(f"started cleaning up WalletActivity temporary tables...")
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=cleanup_queries,
            )
            logger.info(f"finished cleaning up WalletActivity temporary tables.")
        finally:
            loyalty_value_connection.close()

    def cleanup_wallet_transaction_temp_tables(self):
        """
        Clean up temporary tables created for WalletTransaction full dump migration.

        Args:
            None

        Returns:
            None
        """
        query_string = """
            DROP TABLE IF EXISTS temp_wallet_transaction_for_full_dump_migration;
        """

        loyalty_value_connection = self.loyalty_value_handler.hook.get_conn()

        try:
            logger.info(f"started cleaning up WalletTransaction temporary tables...")
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=query_string,
            )
            logger.info(f"finished cleaning up WalletTransaction temporary tables.")
        finally:
            loyalty_value_connection.close()

    def migrate_full_dump_wallet_activity(self):
        """
        The main function for WalletActivity migration task.

        Args:
            None

        Returns:
            None
        """
        WalletActivity(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_full_dump()

    def migrate_incremental_wallet_activity(self):
        """
        The main function for WalletActivity migration task.

        Args:
            None

        Returns:
            None
        """
        WalletActivity(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
            incremental_query_date=self.incremental_query_date,
        ).migrate_incremental()

    def migrate_full_dump_wallet_balance(self):
        """
        The main function for WalletBalance full dump migration task.

        Args:
            None

        Returns:
            None
        """
        WalletBalance(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_full_dump()

    def migrate_incremental_wallet_balance(self):
        """
        The main function for WalletBalance migration task.

        Args:
            None

        Returns:
            None
        """
        WalletBalance(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
            incremental_query_date=self.incremental_query_date,
        ).migrate_incremental()

    def migrate_full_dump_wallet_transaction(self):
        """
        The main function for WalletTransaction full dump migration task.

        Args:
            None

        Returns:
            None
        """
        WalletTransaction(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_full_dump()

    def migrate_incremental_wallet_transaction(self):
        """
        The main function for WalletTransaction incremental migration task.

        Args:
            None

        Returns:
            None
        """
        WalletTransaction(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
            incremental_query_date=self.incremental_query_date,
        ).migrate_incremental()

    def migrate_full_dump_wallet_adjustment_transaction(self):
        """
        The main function for WalletAdjustmentTransaction migration task.

        Args:
            None

        Returns:
            None
        """
        WalletAdjustmentTransaction(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_full_dump()

    def migrate_incremental_wallet_adjustment_transaction(self):
        """
        The main function for WalletAdjustmentTransaction incremental migration task.

        Args:
            None

        Returns:
            None
        """
        WalletAdjustmentTransaction(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
            incremental_query_date=self.incremental_query_date,
        ).migrate_incremental()



with DAG(
    "point_service_full_dump_migration_wallet_activity",
    default_args={
        "owner": "airflow",
    },
    description="Point Service, WalletActivity full dump migration",
    schedule_interval=None,
    start_date=datetime(2025, 2, 26),
    catchup=False,
    tags=["point_service", "full_dump", "wallet_activity"],
) as point_service_full_dump_migration_wallet_activity_dag:
    point_service = PointService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_batch_tracker_table_task = PythonOperator(
        task_id="prepare_batch_tracker_table_task",
        python_callable=point_service.prepare_batch_tracker_table,
    )

    prepare_tables_task = PythonOperator(
        task_id="prepare_tables_task",
        python_callable=point_service.prepare_tables,
    )

    prepare_temp_tables_task = PythonOperator(
        task_id="prepare_temp_tables_task",
        python_callable=point_service.prepare_temp_tables_for_wallet_activity,
        op_args=[True],
    )

    migrate_wallet_activity_task = PythonOperator(
        task_id="migrate_wallet_activity_task",
        retries=3,
        retry_delay=timedelta(minutes=5),
        python_callable=point_service.migrate_full_dump_wallet_activity,
    )

    cleanup_temporary_tables_task = PythonOperator(
        task_id="cleanup_temporary_tables_task",
        python_callable=point_service.cleanup_wallet_activity_temp_tables,
    )

    (
        [
            prepare_migration_result_table_task,
            prepare_batch_tracker_table_task,
            prepare_tables_task,
            prepare_temp_tables_task,
        ]
        >> migrate_wallet_activity_task
        >> cleanup_temporary_tables_task
    )


with DAG(
    "point_service_full_dump_migration_wallet_balance",
    default_args={
        "owner": "airflow",
    },
    description="Point Service, WalletBalance full dump migration",
    schedule_interval=None,
    start_date=datetime(2025, 2, 26),
    catchup=False,
    tags=["point_service", "full_dump", "wallet_balance"],
) as point_service_full_dump_migration_wallet_balance_dag:
    point_service = PointService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_batch_tracker_table_task = PythonOperator(
        task_id="prepare_batch_tracker_table_task",
        python_callable=point_service.prepare_batch_tracker_table,
    )

    prepare_tables_task = PythonOperator(
        task_id="prepare_tables_task",
        python_callable=point_service.prepare_tables,
    )

    migrate_wallet_balance_task = PythonOperator(
        task_id="migrate_wallet_balance_task",
        retries=3,
        retry_delay=timedelta(minutes=5),
        python_callable=point_service.migrate_full_dump_wallet_balance,
    )

    (
        [
            prepare_migration_result_table_task,
            prepare_batch_tracker_table_task,
            prepare_tables_task,
        ]
        >> migrate_wallet_balance_task
    )


with DAG(
    "point_service_full_dump_migration_wallet_transaction",
    default_args={
        "owner": "airflow",
    },
    description="Point Service, WalletTransaction full dump migration",
    schedule_interval=None,
    start_date=datetime(2025, 2, 26),
    catchup=False,
    tags=["point_service", "full_dump", "wallet_transaction"],
) as point_service_full_dump_migration_wallet_transaction_dag:
    point_service = PointService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_batch_tracker_table_task = PythonOperator(
        task_id="prepare_batch_tracker_table_task",
        python_callable=point_service.prepare_batch_tracker_table,
    )

    prepare_tables_task = PythonOperator(
        task_id="prepare_tables_task",
        python_callable=point_service.prepare_tables,
    )

    migrate_wallet_transaction_task = PythonOperator(
        task_id="migrate_wallet_transaction_task",
        retries=3,
        retry_delay=timedelta(minutes=5),
        python_callable=point_service.migrate_full_dump_wallet_transaction,
    )

    cleanup_wallet_transaction_temp_tables_task = PythonOperator(
        task_id="cleanup_wallet_transaction_temp_tables_task",
        python_callable=point_service.cleanup_wallet_transaction_temp_tables,
    )

    (
        [
            prepare_migration_result_table_task,
            prepare_batch_tracker_table_task,
            prepare_tables_task,
        ]
        >> migrate_wallet_transaction_task
        >> cleanup_wallet_transaction_temp_tables_task
    )


with DAG(
    "point_service_full_dump_migration_wallet_adjustment_transaction",
    default_args={
        "owner": "airflow",
    },
    description="Point Service, WalletAdjustmentTransaction full dump migration",
    schedule_interval=None,
    start_date=datetime(2025, 2, 26),
    catchup=False,
    tags=["point_service", "full_dump", "wallet_adjustment_transaction"],
) as point_service_full_dump_migration_wallet_adjustment_transaction_dag:
    point_service = PointService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_batch_tracker_table_task = PythonOperator(
        task_id="prepare_batch_tracker_table_task",
        python_callable=point_service.prepare_batch_tracker_table,
    )

    prepare_tables_task = PythonOperator(
        task_id="prepare_tables_task",
        python_callable=point_service.prepare_tables,
    )

    migrate_wallet_adjustment_transaction_task = PythonOperator(
        task_id="migrate_wallet_adjustment_transaction_task",
        retries=3,
        retry_delay=timedelta(minutes=5),
        python_callable=point_service.migrate_full_dump_wallet_adjustment_transaction,
    )

    [
        prepare_migration_result_table_task,
        prepare_batch_tracker_table_task,
        prepare_tables_task,
    ] >> migrate_wallet_adjustment_transaction_task


with DAG(
    "point_service_incremental_migration_wallet_activity",
    default_args={
        "owner": "airflow",
    },
    description="Point Service, WalletActivity incremental migration",
    schedule_interval="0 20 * * *",
    start_date=datetime(2025, 6, 22, 20, 0),
    catchup=False,
    tags=["point_service", "incremental", "wallet_activity"],
) as point_service_incremental_migration_wallet_activity_dag:
    point_service = PointService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_tables_task = PythonOperator(
        task_id="prepare_tables_task",
        python_callable=point_service.prepare_tables,
    )

    prepare_temp_tables_task = PythonOperator(
        task_id="prepare_temp_tables_task",
        python_callable=point_service.prepare_temp_tables_for_wallet_activity,
        op_args=[False],
    )

    migrate_wallet_activity_task = PythonOperator(
        task_id="migrate_wallet_activity_task",
        python_callable=point_service.migrate_incremental_wallet_activity,
    )

    (
        [
            prepare_migration_result_table_task,
            prepare_tables_task,
            prepare_temp_tables_task,
        ]
        >> migrate_wallet_activity_task
    )


with DAG(
    "point_service_incremental_migration_wallet_balance",
    default_args={
        "owner": "airflow",
    },
    description="Point Service, WalletBalance incremental migration",
    schedule_interval="0 17 * * *",
    start_date=datetime(2025, 6, 22, 17, 0),
    catchup=False,
    tags=["point_service", "incremental", "wallet_balance"],
) as point_service_incremental_migration_wallet_balance_dag:
    point_service = PointService()

    create_lv_data_snapshot_task = PythonOperator(
        task_id="create_lv_data_snapshot_task",
        python_callable=point_service.create_lv_data_snapshot,
    )

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_tables_task = PythonOperator(
        task_id="prepare_tables_task",
        python_callable=point_service.prepare_tables,
    )

    migrate_wallet_balance_task = PythonOperator(
        task_id="migrate_wallet_balance_task",
        python_callable=point_service.migrate_incremental_wallet_balance,
    )

    (
        [
            create_lv_data_snapshot_task,
            prepare_migration_result_table_task,
            prepare_tables_task,
        ]
        >> migrate_wallet_balance_task
    )


with DAG(
    "point_service_incremental_migration_wallet_transaction",
    default_args={
        "owner": "airflow",
    },
    description="Point Service, WalletTransaction incremental migration",
    schedule_interval="0 20 * * *",
    start_date=datetime(2025, 6, 22, 20, 0),
    catchup=False,
    tags=["point_service", "incremental", "wallet_transaction"],
) as point_service_incremental_migration_wallet_transaction_dag:
    point_service = PointService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_tables_task = PythonOperator(
        task_id="prepare_tables_task",
        python_callable=point_service.prepare_tables,
    )

    migrate_wallet_transaction_task = PythonOperator(
        task_id="migrate_wallet_transaction_task",
        python_callable=point_service.migrate_incremental_wallet_transaction,
    )

    [
        prepare_migration_result_table_task,
        prepare_tables_task,
    ] >> migrate_wallet_transaction_task


with DAG(
    "point_service_incremental_migration_wallet_adjustment_transaction",
    default_args={
        "owner": "airflow",
    },
    description="Point Service, WalletAdjustmentTransaction incremental migration",
    schedule_interval="0 20 * * *",
    start_date=datetime(2025, 6, 22, 20, 0),
    catchup=False,
    tags=["point_service", "incremental", "wallet_adjustment_transaction"],
) as point_service_incremental_migration_wallet_adjustment_transaction_dag:
    point_service = PointService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_tables_task = PythonOperator(
        task_id="prepare_tables_task",
        python_callable=point_service.prepare_tables,
    )

    migrate_wallet_adjustment_transaction_task = PythonOperator(
        task_id="migrate_wallet_adjustment_transaction_task",
        python_callable=point_service.migrate_incremental_wallet_adjustment_transaction,
    )

    [
        prepare_migration_result_table_task,
        prepare_tables_task,
    ] >> migrate_wallet_adjustment_transaction_task

