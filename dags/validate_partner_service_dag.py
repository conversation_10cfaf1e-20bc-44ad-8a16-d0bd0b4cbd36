from datetime import datetime

from airflow import DAG
from airflow.operators.bash_operator import <PERSON><PERSON><PERSON><PERSON>ator
from airflow.operators.python_operator import Python<PERSON>perator
from airflow.sensors.external_task import ExternalTaskSensor

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from partner_service.validate_sales_transaction_item import (
    SalesTransactionItemValidation,
)
from partner_service.validate_product_brand import ProductBrandValidation
from partner_service.validate_product_category import ProductCategoryValidation
from partner_service.validate_sales_transaction_burn_payment import (
    SalesTransactionBurnPaymentValidation,
)
from partner_service.validate_sales_transaction_payment import (
    SalesTransactionPaymentValidation,
)
from partner_service.validate_sales_transaction import SalesTransactionValidation
from partner_service.validate_refund_sales_transaction import (
    RefundSalesTransactionValidation,
)


with DAG(
    "validate_partner_service_full_dump",
    default_args={
        "owner": "airflow",
    },
    description="A dag for Partner Service validation.",
    schedule_interval=None,
    start_date=datetime(2025, 6, 1),
    catchup=False,
    tags=["partner_service", "validation"],
) as validate_partner_service_full_dump_dag:
    loyalty_value_handler = MSSQLHandler(conn_id="loyalty_value_smc_db_connection_id")
    newmember_handler = MSSQLHandler(conn_id="newmember_smc_db_connection_id")
    temp_db_handler = PostgresHandler(conn_id="temp_db_connection_id")

    product_brand_validation = ProductBrandValidation(
        mssql_handler=newmember_handler,
        postgresql_handler=temp_db_handler,
    )

    validate_product_brand_task = PythonOperator(
        task_id="validate_product_brand_task",
        python_callable=product_brand_validation.validate,
        op_args=[temp_db_handler, "PartnerService"],
    )

    product_category_validation = ProductCategoryValidation(
        mssql_handler=newmember_handler,
        postgresql_handler=temp_db_handler,
    )

    validate_product_category_task = PythonOperator(
        task_id="validate_product_category_task",
        python_callable=product_category_validation.validate,
        op_args=[temp_db_handler, "PartnerService"],
    )

    sales_transaction_burn_payment_validation = SalesTransactionBurnPaymentValidation(
        mssql_handler=loyalty_value_handler,
        postgresql_handler=temp_db_handler,
    )

    validate_sales_transaction_burn_payment_task = PythonOperator(
        task_id="validate_sales_transaction_burn_payment_task",
        python_callable=sales_transaction_burn_payment_validation.validate,
        op_args=[temp_db_handler, "PartnerService"],
    )

    sales_transaction_payment_validation = SalesTransactionPaymentValidation(
        mssql_handler=loyalty_value_handler,
        postgresql_handler=temp_db_handler,
    )

    validate_sales_transaction_payment_task = PythonOperator(
        task_id="validate_sales_transaction_payment_task",
        python_callable=sales_transaction_payment_validation.validate,
        op_args=[temp_db_handler, "PartnerService"],
    )

    sales_transaction_validation = SalesTransactionValidation(
        mssql_handler=loyalty_value_handler,
        postgresql_handler=temp_db_handler,
    )

    validate_sales_transaction_task = PythonOperator(
        task_id="validate_sales_transaction_task",
        python_callable=sales_transaction_validation.validate,
        op_args=[temp_db_handler, "PartnerService"],
    )

    sales_transaction_item_validation = SalesTransactionItemValidation(
        mssql_handler=loyalty_value_handler,
        postgresql_handler=temp_db_handler,
    )

    validate_sales_transaction_item_task = PythonOperator(
        task_id="validate_sales_transaction_item_task",
        python_callable=sales_transaction_item_validation.validate,
        op_args=[temp_db_handler, "PartnerService"],
    )

    refund_sales_transaction_validation = RefundSalesTransactionValidation(
        mssql_handler=loyalty_value_handler,
        postgresql_handler=temp_db_handler,
    )

    validate_refund_sales_transaction_task = PythonOperator(
        task_id="validate_refund_sales_transaction_task",
        python_callable=refund_sales_transaction_validation.validate,
        op_args=[temp_db_handler, "PartnerService"],
    )

    [
        validate_product_brand_task,
        validate_product_category_task,
        validate_sales_transaction_burn_payment_task,
        validate_sales_transaction_payment_task,
        validate_sales_transaction_task,
        validate_sales_transaction_item_task,
        validate_refund_sales_transaction_task,
    ]

with DAG(
    "validate_partner_service_incremental",
    default_args={
        "owner": "airflow",
    },
    description="A dag for Partner Service incremental validation.",
    schedule_interval="0 20 * * *",
    start_date=datetime(2025, 6, 24, 20, 0),
    catchup=False,
    tags=["partner_service", "validation"],
) as validate_partner_service_incremental_dag:
    loyalty_value_handler = MSSQLHandler(conn_id="loyalty_value_smc_db_connection_id")
    newmember_handler = MSSQLHandler(conn_id="newmember_smc_db_connection_id")
    destination_db_handler = PostgresHandler(conn_id="temp_db_connection_id")

    # Wait for all migrations to complete
    wait_for_product_brand_incremental_migration_task = ExternalTaskSensor(
        task_id="wait_for_product_brand_incremental_migration_task",
        external_dag_id="partner_service_incremental_migration_product_brand",
        external_task_id="migrate_product_brand_task",
    )

    wait_for_product_category_incremental_migration_task = ExternalTaskSensor(
        task_id="wait_for_product_category_incremental_migration_task",
        external_dag_id="partner_service_incremental_migration_product_category",
        external_task_id="migrate_product_category_task",
    )

    wait_for_sales_transaction_burn_payment_incremental_migration_task = ExternalTaskSensor(
        task_id="wait_for_sales_transaction_burn_payment_incremental_migration_task",
        external_dag_id="partner_service_incremental_migration_sales_transaction_burn_payment",
        external_task_id="migrate_sales_transaction_burn_payment_task",
    )

    wait_for_sales_transaction_item_incremental_migration_task = ExternalTaskSensor(
        task_id="wait_for_sales_transaction_item_incremental_migration_task",
        external_dag_id="partner_service_incremental_migration_sales_transaction_item",
        external_task_id="migrate_sales_transaction_item_task",
    )

    wait_for_sales_transaction_payment_incremental_migration_task = ExternalTaskSensor(
        task_id="wait_for_sales_transaction_payment_incremental_migration_task",
        external_dag_id="partner_service_incremental_migration_sales_transaction_payment",
        external_task_id="migrate_sales_transaction_payment_task",
    )

    wait_for_sales_transaction_incremental_migration_task = ExternalTaskSensor(
        task_id="wait_for_sales_transaction_incremental_migration_task",
        external_dag_id="partner_service_incremental_migration_sales_transaction",
        external_task_id="migrate_sales_transaction_without_lv_header_task",
    )

    wait_for_refund_sales_transaction_incremental_migration_task = ExternalTaskSensor(
        task_id="wait_for_refund_sales_transaction_incremental_migration_task",
        external_dag_id="partner_service_incremental_migration_refund_sales_transaction",
        external_task_id="migrate_refund_sales_transaction_task",
    )

    # Validation tasks
    product_brand_validation = ProductBrandValidation(
        mssql_handler=newmember_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_product_brand_task = PythonOperator(
        task_id="validate_product_brand_task",
        python_callable=product_brand_validation.validate,
        op_args=[destination_db_handler, "PartnerService", False],
    )

    product_category_validation = ProductCategoryValidation(
        mssql_handler=newmember_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_product_category_task = PythonOperator(
        task_id="validate_product_category_task",
        python_callable=product_category_validation.validate,
        op_args=[destination_db_handler, "PartnerService", False],
    )

    refund_sales_transaction_validation = RefundSalesTransactionValidation(
        mssql_handler=loyalty_value_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_refund_sales_transaction_task = PythonOperator(
        task_id="validate_refund_sales_transaction_task",
        python_callable=refund_sales_transaction_validation.validate,
        op_args=[destination_db_handler, "PartnerService", False],
    )

    sales_transaction_burn_payment_validation = SalesTransactionBurnPaymentValidation(
        mssql_handler=loyalty_value_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_sales_transaction_burn_payment_task = PythonOperator(
        task_id="validate_sales_transaction_burn_payment_task",
        python_callable=sales_transaction_burn_payment_validation.validate,
        op_args=[destination_db_handler, "PartnerService", False],
    )

    sales_transaction_item_validation = SalesTransactionItemValidation(
        mssql_handler=loyalty_value_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_sales_transaction_item_task = PythonOperator(
        task_id="validate_sales_transaction_item_task",
        python_callable=sales_transaction_item_validation.validate,
        op_args=[destination_db_handler, "PartnerService", False],
    )

    sales_transaction_payment_validation = SalesTransactionPaymentValidation(
        mssql_handler=loyalty_value_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_sales_transaction_payment_task = PythonOperator(
        task_id="validate_sales_transaction_payment_task",
        python_callable=sales_transaction_payment_validation.validate,
        op_args=[destination_db_handler, "PartnerService", False],
    )

    sales_transaction_validation = SalesTransactionValidation(
        mssql_handler=loyalty_value_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_sales_transaction_task = PythonOperator(
        task_id="validate_sales_transaction_task",
        python_callable=sales_transaction_validation.validate,
        op_args=[destination_db_handler, "PartnerService", False],
    )

    delay_after_migrations = BashOperator(
        task_id="delay_after_migrations",
        bash_command="sleep 300",
    )

    validate_partner_service_incremental_final_task = BashOperator(
        task_id="validate_partner_service_incremental_final_task",
        bash_command="sleep 30",
    )

    # Wait for all migrations, then delay, then run all validations in parallel
    (
        [
            wait_for_product_brand_incremental_migration_task,
            wait_for_product_category_incremental_migration_task,
            wait_for_refund_sales_transaction_incremental_migration_task,
            wait_for_sales_transaction_burn_payment_incremental_migration_task,
            wait_for_sales_transaction_incremental_migration_task,
            wait_for_sales_transaction_item_incremental_migration_task,
            wait_for_sales_transaction_payment_incremental_migration_task,
        ]
        >> delay_after_migrations
        >> [
            validate_product_brand_task,
            validate_product_category_task,
            validate_refund_sales_transaction_task,
            validate_sales_transaction_burn_payment_task,
            validate_sales_transaction_item_task,
            validate_sales_transaction_payment_task,
            validate_sales_transaction_task,
        ]
        >> validate_partner_service_incremental_final_task
    )

with DAG(
    "validate_partner_service_incremental_manual",
    default_args={
        "owner": "airflow",
    },
    description="A dag for Partner Service incremental manual validation.",
    schedule_interval=None,
    start_date=datetime(2025, 6, 22),
    catchup=False,
    tags=["partner_service", "validation"],
) as validate_partner_service_incremental_manual_dag:
    loyalty_value_handler = MSSQLHandler(conn_id="loyalty_value_smc_db_connection_id")
    newmember_handler = MSSQLHandler(conn_id="newmember_smc_db_connection_id")
    destination_db_handler = PostgresHandler(conn_id="temp_db_connection_id")

    # ProductBrand incremental validation
    product_brand_validation = ProductBrandValidation(
        mssql_handler=newmember_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_product_brand_task = PythonOperator(
        task_id="validate_product_brand_task",
        python_callable=product_brand_validation.validate,
        op_args=[destination_db_handler, "PartnerService", False],
    )

    # ProductCategory incremental validation
    product_category_validation = ProductCategoryValidation(
        mssql_handler=newmember_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_product_category_task = PythonOperator(
        task_id="validate_product_category_task",
        python_callable=product_category_validation.validate,
        op_args=[destination_db_handler, "PartnerService", False],
    )

    # SalesTransactionBurnPayment incremental validation
    sales_transaction_burn_payment_validation = SalesTransactionBurnPaymentValidation(
        mssql_handler=loyalty_value_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_sales_transaction_burn_payment_task = PythonOperator(
        task_id="validate_sales_transaction_burn_payment_task",
        python_callable=sales_transaction_burn_payment_validation.validate,
        op_args=[destination_db_handler, "PartnerService", False],
    )

    # SalesTransactionItem incremental validation
    sales_transaction_item_validation = SalesTransactionItemValidation(
        mssql_handler=loyalty_value_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_sales_transaction_item_task = PythonOperator(
        task_id="validate_sales_transaction_item_task",
        python_callable=sales_transaction_item_validation.validate,
        op_args=[destination_db_handler, "PartnerService", False],
    )

    # SalesTransactionPayment incremental validation
    sales_transaction_payment_validation = SalesTransactionPaymentValidation(
        mssql_handler=loyalty_value_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_sales_transaction_payment_task = PythonOperator(
        task_id="validate_sales_transaction_payment_task",
        python_callable=sales_transaction_payment_validation.validate,
        op_args=[destination_db_handler, "PartnerService", False],
    )

    # SalesTransaction incremental validation
    sales_transaction_validation = SalesTransactionValidation(
        mssql_handler=loyalty_value_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_sales_transaction_task = PythonOperator(
        task_id="validate_sales_transaction_task",
        python_callable=sales_transaction_validation.validate,
        op_args=[destination_db_handler, "PartnerService", False],
    )

    # RefundSalesTransaction incremental validation
    refund_sales_transaction_validation = RefundSalesTransactionValidation(
        mssql_handler=loyalty_value_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_refund_sales_transaction_task = PythonOperator(
        task_id="validate_refund_sales_transaction_task",
        python_callable=refund_sales_transaction_validation.validate,
        op_args=[destination_db_handler, "PartnerService", False],
    )

    [
        validate_product_brand_task,
        validate_product_category_task,
        validate_sales_transaction_burn_payment_task,
        validate_sales_transaction_item_task,
        validate_sales_transaction_payment_task,
        validate_sales_transaction_task,
        validate_refund_sales_transaction_task,
    ]
