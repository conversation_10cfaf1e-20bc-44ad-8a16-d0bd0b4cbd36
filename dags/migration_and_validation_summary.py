from datetime import datetime
from airflow import DAG
from airflow.operators.bash import <PERSON><PERSON><PERSON><PERSON><PERSON>
from airflow.operators.python import <PERSON><PERSON><PERSON><PERSON>
from airflow.sensors.external_task import ExternalTaskSensor
import pytz

from common_helpers.mail_service import send_mail_notification
from common_helpers.logging import get_logger
from common_helpers.database_services import <PERSON>g<PERSON><PERSON><PERSON><PERSON>
from common_helpers.utils import get_df
from constants import MAIL_MIGRATION_RESULT_SUBJECT, MAIL_MIGRATION_RESULT_TEXT, MAIL_VALIDATION_RESULT_SUBJECT, MAIL_VALIDATION_RESULT_TEXT, TEMP_CONN_ID

logger = get_logger()


def migration_summary():
    postgres = PostgresHandler(conn_id=TEMP_CONN_ID)
    tz = pytz.timezone("Asia/Bangkok")
    now = datetime.now(tz)

    dest_query = f"""
        SELECT
            *
        FROM
            smc2temp_migration_result stmr
        WHERE
            stmr.migration_type = 'INCREMENTAL'
            AND 
            stmr.created_at >= CAST('{now.strftime('%Y-%m-%d')}' AS DATE) - INTERVAL '7 hours'
        ORDER BY
            dag_name,
            destination_table
    """
    logger.info(f"dest_query: {dest_query}")
    df = get_df(dest_query, postgres)
    
    # Debug logging to check DataFrame
    logger.info(f"DataFrame shape: {df.shape}")
    logger.info(f"DataFrame columns: {df.columns.tolist()}")
    logger.info(f"DataFrame head: {df.head()}")
    logger.info(f"DataFrame is empty: {df.empty}")

    send_mail_notification(
        subject=MAIL_MIGRATION_RESULT_SUBJECT.format(date=now.strftime('%d/%m/%Y')),
        text=MAIL_MIGRATION_RESULT_TEXT.format(date=now.strftime('%d/%m/%Y')),
        filename=f"migration_result_{now.strftime('%d-%m-%Y')}.csv",
        df=df,
    )


def validation_summary():
    postgres = PostgresHandler(conn_id=TEMP_CONN_ID)
    tz = pytz.timezone("Asia/Bangkok")
    now = datetime.now(tz)

    dest_query = f"""
        SELECT
            *
        FROM
            validation_results vr
        WHERE
            vr.created_at >= CAST('{now.strftime('%Y-%m-%d')}' AS DATE) - INTERVAL '7 hours'
        ORDER BY
            service,
            destination_table
    """
    logger.info(f"dest_query: {dest_query}")
    df = get_df(dest_query, postgres)
    
    # Debug logging to check DataFrame
    logger.info(f"DataFrame shape: {df.shape}")
    logger.info(f"DataFrame columns: {df.columns.tolist()}")
    logger.info(f"DataFrame head: {df.head()}")
    logger.info(f"DataFrame is empty: {df.empty}")

    send_mail_notification(
        subject=MAIL_VALIDATION_RESULT_SUBJECT.format(date=now.strftime('%d/%m/%Y')),
        text=MAIL_VALIDATION_RESULT_TEXT.format(date=now.strftime('%d/%m/%Y')),
        filename=f"validation_result_{now.strftime('%d-%m-%Y')}.csv",
        df=df,
    )


with DAG(
    "summary_migration",
    description="Send Email summary migration after all migration tasks are completed",
    schedule_interval="0 17 * * *",
    start_date=datetime(2025, 6, 24, 17, 0),
    catchup=False,
    tags=["summary"],
) as dag:
    # partner service
    wait_for_product_brand_incremental_migration_task = ExternalTaskSensor(
        task_id="wait_for_product_brand_incremental_migration_task",
        external_dag_id="partner_service_incremental_migration_product_brand",
        external_task_id="migrate_product_brand_task",
    )

    wait_for_product_category_incremental_migration_task = ExternalTaskSensor(
        task_id="wait_for_product_category_incremental_migration_task",
        external_dag_id="partner_service_incremental_migration_product_category",
        external_task_id="migrate_product_category_task",
    )

    wait_for_sales_transaction_burn_payment_incremental_migration_task = ExternalTaskSensor(
        task_id="wait_for_sales_transaction_burn_payment_incremental_migration_task",
        external_dag_id="partner_service_incremental_migration_sales_transaction_burn_payment",
        external_task_id="migrate_sales_transaction_burn_payment_task",
    )

    wait_for_sales_transaction_item_incremental_migration_task = ExternalTaskSensor(
        task_id="wait_for_sales_transaction_item_incremental_migration_task",
        external_dag_id="partner_service_incremental_migration_sales_transaction_item",
        external_task_id="migrate_sales_transaction_item_task",
    )

    wait_for_sales_transaction_payment_incremental_migration_task = ExternalTaskSensor(
        task_id="wait_for_sales_transaction_payment_incremental_migration_task",
        external_dag_id="partner_service_incremental_migration_sales_transaction_payment",
        external_task_id="migrate_sales_transaction_payment_task",
    )

    wait_for_sales_transaction_incremental_migration_task = ExternalTaskSensor(
        task_id="wait_for_sales_transaction_incremental_migration_task",
        external_dag_id="partner_service_incremental_migration_sales_transaction",
        external_task_id="migrate_sales_transaction_without_lv_header_task",
    )

    wait_for_refund_sales_transaction_incremental_migration_task = ExternalTaskSensor(
        task_id="wait_for_refund_sales_transaction_incremental_migration_task",
        external_dag_id="partner_service_incremental_migration_refund_sales_transaction",
        external_task_id="migrate_refund_sales_transaction_task",
    )

    # loyalty service
    wait_for_migration_loyalty_service_task = ExternalTaskSensor(
        task_id="wait_for_migration_loyalty_service_task",
        external_dag_id="loyalty_service_incremental_migration_scheduled",
        external_task_id="drop_temp_table_for_update_member_spending_task",
    )

    # point service
    wait_for_wallet_activity_incremental_migration_task = ExternalTaskSensor(
        task_id="wait_for_wallet_activity_incremental_migration_task",
        external_dag_id="point_service_incremental_migration_wallet_activity",
        external_task_id="migrate_wallet_activity_task",
    )

    wait_for_wallet_adjustment_transaction_incremental_migration_task = ExternalTaskSensor(
        task_id="wait_for_wallet_adjustment_transaction_incremental_migration_task",
        external_dag_id="point_service_incremental_migration_wallet_adjustment_transaction",
        external_task_id="migrate_wallet_adjustment_transaction_task",
    )

    wait_for_wallet_transaction_incremental_migration_task = ExternalTaskSensor(
        task_id="wait_for_wallet_transaction_incremental_migration_task",
        external_dag_id="point_service_incremental_migration_wallet_transaction",
        external_task_id="migrate_wallet_transaction_task",
    )

    delay_after_migrations = BashOperator(
        task_id="delay_after_migrations",
        bash_command="sleep 300",
    )

    migration_summary = PythonOperator(
        task_id="migration_summary",
        python_callable=migration_summary,
    )

    (
        [
            wait_for_product_brand_incremental_migration_task,
            wait_for_product_category_incremental_migration_task,
            wait_for_sales_transaction_burn_payment_incremental_migration_task,
            wait_for_sales_transaction_item_incremental_migration_task,
            wait_for_sales_transaction_payment_incremental_migration_task,
            wait_for_sales_transaction_incremental_migration_task,
            wait_for_refund_sales_transaction_incremental_migration_task,
            wait_for_migration_loyalty_service_task,
            wait_for_wallet_activity_incremental_migration_task,
            wait_for_wallet_adjustment_transaction_incremental_migration_task,
            wait_for_wallet_transaction_incremental_migration_task,
        ]
        >> delay_after_migrations
        >> migration_summary 
    )


with DAG(
    "summary_validation",
    description="Send Email summary validation after all validation tasks are completed",
    schedule_interval="0 17 * * *",
    start_date=datetime(2025, 6, 24, 17, 0),
    catchup=False,
    tags=["summary"],
) as dag:
    # partner service
    wait_for_partner_service_final_task = ExternalTaskSensor(
        task_id="wait_for_partner_service_final_task",
        external_dag_id="validate_partner_service_incremental",
        external_task_id="validate_partner_service_incremental_final_task",
    )

    # loyalty service
    wait_for_loyalty_final_task = ExternalTaskSensor(
        task_id="wait_for_loyalty_final_task",
        external_dag_id="validate_loyalty_service_all_incremental",
        external_task_id="validate_member_profile_task",
    )

    # point service
    wait_for_point_service_final_task = ExternalTaskSensor(
        task_id="wait_for_point_service_final_task",
        external_dag_id="validate_point_service_incremental",
        external_task_id="validate_point_service_incremental_final_task",
    )

    delay_after_validations = BashOperator(
        task_id="delay_after_validations",
        bash_command="sleep 300",
    )

    validation_summary = PythonOperator(
        task_id="validation_summary",
        python_callable=validation_summary,
    )

    (
        [
            wait_for_partner_service_final_task,
            wait_for_loyalty_final_task,
            wait_for_point_service_final_task,
        ]
        >> delay_after_validations
        >> validation_summary 
    )
