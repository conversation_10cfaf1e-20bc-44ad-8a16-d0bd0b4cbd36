import time
from datetime import datetime

from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.sensors.external_task import ExternalTaskSensor

from common_helpers.logging import get_logger

logger = get_logger()


def sleep_for_one_minute():
    """Task that sleeps for 1 minute before completion."""
    logger.info("Starting sleep task...")
    time.sleep(60)  # Sleep for 60 seconds
    logger.info("Sleep task completed after 1 minute.")


def dependent_task():
    """Task that runs after the first DAG's task completes."""
    logger.info("Dependent task is now running!")
    logger.info("This task waited for the first DAG's task to complete.")


# First DAG - starts at 17:05 UTC+7
with DAG(
    "test_first_dag",
    default_args={
        "owner": "airflow",
    },
    description="First test DAG that sleeps for 1 minute",
    schedule_interval="55 10 * * *",  # 17:05 UTC+7 = 10:05 UTC
    start_date=datetime(2025, 6, 23, 10, 55),  # UTC time
    catchup=False,
    tags=["test", "external_task_sensor"],
) as test_first_dag:

    sleep_task = PythonOperator(
        task_id="sleep_task",
        python_callable=sleep_for_one_minute,
    )


# Second DAG - starts at 17:10 UTC+7
with DAG(
    "test_second_dag",
    default_args={
        "owner": "airflow",
    },
    description="Second test DAG that waits for first DAG to complete",
    schedule_interval="0 11 * * *",  # 17:10 UTC+7 = 10:10 UTC
    start_date=datetime(2025, 6, 24, 11, 0),  # UTC time
    catchup=False,
    tags=["test", "external_task_sensor"],
) as test_second_dag:

    wait_for_first_dag_task = ExternalTaskSensor(
        task_id="wait_for_first_dag_task",
        external_dag_id="test_first_dag",
        external_task_id="sleep_task",
    )

    dependent_task_op = PythonOperator(
        task_id="dependent_task",
        python_callable=dependent_task,
    )

    wait_for_first_dag_task >> dependent_task_op
