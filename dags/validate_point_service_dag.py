from datetime import datetime, timedelta

from airflow import DAG
from airflow.operators.bash_operator import BashOperator
from airflow.operators.python_operator import PythonOperator
from airflow.sensors.external_task import ExternalTaskSensor

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>gresHandler
from point_service.validate_wallet_activity import WalletActivityValidation
from point_service.validate_wallet_adjustment_transaction import (
    WalletAdjustmentTransactionValidation,
)
from point_service.validate_wallet_balance import WalletBalanceValidation
from point_service.validate_wallet_transaction import WalletTransactionValidation


with DAG(
    "validate_point_service_full_dump",
    default_args={
        "owner": "airflow",
    },
    description="A dag for Point Service validation.",
    schedule_interval=None,
    start_date=datetime(2025, 6, 1),
    catchup=False,
    tags=["point_service", "validation"],
) as validate_point_service_full_dump_dag:
    loyalty_value_handler = MSSQLHandler(conn_id="loyalty_value_smc_db_connection_id")
    temp_db_handler = PostgresHandler(conn_id="temp_db_connection_id")

    wallet_activity_validation = WalletActivityValidation(
        mssql_handler=loyalty_value_handler,
        postgresql_handler=temp_db_handler,
    )

    validate_wallet_activity_task = PythonOperator(
        task_id="validate_wallet_activity_task",
        python_callable=wallet_activity_validation.validate,
        op_args=[temp_db_handler, "PointService"],
    )

    wallet_adjustment_transaction_validation = WalletAdjustmentTransactionValidation(
        mssql_handler=loyalty_value_handler,
        postgresql_handler=temp_db_handler,
    )

    validate_wallet_adjustment_transaction_task = PythonOperator(
        task_id="validate_wallet_adjustment_transaction_task",
        python_callable=wallet_adjustment_transaction_validation.validate,
        op_args=[temp_db_handler, "PointService"],
    )

    wallet_balance_validation = WalletBalanceValidation(
        mssql_handler=loyalty_value_handler,
        postgresql_handler=temp_db_handler,
    )

    validate_wallet_balance_task = PythonOperator(
        task_id="validate_wallet_balance_task",
        python_callable=wallet_balance_validation.validate,
        op_args=[temp_db_handler, "PointService"],
    )

    wallet_transaction_validation = WalletTransactionValidation(
        mssql_handler=loyalty_value_handler,
        postgresql_handler=temp_db_handler,
    )

    validate_wallet_transaction_task = PythonOperator(
        task_id="validate_wallet_transaction_task",
        python_callable=wallet_transaction_validation.validate,
        op_args=[temp_db_handler, "PointService"],
    )

    [
        validate_wallet_activity_task,
        validate_wallet_adjustment_transaction_task,
        validate_wallet_balance_task,
        validate_wallet_transaction_task,
    ]

with DAG(
    "validate_point_service_incremental_wallet_balance",
    default_args={
        "owner": "airflow",
    },
    description="Point Service WalletBalance incremental validation - runs at midnight UTC+7",
    schedule_interval="0 17 * * *",
    start_date=datetime(2025, 6, 24, 17, 0),
    catchup=False,
    tags=["point_service", "validation", "wallet_balance"],
) as validate_point_service_incremental_wallet_balance_dag:
    source_db_handler = MSSQLHandler(conn_id="loyalty_value_smc_db_connection_id")
    destination_db_handler = PostgresHandler(conn_id="temp_db_connection_id")

    # Wait for migration to complete
    wait_for_wallet_balance_incremental_migration_task = ExternalTaskSensor(
        task_id="wait_for_wallet_balance_incremental_migration_task",
        external_dag_id="point_service_incremental_migration_wallet_balance",
        external_task_id="migrate_wallet_balance_task",
    )

    # WalletBalance incremental validation
    wallet_balance_validation = WalletBalanceValidation(
        mssql_handler=source_db_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_wallet_balance_task = PythonOperator(
        task_id="validate_wallet_balance_task",
        python_callable=wallet_balance_validation.validate,
        op_args=[destination_db_handler, "PointService"],
    )

    # 5 minute delay after migrations complete
    delay_after_migrations = BashOperator(
        task_id="delay_after_migrations",
        bash_command="sleep 300",
    )

    (
        wait_for_wallet_balance_incremental_migration_task
        >> delay_after_migrations
        >> validate_wallet_balance_task
    )


with DAG(
    "validate_point_service_incremental",
    default_args={
        "owner": "airflow",
    },
    description="Point Service incremental validation (except WalletBalance) - runs at 3am UTC+7",
    schedule_interval="0 20 * * *",
    start_date=datetime(2025, 6, 24, 20, 0),
    catchup=False,
    tags=["point_service", "validation"],
) as validate_point_service_incremental_dag:
    source_db_handler = MSSQLHandler(conn_id="loyalty_value_smc_db_connection_id")
    destination_db_handler = PostgresHandler(conn_id="temp_db_connection_id")

    # Wait for all migrations to complete
    wait_for_wallet_activity_incremental_migration_task = ExternalTaskSensor(
        task_id="wait_for_wallet_activity_incremental_migration_task",
        external_dag_id="point_service_incremental_migration_wallet_activity",
        external_task_id="migrate_wallet_activity_task",
    )

    wait_for_wallet_adjustment_transaction_incremental_migration_task = ExternalTaskSensor(
        task_id="wait_for_wallet_adjustment_transaction_incremental_migration_task",
        external_dag_id="point_service_incremental_migration_wallet_adjustment_transaction",
        external_task_id="migrate_wallet_adjustment_transaction_task",
    )

    wait_for_wallet_transaction_incremental_migration_task = ExternalTaskSensor(
        task_id="wait_for_wallet_transaction_incremental_migration_task",
        external_dag_id="point_service_incremental_migration_wallet_transaction",
        external_task_id="migrate_wallet_transaction_task",
    )

    # Validation tasks
    wallet_activity_validation = WalletActivityValidation(
        mssql_handler=source_db_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_wallet_activity_task = PythonOperator(
        task_id="validate_wallet_activity_task",
        python_callable=wallet_activity_validation.validate,
        op_args=[destination_db_handler, "PointService", False],
    )

    wallet_adjustment_transaction_validation = WalletAdjustmentTransactionValidation(
        mssql_handler=source_db_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_wallet_adjustment_transaction_task = PythonOperator(
        task_id="validate_wallet_adjustment_transaction_task",
        python_callable=wallet_adjustment_transaction_validation.validate,
        op_args=[destination_db_handler, "PointService", False],
    )

    wallet_transaction_validation = WalletTransactionValidation(
        mssql_handler=source_db_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_wallet_transaction_task = PythonOperator(
        task_id="validate_wallet_transaction_task",
        python_callable=wallet_transaction_validation.validate,
        op_args=[destination_db_handler, "PointService", False],
    )

    delay_after_migrations = BashOperator(
        task_id="delay_after_migrations",
        bash_command="sleep 300",
    )

    validate_point_service_incremental_final_task = BashOperator(
        task_id="validate_point_service_incremental_final_task",
        bash_command="sleep 30",
    )

    # Wait for all migrations, then delay, then run all validations in parallel
    (
        [
            wait_for_wallet_activity_incremental_migration_task,
            wait_for_wallet_adjustment_transaction_incremental_migration_task,
            wait_for_wallet_transaction_incremental_migration_task,
        ]
        >> delay_after_migrations
        >> [
            validate_wallet_activity_task,
            validate_wallet_adjustment_transaction_task,
            validate_wallet_transaction_task,
        ]
        >> validate_point_service_incremental_final_task
    )

with DAG(
    "validate_point_service_incremental_manual",
    default_args={
        "owner": "airflow",
    },
    description="A dag for Point Service incremental manual validation.",
    schedule_interval=None,
    start_date=datetime(2025, 6, 22),
    catchup=False,
    tags=["point_service", "validation"],
) as validate_point_service_incremental_manual_dag:
    source_db_handler = MSSQLHandler(conn_id="loyalty_value_smc_db_connection_id")
    destination_db_handler = PostgresHandler(conn_id="temp_db_connection_id")

    # WalletActivity incremental validation
    wallet_activity_validation = WalletActivityValidation(
        mssql_handler=source_db_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_wallet_activity_task = PythonOperator(
        task_id="validate_wallet_activity_task",
        python_callable=wallet_activity_validation.validate,
        op_args=[destination_db_handler, "PointService", False],
    )

    # WalletAdjustmentTransaction incremental validation
    wallet_adjustment_transaction_validation = WalletAdjustmentTransactionValidation(
        mssql_handler=source_db_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_wallet_adjustment_transaction_task = PythonOperator(
        task_id="validate_wallet_adjustment_transaction_task",
        python_callable=wallet_adjustment_transaction_validation.validate,
        op_args=[destination_db_handler, "PointService", False],
    )

    # WalletBalance incremental validation
    wallet_balance_validation = WalletBalanceValidation(
        mssql_handler=source_db_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_wallet_balance_task = PythonOperator(
        task_id="validate_wallet_balance_task",
        python_callable=wallet_balance_validation.validate,
        op_args=[destination_db_handler, "PointService"],
    )

    # WalletTransaction incremental validation
    wallet_transaction_validation = WalletTransactionValidation(
        mssql_handler=source_db_handler,
        postgresql_handler=destination_db_handler,
    )

    validate_wallet_transaction_task = PythonOperator(
        task_id="validate_wallet_transaction_task",
        python_callable=wallet_transaction_validation.validate,
        op_args=[destination_db_handler, "PointService", False],
    )

    [
        validate_wallet_activity_task,
        validate_wallet_adjustment_transaction_task,
        validate_wallet_balance_task,
        validate_wallet_transaction_task,
    ]
