from datetime import datetime
import pytz
from airflow import DAG
from airflow.operators.python import PythonOperator
from migration_utils.migration_ops import fetch_detect_remove_crossdb_dailysync_gwl_prod
from collections import OrderedDict

def remove_crossdb_gwl_to_sep_service_dailysync(table_name, conn_name, **kwargs):
    """
    Run detect member remove and remove from related tables for a specific service
    """
    fetch_detect_remove_crossdb_dailysync_gwl_prod(table_name, prod_service_conn_name=conn_name, **kwargs)
    print(f"Data for {table_name} removed successfully.")

table_to_remove = OrderedDict([
    ('loyalty_service."Member"', 'loyalty_service_conn'),
    # ('engagement_service."MemberPrivilege"', 'engagement_service_conn'),
    ('engagement_service."MemberCoupon"', 'engagement_service_conn'),
    ('engagement_service."MemberCouponActivity"', 'engagement_service_conn'),
    ('partner_service."SalesTransaction"', 'partner_service_conn'),
    ('point_service."WalletActivity"', 'point_service_conn'),
    ('point_service."WalletBalance"', 'point_service_conn')
])


# Get the current time in Bangkok, then convert to UTC
now_bangkok = datetime.now(pytz.timezone('Asia/Bangkok'))
now_utc = now_bangkok.astimezone(pytz.utc)

# default_end_timestamp =  '2025-06-16 22:00:00+00:00'
# default_start_timestamp =  '2025-06-15 17:00:00+00:00'
# default_end_timestamp =  '2025-06-13 22:00:00+00:00'
# default_start_timestamp =  '2025-06-07 17:00:00+00:00'
default_end_timestamp =  '2025-06-25 22:00:00+00:00'
default_start_timestamp =  '2025-06-22 17:00:00+00:00'

with DAG(
    dag_id="remove_gwl_sep_service_dailysync_dag",
    start_date=None,
    schedule_interval=None,
    catchup=False,
    tags=["gwl_sep_service_dailysync", "prod", 'dailysync'],
    params= {
        'start_timestamps': default_start_timestamp,
        'end_timestamps': default_end_timestamp,   
    }
) as dag:

    previous_task = None
    ### remove data ###
    for table, conn in table_to_remove.items():
        task = PythonOperator(
            task_id=f'''remove_{table.replace('.', '_').replace('"', '')}''',
            python_callable=remove_crossdb_gwl_to_sep_service_dailysync,
            op_args=[table, conn],
        )
        if previous_task is not None:
            previous_task >> task
        previous_task = task
