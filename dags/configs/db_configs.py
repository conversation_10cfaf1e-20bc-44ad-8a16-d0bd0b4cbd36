from airflow.models import Variable

class DBConfig:

    # temp database
    temp_dbname = Variable.get('temp_dbname', '')
    temp_user = Variable.get('temp_user', '')
    temp_password = Variable.get('temp_password', '')
    temp_host = Variable.get('temp_host', '')
    temp_port = Variable.get('temp_port', '')
    # gwl database
    gwl_dbname = Variable.get('gwl_dbname', '')
    gwl_user = Variable.get('gwl_user', '')
    gwl_password = Variable.get('gwl_password', '')
    gwl_host = Variable.get('gwl_host', '')
    gwl_port = Variable.get('gwl_port', '')
    # SECRET_PII_ENCRYPTION
    secret_pii_encryption = Variable.get('SECRET_PII_ENCRYPTION', '')
