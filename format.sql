SELECT DISTINCT
    CAST(ssh.key_search AS CHAR(30)) COLLATE THAI_CI_AS AS KeySearch INTO TmpT0_192_168_1_103
FROM
    Newmember.dbo.SMCSalesHeader ssh
WITH
    (NOLOCK)
    INNER JOIN Newmember.dbo.SMCSalesTrans sst
WITH
    (NOLOCK) ON ssh.key_search = sst.key_search
    AND sst.lineCancel = 0
    AND sst.CancelStatus = 0
    INNER JOIN Newmember.dbo.SMCSalesPayment ssp
WITH
    (NOLOCK) ON ssh.key_search = ssp.key_search
    INNER JOIN MAST_NonAccCarat mnac
WITH
    (NOLOCK) ON ssp.MethodCode = mnac.code
    AND mnac.type = 'P'
    AND mnac.IsCancel = 0
    INNER JOIN df_member
WITH
    (NOLOCK) ON CAST(ssh.member_id AS CHAR(8)) = df_member.member_id
WHERE
    ssh.SaleStatus <> 'R';

CREATE NONCLUSTERED INDEX ix_tmpT0 ON TmpT0_192_168_1_103 (KeySearch);

--
SELECT
    CAST(L1.KeySearch AS CHAR(30)) COLLATE THAI_CI_AS AS KeySearch,
    SUM(-1 * l2.amount) AS net INTO TmpT1_192_168_1_103
FROM
    LVHeader L1
WITH
    (NOLOCK)
    INNER JOIN LVTrans L2
WITH
    (NOLOCK) ON L1.LVHeaderKey = L2.LVHeaderKey
    INNER JOIN LVdata L3
WITH
    (NOLOCK) ON L2.LVMainKey = L3.LVMainKey
    INNER JOIN df_member
WITH
    (NOLOCK) ON l2.LVNumber COLLATE THAI_CI_AS = df_member.member_id COLLATE THAI_CI_AS
    INNER JOIN TmpT0_192_168_1_103 L4 ON CAST(L1.KeySearch AS CHAR(30)) COLLATE THAI_CI_AS = L4.KeySearch
WHERE
    L3.valuecode IN (
        'EP001',
        'EP007',
        'EP008',
        'EP009',
        'EP010',
        'KPC01',
        'KPO02'
    )
    AND l2.movementcode = 'USE'
GROUP BY
    CAST(L1.KeySearch AS CHAR(30));

--
CREATE NONCLUSTERED INDEX ix_tmpT1 ON TmpT1_192_168_1_103 (KeySearch);

SELECT
    t1.BranchNo,
    CAST(t1.key_search AS CHAR(30)) COLLATE THAI_CI_AS AS key_search,
    t1.salesBranch,
    CAST(t1.member_id AS CHAR(8)) AS member_id,
    SUM(t2.qty) AS qty INTO TmpT2_192_168_1_103
FROM
    Newmember.dbo.SMCSalesHeader t1
WITH
    (NOLOCK)
    INNER JOIN Newmember.dbo.SMCSalesTrans t2
WITH
    (NOLOCK) ON t1.key_search = t2.key_search
    AND lineCancel = 0
    AND CancelStatus = 0
    INNER JOIN df_member
WITH
    (NOLOCK) ON CAST(t1.member_id AS CHAR(8)) = df_member.member_id
WHERE
    t1.saleStatus <> 'R'
GROUP BY
    t1.BranchNo,
    t1.key_search,
    t1.salesBranch,
    t1.member_id;

CREATE NONCLUSTERED INDEX ix_tmpT2 ON TmpT2_192_168_1_103 (key_search);

--
SELECT
    * INTO TmpT3_192_168_1_103
FROM
    TmpT2_192_168_1_103 T2
    INNER JOIN TmpT1_192_168_1_103 T1 ON T2.key_search = T1.KeySearch;

CREATE NONCLUSTERED INDEX ix_tmpT3 ON TmpT3_192_168_1_103 (key_search);

--
SELECT
    x.member_id,
    x.key_search,
    SUM(t3.net) AS total INTO TmpT4_192_168_1_103
FROM
    (
        SELECT
            t1.key_search,
            CAST(t1.member_id AS CHAR(8)) AS member_id
        FROM
            Newmember.dbo.SMCSalesHeader t1
        WITH
            (NOLOCK)
            INNER JOIN Newmember.dbo.SMCSalesTrans t2
        WITH
            (NOLOCK) ON t1.key_search = t2.key_search
            AND t2.lineCancel = 0
            AND t2.CancelStatus = 0
            INNER JOIN df_member
        WITH
            (NOLOCK) ON CAST(t1.member_id AS CHAR(8)) = df_member.member_id
        WHERE
            t1.saleStatus <> 'R'
        GROUP BY
            t1.BranchNo,
            t1.key_search,
            t1.salesBranch,
            t1.member_id
    ) x
    INNER JOIN Newmember.dbo.SMCSalesPayment t3
WITH
    (NOLOCK) ON x.key_search = t3.key_search
    AND t3.MethodCode NOT IN (
        SELECT
            Code
        FROM
            MAST_NonAccCarat
        WHERE
            type = 'P'
            AND iscancel = 0
    )
GROUP BY
    x.key_search,
    x.member_id;

CREATE NONCLUSTERED INDEX ix_tmpT4 ON TmpT4_192_168_1_103 (key_search);

--
SELECT
    z.key_search,
    z.totalEarnableAmount,
    z.totalAccumSpendableAmount INTO TmpT5_192_168_1_103
FROM
    (
        SELECT
            key_search,
            net AS totalEarnableAmount,
            net AS totalAccumSpendableAmount
        FROM
            TmpT3_192_168_1_103
        UNION
        SELECT
            key_search,
            total AS totalEarnableAmount,
            total AS totalAccumSpendableAmount
        FROM
            TmpT4_192_168_1_103
    ) z;

CREATE NONCLUSTERED INDEX ix_tmpT5 ON TmpT5_192_168_1_103 (key_search);